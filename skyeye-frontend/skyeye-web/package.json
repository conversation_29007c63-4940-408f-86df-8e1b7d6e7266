{"name": "skyeyecloud", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@chenfengyuan/vue-barcode": "^2.0.2", "@codemirror/autocomplete": "^6.18.4", "@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^6.7.1", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/language": "^6.10.8", "@codemirror/lint": "^6.8.4", "@codemirror/search": "^6.5.8", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.36.1", "@fortawesome/fontawesome-free": "^6.5.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.2.6", "axios": "^1.6.7", "bpmn-js": "^18.1.2", "bpmn-js-properties-panel": "^1.0.0", "camunda-bpmn-moddle": "^7.0.1", "dhtmlx-gantt": "^9.0.3", "diagram-js-minimap": "^5.2.0", "dompurify": "^3.2.4", "echarts": "^5.6.0", "highlight.js": "^11.10.0", "html2canvas": "^1.4.1", "js-beautify": "^1.15.1", "jspdf": "^2.5.2", "md-editor-v3": "^5.0.2", "path": "^0.12.7", "pdf-lib": "^1.17.1", "qrcode-vue3": "^1.7.1", "sass": "^1.69.0", "stats.js": "^0.17.0", "three": "^0.172.0", "viewerjs": "^1.11.7", "vue": "^3.3.4", "vue-i18n": "^9.14.1", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "less": "^4.2.0", "vite": "^5.0.0"}}