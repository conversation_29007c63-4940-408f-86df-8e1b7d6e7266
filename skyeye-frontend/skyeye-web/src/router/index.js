import { createRouter, createWebHashHistory } from 'vue-router'
import $config from '@/plugins/getConfig'
import exampleRoutes from './modules/example.js'
import baseRoutes from './modules/base.js'
import uniExampleRoutes from './modules/uni-example.js'
import shopRoutes from './modules/shop.js'
import erpRoutes from './modules/erp.js'
import oaRoutes from './modules/oa.js'
import erpProduce from './modules/erp-produce.js'
import sealServiceRoutes from './modules/seal-service.js'
import schoolManagement from './modules/school.js'
import activitiRoutes from './modules/activiti.js'
import ifsRoutes from './modules/ifs.js'
import crmRoutes from './modules/crm.js'
import adminRoutes from './modules/admin-assistant.js'
import bossRoutes from './modules/boss.js'
import projectRoutes from './modules/project.js'
import reportRoutes from './modules/report.js'
import userauthRoutes from './modules/userauth.js'
import wagesRoutes from './modules/wages.js'
import checkWorkRoutes from './modules/checkwork.js'
import knowlgRoutes from './modules/knowlg.js'




const routes = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/login/index.vue')
    },
    {
        path: '/',
        name: 'Layout',
        component: () => import('@/views/layout/index.vue'),
        redirect: '/dashboard',
        children: [
            {
                path: 'dashboard',
                name: 'Dashboard',
                component: () => import('@/views/dashboard/index.vue'),
                meta: { title: '仪表盘', icon: 'DashboardOutlined' }
            },
            {
                path: 'file',
                name: 'FileManager',
                component: () => import('@/views/file/index.vue'),
                meta: {
                    title: '文件管理',
                    icon: 'FolderOutlined'
                }
            },
            {
                path: 'note',
                name: 'NoteManager',
                component: () => import('@/views/note/index.vue'),
                meta: {
                    title: '笔记管理',
                    icon: 'FileTextOutlined'
                }
            },
            {
                path: '/dsForm/show/index',
                name: 'DsFormShow',
                component: () => import('@/views/dsForm/show/index.vue'),
                meta: {
                    title: '表单展示',
                    keepAlive: true,
                    dynamicTitle: true
                },
                props: (route) => ({
                    pageId: route.query.pageId,
                    title: route.query.title
                })
            },
            baseRoutes,
            shopRoutes,
            schoolManagement,
            erpRoutes,
            exampleRoutes,
            uniExampleRoutes,
            erpProduce,
            oaRoutes,
            sealServiceRoutes,
            activitiRoutes,
            uniExampleRoutes,
            ifsRoutes,
            crmRoutes,
            adminRoutes,
            bossRoutes,
            projectRoutes,
            reportRoutes,
            userauthRoutes,
            wagesRoutes,
            checkWorkRoutes,
            knowlgRoutes
        ]
    }, {
        path: '/apiDoc',
        name: 'ApiDoc',
        component: () => import('@/views/system/apiDoc/index.vue'),
        meta: { title: 'API文档' }
    }, {
        path: '/fileStatistics',
        name: 'FileStatistics',
        component: () => import('@/views/file/statistics.vue'),
        meta: { title: '文件统计' }
    }
]

const router = createRouter({
    history: createWebHashHistory(import.meta.env.VITE_BASE_URL),
    routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
    const token = $config.getUserToken()
    if (to.path !== '/login' && !token) {
        next('/login')
    } else {
        next()
    }
})

export default router 