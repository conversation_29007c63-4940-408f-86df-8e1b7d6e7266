export default {
    path: '/example',
    name: 'Example',
    meta: { title: '组件示例', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'input',
            name: 'InputExample',
            component: () => import('@/views/example/input/index.vue'),
            meta: { title: '输入框', keepAlive: true }
        },
        {
            path: 'textarea',
            name: 'TextareaExample',
            component: () => import('@/views/example/textarea/index.vue'),
            meta: { title: '文本域' }
        },
        {
            path: 'sk-tabs',
            name: 'SkTabsExample',
            component: () => import('@/views/example/tabs/index.vue'),
            meta: { title: '选项卡' }
        },
        {
            path: 'i18n',
            name: 'I18nExample',
            component: () => import('@/views/example/i18n/index.vue'),
            meta: { title: '国际化' }
        },
        {
            path: 'account',
            name: 'AccountDemo',
            component: () => import('@/views/example/account/index.vue'),
            meta: { title: '账户选择器' }
        },
        {
            path: 'color-picker',
            name: 'ColorPickerExample',
            component: () => import('@/views/example/color-picker/index.vue'),
            meta: { title: '颜色选择器' }
        },
        {
            path: 'slider',
            name: 'SliderExample',
            component: () => import('@/views/example/slider/index.vue'),
            meta: { title: '滑块', icon: 'SliderOutlined' }
        },
        {
            path: 'rate',
            name: 'RateExample',
            component: () => import('@/views/example/rate/index.vue'),
            meta: { title: '评分', icon: 'StarOutlined' }
        },
        {
            path: 'collapse',
            name: 'CollapseExample',
            component: () => import('@/views/example/collapse/index.vue'),
            meta: { title: '折叠面板', icon: 'MenuFoldOutlined' }
        },
        {
            path: 'badge',
            name: 'BadgeExample',
            component: () => import('@/views/example/badge/index.vue'),
            meta: { title: '徽标数', icon: 'NotificationOutlined' }
        },
        {
            path: 'switch',
            name: 'SwitchExample',
            component: () => import('@/views/example/switch/index.vue'),
            meta: { title: '开关选择器', icon: 'SwitcherOutlined' }
        },
        {
            path: 'transfer',
            name: 'TransferExample',
            component: () => import('@/views/example/transfer/index.vue'),
            meta: { title: '穿梭框', icon: 'RetweetOutlined' }
        },
        {
            path: 'tree-select',
            name: 'TreeSelectExample',
            component: () => import('@/views/example/tree-select/index.vue'),
            meta: { title: '树型选择', icon: 'ApartmentOutlined' }
        },
        {
            path: 'upload',
            name: 'UploadExample',
            component: () => import('@/views/example/upload/index.vue'),
            meta: { title: '上传组件', icon: 'UploadOutlined' }
        },
        {
            path: 'tree',
            name: 'TreeExample',
            component: () => import('@/views/example/tree/index.vue'),
            meta: { title: '树形控件' }
        },
        {
            path: 'popover',
            name: 'PopoverExample',
            component: () => import('@/views/example/popover/index.vue'),
            meta: { title: '气泡卡片' }
        },
        {
            path: 'segmented',
            name: 'SegmentedExample',
            component: () => import('@/views/example/segmented/index.vue'),
            meta: { title: '分段器' }
        },
        {
            path: 'statistic',
            name: 'StatisticExample',
            component: () => import('@/views/example/statistic/index.vue'),
            meta: { title: '统计数值' }
        },
        {
            path: 'tag',
            name: 'TagExample',
            component: () => import('@/views/example/tag/index.vue'),
            meta: { title: '标签' }
        },
        {
            path: 'timeline',
            name: 'TimelineExample',
            component: () => import('@/views/example/timeline/index.vue'),
            meta: { title: '时间轴' }
        },
        {
            path: 'tooltip',
            name: 'TooltipExample',
            component: () => import('@/views/example/tooltip/index.vue'),
            meta: { title: '文字提示' }
        },
        {
            path: 'tour',
            name: 'TourExample',
            component: () => import('@/views/example/tour/index.vue'),
            meta: { title: '引导页' }
        },
        {
            path: 'alert',
            name: 'AlertExample',
            component: () => import('@/views/example/alert/index.vue'),
            meta: { title: '提示框' }
        },
        {
            path: 'drawer',
            name: 'DrawerExample',
            component: () => import('@/views/example/drawer/index.vue'),
            meta: { title: '抽屉' }
        },
        {
            path: 'message',
            name: 'MessageExample',
            component: () => import('@/views/example/message/index.vue'),
            meta: { title: '消息提示' }
        },
        {
            path: 'modal',
            name: 'ModalExample',
            component: () => import('@/views/example/modal/index.vue'),
            meta: { title: '对话框' }
        },
        {
            path: 'notification',
            name: 'ExampleNotification',
            component: () => import('@/views/example/notification/index.vue'),
            meta: {
                title: '通知提醒框'
            }
        },
        {
            path: 'popconfirm',
            name: 'PopconfirmExample',
            component: () => import('@/views/example/popconfirm/index.vue'),
            meta: { title: '气泡确认框' }
        },
        {
            path: 'progress',
            name: 'ProgressExample',
            component: () => import('@/views/example/progress/index.vue'),
            meta: { title: '进度条' }
        },
        {
            path: 'result',
            name: 'ResultExample',
            component: () => import('@/views/example/result/index.vue'),
            meta: { title: '结果页' }
        },
        {
            path: 'skeleton',
            name: 'SkeletonExample',
            component: () => import('@/views/example/skeleton/index.vue'),
            meta: { title: '骨架屏' }
        },
        {
            path: 'spin',
            name: 'SpinExample',
            component: () => import('@/views/example/spin/index.vue'),
            meta: { title: '加载中' }
        },
        {
            path: 'table',
            name: 'TableExample',
            component: () => import('@/views/example/table/index.vue'),
            meta: { title: '表格' }
        },
        {
            path: 'qrcode',
            name: 'QrcodeExample',
            component: () => import('@/views/example/qrcode/index.vue'),
            meta: { title: '二维码' }
        },
        {
            path: 'empty',
            name: 'ExampleEmpty',
            component: () => import('@/views/example/empty/index.vue'),
            meta: {
                title: '空状态组件示例'
            }
        },
        {
            path: 'image',
            name: 'ImageExample',
            component: () => import('@/views/example/image/index.vue'),
            meta: { title: '图片' }
        },
        {
            path: 'list',
            name: 'ListExample',
            component: () => import('@/views/example/list/index.vue'),
            meta: { title: '列表' }
        },
        {
            path: 'description',
            name: 'DescriptionExample',
            component: () => import('@/views/example/description/index.vue'),
            meta: { title: '描述列表' }
        },
        {
            path: 'comment',
            name: 'CommentExample',
            component: () => import('@/views/example/comment/index.vue'),
            meta: { title: '评论组件' }
        },
        {
            path: 'carousel',
            name: 'CarouselExample',
            component: () => import('@/views/example/carousel/index.vue'),
            meta: { title: '轮播图' }
        },
        {
            path: 'card',
            name: 'CardExample',
            component: () => import('@/views/example/card/index.vue'),
            meta: { title: '卡片' }
        },
        {
            path: 'calendar',
            name: 'CalendarExample',
            component: () => import('@/views/example/calendar/index.vue'),
            meta: { title: '日历' }
        },
        {
            path: 'avatar',
            name: 'AvatarExample',
            component: () => import('@/views/example/avatar/index.vue'),
            meta: { title: '头像' }
        },
        {
            path: 'anchor',
            name: 'AnchorExample',
            component: () => import('@/views/example/anchor/index.vue'),
            meta: { title: '锚点' }
        },
        {
            path: 'breadcrumb',
            name: 'BreadcrumbExample',
            component: () => import('@/views/example/breadcrumb/index.vue'),
            meta: { title: '面包屑' }
        },
        {
            path: 'dropdown',
            name: 'DropdownExample',
            component: () => import('@/views/example/dropdown/index.vue'),
            meta: { title: '下拉菜单' }
        },
        {
            path: 'menu',
            name: 'MenuExample',
            component: () => import('@/views/example/menu/index.vue'),
            meta: { title: '菜单' }
        },
        {
            path: 'pageheader',
            name: 'PageHeaderExample',
            component: () => import('@/views/example/pageheader/index.vue'),
            meta: { title: '页面头' }
        },
        {
            path: 'pagination',
            name: 'PaginationExample',
            component: () => import('@/views/example/pagination/index.vue'),
            meta: { title: '分页' }
        },
        {
            path: 'steps',
            name: 'StepsExample',
            component: () => import('@/views/example/steps/index.vue'),
            meta: { title: '步骤条' }
        },
        {
            path: 'divider',
            name: 'DividerExample',
            component: () => import('@/views/example/divider/index.vue'),
            meta: { title: '分割线' }
        },
        {
            path: 'flex',
            name: 'FlexExample',
            component: () => import('@/views/example/flex/index.vue'),
            meta: { title: 'Flex 布局' }
        },
        {
            path: 'grid',
            name: 'GridExample',
            component: () => import('@/views/example/grid/index.vue'),
            meta: { title: '栅格布局' }
        },
        {
            path: 'layout',
            name: 'LayoutExample',
            component: () => import('@/views/example/layout/index.vue'),
            meta: { title: '布局组件' }
        },
        {
            path: 'space',
            name: 'SpaceExample',
            component: () => import('@/views/example/space/index.vue'),
            meta: { title: '间距组件' }
        },
        {
            path: 'auto-complete',
            name: 'AutoCompleteExample',
            component: () => import('@/views/example/auto-complete/index.vue'),
            meta: { title: '自动完成' }
        },
        {
            path: 'select',
            name: 'SelectExample',
            component: () => import('@/views/example/select/index.vue'),
            meta: { title: '选择器' }
        },
        {
            path: 'radio',
            name: 'RadioExample',
            component: () => import('@/views/example/radio/index.vue'),
            meta: { title: '单选框' }
        },
        {
            path: 'checkbox',
            name: 'CheckboxExample',
            component: () => import('@/views/example/checkbox/index.vue'),
            meta: { title: '多选框' }
        },
        {
            path: 'date-picker',
            name: 'DatePickerExample',
            component: () => import('@/views/example/date-picker/index.vue'),
            meta: { title: '日期选择框' }
        },
        {
            path: 'time-picker',
            name: 'TimePickerExample',
            component: () => import('@/views/example/time-picker/index.vue'),
            meta: { title: '时间选择框' }
        },
        {
            path: 'annotation',
            name: 'AnnotationExample',
            component: () => import('@/views/example/annotation/index.vue'),
            meta: { title: '批注组件' }
        },
        {
            path: 'form',
            name: 'FormExample',
            component: () => import('@/views/example/form/index.vue'),
            meta: { title: '表单组件' }
        },
        {
            path: 'input-number',
            name: 'InputNumberExample',
            component: () => import('@/views/example/input-number/index.vue'),
            meta: { title: '数字输入框' }
        },
        {
            path: 'button',
            name: 'ButtonExample',
            component: () => import('@/views/example/button/index.vue'),
            meta: { title: '按钮' }
        },
        {
            path: 'hr-title',
            name: 'HrTitleExample',
            component: () => import('@/views/example/hr-title/index.vue'),
            meta: { title: '分隔标题' }
        },
        {
            path: 'office',
            name: 'OfficeExample',
            component: () => import('@/views/system/office/show/demo.vue'),
            meta: { title: 'Office预览' }
        },
        {
            path: 'print-template',
            name: 'PrintTemplateExample',
            component: () => import('@/views/system/print-template/index.vue'),
            meta: { title: '打印模板' }
        },
        {
            path: 'scheduling',
            name: 'SchedulingExample',
            component: () => import('@/views/erp/scheduling/index.vue'),
            meta: { title: '排班管理' }
        }
    ]
} 