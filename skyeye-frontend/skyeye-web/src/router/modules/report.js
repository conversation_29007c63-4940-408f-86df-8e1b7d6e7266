// 报表
export default {
    path: '/report',
    name: 'Report',
    meta: { title: '报表', icon: 'OaOutlined' },
    children: [
        {
            path: 'reportProperty',
            name: 'ReportProperty',
            component: () => import('@/views/report/reportProperty/write.vue'),
            meta: { title: '属性配置' }
        },
        {
            path: 'reportWordModel',
            name: 'ReportWordModel',
            component: () => import('@/views/report/reportWordModel/write.vue'),
            meta: { title: '文字模型管理' }
        },
        {
            path: 'reportImportModel',
            name: 'ReportImportModel',
            component: () => import('@/views/report/reportImportModel/index.vue'),
            meta: { title: 'Echarts 模型配置' }
        }
    ]
}