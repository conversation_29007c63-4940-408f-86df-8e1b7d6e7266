// 售后服务
export default {
    path: '/seal-service',
    name: 'SealService',
    children: [
        {
            path: 'sealWorkerMap',
            name: 'SealWorkerMap',
            component: () => import('@/views/sealService/sealWorkerMap/index.vue'),
            meta: { title: '人员分布图' }
        },
        {
            path: 'orderService',
            name: 'OrderService',
            component: () => import('@/views/sealService/order/service.vue'),
            meta: { title: '订单服务' }
        },
        {
            path: 'sealApply',
            name: 'SealApply',
            component: () => import('@/views/sealService/sealApply/index.vue'),
            meta: { title: '我的申领单' }
        },
        {
            path: 'myPartsList',
            name: 'MyPartsList',
            component: () => import('@/views/sealService/parts/index.vue'),
            meta: { title: '我的配件库存' }
        }
    ]
}