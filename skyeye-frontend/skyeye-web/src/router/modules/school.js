// 学校管理
export default {
    path: '/school',
    name: 'School',
    meta: { title: '学校管理', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'floorServiceList',
            name: 'FloorService',
            component: () => import('@/views/school/floorServiceList/index.vue'),
            meta: { title: '楼层服务管理' },
        }, 
        {
            path: 'examDesign',
            name: 'examDesignList',
            component: () => import('@/views/school/examDesign/examDesignList.vue'),
            meta: { title: '所有试卷' },
        }, 
        {
            path: 'routeManagement',
            name: 'routeManagement',
            component: () => import('@/views/school/routeManagement/index.vue'),
            meta: { title: '路线管理' }
        }, 
        {
            path: 'examDesign/examDesignMyList',
            name: 'examDesignMyList',
            component: () => import('@/views/school/examDesign/examDesignMyList.vue'),
            meta: { title: '我的试卷' },
        }, 
        {
            path: 'schoolQuestionBankCommonList',
            name: 'questionBank',
            component: () => import('@/views/school/schoolQuestionBankCommonList/index.vue'),
            meta: { title: '试题题库' }
        },
        {
            path: 'schoolQuestionBank',
            name: 'schoolQuestionBank',
            component: () => import('@/views/school/schoolQuestionBank/schoolQuestionBankList.vue'),
            meta: { title: '我的题库' }
        },{
            path: 'myWaitMarkingList',
            name: 'myWaitMarkingList',
            component: () => import('@/views/school/myWaitMarkingList/index.vue'),
            meta: { title: '待批阅试卷' }
        },{
            path: 'myEndMarkingList',
            name: 'myEndMarkingList',
            component: () => import('@/views/school/myEndMarkingList/index.vue'),
            meta: { title: '已批阅试卷' }
        },{
            path: 'subjectManage',
            name: 'subjectManage',
            component: () => import('@/views/school/subjectManage/index.vue'),
            meta: { title: '科目管理' }
        }
    ]
}

