import http from '@/plugins/http'
var configPath = "http://172.18.92.41:8080/";
// var configPath = "http://123.232.103.196:10380/";
import sysServiceMation from './sysServiceMation.json'
import skyeyeEditor from './skyeyeEditor.json'
import SkMessage from '@/components/SkMessage/index.vue'
import Viewer from 'viewerjs'

// 定义一个缓存的前缀
var cachePrefix = "pcVue-";

var key = cachePrefix + "configRation.json";

// 权限点缓存存储的key
var authPointKey = cachePrefix + "authPoint";

// 菜单缓存存储的key
var menuListKey = cachePrefix + "userMenu";

// 用户信息
var userMationKey = cachePrefix + "userMation";

// 用户token的key
var userTokenKey = cachePrefix + "userToken";

var pageType = {
	ADD: "add",
	EDIT: "edit"
}

var formEditType = {
	isEdit: 1,
	notEdit: 0
}

const buttonWrapperCol = {
	style: {
		marginLeft: '140px',
		width: 'calc(100% - 140px)'
	}
}

const fieldNames = {
	label: 'name',
	value: 'id'
}

const serviceMap = [
	{ "id": "sysMainMation.reqBasePath", "name": "基础服务" },
	{ "id": "sysMainMation.shopBasePath", "name": "商城服务" },
	{ "id": "sysMainMation.flowableBasePath", "name": "工作流相关功能的服务" },
	{ "id": "sysMainMation.schoolBasePath", "name": "学校服务" },
	{ "id": "sysMainMation.reportBasePath", "name": "报表服务" },
	{ "id": "sysMainMation.surveyBasePath", "name": "问卷服务" },
	{ "id": "sysMainMation.noteBasePath", "name": "笔记服务" },
	{ "id": "sysMainMation.rmprogramBasePath", "name": "小程序设计服务" },
	{ "id": "sysMainMation.knowlgBasePath", "name": "知识库服务" },
	{ "id": "sysMainMation.mailBasePath", "name": "通讯录服务" },
	{ "id": "sysMainMation.diskCloudBasePath", "name": "云盘服务" },
	{ "id": "sysMainMation.emailBasePath", "name": "邮箱服务" },
	{ "id": "sysMainMation.scheduleBasePath", "name": "日程服务" },
	{ "id": "sysMainMation.businessFlowBasePath", "name": "业务流程规划服务" },
	{ "id": "sysMainMation.noticeBasePath", "name": "公告服务" },
	{ "id": "sysMainMation.forumBasePath", "name": "论坛服务" },
	{ "id": "sysMainMation.jobdiaayBasePath", "name": "日报服务" },
	{ "id": "sysMainMation.ehrBasePath", "name": "EHR服务" },
	{ "id": "sysMainMation.lightAppBasePath", "name": "轻应用服务" },
	{ "id": "sysMainMation.wagesBasePath", "name": "薪资服务" },
	{ "id": "sysMainMation.crmBasePath", "name": "CRM服务" },
	{ "id": "sysMainMation.ifsBasePath", "name": "IFS财务服务" },
	{ "id": "sysMainMation.erpBasePath", "name": "ERP+生产服务" },
	{ "id": "sysMainMation.checkworkBasePath", "name": "考勤服务" },
	{ "id": "sysMainMation.bossBasePath", "name": "招聘服务" },
	{ "id": "sysMainMation.admBasePath", "name": "行政服务" },
	{ "id": "sysMainMation.projectBasePath", "name": "PM项目服务" },
	{ "id": "sysMainMation.sealServiceBasePath", "name": "售后工单服务" },
	{ "id": "sysMainMation.autoBasePath", "name": "自动化服务" },
	{ "id": "sysMainMation.wallBasePath", "name": "表白墙服务" },
	{ "id": "sysMainMation.tmsBasePath", "name": "物流服务" },
]

const pagination = () => {
	return {
		total: 0,
		current: 1,
		pageSize: 10,
		showSizeChanger: true,
		showQuickJumper: true,
		pageSizeOptions: ["10", "20", "50", "100"],
		showTotal: total => `共 ${total} 条`
	}
}

// 简化的 requestSync 函数，只处理 GET 请求
function requestSync(url) {
	return new Promise((resolve, reject) => {
		const xhr = new XMLHttpRequest()
		xhr.open('GET', url, true)
		xhr.onreadystatechange = function () {
			if (xhr.readyState === 4) {
				if (xhr.status === 200) {
					try {
						const response = JSON.parse(xhr.responseText)
						resolve(response)
					} catch (error) {
						reject(error)
					}
				} else {
					reject(new Error('Request failed with status: ' + xhr.status))
				}
			}
		}
		xhr.onerror = function () {
			reject(new Error('Request failed'))
		}
		xhr.send()
	})
}

// 修改 loadConfig 函数使用新的 requestSync
var loadConfig = async function (callBack) {
	if (http.cachesIsNull(key)) {
		try {
			const response = await requestSync(configPath + "configRation.json?env=")
			localStorage.setItem(key, JSON.stringify(response))
			if (typeof callBack === 'function') {
				callBack(response)
			}
			return response
		} catch (error) {
			console.error('加载配置失败:', error)
			throw error
		}
	} else {
		let result = JSON.parse(localStorage.getItem(key))
		if (typeof callBack === 'function') {
			callBack(result)
		}
		return result
	}
}

var getConfig = function () {
	if (http.cachesIsNull(key)) {
		return loadConfig()
	}
	return JSON.parse(localStorage.getItem(key));
}

var getCurrentUserId = function () {
	return this.getCurrentUser()?.id;
}

var setCurrentUser = function (userMation) {
	localStorage.setItem(userMationKey, JSON.stringify(userMation));
	this.setUserToken(userMation.userToken);
}

var setUserToken = function (userToken) {
	localStorage.setItem(userTokenKey, userToken);
}

var getUserToken = function () {
	return localStorage.getItem(userTokenKey);
}

var getCurrentUser = function () {
	return JSON.parse(localStorage.getItem(userMationKey));
}

// 设置权限点信息放入缓存
var setAuthPointMation = function (authPointList) {
	localStorage.setItem(authPointKey, authPointList);
}

// 从缓存中获取权限点信息
var getAuthPointMation = function () {
	return localStorage.getItem(authPointKey);
}

// 设置菜单信息放入缓存
var setMenuListMation = function (menuList) {
	var userId = this.getCurrentUserId();
	localStorage.setItem(menuListKey, menuList);
}

// 从缓存中获取菜单信息
var getMenuListMation = function () {
	return localStorage.getItem(menuListKey);
}

// 工作台是根据桌面分类展示的，需要根据获取桌面id，才能获取到指定桌面下的菜单
var getDeskTopMenuListByDesktopId = function (desktopId) {
	var menuList = this.getMenuListMation();
	for (var i = 0; i <= menuList.length; i++) {
		if (menuList[i].id == desktopId) {
			return menuList[i].children;
		}
	}
	return [];
}

// 权限点校验
var auth = function (urlNum) {
	let authList = this.getAuthPointMation();
	let result = false;
	if (authList != null) {
		try {
			JSON.parse(authList).forEach(item => {
				if (item.menuNum === urlNum) {
					result = true
					throw new Error("exit foreach");
				} else {
					// 数据权限分组不为空
					if (item.children != null && item.children.length > 0) {
						var dataGroup = item.children;
						for (var j = 0; j < dataGroup.length; j++) {
							// 数据权限不为空
							if (dataGroup[j].children != null && dataGroup[j].children.length > 0) {
								var dataAuthPoint = dataGroup[j].children;
								for (var k = 0; k < dataAuthPoint.length; k++) {
									if (dataAuthPoint[k].menuNum === urlNum) {
										result = true
										throw new Error("exit foreach");
									}
								}
							}
						}
					}
				}
			})
		} catch (e) { }
	}
	return result;
}

// 加载列表接口的数据权限菜单
var loadAuthBtnGroup = function (urlNum) {
	const result = []
	let index = 0;
	if (urlNum == null || urlNum == "") {
		return result;
	}
	let authList = JSON.parse(this.getAuthPointMation());
	if (authList != null) {
		authList.forEach(item => {
			if (item.menuNum === urlNum && item.children.length > 0) {
				// 数据权限分组不为空
				item.children.forEach(dataGroupItem => {
					if (dataGroupItem.children != null) {
						if (dataGroupItem.children.length > 0) {
							// 数据权限不为空
							var dataAuthPoint = dataGroupItem.children;
							dataAuthPoint.sort(function (a, b) {
								if (a.orderBy == null || a.orderBy == "") {
									a.orderBy = 0;
								}
								if (b.orderBy == null || b.orderBy == "") {
									b.orderBy = 0;
								}
								return a.orderBy - b.orderBy; // 升序排序
							});
							dataAuthPoint.forEach(dataAuth => {
								result.push({
									index: index,
									value: dataAuth.authMenu,
									label: dataAuth.name
								})
								index++
							})
						}
					}
				})
			}
		})
	}
	return result;
}

var teamObjectPermissionUtil = {

	checkTeamBusinessAuthPermission: async function (objectId, enumKey, enumClassName) {
		var params = {
			objectId: objectId,
			enumKey: enumKey,
			enumClassName: enumClassName
		};
		const result = await http.post(getConfig().reqBasePath + "checkTeamBusinessAuthPermission", params)
		return result.bean
	}

}

const getSysServiceMationAppIdByClassName = (className) => {
	// 如果 className 为空，直接返回空字符串
	if (!className) return ''

	// 遍历对象的所有值，查找匹配的 key
	for (const serviceName in sysServiceMation) {
		const service = sysServiceMation[serviceName]
		if (service.key === className) {
			return service.appId || ''
		}
	}

	// 如果没找到匹配的，返回空字符串
	return ''
}

const $fileType = {
	imageType: ["png", "jpg", "xbm", "bmp", "webp", "jpeg", "svgz", "git", "ico", "tiff", "svg", "jiff",
		"pjpeg", "pjp", "tif"
	],
	officeType: ["docx", "doc", "xls", "xlsx", "ppt", "pptx", "wps", "et", "dps", "csv", "pdf"],
	vedioType: ["mp4", "rm", "rmvb", "wmv", "avi", "3gp", "mkv"],
	packageType: ["zip", "rar"],
	epubType: ["epub"],
	aceType: ["txt", "sql", "java", "css", "html", "htm", "json", "js", "tpl"],
}

// 保存当前活动的预览器实例
let activeViewer = null;

// 预览文件
const previewFile = (fileName, imageUrl) => {
	// 判断是否有预览地址
	if (!imageUrl) {
		SkMessage.warning('暂无可预览的文件')
		return
	}

	// 判断是否是图片
	const imageTypes = $fileType.imageType
	const fileExt = imageUrl?.split('.')?.pop()?.toLowerCase()

	if (imageTypes.includes(fileExt)) {
		// 如果已经有活动的预览器，先销毁它
		if (activeViewer) {
			activeViewer.destroy();
			if (activeViewer.container && activeViewer.container.parentNode) {
				activeViewer.container.parentNode.removeChild(activeViewer.container);
			}
			activeViewer = null;
		}

		// 创建图片容器
		const container = document.createElement('div')
		container.className = 'image-preview-container';
		const img = document.createElement('img')
		img.src = imageUrl.startsWith('http') ? imageUrl : getConfig().fileBasePath + imageUrl
		container.appendChild(img)
		document.body.appendChild(container)

		// 创建预览实例
		const viewer = new Viewer(container, {
			title: [fileName],
			navbar: false,
			toolbar: {
				zoomIn: true,
				zoomOut: true,
				oneToOne: true,
				reset: true,
				rotateLeft: true,
				rotateRight: true,
				flipHorizontal: true,
				flipVertical: true,
			},
			hidden() {
				viewer.destroy()
				document.body.removeChild(container)
				activeViewer = null;
			}
		})

		// 保存当前预览器实例
		activeViewer = viewer;

		// 显示预览
		viewer.show()
	} else {
		// 非图片文件，使用系统默认预览或下载
		window.open(imageUrl.startsWith('http') ? imageUrl : getConfig().fileBasePath + imageUrl)
	}
}

// 文件下载函数
const downloadFile = (fileName, fileUrl) => {
	// 判断是否有下载地址
	if (!fileUrl) {
		SkMessage.warning('暂无可下载的文件')
		return
	}

	try {
		// 创建 XMLHttpRequest 对象
		const xhr = new XMLHttpRequest()
		xhr.open('GET', getConfig().fileBasePath + fileUrl, true)
		xhr.responseType = 'blob'

		xhr.onload = () => {
			if (xhr.status === 200) {
				// 创建 Blob 对象
				const blob = new Blob([xhr.response], { type: xhr.getResponseHeader('Content-Type') })

				// 创建下载链接
				const link = document.createElement('a')
				link.href = URL.createObjectURL(blob)
				link.download = fileName || '未命名文件'

				// 添加到 body
				document.body.appendChild(link)

				// 触发点击
				link.click()

				// 清理
				document.body.removeChild(link)
				URL.revokeObjectURL(link.href)
			} else {
				SkMessage.error('下载文件失败')
			}
		}

		xhr.onerror = () => {
			SkMessage.error('下载文件失败')
		}

		xhr.send()
	} catch (error) {
		console.error('下载文件失败:', error)
		SkMessage.error('下载文件失败')
	}
}

// 下载文件方法
const postDownLoadFile = (options) => {
	// 合并默认配置
	const config = {
		method: 'post',
		...options
	}

	// 创建一个临时的 iframe 和 form
	const iframe = document.createElement('iframe')
	iframe.id = 'down-file-iframe'
	iframe.style.display = 'none'

	const form = document.createElement('form')
	form.target = 'down-file-iframe'
	form.method = config.method
	form.action = config.url

	// 添加参数
	if (config.params) {
		Object.entries(config.params).forEach(([key, value]) => {
			const input = document.createElement('input')
			input.type = 'hidden'
			input.name = key
			input.value = value
			form.appendChild(input)
		})
	}

	// 添加用户token
	const tokenInput = document.createElement('input')
	tokenInput.type = 'hidden'
	tokenInput.name = 'userToken'
	tokenInput.value = localStorage.getItem('userToken') || ''
	form.appendChild(tokenInput)

	// 添加base64数据(如果有)
	if (config.data) {
		const dataInput = document.createElement('input')
		dataInput.type = 'hidden'
		dataInput.name = 'base64Info'
		dataInput.value = config.data
		form.appendChild(dataInput)
	}

	// 提交表单并清理
	iframe.appendChild(form)
	document.body.appendChild(iframe)
	form.submit()

	// 延迟移除iframe
	setTimeout(() => {
		document.body.removeChild(iframe)
	}, 100)
}

const skyeyeReportUtil = {

	/**
	 * 获取报表编辑器支持的类型
	 *
	 * @param showDomId 展示的bom结构id
	 * @param template 模板
	 * @param callBack 回调函数
	 */
	getReportEditorType: function () {
		var rows = new Array();
		Object.keys(skyeyeEditor).forEach(key => {
			rows.push({
				id: key,
				name: skyeyeEditor[key].name
			});
		});
		return rows
	}

};

const config = {
	key,
	loadConfig,
	getConfig,

	getCurrentUserId,
	setCurrentUser,
	getCurrentUser,
	setUserToken,
	getUserToken,

	setAuthPointMation,
	getAuthPointMation,

	setMenuListMation,
	getMenuListMation,

	getDeskTopMenuListByDesktopId,

	auth,

	pageType,
	formEditType,

	authPointKey,
	menuListKey,
	userMationKey,
	userTokenKey,

	loadAuthBtnGroup,
	teamObjectPermissionUtil,

	buttonWrapperCol,
	fieldNames,
	pagination,
	$fileType,

	getSysServiceMationAppIdByClassName,
	sysServiceMation,

	previewFile,
	downloadFile,
	postDownLoadFile,

	skyeyeReportUtil,
	serviceMap,

	// 使用getter函数延迟初始化defaultUserImg
	get defaultUserImg() {
		// 这里会在真正访问属性时才执行，避免初始化时的循环引用
		return (this.getConfig().fileBasePath || '') + '/images/util/assest/common/img/user.png';
	}
}
// 注册全局方法
export function setupConfig(app) {
	// 添加到全局属性
	app.config.globalProperties.$config = config
	// 添加到全局对象
	window.$config = config
}

export default config
