import '@fortawesome/fontawesome-free/css/all.css'
import '@fortawesome/fontawesome-free/css/v4-shims.css'

// 注册全局方法
export function setupFontAwesome(app) {
  // 添加全局方法用于获取图标类名
  app.config.globalProperties.$getIconClass = (icon) => {
    if (!icon) return ''
    
    // 处理 v4 图标
    if (icon.startsWith('fa-')) {
      return ['fa', icon]
    }
    
    // 处理 v6 图标
    return ['fas', `fa-${icon}`]
  }
}

export default {
  install: (app) => {
    setupFontAwesome(app)
  }
} 