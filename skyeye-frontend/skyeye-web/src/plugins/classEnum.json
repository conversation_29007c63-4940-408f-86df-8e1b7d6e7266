{"commonEnable": {"name": "启用/禁用", "className": "skyeye-pro#com.skyeye.common.enumeration.EnableEnum"}, "searchParamsConfigDataType": {"name": "搜索条件配置数据类型", "className": "skyeye-pro#com.skyeye.common.enumeration.SearchParamsConfigDataType"}, "searchOperator": {"name": "搜索条件配置操作符", "className": "skyeye-pro#com.skyeye.common.enumeration.SearchOperator"}, "enableEnum": {"name": "标签状态", "className": "skyeye-adm#com.skyeye.common.enumeration.EnableEnum"}, "smsChannelEnum": {"name": "短信渠道编码", "className": "skyeye-pro#com.skyeye.sms.classenum.SmsChannelEnum"}, "smsTemplateTypeEnum": {"name": "短信类型", "className": "skyeye-pro#com.skyeye.sms.classenum.SmsTemplateTypeEnum"}, "commonIsDefault": {"name": "是否默认", "className": "skyeye-pro#com.skyeye.common.enumeration.IsDefaultEnum"}, "sexEnum": {"name": "性别", "className": "skyeye-pro#com.skyeye.common.enumeration.SexEnum"}, "userIsTermOfValidity": {"name": "用户是否长期有效", "className": "skyeye-pro#com.skyeye.personnel.classenum.UserIsTermOfValidity"}, "httpMethodEnum": {"name": "http请求方式", "className": "skyeye-pro#com.skyeye.common.enumeration.HttpMethodEnum"}, "verificationParams": {"name": "字段校验", "className": "skyeye-pro#com.skyeye.common.enumeration.VerificationParamsEnum"}, "isUsed": {"name": "是否使用", "className": "skyeye-pro#com.skyeye.common.enumeration.IsUsedEnum"}, "whetherEnum": {"name": "是否", "className": "skyeye-pro#com.skyeye.common.enumeration.WhetherEnum"}, "dateTimeType": {"name": "日期类型", "className": "skyeye-pro#com.skyeye.dsform.classenum.DateTimeType"}, "payTypeEnum": {"name": "付款类型", "className": "skyeye-pro#com.skyeye.common.enumeration.PayTypeEnum"}, "userStaffState": {"name": "员工状态", "className": "skyeye-pro#com.skyeye.common.enumeration.UserStaffState"}, "userQuitType": {"name": "员工离职类型", "className": "skyeye-pro#com.skyeye.common.enumeration.UserQuitType"}, "userStaffType": {"name": "员工类型", "className": "skyeye-pro#com.skyeye.personnel.classenum.UserStaffType"}, "bloodTypeEnum": {"name": "血型", "className": "skyeye-pro#com.skyeye.common.enumeration.BloodTypeEnum"}, "payChannelVersion": {"name": "支付渠道版本", "className": "skyeye-pro#com.skyeye.pay.enums.PayChannelVersion"}, "orderCommentType": {"name": "订单评价方式", "className": "skyeye-shop#com.skyeye.order.enums.OrderCommentType"}, "healthStatus": {"name": "健康状态", "className": "skyeye-pro#com.skyeye.common.enumeration.HealthStatus"}, "idCardType": {"name": "证件类型", "className": "skyeye-pro#com.skyeye.common.enumeration.IDCardType"}, "registrationType": {"name": "户口类型", "className": "skyeye-pro#com.skyeye.common.enumeration.RegistrationType"}, "applicableObjectsType": {"name": "适用对象类型", "className": "skyeye-pro#com.skyeye.common.enumeration.ApplicableObjectsType"}, "fileStorageEnum": {"name": "存储器", "className": "skyeye-pro#com.skyeye.upload.enums.FileStorageEnum"}, "fileFtpMode": {"name": "连接模式", "className": "skyeye-pro#com.skyeye.upload.enums.FileFtpMode"}, "bossInterviewArrangementState": {"name": "面试安排状态", "className": "skyeye-boss#com.skyeye.arrangement.classenum.ArrangementState"}, "bossInterviewArrangementState1": {"name": "面试安排状态枚举类-获取我录入的人员需求关联的面试者信息列表", "className": "skyeye-boss#com.skyeye.arrangement.classenum.ArrangementState", "filterKey": "id", "filterValue": "2,3,4,5,6,7"}, "bossInterviewArrangementState2": {"name": "面试安排状态枚举类-获取面试官为当前登录用户的面试者信息列表", "className": "skyeye-boss#com.skyeye.arrangement.classenum.ArrangementState", "filterKey": "id", "filterValue": "3,4,5"}, "bossIntervieweeStatus": {"name": "面试者状态", "className": "skyeye-boss#com.skyeye.interviewee.classenum.IntervieweeStatusEnum"}, "bossUserTransferType": {"name": "员工转岗类型", "className": "skyeye-boss#com.skyeye.interviewee.classenum.UserTransferType"}, "bossPersonRequireState": {"name": "人员需求申请单状态", "className": "skyeye-boss#com.skyeye.personrequire.classenum.PersonRequireStateEnum"}, "materialFromType": {"name": "商品来源类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialFromType"}, "materialType": {"name": "商品类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialType"}, "materialUnit": {"name": "规格类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialUnit"}, "materialNormsStockType": {"name": "商品规格库存类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialNormsStockType"}, "materialInOrderType": {"name": "商品在单据中的类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialInOrderType"}, "depotPutOutType": {"name": "出入库类型", "className": "skyeye-erp#com.skyeye.depot.classenum.DepotPutOutType"}, "erpOrderStateEnum": {"name": "ERP单据状态", "className": "skyeye-erp#com.skyeye.classenum.ErpOrderStateEnum"}, "supplierContractStateEnum": {"name": "供应商合同状态", "className": "skyeye-erp#com.skyeye.contract.classenum.SupplierContractStateEnum"}, "supplierContractAuthEnum": {"name": "供应商合同权限", "className": "skyeye-erp#com.skyeye.contract.classenum.SupplierContractAuthEnum"}, "productionState": {"name": "生产计划单状态", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionStateEnum"}, "machinStateEnum": {"name": "加工单状态", "className": "skyeye-erp#com.skyeye.machin.classenum.MachinStateEnum"}, "machinPickStateEnum": {"name": "加工单领料状态", "className": "skyeye-erp#com.skyeye.machin.classenum.MachinPickStateEnum"}, "machinChildStateEnum": {"name": "工序验收单状态", "className": "skyeye-erp#com.skyeye.machin.classenum.MachinChildStateEnum"}, "skyeyeView": {"name": "视图类型", "className": "skyeye-pro#com.skyeye.common.SkyeyeViewEnum"}, "widthScale": {"name": "宽度比例", "className": "skyeye-pro#com.skyeye.attr.classenum.WidthScale"}, "dsFormShowType": {"name": "组件展示类型", "className": "skyeye-pro#com.skyeye.attr.classenum.DsFormShowType"}, "alignment": {"name": "对齐方式", "className": "skyeye-pro#com.skyeye.attr.classenum.Alignment"}, "fixedType": {"name": "表格列固定位置", "className": "skyeye-pro#com.skyeye.dsform.classenum.FixedType"}, "tenantEnum": {"name": "租户类型", "className": "skyeye-pro#com.skyeye.common.enumeration.TenantEnum"}, "teamObjectType": {"name": "团队适用对象", "className": "skyeye-pro#com.skyeye.team.classenum.TeamObjectTypeEnum"}, "contactsAuthEnum": {"name": "联系人权限", "className": "skyeye-pro#com.skyeye.contacts.classenum.ContactsAuthEnum"}, "catalogAuthEnum": {"name": "目录权限", "className": "skyeye-pro#com.skyeye.catalog.classenum.CatalogAuthEnum"}, "documentAuthEnum": {"name": "文档权限", "className": "skyeye-pro#com.skyeye.document.classenum.DocumentAuthEnum"}, "disCussionAuthEnum": {"name": "讨论帖权限", "className": "skyeye-pro#com.skyeye.discussion.classenum.DisCussionAuthEnum"}, "flowableStateEnum": {"name": "工作流状态", "className": "skyeye-pro#com.skyeye.common.enumeration.FlowableStateEnum"}, "deliveryExpressType": {"name": "配送计费方式", "className": "skyeye-shop#com.skyeye.delivery.enums.DeliveryExpressType"}, "correspondentEnterEnum": {"name": "往来单位类型", "className": "skyeye-pro#com.skyeye.common.enumeration.CorrespondentEnterEnum", "remark": "往来单位的组件中使用"}, "correspondentAllEnterEnum": {"name": "所有往来单位类型", "className": "skyeye-pro#com.skyeye.common.enumeration.CorrespondentAllEnterEnum", "remark": "往来单位的组件中使用"}, "payType": {"name": "支付渠道编码", "className": "skyeye-pro#com.skyeye.pay.enums.PayType"}, "projectStateEnum": {"name": "项目状态", "className": "skyeye-project#com.skyeye.project.classenum.ProjectStateEnum"}, "taskStateEnum": {"name": "项目任务状态", "className": "skyeye-project#com.skyeye.task.classenum.TaskStateEnum"}, "taskAuthEnum": {"name": "项目任务权限", "className": "skyeye-project#com.skyeye.task.classenum.TaskAuthEnum"}, "milestoneAuthEnum": {"name": "里程碑权限", "className": "skyeye-project#com.skyeye.milestone.classenum.MilestoneAuthEnum"}, "milestoneStateEnum": {"name": "里程碑状态", "className": "skyeye-project#com.skyeye.milestone.classenum.MilestoneStateEnum"}, "milestoneImported": {"name": "里程碑重要性", "className": "skyeye-project#com.skyeye.milestone.classenum.MilestoneImported"}, "taskImported": {"name": "任务重要性", "className": "skyeye-project#com.skyeye.task.classenum.TaskImported"}, "dsFormPageType": {"name": "表单布局类型", "className": "skyeye-pro#com.skyeye.dsform.classenum.DsFormPageType"}, "simpleTableWhetherChoose": {"name": "表格类型的布局是否开启选择功能", "className": "skyeye-pro#com.skyeye.dsform.classenum.SimpleTableWhetherChoose"}, "attrSymbols": {"name": "属性与值的对比符号", "className": "skyeye-pro#com.skyeye.attr.classenum.AttrSymbols"}, "attrKeyDataType": {"name": "表单布局里面的组件关联的数据类型", "className": "skyeye-pro#com.skyeye.attr.classenum.AttrKeyDataType"}, "componentAttr": {"name": "组件关联的属性", "className": "skyeye-pro#com.skyeye.dsform.classenum.ComponentAttr"}, "componentApplyRange": {"name": "组件适用范围类型", "className": "skyeye-pro#com.skyeye.dsform.classenum.ComponentApplyRange"}, "componentValueMergType": {"name": "组件值合入方式", "className": "skyeye-pro#com.skyeye.dsform.classenum.ComponentValueMergType"}, "buttonColorType": {"name": "操作按钮颜色", "className": "skyeye-pro#com.skyeye.attr.classenum.ButtonColorType"}, "eventType": {"name": "操作按钮事件类型", "className": "skyeye-pro#com.skyeye.operate.classenum.EventType"}, "operatePosition": {"name": "操作按钮展示位置", "className": "skyeye-pro#com.skyeye.operate.classenum.OperatePosition"}, "crmContractFromType": {"name": "客户合同来源单据类型", "className": "skyeye-crm#com.skyeye.contract.classenum.CrmContractFromType"}, "crmContractStateEnum": {"name": "合同状态", "className": "skyeye-crm#com.skyeye.contract.classenum.CrmContractStateEnum"}, "crmContractAuthEnum": {"name": "合同权限", "className": "skyeye-crm#com.skyeye.contract.classenum.CrmContractAuthEnum"}, "crmOpportunityStateEnum": {"name": "商机状态", "className": "skyeye-crm#com.skyeye.opportunity.classenum.CrmOpportunityStateEnum"}, "crmOpportunityAuthEnum": {"name": "商机权限", "className": "skyeye-crm#com.skyeye.opportunity.classenum.CrmOpportunityAuthEnum"}, "crmDocumentaryAuthEnum": {"name": "跟单权限", "className": "skyeye-crm#com.skyeye.documentary.classenum.CrmDocumentaryAuthEnum"}, "accountSubjectType": {"name": "会计科目类型", "className": "skyeye-ifs#com.skyeye.subject.classenum.AccountSubjectType"}, "amountDirection": {"name": "余额方向", "className": "skyeye-ifs#com.skyeye.subject.classenum.AmountDirection"}, "incomeAndExpenseType": {"name": "收支项目类型", "className": "skyeye-ifs#com.skyeye.incomeandexpense.classenum.IncomeAndExpenseType"}, "holderNormsChildState": {"name": "关联的客户/供应商/会员购买或者出售的商品子信息状态", "className": "skyeye-erp#com.skyeye.holder.classenum.HolderNormsChildState"}, "voucherState": {"name": "凭证状态", "className": "skyeye-ifs#com.skyeye.voucher.classenum.VoucherState"}, "checkworkTypeEnum": {"name": "考勤类型", "className": "skyeye-school#com.skyeye.school.checkwork.classenum.CheckworkType"}, "checkworkStateEnum": {"name": "考勤状态", "className": "skyeye-school#com.skyeye.school.checkwork.classenum.CheckworkSignState"}, "voucherType": {"name": "凭证类型", "className": "skyeye-ifs#com.skyeye.voucher.classenum.VoucherType"}, "checkWorkTimeType": {"name": "考勤班次类型", "className": "skyeye-checkwork#com.skyeye.worktime.classenum.CheckWorkTimeType"}, "overtimeSoltSettleState": {"name": "加班是否计入补休/薪资结算状态", "className": "skyeye-checkwork#com.skyeye.overtime.classenum.OvertimeSoltSettleState"}, "overtimeSettlementType": {"name": "部门加班结算方式", "className": "skyeye-checkwork#com.skyeye.common.enumeration.OvertimeSettlementType"}, "leaveType": {"name": "请假类型", "className": "skyeye-checkwork#com.skyeye.leave.classenum.LeaveType"}, "useYearHolidayType": {"name": "请假是否使用年假/补休的类型", "className": "skyeye-checkwork#com.skyeye.leave.classenum.UseYearHolidayType"}, "clockInTime": {"name": "上班打卡状态", "className": "skyeye-checkwork#com.skyeye.checkwork.classenum.ClockInTime"}, "clockOutTime": {"name": "下班打卡状态", "className": "skyeye-checkwork#com.skyeye.checkwork.classenum.ClockOutTime"}, "assetReportState": {"name": "资产明细状态", "className": "skyeye-adm#com.skyeye.eve.assets.classenum.AssetReportState"}, "assetPurchasePutFromType": {"name": "资产采购入库单来源单据类型", "className": "skyeye-adm#com.skyeye.eve.assets.classenum.PurchasePutFromType"}, "assetPurchaseReturnFromType": {"name": "资产采购退货单来源单据类型", "className": "skyeye-adm#com.skyeye.eve.assets.classenum.PurchaseReturnFromType"}, "purchaseOrderState": {"name": "资产采购订单状态", "className": "skyeye-adm#com.skyeye.eve.assets.classenum.PurchaseOrderStateEnum"}, "conferenceState": {"name": "会议室状态", "className": "skyeye-adm#com.skyeye.eve.conference.classenum.ConferenceState"}, "vehicleState": {"name": "车辆状态", "className": "skyeye-adm#com.skyeye.eve.vehicle.classenum.VehicleState"}, "maintenanceType": {"name": "车辆维修类型", "className": "skyeye-adm#com.skyeye.eve.vehicle.classenum.MaintenanceType"}, "schoolPower": {"name": "学校数据权限", "className": "skyeye-school#com.skyeye.eve.classenum.SchoolPower"}, "floorInfoEnum": {"name": "楼层、教室、服务类型", "className": "skyeye-school#com.skyeye.school.building.floorenum.FloorInfoEnum"}, "admissionMethod": {"name": "入学方式", "className": "skyeye-school#com.skyeye.school.student.classenum.AdmissionMethod"}, "modeOfStudy": {"name": "就读方式", "className": "skyeye-school#com.skyeye.school.student.classenum.ModeOfStudy"}, "vaccinationStatus": {"name": "疫苗接种情况", "className": "skyeye-school#com.skyeye.school.student.classenum.VaccinationStatus"}, "studentState": {"name": "学生状态", "className": "skyeye-school#com.skyeye.school.student.classenum.StudentState"}, "studentType": {"name": "学生类型", "className": "skyeye-school#com.skyeye.school.student.classenum.StudentType"}, "productWarrantyType": {"name": "售后工单质保类型", "className": "skyeye-seal-service#com.skyeye.afterseal.classenum.ProductWarrantyType"}, "sealOrderType": {"name": "售后工单工单类型", "className": "skyeye-seal-service#com.skyeye.afterseal.classenum.SealOrderType"}, "afterSealState": {"name": "售后工单状态", "className": "skyeye-seal-service#com.skyeye.afterseal.classenum.AfterSealState"}, "knowlgContentState": {"name": "知识库状态", "className": "skyeye-adm#com.skyeye.eve.knowlg.classenum.KnowlgContentState"}, "wagesTypeEnum": {"name": "薪资字段类型", "className": "skyeye-wages#com.skyeye.eve.field.classenum.WagesTypeEnum"}, "wagesModelFieldType": {"name": "模板关联字段的字段类型", "className": "skyeye-wages#com.skyeye.eve.model.classenum.WagesModelFieldType"}, "staffWagesStateEnum": {"name": "员工薪资设定状态", "className": "skyeye-pro#com.skyeye.personnel.classenum.StaffWagesStateEnum"}, "paymentHistoryState": {"name": "薪资发放状态", "className": "skyeye-wages#com.skyeye.eve.payment.classenum.PaymentHistoryState"}, "paymentHistoryType": {"name": "薪资核算类型", "className": "skyeye-wages#com.skyeye.eve.payment.classenum.PaymentHistoryType"}, "noticeState": {"name": "公告状态", "className": "skyeye-adm#com.skyeye.eve.notice.classenum.NoticeState"}, "noticeRealLinesType": {"name": "公告上线类型", "className": "skyeye-adm#com.skyeye.eve.notice.classenum.NoticeRealLinesType"}, "noticeTimeSend": {"name": "是否设置定时发送", "className": "skyeye-adm#com.skyeye.eve.notice.classenum.NoticeTimeSend"}, "mailCategory": {"name": "通讯录类型", "className": "skyeye-adm#com.skyeye.eve.mail.classenum.MailCategory"}, "jobDiaryType": {"name": "日志类型", "className": "skyeye-adm#com.skyeye.eve.jobdiary.classenum.JobDiaryType"}, "jobDiaryState": {"name": "日志状态", "className": "skyeye-adm#com.skyeye.eve.jobdiary.classenum.JobDiaryState"}, "readState": {"name": "日志阅读状态", "className": "skyeye-adm#com.skyeye.eve.jobdiary.classenum.ReadState"}, "scheduleRemindType": {"name": "日程提醒时间所属类型", "className": "skyeye-adm#com.skyeye.eve.schedule.classenum.ScheduleRemindType"}, "scheduleImported": {"name": "日程重要性", "className": "skyeye-adm#com.skyeye.eve.schedule.classenum.ScheduleImported"}, "checkDayType": {"name": "日程插件上的类型(包含日程的)", "className": "skyeye-pro#com.skyeye.common.enumeration.CheckDayType"}, "gwDocumentOpenCategory": {"name": "公文-公开类别", "className": "skyeye-adm#com.skyeye.eve.gw.classenum.GwDocumentOpenCategory"}, "gwDocumentPeriod": {"name": "公文-保密期间", "className": "skyeye-adm#com.skyeye.eve.gw.classenum.GwDocumentPeriod"}, "gwDocumentSecret": {"name": "公文-公文密级", "className": "skyeye-adm#com.skyeye.eve.gw.classenum.GwDocumentSecret"}, "gwDocumentUrgency": {"name": "公文-紧急程度", "className": "skyeye-adm#com.skyeye.eve.gw.classenum.GwDocumentUrgency"}, "sealBgColorType": {"name": "印章背景类型", "className": "skyeye-adm#com.skyeye.eve.seal.classenum.SealBgColorType"}, "crmFollowUpAuthEnum": {"name": "回访权限", "className": "skyeye-crm#com.skyeye.follow.classenum.CrmFollowUpAuthEnum"}, "crmPaymentCollectionAuthEnum": {"name": "回款权限", "className": "skyeye-crm#com.skyeye.payment.classenum.CrmPaymentCollectionAuthEnum"}, "crmInvoiceHeaderAuthEnum": {"name": "发票抬头权限", "className": "skyeye-crm#com.skyeye.invoice.classenum.CrmInvoiceHeaderAuthEnum"}, "crmInvoiceAuthEnum": {"name": "发票权限", "className": "skyeye-crm#com.skyeye.invoice.classenum.CrmInvoiceAuthEnum"}, "storeOnlineBookType": {"name": "商城-门店线上预约类型", "className": "skyeye-shop#com.skyeye.store.classenum.StoreOnlineBookType"}, "shopMealType": {"name": "商城-套餐类型", "className": "skyeye-shop#com.skyeye.meal.classenum.ShopMealType"}, "purchaseRequestChildInquiry": {"name": "ERP-申请单子单据明细是否询价", "className": "skyeye-erp#com.skyeye.request.classenum.PurchaseRequestChildInquiry"}, "purchaseRequestFromType": {"name": "ERP-采购申请单来源单据", "className": "skyeye-erp#com.skyeye.request.classenum.PurchaseRequestFromType"}, "purchaseRequestInquiryState": {"name": "ERP-采购申请单询价状态", "className": "skyeye-erp#com.skyeye.request.classenum.PurchaseRequestInquiryState"}, "purchaseRequestStateEnum": {"name": "ERP-采购申请状态", "className": "skyeye-erp#com.skyeye.request.classenum.PurchaseRequestStateEnum"}, "materialNormsCodeType": {"name": "条形码商品规格类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialNormsCodeType"}, "inventoryChildState": {"name": "ERP-我的任务盘点状态", "className": "skyeye-erp#com.skyeye.inventory.classenum.InventoryChildState"}, "carAttribute": {"name": "车辆属性", "className": "skyeye-tms#com.skyeye.tms.car.classenum.CarAttribute"}, "carState": {"name": "车辆当前状态", "className": "skyeye-tms#com.skyeye.tms.car.classenum.CarState"}, "purchaseOrderFromType": {"name": "采购订单来源单据类型", "className": "skyeye-erp#com.skyeye.purchase.classenum.PurchaseOrderFromType"}, "purchaseDeliveryFromType": {"name": "到货单来源单据类型", "className": "skyeye-erp#com.skyeye.purchase.classenum.PurchaseDeliveryFromType"}, "purchasePutFromType": {"name": "采购入库单来源单据类型", "className": "skyeye-erp#com.skyeye.purchase.classenum.PurchasePutFromType"}, "purchaseReturnsFromType": {"name": "采购退货单来源单据类型", "className": "skyeye-erp#com.skyeye.purchase.classenum.PurchaseReturnsFromType"}, "supplierContractFromType": {"name": "供应商合同来源单据类型", "className": "skyeye-erp#com.skyeye.contract.classenum.SupplierContractFromType"}, "supplierContractChildStateEnum": {"name": "ERP-采购合同是否转采购订单状态", "className": "skyeye-erp#com.skyeye.contract.classenum.SupplierContractChildStateEnum"}, "orderItemQualityInspectionType": {"name": "ERP采购子单据质检类型", "className": "skyeye-erp#com.skyeye.business.classenum.OrderItemQualityInspectionType"}, "deliveryPutState": {"name": "ERP-采购到货单免检商品入库状态", "className": "skyeye-erp#com.skyeye.purchase.classenum.DeliveryPutState"}, "qualityInspectionPutState": {"name": "ERP-采购质检单商品入库状态", "className": "skyeye-erp#com.skyeye.inspection.classenum.QualityInspectionPutState"}, "qualityInspectionReturnState": {"name": "ERP-采购质检单商品退货状态", "className": "skyeye-erp#com.skyeye.inspection.classenum.QualityInspectionReturnState"}, "orderArrivalState": {"name": "采购订单到货状态", "className": "skyeye-erp#com.skyeye.purchase.classenum.OrderArrivalState"}, "orderQualityInspectionType": {"name": "ERP-采购订单质检状态", "className": "skyeye-erp#com.skyeye.business.classenum.OrderQualityInspectionType"}, "qualityInspectionFromType": {"name": "质检单来源单据类型", "className": "skyeye-erp#com.skyeye.inspection.classenum.QualityInspectionFromType"}, "depotOutFromType": {"name": "仓库出库单来源单据类型", "className": "skyeye-erp#com.skyeye.depot.classenum.DepotOutFromType"}, "sealReturnFromType": {"name": "销售退货单来源单据类型", "className": "skyeye-erp#com.skyeye.seal.classenum.SealReturnFromType"}, "sealOutLetFromType": {"name": "销售出库单来源单据类型", "className": "skyeye-erp#com.skyeye.seal.classenum.SealOutLetFromType"}, "depotPutFromType": {"name": "仓库入库单来源单据类型", "className": "skyeye-erp#com.skyeye.depot.classenum.DepotPutFromType"}, "depotPutState": {"name": "仓库入库单入库状态", "className": "skyeye-erp#com.skyeye.depot.classenum.DepotPutState"}, "outLetState": {"name": "领料/补料单出库状态", "className": "skyeye-erp#com.skyeye.pick.classenum.OutLetState"}, "depotOutState": {"name": "领料/补料出库单出库状态", "className": "skyeye-erp#com.skyeye.depot.classenum.DepotOutState"}, "requisitionOutLetFromType": {"name": "领料出库单来源单据类型", "className": "skyeye-erp#com.skyeye.pick.classenum.RequisitionOutLetFromType"}, "patchOutLetFromType": {"name": "补料出库单来源单据类型", "className": "skyeye-erp#com.skyeye.pick.classenum.PatchOutLetFromType"}, "returnPutFromType": {"name": "退料入库单来源单据类型", "className": "skyeye-erp#com.skyeye.pick.classenum.ReturnPutFromType"}, "putState": {"name": "退料单入库状态", "className": "skyeye-erp#com.skyeye.pick.classenum.PutState"}, "pickFromType": {"name": "领料申请单/补料申请单/退料申请单来源单据类型", "className": "skyeye-erp#com.skyeye.pick.classenum.PickFromType"}, "productionChildFromType": {"name": "生产计划单子单据生产类型", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionChildType"}, "depotOutOtherState": {"name": "待确认物料单确认状态", "className": "skyeye-erp#com.skyeye.depot.classenum.DepotOutOtherState"}, "productionMachinOrderState": {"name": "生产计划单下达加工单的状态", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionMachinOrderState"}, "productionOutState": {"name": "生产计划单委外状态", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionOutState"}, "productionFromType": {"name": "生产计划单来源单据类型", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionFromType"}, "productionPlanFromType": {"name": "出货计划来源单据类型", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionPlanFromType"}, "confirmFromType": {"name": "物料接收单/物料退货单来源单据类型", "className": "skyeye-erp#com.skyeye.pickconfirm.classenum.ConfirmFromType"}, "machinFromType": {"name": "加工单来源单据类型", "className": "skyeye-erp#com.skyeye.machin.classenum.MachinFromType"}, "wholeOrderOutFromType": {"name": "整单委外单来源单据类型", "className": "skyeye-erp#com.skyeye.whole.classenum.WholeOrderOutFromType"}, "procedureCapacitySubject": {"name": "工序产能主体", "className": "skyeye-erp#com.skyeye.procedure.classenum.ProcedureCapacitySubject"}, "machinProcedureFarmState": {"name": "车间任务状态", "className": "skyeye-erp#com.skyeye.machinprocedure.classenum.MachinProcedureFarmState"}, "shopMealOrderType": {"name": "智慧门店-订单来源", "className": "skyeye-shop#com.skyeye.meal.classenum.ShopMealOrderType"}, "shopMealOrderState": {"name": "智慧门店-套餐订单状态", "className": "skyeye-shop#com.skyeye.meal.classenum.ShopMealOrderState"}, "shopConfirmFromType": {"name": "智慧门店-门店物料接收/退货来源单据类型", "className": "skyeye-erp#com.skyeye.shop.classenum.ShopConfirmFromType"}, "keepFitOrderState": {"name": "智慧门店-保养订单状态", "className": "skyeye-shop#com.skyeye.keepfit.classenum.KeepFitOrderState"}, "keepFitOrderUserType": {"name": "智慧门店-保养订单的用户类型", "className": "skyeye-shop#com.skyeye.keepfit.classenum.KeepFitOrderUserType"}, "machinProcedureAcceptChildType": {"name": "工序验收单耗材类型枚举", "className": "skyeye-erp#com.skyeye.machinprocedure.classenum.MachinProcedureAcceptChildType"}, "productionPlanPurchaseState": {"name": "出货计划单采购状态", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionPlanPurchaseState"}, "productionPlanProduceState": {"name": "出货计划单生产状态", "className": "skyeye-erp#com.skyeye.production.classenum.ProductionPlanProduceState"}, "machinPutFromType": {"name": "加工入库单来源单据类型", "className": "skyeye-erp#com.skyeye.machin.classenum.MachinPutFromType"}, "autoProductState": {"name": "自动化-产品状态", "className": "skyeye-auto#com.skyeye.product.classenum.AutoProductState"}, "autoProjectState": {"name": "自动化-项目状态", "className": "skyeye-auto#com.skyeye.project.classenum.AutoProjectState"}, "materialNormsCodeInDepot": {"name": "商品规格一物一码库存状态", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialNormsCodeInDepot"}, "materialItemCode": {"name": "商品条形码开启类型", "className": "skyeye-erp#com.skyeye.material.classenum.MaterialItemCode"}, "routeTypeEnum": {"name": "路线类型", "className": "skyeye-school#com.skyeye.school.route.routeenum.RouteTypeEnum"}, "generateDepotLevelValType": {"name": "ERP-批量生成仓库级别的值的类型", "className": "skyeye-erp#com.skyeye.depot.classenum.GenerateDepotLevelValType"}, "menuType": {"name": "APP菜单类型", "className": "skyeye-pro#com.skyeye.menu.classenum.MenuType"}, "urlType": {"name": "APP菜单URL类型", "className": "skyeye-pro#com.skyeye.menu.classenum.UrlType"}, "storeNormsCodeUseState": {"name": "门店商品使用状态", "className": "skyeye-erp#com.skyeye.shop.classenum.StoreNormsCodeUseState"}, "pickNormsCodeUseState": {"name": "物料加工时的使用状态", "className": "skyeye-erp#com.skyeye.pick.classenum.PickNormsCodeUseState"}, "studentStateEnum": {"name": "表白墙-学生审核状态", "className": "skyeye-wall#com.skyeye.certification.classenum.StateEnum"}, "studentStateEnum2": {"name": "表白墙-学生审核状态-审核页面", "className": "skyeye-wall#com.skyeye.certification.classenum.StateEnum", "filterKey": "id", "filterValue": "3,4"}, "homeworkTypeEnum": {"name": "云课堂-作业类型", "className": "skyeye-school#com.skyeye.school.assignment.classenum.AssignmentType"}, "homeworkApplicationProcessEnum": {"name": "云课堂-作业应用环节", "className": "skyeye-school#com.skyeye.school.assignment.classenum.AssignmentApplicationProcess"}, "aiPlatformEnum": {"name": "AI平台", "className": "skyeye-adm#com.skyeye.ai.core.enums.AiPlatformEnum"}}