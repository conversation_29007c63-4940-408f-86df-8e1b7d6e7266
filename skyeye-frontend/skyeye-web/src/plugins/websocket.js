import { SkMessage } from '@/components/SkMessage/index.vue'
import config from './getConfig'

class WebSocketClient {
    constructor() {
        this.ws = null
        this.url = ''
        this.options = {
            reconnectInterval: 2000,  // 重连间隔时间
            heartbeatInterval: 30000,  // 心跳间隔
            heartbeatContent: 'ping'   // 心跳内容
        }
        this.reconnectCount = 0
        this.heartbeatTimer = null
        this.reconnectTimer = null
        this.messageHandlers = new Map()
        this.isConnected = false
        this.wasConnected = false // 记录之前是否已连接过
        this.token = ''  // 用于权限验证
    }

    // 初始化连接
    connect(url, token) {
        if (this.ws) {
            this.close()
        }

        this.url = url
        this.token = token

        try {
            if (token) {
                // 添加token到url
                const wsUrl = `${url}?token=${token}`
                this.ws = new WebSocket(wsUrl)
            } else {
                this.ws = new WebSocket(url)
            }

            this.initEventHandlers()
            return true
        } catch (error) {
            // console.error('WebSocket connection error:', error)
            return false
        }
    }

    // 初始化事件处理
    initEventHandlers() {
        if (!this.ws) return

        this.ws.onopen = () => {
            console.log('WebSocket connected')
            this.isConnected = true

            // 如果之前连接过且当前是重连成功的情况
            if (this.wasConnected && this.reconnectCount > 0) {
                SkMessage.success('WebSocket重连成功！')
            }

            this.wasConnected = true // 标记为已经连接过
            this.reconnectCount = 0
            this.startHeartbeat()
            // 触发所有注册的 onOpen 处理器
            this.triggerHandlers('onOpen')
        }

        this.ws.onclose = () => {
            console.log('WebSocket closed')
            this.isConnected = false
            this.stopHeartbeat()
            this.triggerHandlers('onClose')
            this.reconnect()
        }

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error)
            this.triggerHandlers('onError', error)
        }

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data)
                // 处理心跳响应
                if (data.type === 'pong') {
                    return
                }
                // 处理权限错误
                if (data.code === 401) {
                    SkMessage.error('WebSocket认证失败')
                    this.close()
                    return
                }
                this.triggerHandlers('onMessage', data)
            } catch (error) {
                console.error('Message parsing error:', error)
            }
        }
    }

    // 注册消息处理器
    on(event, handler) {
        if (!this.messageHandlers.has(event)) {
            this.messageHandlers.set(event, new Set())
        }
        this.messageHandlers.get(event).add(handler)
    }

    // 移除消息处理器
    off(event, handler) {
        if (this.messageHandlers.has(event)) {
            if (handler) {
                this.messageHandlers.get(event).delete(handler)
            } else {
                this.messageHandlers.delete(event)
            }
        }
    }

    // 触发处理器
    triggerHandlers(event, data) {
        const handlers = this.messageHandlers.get(event)
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data)
                } catch (error) {
                    console.error(`Error in ${event} handler:`, error)
                }
            })
        }
    }

    // 发送消息
    send(data) {
        if (!this.isConnected) {
            SkMessage.error('WebSocket未连接')
            return false
        }

        try {
            const message = typeof data === 'string' ? data : JSON.stringify(data)
            this.ws.send(message)
            return true
        } catch (error) {
            // console.error('Send message error:', error)
            return false
        }
    }

    // 开始心跳
    startHeartbeat() {
        this.stopHeartbeat()
        this.heartbeatTimer = setInterval(() => {
            this.send({ type: 'ping' })
        }, this.options.heartbeatInterval)
    }

    // 停止心跳
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
            this.heartbeatTimer = null
        }
    }

    // 重连 - 修改为无限重连直到成功
    reconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer)
        }

        this.reconnectCount++
        console.log(`尝试重新连接... (第${this.reconnectCount}次尝试)`)

        this.reconnectTimer = setTimeout(() => {
            this.connect(this.url, this.token)
        }, this.options.reconnectInterval)
    }

    // 关闭连接
    close() {
        this.stopHeartbeat()
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer)
            this.reconnectTimer = null
        }
        if (this.ws) {
            this.ws.close()
            this.ws = null
        }
        this.isConnected = false
    }

    // 更新配置
    updateOptions(options) {
        this.options = { ...this.options, ...options }
    }

    // 检查连接状态
    isAlive() {
        return this.isConnected && this.ws?.readyState === WebSocket.OPEN
    }
}

// 导出 WebSocketClient 类，以便创建多个实例
export { WebSocketClient }

// 导出一个默认实例，用于通用场景
export const wsClient = new WebSocketClient()

export const setupWebSocket = (app, config = {}) => {
    // 注册默认实例
    app.config.globalProperties.$ws = wsClient

    // 如果提供了连接配置，则自动连接
    if (config.url) {
        wsClient.connect(config.url, config.token)
    }
}