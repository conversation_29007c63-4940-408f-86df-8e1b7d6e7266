import { WebSocketClient } from './websocket'

export class Conference {
    constructor() {
        this.pc = null // RTCPeerConnection 实例
        this.localStream = null // 本地媒体流
        this.remoteStreams = new Map() // 远程媒体流集合
        this.ws = null // WebSocket 实例
        this.recorder = null // 媒体录制器
        this.networkMonitor = null // 网络监控器
        this.userId = null
        this.userName = null
        this.role = 'attendee' // 角色: host(主持人) / attendee(参会者)
        this.options = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' }
            ]
        }

        // 添加会议统计
        this.stats = {
            startTime: 0,
            duration: 0,
            participants: new Map(),
            networkQuality: {
                good: 0,
                medium: 0,
                poor: 0
            },
            bandwidth: {
                upload: 0,
                download: 0
            },
            videoStats: {
                resolution: '',
                frameRate: 0,
                bitrate: 0,
                packetLoss: 0,
                jitter: 0
            },
            audioStats: {
                bitrate: 0,
                packetLoss: 0,
                jitter: 0,
                audioLevel: 0
            },
            connectionStats: {
                reconnectCount: 0,
                lastReconnectTime: 0,
                totalDisconnections: 0,
                averageReconnectTime: 0
            }
        }

        // 网络自适应配置
        this.networkConfig = {
            qualityLevels: [
                {
                    level: 'high',
                    constraints: {
                        video: {
                            width: 1280,
                            height: 720,
                            frameRate: 30,
                            bitrate: 2500000
                        },
                        audio: {
                            sampleRate: 48000,
                            bitrate: 128000
                        }
                    }
                },
                {
                    level: 'medium',
                    constraints: {
                        video: {
                            width: 854,
                            height: 480,
                            frameRate: 25,
                            bitrate: 1000000
                        },
                        audio: {
                            sampleRate: 44100,
                            bitrate: 96000
                        }
                    }
                },
                {
                    level: 'low',
                    constraints: {
                        video: {
                            width: 640,
                            height: 360,
                            frameRate: 20,
                            bitrate: 500000
                        },
                        audio: {
                            sampleRate: 22050,
                            bitrate: 64000
                        }
                    }
                }
            ],
            currentLevel: 'high'
        }

        // 重连策略配置
        this.reconnectStrategy = {
            maxAttempts: 5,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 1.5,
            currentAttempt: 0
        }

        this.recordedChunks = [] // 录制的数据块
    }

    // 初始化会议
    async init({ localVideo, onRemoteStream, onMessage, onRecordComplete }) {
        try {
            // 初始化 WebSocket
            this.ws = new WebSocketClient()
            await this.initWebSocket()

            // 获取本地媒体流
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            })

            // 显示本地视频
            if (localVideo) {
                localVideo.srcObject = this.localStream
            }

            // 初始化 RTCPeerConnection
            this.pc = new RTCPeerConnection(this.options)
            this.initPeerConnection(onRemoteStream)

            // 添加本地流
            this.localStream.getTracks().forEach(track => {
                this.pc.addTrack(track, this.localStream)
            })

            // 初始化网络监控
            this.initNetworkMonitor()

            // 保存回调
            this.onMessage = onMessage
            this.onRecordComplete = onRecordComplete

            return true
        } catch (error) {
            console.error('Conference init error:', error)
            throw new Error('会议初始化失败')
        }
    }

    // 初始化 WebSocket
    async initWebSocket() {
        try {
            const connected = await this.ws.connect(
                `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/conference`
            )
            if (!connected) {
                throw new Error('WebSocket 连接失败')
            }

            // 注册消息处理
            this.ws.on('message', (data) => this.handleMessage(data))
            this.ws.on('close', () => this.handleDisconnect())
        } catch (error) {
            console.error('WebSocket init error:', error)
            throw error
        }
    }

    // 初始化 RTCPeerConnection
    initPeerConnection(onRemoteStream) {
        // 处理 ICE 候选
        this.pc.onicecandidate = event => {
            if (event.candidate) {
                this.ws.send({
                    type: 'candidate',
                    candidate: event.candidate
                })
            }
        }

        // 处理连接状态变化
        this.pc.onconnectionstatechange = () => {
            switch (this.pc.connectionState) {
                case 'disconnected':
                case 'failed':
                    this.handleDisconnect()
                    break
                case 'connected':
                    this.handleConnected()
                    break
            }
        }

        // 处理远程流
        this.pc.ontrack = event => {
            const stream = event.streams[0]
            if (!this.remoteStreams.has(stream.id)) {
                this.remoteStreams.set(stream.id, stream)
                onRemoteStream?.({
                    id: stream.id,
                    stream,
                    userName: event.track.remote?.userName || '未知用户'
                })
            }
        }
    }

    // 增强的网络监控
    initNetworkMonitor() {
        this.networkMonitor = setInterval(async () => {
            if (!this.pc) return

            try {
                const stats = await this.pc.getStats()
                let totalRtt = 0
                let sampleCount = 0
                let totalLost = 0
                let totalPackets = 0

                stats.forEach(report => {
                    if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                        totalRtt += report.currentRoundTripTime
                        sampleCount++

                        // 更新带宽统计
                        this.stats.bandwidth.upload = report.availableOutgoingBitrate
                        this.stats.bandwidth.download = report.availableIncomingBitrate
                    }

                    if (report.type === 'inbound-rtp') {
                        totalLost += report.packetsLost
                        totalPackets += report.packetsReceived
                    }
                })

                const averageRtt = totalRtt / sampleCount
                const packetLossRate = totalPackets ? (totalLost / totalPackets) * 100 : 0

                this.updateNetworkQuality(averageRtt, packetLossRate)
            } catch (error) {
                console.error('Network monitoring error:', error)
            }
        }, 2000)
    }

    // 网络质量评估和自适应
    updateNetworkQuality(rtt, packetLoss) {
        let quality = 'good'

        if (rtt > 300 || packetLoss > 10) {
            quality = 'poor'
        } else if (rtt > 150 || packetLoss > 5) {
            quality = 'medium'
        }

        this.stats.networkQuality[quality]++

        // 自适应质量调整
        this.adaptStreamQuality(quality)
    }

    // 流质量自适应
    async adaptStreamQuality(quality) {
        let targetLevel = 'high'

        switch (quality) {
            case 'poor':
                targetLevel = 'low'
                break
            case 'medium':
                targetLevel = 'medium'
                break
        }

        if (targetLevel !== this.networkConfig.currentLevel) {
            const constraints = this.networkConfig.qualityLevels.find(
                level => level.level === targetLevel
            ).constraints

            await this.applyMediaConstraints(constraints)
            this.networkConfig.currentLevel = targetLevel
        }
    }

    // 应用媒体约束
    async applyMediaConstraints(constraints) {
        if (!this.localStream) return

        try {
            // 更新视频约束
            const videoTrack = this.localStream.getVideoTracks()[0]
            if (videoTrack) {
                await videoTrack.applyConstraints(constraints.video)
            }

            // 更新音频约束
            const audioTrack = this.localStream.getAudioTracks()[0]
            if (audioTrack) {
                await audioTrack.applyConstraints(constraints.audio)
            }
        } catch (error) {
            console.error('Error applying constraints:', error)
        }
    }

    // 处理 WebSocket 消息
    handleMessage(data) {
        if (typeof data === 'string') {
            try {
                data = JSON.parse(data)
            } catch (error) {
                console.error('Invalid message format:', error)
                return
            }
        }

        switch (data.type) {
            case 'offer':
                this.handleOffer(data)
                break
            case 'answer':
                this.handleAnswer(data)
                break
            case 'candidate':
                this.handleCandidate(data)
                break
            case 'chat':
                this.onMessage?.(data)
                break
            case 'user-role':
                this.handleUserRole(data)
                break
            default:
                console.warn('Unknown message type:', data.type)
        }
    }

    // 处理用户角色变更
    handleUserRole(data) {
        this.role = data.role
        // 更新UI权限
    }

    // 音频控制
    toggleAudio(enabled) {
        if (this.localStream) {
            this.localStream.getAudioTracks().forEach(track => {
                track.enabled = enabled
            })
        }
    }

    // 视频控制
    toggleVideo(enabled) {
        if (this.localStream) {
            this.localStream.getVideoTracks().forEach(track => {
                track.enabled = enabled
            })
        }
    }

    // 开始录制
    async startRecording() {
        if (!this.localStream) {
            throw new Error('没有可用的媒体流')
        }

        try {
            this.recordedChunks = []
            this.recorder = new MediaRecorder(this.localStream, {
                mimeType: 'video/webm;codecs=vp9'
            })

            this.recorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data)
                }
            }

            this.recorder.start()
            return true
        } catch (error) {
            console.error('Recording start error:', error)
            throw new Error('开始录制失败')
        }
    }

    // 停止录制
    async stopRecording() {
        if (!this.recorder) {
            return
        }

        if (this.recorder.state !== 'recording') {
            throw new Error('没有正在进行的录制')
        }

        try {
            return new Promise((resolve) => {
                this.recorder.onstop = () => {
                    const blob = new Blob(this.recordedChunks, { type: 'video/webm' })
                    const file = new File([blob], `recording_${Date.now()}.webm`, { type: 'video/webm' })
                    resolve([file])
                }
                this.recorder.stop()
            })
        } catch (error) {
            console.error('Recording stop error:', error)
            throw new Error('停止录制失败')
        }
    }

    // 屏幕共享
    async shareScreen() {
        try {
            // 检查是否支持屏幕共享
            if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
                throw new Error('您的浏览器不支持屏幕共享')
            }

            // 获取屏幕共享流
            const screenStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    cursor: 'always',
                    displaySurface: 'monitor',
                    logicalSurface: true,
                    width: { max: 1920 },
                    height: { max: 1080 },
                    frameRate: { max: 30 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            })

            // 保存原始视频轨道，以便后续恢复
            const originalVideoTrack = this.localStream.getVideoTracks()[0]

            // 替换视频轨道
            const sender = this.pc.getSenders().find(s => s.track?.kind === 'video')
            if (sender) {
                const screenTrack = screenStream.getVideoTracks()[0]
                // 监听屏幕共享结束
                screenTrack.addEventListener('ended', async () => {
                    try {
                        // 恢复原始视频轨道
                        await sender.replaceTrack(originalVideoTrack)
                        // 停止屏幕共享轨道
                        screenTrack.stop()
                    } catch (error) {
                        console.error('Error restoring video track:', error)
                        throw new Error('恢复视频失败')
                    }
                })

                await sender.replaceTrack(screenTrack)
            }

            return true
        } catch (error) {
            // 根据错误类型提供不同的错误信息
            if (error.name === 'NotAllowedError') {
                throw new Error('您拒绝了屏幕共享权限')
            } else if (error.name === 'NotFoundError') {
                throw new Error('未找到可共享的屏幕')
            } else if (error.name === 'NotReadableError') {
                throw new Error('无法读取屏幕内容')
            } else if (error.name === 'AbortError') {
                throw new Error('屏幕共享已取消')
            } else {
                console.error('Screen share error:', error)
                throw new Error('屏幕共享失败')
            }
        }
    }

    // 发送消息
    sendMessage(message) {
        this.ws.send({
            type: 'chat',
            ...message
        })
    }

    // 离开会议
    async leave() {
        try {
            // 停止所有媒体轨道
            if (this.localStream) {
                this.localStream.getTracks().forEach(track => track.stop())
            }

            // 关闭 PeerConnection
            if (this.pc) {
                this.pc.close()
            }

            // 关闭 WebSocket
            if (this.ws) {
                this.ws.close()
            }

            // 清理其他资源
            this.cleanup()

            return true
        } catch (error) {
            console.error('Leave conference error:', error)
            throw error
        }
    }

    // 主持人控制功能增强
    async promoteToHost(userId) {
        if (this.role !== 'host') return

        try {
            await this.ws.send({
                type: 'host-command',
                command: 'promote',
                userId
            })
        } catch (error) {
            throw new Error('提升主持人失败')
        }
    }

    async setParticipantPermission(userId, permissions) {
        if (this.role !== 'host') return

        try {
            await this.ws.send({
                type: 'host-command',
                command: 'set-permissions',
                userId,
                permissions
            })
        } catch (error) {
            throw new Error('设置权限失败')
        }
    }

    async lockMeeting() {
        if (this.role !== 'host') return

        try {
            await this.ws.send({
                type: 'host-command',
                command: 'lock-meeting'
            })
        } catch (error) {
            throw new Error('锁定会议失败')
        }
    }

    // 优化的重连机制
    async reconnect() {
        const strategy = this.reconnectStrategy

        if (strategy.currentAttempt >= strategy.maxAttempts) {
            throw new Error('超过最大重连次数')
        }

        try {
            // 记录重连开始时间
            const startTime = Date.now()
            this.stats.connectionStats.lastReconnectTime = startTime

            // 计算延迟时间
            const delay = Math.min(
                strategy.baseDelay * Math.pow(strategy.backoffFactor, strategy.currentAttempt),
                strategy.maxDelay
            )

            await new Promise(resolve => setTimeout(resolve, delay))

            // 保存当前状态
            const currentState = this.saveCurrentState()

            // 清理现有连接
            await this.cleanup()

            // 重新初始化
            await this.init({
                localVideo: this.localVideo,
                onRemoteStream: this.onRemoteStream,
                onMessage: this.onMessage,
                onRecordComplete: this.onRecordComplete
            })

            // 恢复状态
            await this.restoreState(currentState)

            // 更新统计信息
            this.updateReconnectionStats(startTime)

            // 重置重连计数
            strategy.currentAttempt = 0

            return true
        } catch (error) {
            strategy.currentAttempt++
            console.error('Reconnection attempt failed:', error)

            // 递归重试
            if (strategy.currentAttempt < strategy.maxAttempts) {
                return this.reconnect()
            }

            throw new Error('重连失败')
        }
    }

    // 更新重连统计
    updateReconnectionStats(startTime) {
        const stats = this.stats.connectionStats
        const reconnectTime = Date.now() - startTime

        stats.reconnectCount++
        stats.totalDisconnections++
        stats.averageReconnectTime = (
            (stats.averageReconnectTime * (stats.reconnectCount - 1) + reconnectTime) /
            stats.reconnectCount
        )
    }

    // 主持人高级控制功能
    async setMeetingLayout(layout) {
        if (this.role !== 'host') return
        await this.ws.send({
            type: 'host-command',
            command: 'set-layout',
            layout
        })
    }

    async startBreakoutRooms(config) {
        if (this.role !== 'host') return
        await this.ws.send({
            type: 'host-command',
            command: 'create-breakout-rooms',
            config
        })
    }

    async broadcastMessage(message) {
        if (this.role !== 'host') return
        await this.ws.send({
            type: 'host-command',
            command: 'broadcast',
            message
        })
    }

    async setParticipantRole(userId, role) {
        if (this.role !== 'host') return
        await this.ws.send({
            type: 'host-command',
            command: 'set-role',
            userId,
            role
        })
    }

    // 详细的媒体统计收集
    async collectMediaStats() {
        if (!this.pc) return

        const stats = await this.pc.getStats()

        stats.forEach(report => {
            if (report.type === 'inbound-rtp' && report.kind === 'video') {
                this.stats.videoStats = {
                    resolution: `${report.frameWidth}x${report.frameHeight}`,
                    frameRate: report.framesPerSecond,
                    bitrate: report.bitrateMean,
                    packetLoss: report.packetsLost,
                    jitter: report.jitter
                }
            } else if (report.type === 'inbound-rtp' && report.kind === 'audio') {
                this.stats.audioStats = {
                    bitrate: report.bitrateMean,
                    packetLoss: report.packetsLost,
                    jitter: report.jitter,
                    audioLevel: report.audioLevel
                }
            }
        })
    }

    // 处理连接断开
    handleDisconnect() {
        console.log('Connection lost, attempting to reconnect...')
        this.reconnect().catch(error => {
            console.error('Reconnection failed:', error)
        })
    }

    // 处理连接成功
    handleConnected() {
        console.log('Connection established')
        this.stats.startTime = Date.now()
        this.reconnectStrategy.currentAttempt = 0
    }

    // 处理 Offer
    async handleOffer(data) {
        try {
            await this.pc.setRemoteDescription(new RTCSessionDescription(data.offer))
            const answer = await this.pc.createAnswer()
            await this.pc.setLocalDescription(answer)
            this.ws.send({
                type: 'answer',
                answer
            })
        } catch (error) {
            console.error('Error handling offer:', error)
        }
    }

    // 处理 Answer
    async handleAnswer(data) {
        try {
            await this.pc.setRemoteDescription(new RTCSessionDescription(data.answer))
        } catch (error) {
            console.error('Error handling answer:', error)
        }
    }

    // 处理 ICE Candidate
    async handleCandidate(data) {
        try {
            await this.pc.addIceCandidate(new RTCIceCandidate(data.candidate))
        } catch (error) {
            console.error('Error handling ice candidate:', error)
        }
    }

    // 清理资源
    cleanup() {
        try {
            // 清理录制相关资源
            if (this.recorder) {
                if (this.recorder.state === 'recording') {
                    this.recorder.stop()
                }
                this.recorder = null
                this.recordedChunks = []
            }

            // 重置状态
            this.stats = {
                startTime: 0,
                duration: 0,
                participants: new Map(),
                networkQuality: { good: 0, medium: 0, poor: 0 },
                bandwidth: { upload: 0, download: 0 },
                videoStats: { resolution: '', frameRate: 0, bitrate: 0, packetLoss: 0, jitter: 0 },
                audioStats: { bitrate: 0, packetLoss: 0, jitter: 0, audioLevel: 0 },
                connectionStats: { reconnectCount: 0, lastReconnectTime: 0, totalDisconnections: 0, averageReconnectTime: 0 }
            }
        } catch (error) {
            console.error('Cleanup error:', error)
        }
    }
} 