import $config from '@/plugins/getConfig'
import http from '@/plugins/http'
import classEnumMap from './classEnum.json'

var formatTime = function (time) {
	if (typeof time !== 'number' || time < 0) {
		return time
	}

	var hour = parseInt(time / 3600)
	time = time % 3600
	var minute = parseInt(time / 60)
	time = time % 60
	var second = time

	return ([hour, minute, second]).map(function (n) {
		n = n.toString()
		return n[1] ? n : '0' + n
	}).join(':')
}

var formatLocation = function (longitude, latitude) {
	if (typeof longitude === 'string' && typeof latitude === 'string') {
		longitude = parseFloat(longitude)
		latitude = parseFloat(latitude)
	}

	longitude = longitude.toFixed(2)
	latitude = latitude.toFixed(2)

	return {
		longitude: longitude.toString().split('.'),
		latitude: latitude.toString().split('.')
	}
}
var dateUtils = {
	UNITS: {
		'年': 31557600000,
		'月': 2629800000,
		'天': 86400000,
		'小时': 3600000,
		'分钟': 60000,
		'秒': 1000
	},
	humanize: function (milliseconds) {
		var humanize = '';
		for (var key in this.UNITS) {
			if (milliseconds >= this.UNITS[key]) {
				humanize = Math.floor(milliseconds / this.UNITS[key]) + key + '前';
				break;
			}
		}
		return humanize || '刚刚';
	},
	format: function (dateStr) {
		var date = this.parse(dateStr)
		var diff = Date.now() - date.getTime();
		if (diff < this.UNITS['天']) {
			return this.humanize(diff);
		}
		var _format = function (number) {
			return (number < 10 ? ('0' + number) : number);
		};
		return date.getFullYear() + '/' + _format(date.getMonth() + 1) + '/' + _format(date.getDay()) + '-' +
			_format(date.getHours()) + ':' + _format(date.getMinutes());
	},
	parse: function (str) { //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
		var a = str.split(/[^0-9]/);
		return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
	},

	// 获取今天是几号
	getTodayDay: function () {
		var today = new Date();
		var todayDay = today.getDate();
		return todayDay;
	},
	// 获取当月第一天
	getOneYMDFormatDate: function () {
		var date = new Date;
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		month = (month < 10 ? "0" + month : month);
		return year.toString() + "-" + month.toString() + "-" + "01";
	},

	// 获取上个月一号的日期
	getLastOneYMDFormatDate: function () {
		var date = new Date;
		var year = date.getFullYear();
		var month = date.getMonth();
		month = (month < 10 ? "0" + month : month);
		return year.toString() + "-" + month.toString() + "-" + "01";
	},

	// 获取前一天的时间
	getYesterdayYMDFormatDate: function () {
		var myDate = new Date();
		var lw = new Date(myDate - 1000 * 60 * 60 * 24 * 1);
		var lastY = lw.getFullYear();
		var lastM = lw.getMonth() + 1;
		var lastD = lw.getDate();

		return lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-" + (lastD < 10 ? "0" + lastD : lastD);
	},

	// 获取本月日期
	getOneYMFormatDate: function () {
		var date = new Date;
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		month = (month < 10 ? "0" + month : month);
		return year.toString() + "-" + month.toString();
	},

	// 获取指定日期是第几周，sdate日期格式yyyy-mm-dd
	weekofyear: function (sdate) {
		var d = new Date(sdate);
		var myYear = d.getFullYear();
		var firstDate = new Date(myYear + "-01-01");
		var dayofyear = 0;
		for (var i = 0; i < d.getMonth(); i++) {

			switch (i) {
				case 0:
				case 2:
				case 4:
				case 6:
				case 7:
				case 9:
					dayofyear += 31;
					break;
				case 1:
					if (this.isLeapYear(d)) {
						dayofyear += 29;
					} else {
						dayofyear += 28;
					}
					break;
				case 3:
				case 5:
				case 8:
				case 10:
					dayofyear += 30;
					break;
			}
		}
		dayofyear += d.getDate() + 1;
		var week = firstDate.getDay();
		var dayNum = dayofyear - (7 - week);
		var weekNum = 1;
		weekNum = weekNum + (dayNum / 7);
		if (dayNum % 7 != 0)
			weekNum = weekNum + 1;
		return parseInt(weekNum);
	},

	// 判断是否为闰年
	isLeapYear: function (date) {
		return (0 == date.getFullYear() % 4 && ((date.getFullYear() % 100 != 0) || (date.getFullYear() % 400 == 0)));
	},

	// 获取指定日期是星期几
	weekDay: function (date) {
		var _date = new Date(date);
		var num = _date.getDay();
		if (num == 0) {
			return 7;
		}
		return num;
	},

	// 获取星期几
	getWeekDay: function (date) {
		const weekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
		return weekMap[new Date(date).getDay()]
	},

	formatTime: function (time) {
		const now = new Date()
		const date = new Date(time)
		const diff = now - date // 时间差（毫秒）

		// 一分钟内
		if (diff < 60 * 1000) {
			return '刚刚'
		}

		// 一小时内
		if (diff < 60 * 60 * 1000) {
			const minutes = Math.floor(diff / (60 * 1000))
			return `${minutes}分钟前`
		}

		// 24小时内
		if (diff < 24 * 60 * 60 * 1000) {
			const hours = Math.floor(diff / (60 * 60 * 1000))
			return `${hours}小时前`
		}

		// 7天内
		if (diff < 7 * 24 * 60 * 60 * 1000) {
			const days = Math.floor(diff / (24 * 60 * 60 * 1000))
			return `${days}天前`
		}

		// 超过7天显示具体日期
		const year = date.getFullYear()
		const month = (date.getMonth() + 1).toString().padStart(2, '0')
		const day = date.getDate().toString().padStart(2, '0')
		const hour = date.getHours().toString().padStart(2, '0')
		const minute = date.getMinutes().toString().padStart(2, '0')

		// 如果是今年的消息，不显示年份
		if (year === now.getFullYear()) {
			return `${month}-${day} ${hour}:${minute}`
		}

		return `${year}-${month}-${day} ${hour}:${minute}`
	},

	// 计算开始时间和结束时间之间间隔minute分钟的多个时间段
	getTimePointMinute: function (startTime, endTime, minute) {
		var result = new Array();
		var startHour = parseInt(startTime.split(":")[0]);
		var startMinute = parseInt(startTime.split(":")[1]);
		var currentMinute = startHour * 60 + startMinute;

		var endHour = parseInt(endTime.split(":")[0]);
		var endMinute = parseInt(endTime.split(":")[1]);
		var maxMinute = endHour * 60 + endMinute;
		if ((currentMinute + minute) <= maxMinute) {
			var newStartTime = this.turnTime(startTime, minute);
			result.push(startTime + " ~ " + newStartTime);
			result = result.concat(this.getTimePointMinute(newStartTime, endTime, minute));
		}
		return result;
	},

	turnTime: function (time, mm) {
		var hour = parseInt(time.split(":")[0]);
		var minute = parseInt(time.split(":")[1]);
		if ((minute + mm) >= 60) {
			minute = minute + mm - 60;
			hour = hour + 1;
			while (minute >= 60) {
				minute = minute - 60;
				hour = hour + 1;
			}
		} else {
			minute = minute + mm;
		}
		return (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute);
	}

};

var getNowDate = function () {

	var datetime = new Date();
	return formateDate(datetime, "Y-M-D")
}

var deteleObject = function (obj) {
	var uniques = [];
	var stringify = {};
	for (var i = 0; i < obj.length; i++) {
		var keys = Object.keys(obj[i]);
		keys.sort(function (a, b) {
			return (Number(a) - Number(b));
		});
		var str = '';
		for (var j = 0; j < keys.length; j++) {
			str += JSON.stringify(keys[j]);
			str += JSON.stringify(obj[i][keys[j]]);
		}
		if (!stringify.hasOwnProperty(str)) {
			uniques.push(obj[i]);
			stringify[str] = true;
		}
	}
	uniques = uniques;
	return uniques;
}

var formateDate = function (datetime, type) {
	var year = datetime.getFullYear(),
		month = ("0" + (datetime.getMonth() + 1)).slice(-2),
		date = ("0" + datetime.getDate()).slice(-2),
		hour = ("0" + datetime.getHours()).slice(-2),
		minute = ("0" + datetime.getMinutes()).slice(-2),
		second = ("0" + datetime.getSeconds()).slice(-2);
	let result
	if (type === "Y-M-D h:min:s") {
		result = year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
	} else if (type === "Y-M-D") {
		result = year + "-" + month + "-" + date;
	} else if (type === "Y-M") {
		result = year + "-" + month;
	} else if (type === "h:min:s") {
		result = hour + ":" + minute + ":" + second;
	} else if (type === "h") {
		result = hour;
	} else if (type === "min") {
		result = minute;
	} else if (type === "h:min") {
		result = hour + ":" + minute;
	} else if (type === "Y-M-D h:min") {
		result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
	} else if (type === "Y") {
		result = year;
	}
	return result;
}
//生成从minNum到maxNum的随机数
var randomNum = function (minNum, maxNum) {
	switch (arguments.length) {
		case 1:
			return parseInt(Math.random() * minNum + 1, 10);
			break;
		case 2:
			return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
			break;
		default:
			return 0;
			break;
	}
}

var pointInsideCircle = function (point, circle, r) {
	if (r === 0) return false
	var dx = circle[0] - point[0]
	var dy = circle[1] - point[1]
	return dx * dx + dy * dy <= r * r;
}

// 判断是否是一天
var isSameDay = function (timeStampA) {
	let dateA = new Date(timeStampA);
	let dateB = new Date();
	return (dateA.setHours(0, 0, 0, 0) == dateB.setHours(0, 0, 0, 0));
}

// 判断是否为空
var isNull = function (str) {
	if (str == null || str == "" || str == '' || str == "null" || str == "undefined") {
		if (str === 0) {
			return false;
		}
		return true;
	} else {
		return false;
	}
}

var getServiceKey = function (serviceStr) {
	return serviceStr.substring(serviceStr.indexOf(".") + 1);
}

// 获取属性的数据展示类型
var getShowType = function (attr) {
	if (!isNull(attr) && !isNull(attr.attrDefinitionCustom)) {
		if (!isNull(attr.attrDefinitionCustom.dsFormComponent)) {
			return attr.attrDefinitionCustom.dsFormComponent.showType;
		}
	}
	return null;
}

var getKeyIdToMation = function (key) {
	return key.replace("Id", "") + "Mation";
}

var getKeyIdToKey = function (key) {
	return key.replace("Id", "") + "Key";
}

var getContentLinkedDataValue = function (content, data) {
	let value = content.attrKey == '' ? '' : data[content.attrKey]
	if (isNull(value) && value + '' != '0') {
		return '';
	}
	let attrDefinition = content.attrDefinition;
	if (isNull(attrDefinition) || isNull(attrDefinition.attrDefinitionCustom)) {
		return '';
	}
	let customAttr = attrDefinition.attrDefinitionCustom;

	if (!isNull(customAttr.objectId) || !isNull(customAttr.defaultData) ||
		(!isNull(customAttr.businessApi) && Object.keys(customAttr.businessApi).length != 0)) {
		const dataType = customAttr.dataType;
		if (dataType == 1) {
			// 自定义
			let obj = isNull(customAttr.defaultData) ? [] : customAttr.defaultData;
			if (typeof obj == 'string') {
				obj = JSON.parse(obj);
			}
			return getInPoingArr(obj, "id", value, "name");
		} else if (dataType == 2) {
			// 枚举
			return value;
		} else if (dataType == 3) {
			// 数据字典
			return value;
		} else if (dataType == 4) {
			// 自定义接口
			var key = attrDefinition.attrKey;
			key = getKeyIdToMation(key);
			var tmp = data[key];
			if (isNull(tmp)) {
				return '';
			}
			return tmp.name || tmp.title;
		}
	}

	if (!isNull(customAttr.dsFormComponent)) {
		var showType = getShowType(attrDefinition);
		var numCode = customAttr.dsFormComponent.numCode;
		// Id转Mation取值转换
		if (showType == 8) {
			var key = attrDefinition.attrKey;
			// 例如：将key由relationUserId变为relationUserMation
			key = getKeyIdToMation(key);
			value = data[key];
			if (!isNull(value) && Object.keys(value).length != 0) {
				// 判断值是否为对象，如果是对象，则组装成数组
				if (!Array.isArray(value)) {
					value = [].concat(value);
				}
			}
		}
	}

	return value;
}

/**
 * 获取一个值是否在指定的集合中匹配到的值的指定key
 *
 * @param array 集合
 * @param key 要比较的key
 * @param value 要比较的值
 * @param getKey 要获取的值
 * @returns {null|*}
 */
var getInPoingArr = function (array, key, value, getKey) {
	if (array == null) {
		return null;
	}
	for (var i = 0; i < array.length; i++) {
		if (array[i][key] == value) {
			if (isNull(getKey)) {
				return array[i];
			}
			return array[i][getKey];
		}
	}
	return null;
}

// 枚举数据缓存
const templateStaticDataMap = {}

var getEnumMapByCode = async function (code) {
	const rows = await getEnumListByCode(code)
	let result = {}
	rows.forEach((item, index) => {
		result[item.id] = item.name
	})
	return result
}

var getEnumListByCode = async function (code) {
	// 先判断缓存中是否有数据
	if (templateStaticDataMap[code]) {
		return templateStaticDataMap[code]
	}
	// 缓存中没有数据，则请求数据
	const params = {
		className: classEnumMap[code].className
	}
	if (!isNull(classEnumMap[code]["filterValue"])) {
		params["filterValue"] = classEnumMap[code]["filterValue"];
		params["filterKey"] = classEnumMap[code]["filterKey"];
	}
	const res = await http.post($config.getConfig().reqBasePath + "getEnumDataByClassName", params);
	templateStaticDataMap[code] = res.rows
	return res.rows
}

var getEnumListMapByCode = async function (codeList) {
	// 遍历codeList，先判断缓存中是否有数据
	let result = {}
	let tempList = []
	codeList.forEach((item, index) => {
		if (templateStaticDataMap[item]) {
			result[item] = templateStaticDataMap[item]
		} else {
			tempList.push(classEnumMap[item].className)
		}
	})
	// 如果tempList不为空，则请求数据
	if (tempList.length > 0) {
		const params = {
			classNameList: JSON.stringify(tempList)
		}
		const res = await http.post($config.getConfig().reqBasePath + "getEnumDataMapByClassName", params)
		// 将数据添加到result中
		for (let key in res.bean) {
			let code = getCodeByClassName(key);
			result[code] = res.bean[key]
			templateStaticDataMap[code] = res.bean[key]
		}
	}
	return result
}

var getEnumListMapForStandardByCode = async function (codeList) {
	const result = await getEnumListMapByCode(codeList)
	let resultMap = {}
	for (let code in result) {
		resultMap[code] = result[code].map(item => ({
			label: item.name,
			value: item.id,
			isDefault: item.isDefault
		}))
	}
	return resultMap
}

var getEnumList2MapByCode = async function (codeList) {
	const bean = await getEnumListMapByCode(codeList)
	let result = {}
	for (let code in bean) {
		let map = {};
		bean[code].forEach((item, index) => {
			map[item.id] = item.name
		});
		result[code] = map
	}
	return result
}

let getCodeByClassName = function (className) {
	for (let key in classEnumMap) {
		if (className === classEnumMap[key].className) {
			return key;
		}
	}
	return '';
}

var getDictMapByCode = async function (code) {
	const rows = await getDictListByCode(code)
	let result = {}
	rows.forEach((item, index) => {
		result[item.id] = item.name
	})
	return result
}

var getDictListByCode = async function (code) {
	const params = {
		dictTypeCode: code
	}
	const res = await http.get($config.getConfig().reqBasePath + "queryDictDataListByDictTypeCode", params);
	return res.rows
}

var getDictListMapByCode = async function (codeList, enabled = null) {
	const params = {
		dictTypeCodeList: JSON.stringify(codeList),
		enabled: enabled
	}
	const res = await http.post($config.getConfig().reqBasePath + "queryDictDataListByDictTypeCodeList", params)
	let result = {}
	for (let key in res.bean) {
		let map = {};
		if (res.bean[key].length > 0) {
			res.bean[key].forEach((item, index) => {
				map[item.id] = item.dictName
			});
		}
		result[key] = map
	}
	return result
}


// 在小程序中，json 中不能使用正则对象，如：/^\S+?@\S+?\.\S+?$/，使用正则对象会被微信序列化，导致正则失效。
// 所以建议统一使用字符串的方式来使用正则 ，如'^\\S+?@\\S+?\\.\\S+?$' ，需要注意 \ 需要使用 \\ 来转译。
// 如验证邮箱：/^\S+?@\S+?.\S+?$/ （注意不带引号）,或使用 "^\S+?@\S+?\.\S+?$"（注意带引号需要使用 \ 转义）
var verify = {
	phone: {
		pattern: '(^$)|^1\\d{10}$',
		message: '请输入正确的手机号',
		trigger: 'blur'
	},
	tel: {
		pattern: '(^$)|^0\\d{2,3}-?\\d{7,8}$',
		message: '请输入正确的电话号',
		trigger: 'blur'
	},
	email: {
		pattern: '(^$)|^([a-zA-Z0-9_\\.\\-])+\\@(([a-zA-Z0-9\\-])+\\.)+([a-zA-Z0-9]{2,4})+$',
		message: '邮箱格式不正确',
		trigger: 'blur'
	},
	url: {
		pattern: '(^$)|(^#)|(^http(s*):\\/\\/[^\\s]+\\.[^\\s]+)',
		message: '链接格式不正确',
		trigger: 'blur'
	},
	number: {
		pattern: '^$|^[0-9]+$|^[0-9]+\\.?[0-9]*$',
		message: '只能填写数字',
		trigger: 'blur'
	},
	date: {
		pattern: '(^$)|^(\\d{4})[-\\/](\\d{1}|0\\d{1}|1[0-2])([-\\/](\\d{1}|0\\d{1}|[1-2][0-9]|3[0-1]))*$',
		message: '日期格式不正确',
		trigger: 'blur'
	},
	identity: {
		pattern: '(^$)|(^\\d{18}$)|(^\\d{15}$)|(^\\d{17}(x|X|\\d)$)',
		message: '请输入正确的身份证号',
		trigger: 'blur'
	},
	double: {
		pattern: '(^$)|^[0-9]+(.[0-9]{1,6})?$',
		message: '请输入正确正数,小数点后最多六位',
		trigger: 'blur'
	},
	postcode: {
		pattern: '(^$)|^\\d{6}$',
		message: '请输入正确邮编',
		trigger: 'blur'
	},
	money: {
		pattern: '(^$)|^0{1}([.]\\d{1,6})?$|^[1-9]\\d*([.]{1}[0-9]{1,6})?$',
		message: '请输入正确的金额, 可保留小数点后六位',
		trigger: 'blur'
	},
	percentage: {
		pattern: '(^$)|^0{1}([.]\\d{1,2})?$|^[1-9]\\d*([.]{1}[0-9]{1,2})?$',
		message: '请输入正确的小数, 可保留小数点后两位',
		trigger: 'blur'
	},
	vincode: {
		pattern: '^(?![^A-Z]+$)(?![^0-9]+$)[1-9WTJSKLVRYZ]{1}[A-HJ-NPR-Z0-9]{16}$',
		message: '请输入正确的VIN码',
		trigger: 'blur'
	},
	plateno: {
		pattern: '^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$',
		message: '请输入正确的车牌号',
		trigger: 'blur'
	}
}
var getRules = function (formItem) {
	let rules = []
	if (formItem.isEdit == $config.formEditType.notEdit) {
		return rules
	}
	if (!isNull(formItem['required']) && formItem['required']) {
		rules.push({
			required: true,
			message: getTipContent(formItem),
			trigger: ['change', 'blur']
		})
	}

	if (!isNull(formItem.require)) {
		formItem.require.forEach((item, i) => {
			if (item != 'required') {
				if (item == 'number') {
					rules.push({
						pattern: verify[item].pattern,
						validator: (rule, value) => {
							// 如果值为空，直接通过（空值由 required 规则处理）
							if (value === '' || value === undefined || value === null) {
								return Promise.resolve()
							}
							// 转换为字符串后验证是否为数字
							const strValue = String(value)
							if (/^[0-9]+$/.test(strValue)) {
								return Promise.resolve()
							}
							return Promise.reject(verify[item].message)
						},
						message: verify[item].message,
						trigger: ['blur', 'change']
					})
				} else {
					rules.push(verify[item])
				}
			}
		})
	}

	return rules;
}

var getTipContent = function (formItem) {
	var tips = formItem.placeholder;
	if (isNull(tips)) {
		if (formItem.numCode === 'select' || formItem.numCode === 'simpleTable') {
			tips = '请选择' + formItem.title
		} else if (formItem.numCode === 'input') {
			tips = '请输入' + formItem.title
		} else {
			tips = formItem.title || formItem.name + '不能为空'
		}
	}
	return tips
}

var calculationUtil = {
	// 加法
	sum: function (num1, num2) {
		var a1 = parseFloat(isNull(num1) ? 0 : num1);
		var a2 = parseFloat(isNull(num2) ? 0 : num2);
		return (a1 + a2).toFixed(2);
	},

	// 减法
	subtraction: function (num1, num2) {
		var a1 = parseFloat(isNull(num1) ? 0 : num1);
		var a2 = parseFloat(isNull(num2) ? 0 : num2);
		return (a1 - a2).toFixed(2);
	},

	// 乘法
	multiplication: function (num1, num2) {
		return calculationUtil.multiplicationByFixed(num1, num2, 2);
	},

	// 乘法
	multiplicationByFixed: function (num1, num2, fixedNum) {
		var a1 = parseFloat(isNull(num1) ? 0 : num1);
		var a2 = parseFloat(isNull(num2) ? 0 : num2);
		return (a1 * a2).toFixed(fixedNum);
	},

	// 除法
	division: function (num1, num2) {
		var a1 = parseFloat(isNull(num1) ? 0 : num1);
		var a2 = parseFloat(isNull(num2) ? 0 : num2);
		if (a2 == 0) {
			return 0;
		}
		return (a1 / a2).toFixed(2);
	}
}

var erpUtil = {
	calcOrderItem(list) {
		list.forEach((item, index) => {
			let result = erpUtil.calcMoneyKey('operNumber', item);
			item.allPrice = result.allPrice
			item.taxUnitPrice = result.taxUnitPrice
			item.taxMoney = result.taxMoney
			item.taxLastMoney = result.taxLastMoney
		})
		return list
	},

	disabledTableCardFormItem(attrList, callback) {
		attrList.forEach((item, index) => {
			callback(item, {
				onlyShow: true
			})
		})
	},

	// 计算优惠金额
	calcDiscountMoney(value, totalPrice) {
		const discount = parseFloat(isNull(value?.discount) ? 0 : value?.discount)
		const discountMoney = calculationUtil.division(
			calculationUtil.multiplication(totalPrice, discount), 100)
		return discountMoney
	},

	// 获取单据金额等相关信息
	calcMoneyKey: function (childAttrKey, itemFormData) {
		const result = {}
		var operNumber = {}


		// 获取数量(质检情况)
		if (!isNull(itemFormData.qualifiedNumber) && !isNull(itemFormData.concessionNumber)) {
			// 获取合格数量和让步接收数量
			const qualifiedNumber = parseInt(isNull(itemFormData.qualifiedNumber) ? 0 : itemFormData.qualifiedNumber)
			const concessionNumber = parseInt(isNull(itemFormData.concessionNumber) ? 0 : itemFormData.concessionNumber)
			// 计算实际数量（合格数量 + 让步接收数量）
			operNumber = qualifiedNumber + concessionNumber
			//其他情况
		} else {
			operNumber = parseInt(isNull(itemFormData.operNumber) ? 0 : itemFormData.operNumber)
		}


		// 获取单价
		const unitPrice = parseFloat(isNull(itemFormData.unitPrice) ? 0 : itemFormData.unitPrice)
		// 获取税率
		const taxRate = parseFloat(isNull(itemFormData.taxRate) ? 0 : itemFormData.taxRate) / 100
		if ('operNumber' === childAttrKey || 'unitPrice' === childAttrKey ||
			'taxRate' === childAttrKey || 'materialId' === childAttrKey || 'normsId' === childAttrKey) { // 数量 || 单价 || 税率 || 物料 || 规格
			// 输出金额
			result.allPrice = (operNumber * unitPrice).toFixed(2)
			// 输出税额=数量*税率*单价
			result.taxMoney = (operNumber * taxRate * unitPrice).toFixed(2)
			// 输出含税单价
			result.taxUnitPrice = (taxRate * unitPrice + unitPrice).toFixed(2)
			// 输出合计价税
			result.taxLastMoney = (operNumber * taxRate * unitPrice + operNumber * unitPrice).toFixed(2)
		} else if ('allPrice' === childAttrKey) { //金额
			// 获取金额
			const allPrice = parseFloat(isNull(itemFormData.allPrice) ? 0 : itemFormData.allPrice)
			// 输出税额=金额*税率
			result.taxMoney = (allPrice * taxRate).toFixed(2)
			// 输出单价,含税单价,合计价税
			if (operNumber != 0) {
				result.unitPrice = (allPrice / operNumber).toFixed(2)
				result.taxUnitPrice = (allPrice / operNumber * taxRate + allPrice / operNumber).toFixed(2)
				result.taxLastMoney = (allPrice * taxRate + allPrice).toFixed(2)
			} else {
				result.unitPrice = 0
				result.taxUnitPrice = 0
				result.taxLastMoney = 0
			}
		} else if ('taxMoney' === childAttrKey) { //税额
			// 获取税额
			const taxMoney = parseFloat(isNull(itemFormData.taxMoney) ? 0 : itemFormData.taxMoney)
			// 输出金额
			result.allPrice = (operNumber * unitPrice).toFixed(2)
			// 获取金额
			const allPrice = parseFloat(isNull(result.allPrice) ? 0 : result.allPrice)
			//输出含税单价,合计价税,税率
			if (operNumber != 0) {
				if (unitPrice != 0) {
					result.taxUnitPrice = (taxMoney / operNumber + unitPrice).toFixed(2)
					result.taxRate = (taxMoney / unitPrice / operNumber * 100).toFixed(2)
				} else {
					result.taxUnitPrice = 0
					result.taxRate = 0
					result.unitPrice = 0
					result.allPrice = 0
				}
				if (allPrice != 0) {
					result.taxLastMoney = (allPrice + taxMoney).toFixed(2)
				} else {
					result.taxLastMoney = 0
				}
			} else {
				result.taxUnitPrice = 0
				result.taxLastMoney = 0
			}
		} else if ('taxUnitPrice' === childAttrKey) { //含税单价
			// 获取含税单价
			var taxUnitPrice = parseFloat(isNull(itemFormData.taxUnitPrice) ? 0 : itemFormData.taxUnitPrice)
			if (taxUnitPrice == 0) {
				result.taxLastMoney = 0
				result.unitPrice = 0
				result.allPrice = 0
				result.taxMoney = 0
				result.taxRate = 0
			} else {
				//输出合计价税,税额,税率
				if (unitPrice != 0) {
					if (operNumber != 0) {
						result.taxLastMoney = (taxUnitPrice * operNumber).toFixed(2)
						result.allPrice = (unitPrice * operNumber).toFixed(2)
					} else {
						result.taxLastMoney = 0
						result.allPrice = 0
					}
					result.taxMoney = (taxUnitPrice - unitPrice).toFixed(2)
					result.taxRate = ((taxUnitPrice / unitPrice - 1) * 100).toFixed(2)
				} else {
					result.taxLastMoney = 0
					result.unitPrice = 0
					result.allPrice = 0
					result.taxMoney = 0
					result.taxRate = 0
				}
			}
		} else if ('taxLastMoney' === childAttrKey) { //合计价税
			// 获取合计价税
			var taxLastMoney = parseFloat(isNull(itemFormData.taxLastMoney) ? 0 : itemFormData.taxLastMoney)
			if (taxLastMoney == 0) {
				result.taxUnitPrice = 0
				result.unitPrice = 0
				result.allPrice = 0
				result.taxMoney = 0
				result.taxRate = 0
			} else {
				// 输出含税单价,税额,税率
				if (operNumber != 0) {
					if (unitPrice != 0) {
						result.taxUnitPrice = (taxLastMoney / operNumber).toFixed(2)
						result.taxMoney = (taxLastMoney / operNumber - unitPrice).toFixed(2)
						result.taxRate = ((taxLastMoney / operNumber / unitPrice - 1) * 100).toFixed(2)
						result.allPrice = (unitPrice * operNumber).toFixed(2)
					} else {
						result.allPrice = 0
						result.taxMoney = 0
						result.taxUnitPrice = 0
						result.unitPrice = 0
					}
				} else {
					result.taxUnitPrice = 0
					result.unitPrice = 0
					result.allPrice = 0
					result.taxMoney = 0
					result.taxRate = 0
				}
			}
		} else if ('concessionNumber' === childAttrKey || 'qualifiedNumber' === childAttrKey) { //让步接收数量 || 合格数量 
			// 输出金额
			result.allPrice = (operNumber * unitPrice).toFixed(2)
			// 输出税额=数量*税率*单价
			result.taxMoney = (operNumber * taxRate * unitPrice).toFixed(2)
			// 输出含税单价
			result.taxUnitPrice = (taxRate * unitPrice + unitPrice).toFixed(2)
			// 输出合计价税
			result.taxLastMoney = (operNumber * taxRate * unitPrice + operNumber * unitPrice).toFixed(2)
		}
		return result
	}

}

var timeUtil = {

	/**
	 * 计算出差工时
	 *
	 */
	calcBusinessTravelHour: function (timeMation) {
		let hour = "0";
		if (!isNull(timeMation.id)) {
			const startTime = timeMation.startTime + ":00";
			const endTime = timeMation.endTime + ":00";
			// 作息时间是否为空
			if (!isNull(timeMation.restStartTime) && !isNull(timeMation.restEndTime)) {
				var restStartTime = timeMation.restStartTime + ":00";
				var restEndTime = timeMation.restEndTime + ":00";
				if (timeUtil.compare_HHmmss(restStartTime, endTime) ||
					timeUtil.compare_HHmmss(startTime, restEndTime)) {
					// 出差结束时间比作息开始时间小或者出差开始时间比作息结束时间大，说明没有与作息时间重合的时间
					hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
				} else {
					var overlapTime = timeUtil.getOverlapTime(startTime, endTime, restStartTime, restEndTime);
					// 时间计算为：选择的出差时间时间减去与作息时间重复的时间段
					hour = calculationUtil.division(calculationUtil.subtraction(
						timeUtil.timeDifference(startTime, endTime),
						timeUtil.timeDifference(overlapTime[0], overlapTime[1])), 60);
				}
			} else {
				hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
			}
		}
		return hour
	},

	/**
	 * 计算请假工时
	 *
	 * @param num 表格的序号
	 */
	calcLeaveHour: function (timeMation, data) {
		var startTime = data.leaveStartTime;
		var endTime = data.leaveEndTime;
		var hour = "0"
		if (!isNull(startTime) && !isNull(endTime)) {
			startTime = startTime + ":00";
			endTime = endTime + ":00";
			// 作息时间是否为空
			if (!isNull(timeMation.restStartTime) && !isNull(timeMation.restEndTime)) {
				var restStartTime = timeMation.restStartTime + ":00";
				var restEndTime = timeMation.restEndTime + ":00";
				if (timeUtil.compare_HHmmss(restStartTime, endTime) || timeUtil.compare_HHmmss(startTime,
					restEndTime)) {
					// 请假结束时间比作息开始时间小或者请假开始时间比作息结束时间大，说明没有与作息时间重合的时间
					hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
				} else {
					var overlapTime = timeUtil.getOverlapTime(startTime, endTime, restStartTime, restEndTime);
					// 时间计算为：选择的请假时间时间减去与作息时间重复的时间段
					hour = calculationUtil.division(calculationUtil.subtraction(
						timeUtil.timeDifference(startTime, endTime),
						timeUtil.timeDifference(overlapTime[0], overlapTime[1])), 60);
				}
			} else {
				hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
			}
		}
		return hour
	},

	/**
	 * 计算加班工时
	 *
	 */
	calcOverHour: function (data) {
		let hour = "0";
		var startTime = data.overtimeStartTime;
		var endTime = data.overtimeEndTime;
		if (!isNull(startTime) && !isNull(endTime)) {
			hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
		}
		return hour
	},

	/**
	 * 计算销假工时
	 *
	 * @param num 表格的序号
	 */
	calcCancelHour: function (timeMation, data) {
		var startTime = data.cancelStartTime;
		var endTime = data.cancelEndTime;
		if (!isNull(startTime) && !isNull(endTime)) {
			var timeId = data.timeId;
			if (!isNull(timeId)) {
				var hour = "0";
				startTime = startTime + ":00";
				endTime = endTime + ":00";
				// 作息时间是否为空
				if (!isNull(timeMation.restStartTime) && !isNull(timeMation.restEndTime)) {
					var restStartTime = timeMation.restStartTime + ":00";
					var restEndTime = timeMation.restEndTime + ":00";
					if (timeUtil.compare_HHmmss(restStartTime, endTime) || timeUtil.compare_HHmmss(startTime,
						restEndTime)) {
						// 销假结束时间比作息开始时间小或者销假开始时间比作息结束时间大，说明没有与作息时间重合的时间
						hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
					} else {
						var overlapTime = timeUtil.getOverlapTime(startTime, endTime, restStartTime,
							restEndTime);
						// 时间计算为：选择的销假时间时间减去与作息时间重复的时间段
						hour = calculationUtil.division(calculationUtil.subtraction(
							timeUtil.timeDifference(startTime, endTime),
							timeUtil.timeDifference(overlapTime[0], overlapTime[1])), 60);
					}
				} else {
					hour = calculationUtil.division(timeUtil.timeDifference(startTime, endTime), 60);
				}
			}
		}
		return hour
	},


	/**
	 * 比较时间大小-时分秒，格式为HH:mm:ss,参数a大于参数b返回true
	 * @param a
	 * @param b
	 */
	compare_HHmmss: function (a, b) {
		var array1 = a.split(":");
		var total1 = array1[0] * 3600 + array1[1] * 60 + array1[2];
		var array2 = b.split(":");
		var total2 = array2[0] * 3600 + array2[1] * 60 + array2[2];
		return total1 - total2 > 0 ? true : false;
	},

	/**
	 * 计算时间差（相差分钟）
	 *
	 * @param startTime 开始时间，格式为HH:mm:ss
	 * @param endTime 结束时间，格式为HH:mm:ss
	 * @returns {number}
	 */
	timeDifference: function (startTime, endTime) {
		var start1 = startTime.split(":");
		var startAll = parseInt(start1[0] * 60) + parseInt(start1[1]);
		var end1 = endTime.split(":");
		var endAll = parseInt(end1[0] * 60) + parseInt(end1[1]);
		return endAll - startAll;
	},

	/**
	 * 获取两个时间段重叠的时间段,参数格式为HH:mm:ss
	 * @param startTime 开始时间1
	 * @param endTime 结束时间1
	 * @param restStartTime 开始时间2
	 * @param restEndTime 结束时间2
	 */
	getOverlapTime: function (startTime, endTime, restStartTime, restEndTime) {
		var result = [];
		// 开始时间以大的为准
		if (timeUtil.compare_HHmmss(startTime, restStartTime)) {
			result.push(startTime);
		} else {
			result.push(restStartTime);
		}
		// 结束时间以小的为准
		if (timeUtil.compare_HHmmss(restEndTime, endTime)) {
			result.push(endTime);
		} else {
			result.push(restEndTime);
		}
		return result;
	}
}

// 数组去重
var uniqueArr = function (arr) {
	let newArr = [arr[0]];
	for (let i = 1; i < arr.length; i++) {
		let repeat = false;
		for (let j = 0; j < newArr.length; j++) {
			if (arr[i] === newArr[j]) {
				repeat = true;
				break;
			} else {

			}
		}
		if (!repeat) {
			newArr.push(arr[i]);
		}
	}
	return newArr;
}

// 常用菜单的工具类
var commonMenusUtil = {
	cacheKey: 'commonMenuList',

	setCommonMenus: function (menuId) {
		let list = uni.getStorageSync(commonMenusUtil.cacheKey);
		if (isNull(list)) {
			list = [];
		}
		if (isNull(menuId)) {
			return;
		}
		list.splice(0, 0, menuId)
		uni.setStorageSync(commonMenusUtil.cacheKey, uniqueArr(list).slice(0, 8));
	},

	getCommonMenus: function () {
		let list = uni.getStorageSync(commonMenusUtil.cacheKey);
		if (isNull(list)) {
			list = [];
		}
		let commonUseMenuList = [];
		const menuList = $config.getMenuListMation();
		let newList = [];
		list.forEach((item, i) => {
			menuList.forEach((firstMenu, j) => {
				if (!isNull(firstMenu.children)) {
					firstMenu.children.forEach((secondMenu, k) => {
						secondMenu.children.forEach((mm, k) => {
							if (mm.id === item) {
								newList.push(item)
								commonUseMenuList.push(mm)
							}
						})
					})
				}
			});
		});
		uni.setStorageSync(commonMenusUtil.cacheKey, newList)
		return commonUseMenuList;
	}

}

var filterImage = function (data) {
	if (isNull(data)) {
		return ''
	}
	// g代表全部
	let reg = new RegExp('"/images', 'g')
	let newMsg = data.replace(reg, '"' + $config.getConfig().fileBasePath + 'images');
	return newMsg
}

var filterImageBasePath = function (data) {
	if (isNull(data)) {
		return ''
	}
	// g代表全部
	let reg = new RegExp($config.getConfig().fileBasePath + 'images', 'g')
	let newMsg = data.replace(reg, '/images');

	let reg1 = new RegExp($config.getConfig().fileBasePath + '/images', 'g')
	newMsg = newMsg.replace(reg1, '/images');
	return newMsg
}

var filters = {
	filtersText: function (val) {
		if (!isNull(val)) {
			return val.replace(/<[^>]+>/g, "");
		} else return '';
	}
}

var autoRandomCode = function () {
	//可获取的字符串
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWSYZabcdefghijklmnopqrstuvwsyz0123456789';
	const list = [];
	//通过随机获取八个字符串的索引下标
	for (let i = 0; i < 8; i++) {
		//61为chars字符串长度为62，注意索引是从0开始的
		const val_1 = Math.round(Math.random() * 61);
		list.push(val_1);
	}

	//再根据随机获取的8个字符串索引拿到这8个字符串进行拼接
	let passwd = '';
	for (let n = 0; n < list.length; n++) {
		passwd += chars.charAt(list[n]);
	}
	// var regNumber = /[A-Za-z0-9]{8}$/
	//最后判断是否符合要求（长度为8，由字母和数字组成），符合，将赋值到密码字段，不符合，重新调用该函数获取，只到符合为止
	const regNumber = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8}$/;
	if (regNumber.test(passwd)) {
		return passwd;
	} else {
		return this.autoRandomCode();
	}
}

// 添加标签颜色工具函数
export const getTagColor = (key) => {
	const colorMap = {
		// 部门相关
		'department': 'blue',          // 部门
		'branch': 'geekblue',         // 分部
		'group': 'cyan',              // 小组

		// 职位相关
		'position': 'purple',         // 职位
		'role': 'magenta',           // 角色
		'level': 'volcano',          // 级别

		// 状态相关
		'status': 'orange',          // 状态
		'progress': 'green',         // 进度
		'priority': 'red',           // 优先级

		// 类型相关
		'type': 'gold',              // 类型
		'category': 'lime',          // 分类
		'tag': 'blue',               // 标签

		// 时间相关
		'date': 'cyan',              // 日期
		'time': 'blue',              // 时间
		'period': 'geekblue',        // 时段

		// 用户相关
		'user': 'purple',            // 用户
		'permission': 'red',         // 权限

		// 业务相关
		'business': 'green',         // 业务
		'project': 'blue',           // 项目
		'module': 'cyan',            // 模块

		// 其他通用
		'name': 'blue',              // 名称
		'description': 'cyan',       // 描述
		'remark': 'orange',          // 备注
		'other': 'default',           // 其他
		'oddNumber': 'geekblue'       // 单据编号
	}

	// 支持自定义颜色映射扩展
	const customColorMap = {
		// 示例：可以根据具体业务添加更多映射
		'tech': 'geekblue',          // 技术相关
		'design': 'purple',          // 设计相关
		'operation': 'green',        // 运营相关
		'marketing': 'orange',       // 市场相关
		'finance': 'gold',           // 财务相关
		'hr': 'magenta'             // 人事相关
	}

	// 合并默认和自定义的颜色映射
	const finalColorMap = { ...colorMap, ...customColorMap }

	return finalColorMap[key] || 'default'
}

// 可用的标签颜色列表，用于参考
export const tagColors = [
	'blue',         // 蓝色
	'purple',       // 紫色
	'cyan',         // 青色
	'green',        // 绿色
	'magenta',      // 洋红
	'red',          // 红色
	'orange',       // 橙色
	'gold',         // 金色
	'lime',         // 青柠
	'geekblue',     // 极客蓝
	'volcano',      // 火山
	'default'       // 默认灰色
]

/**
 * 将列表数据转换为树形结构
 * @param {Array} list 列表数据
 * @param {Object} options 配置项
 * @param {string} options.id ID字段名
 * @param {string} options.parentId 父ID字段名
 * @param {string} options.children 子节点字段名
 * @returns {Array} 树形结构数据
 */
export function listToTree(list, options = {}) {
	const {
		id = 'id',
		parentId = 'parentId',
		children = 'children'
	} = options

	const map = {}
	const roots = []

	// 首先遍历列表，建立节点映射关系
	for (const item of list) {
		// 创建一个新对象，避免修改原始数据
		const node = { ...item }
		// 初始化子节点数组
		node[children] = []
		// 建立映射关系，方便后续查找
		map[node[id]] = node
	}

	// 再次遍历，构建树形结构
	for (const item of list) {
		const node = map[item[id]]
		const parent = map[item[parentId]]

		if (parent) {
			// 如果有父节点，将当前节点添加到父节点的children中
			parent[children].push(node)
		} else {
			// 如果没有父节点，则作为根节点
			roots.push(node)
		}
	}

	// 清理空的children数组
	const cleanEmptyChildren = (nodes) => {
		return nodes.map(node => {
			const newNode = { ...node }
			if (newNode[children].length > 0) {
				newNode[children] = cleanEmptyChildren(newNode[children])
			} else {
				delete newNode[children]
			}
			return newNode
		})
	}

	return cleanEmptyChildren(roots)
}

export function getLevelText(level) {
	const levelMap = {
		0: '省级',
		1: '市级',
		2: '区/县级',
		3: '街道/乡镇级',
		4: '村/社区级'
	}
	return levelMap[level] || ''
}

/**
 * 搜索树结构数据
 * @param {Array} tree 树形数据
 * @param {string} keyword 搜索关键字
 * @param {Object} options 配置项
 * @param {Array<string>} options.matchFields 要匹配的字段数组，如 ['name', 'code']
 * @param {string} options.children 子节点字段名，默认 'children'
 * @returns {Array} 过滤后的树形数据
 */
export function searchTree(tree = [], keyword = '', options = {}) {
	const {
		matchFields = ['name'],  // 默认匹配 name 字段
		children = 'children'    // 默认子节点字段名为 children
	} = options

	// 如果没有关键字，返回原树
	if (!keyword) return tree

	// 转换为小写，便于不区分大小写搜索
	const loweredKeyword = keyword.toLowerCase()

	// 检查节点是否匹配关键字
	const isMatch = (node) => {
		return matchFields.some(field => {
			const value = node[field]
			return value && String(value).toLowerCase().includes(loweredKeyword)
		})
	}

	// 递归搜索树
	const filterTree = (nodes) => {
		return nodes.filter(node => {
			// 检查当前节点是否匹配
			const currentNodeMatch = isMatch(node)

			// 处理子节点
			if (node[children] && node[children].length) {
				const filteredChildren = filterTree(node[children])
				node[children] = filteredChildren
				// 如果子节点有匹配项或当前节点匹配，则保留父节点
				const hasMatchingChild = filteredChildren.length > 0
				if (hasMatchingChild || currentNodeMatch) {
					// 添加展开状态
					node.expanded = true
				}
				return hasMatchingChild || currentNodeMatch
			}

			return currentNodeMatch
		})
	}

	// 深拷贝避免修改原数据
	const clonedTree = JSON.parse(JSON.stringify(tree))
	return filterTree(clonedTree)
}

// 枚举工具类
export const skyeyeClassEnumUtil = {

	getEnumDataNameByCodeAndKeyStatic: function (enumList, idKey, val, displayNameKey) {
		const item = enumList.find(item => item[idKey] == val);
		if (item) {
			if (!isNull(item.color)) {
				return '<span class="tag-color" style="--tag-color: ' + item.color + ';">' + item[displayNameKey] + '</span>';
			} else {
				return item[displayNameKey];
			}
		}
		return '';
	},

	getEnumDataNameByCodeAndKey: (enumCode, codeKey, codeValue, nameKey) => {
		const enumList = templateStaticDataMap[enumCode]
		return skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(enumList, codeKey, codeValue, nameKey)
	}
}

// 数据字典工具类
export const skyeyeDictUtil = {

	getDictDataNameByCodeAndKey: function (dictList, idKey, val, displayNameKey) {
		const item = dictList.find(item => item[idKey] == val);
		if (item) {
			return item[displayNameKey];
		}
		return '';
	}
}

// 表单组件工具类
const dsFormComponentUtil = {
	// 获取属性的数据展示类型
	getShowType: function (attr) {
		if (!isNull(attr) && !isNull(attr.attrDefinitionCustom)) {
			if (!isNull(attr.attrDefinitionCustom.dsFormComponent)) {
				return attr.attrDefinitionCustom.dsFormComponent.showType;
			}
		}
		return null;
	}
}

const systemCommonUtil = {
	/**
	 * 获取路径的访问地址
	 *
	 * @param url
	 */
	getFilePath: function (url) {
		if (isNull(url)) {
			return "";
		}
		if (url.startsWith("../../assets/")) {
			return $config.getConfig().homePagePath + url;
		} else {
			return $config.getConfig().fileBasePath + url;
		}
	},

	// 获取图标显示脚本
	initIconShow: function (bean) {
		var str = '';
		if (bean.iconType == '1') {
			if (isNull(bean.icon)) {
				return str;
			}
			if (isNull(bean.iconBg)) {
				str += '<div class="winui-icon winui-icon-font" style="text-align: center; overflow: hidden">';
			} else {
				str += '<div class="winui-icon winui-icon-font" style="text-align: center; overflow: hidden; background-color:' + bean.iconBg + '">';
			}
			// 自定义svg图标时
			if (bean.icon.indexOf('skyeye-') >= 0) {
				if (isNull(bean.iconColor)) {
					str += '<i class="fa fa-fw skyeye-svg ' + bean.icon + '" style="filter: drop-shadow(white 80px 0); transform: translateX(-80px)"></i>';
				} else {
					str += '<i class="fa fa-fw skyeye-svg ' + bean.icon + '" style="filter: drop-shadow(' + bean.iconColor + ' 80px 0); transform: translateX(-80px)"></i>';
				}
			} else {
				if (isNull(bean.iconColor)) {
					str += '<i class="fa fa-fw skyeye-fa ' + bean.icon + '" style="color: white"></i>';
				} else {
					str += '<i class="fa fa-fw skyeye-fa ' + bean.icon + '" style="color: ' + bean.iconColor + '"></i>';
				}
			}
			str += '</div>';
		} else if (bean.iconType = '2') {
			str = '<img src="' + $config.getConfig().fileBasePath + bean.iconPic + '" class="photo-img" lay-event="iconPic">';
		}
		return str;
	}
}

const getFormItemWidth = (width) => {
	if (isNull(width)) {
		return 24
	}
	// 如果是数字类型，直接返回
	if (typeof width === 'number') {
		return width
	}
	// 如果是字符串类型的数字，转换后返回
	if (!isNaN(Number(width))) {
		return Number(width)
	}
	// 处理 layui-col-xs 格式
	if (typeof width === 'string' && width.includes('layui-col-xs')) {
		return Number(width.replace('layui-col-xs', '')) * 2
	}
	// 其他情况返回默认值
	return 24
}

const getFlowable = (pageMation) => {
	const flowable = pageMation.serviceBeanCustom.serviceBean?.flowable;
	// 判断业务对象是否开启了工作流
	if (flowable) {
		if ((isNull(pageMation.isFlowable) && pageMation.isFlowable + "" != "0") ||
			pageMation.isFlowable == 1) {
			return true;
		} else {
			return false;
		}
	}
	return false;
}

/**
 * 格式化时间
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
const formatTimeZhCn = (timestamp) => {
	const date = new Date(timestamp)
	const now = new Date()

	// 今天的消息只显示时间
	if (date.toDateString() === now.toDateString()) {
		return date.toLocaleTimeString('zh-CN', {
			hour: '2-digit',
			minute: '2-digit'
		})
	}

	// 其他显示完整日期时间
	return date.toLocaleString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit'
	})
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
const formatFileSize = (bytes) => {
	if (bytes === 0) return '0 B'

	const k = 1024
	const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
	const i = Math.floor(Math.log(bytes) / Math.log(k))

	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取随机值
 * @return {}
 */
const getRandomValueToString = () => {
	return Date.parse(new Date()) + "" + getRandom(999);
}

/**
 * 生成指定位数的随机整数
 * @param {} n
 * @return {}
 */
const getRandom = (n) => {
	return Math.floor(Math.random() * n + 1);
}

/**
 * 生成 Markdown 表格
 * @param {Array} headers 表格头部
 * @param {Array} rows 表格数据
 * @returns {string} markdown格式的表格
 */
const generateMarkdownTable = (headers, rows) => {
	if (!headers?.length || !rows?.length) return ''

	// 生成表头
	let table = '| ' + headers.join(' | ') + ' |\n'
	// 生成分隔行
	table += '| ' + headers.map(() => '---').join(' | ') + ' |\n'
	// 生成数据行
	rows.forEach(row => {
		table += '| ' + row.join(' | ') + ' |\n'
	})

	return table
}

/**
 * 下载文本文件
 * @param {string} content 文件内容
 * @param {string} filename 文件名
 */
const downloadTextFile = (content, filename) => {
	const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
	const url = window.URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = filename
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
	window.URL.revokeObjectURL(url)
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本内容
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 复制成功的回调函数
 * @param {Function} options.onError - 复制失败的回调函数
 * @param {string} options.successMsg - 复制成功时的提示消息
 * @param {string} options.errorMsg - 复制失败时的提示消息
 * @returns {Promise<boolean>} - 返回一个Promise，表示复制操作是否成功
 */
const copyToClipboard = (text, options = {}) => {
	const {
		onSuccess,
		onError,
		successMsg = '复制成功',
		errorMsg = '复制失败'
	} = options

	return new Promise((resolve, reject) => {
		// 检查是否支持navigator.clipboard API
		if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
			// 使用现代Clipboard API
			navigator.clipboard.writeText(text)
				.then(() => {
					if (onSuccess) onSuccess(successMsg)
					resolve(true)
				})
				.catch((err) => {
					console.error('Clipboard API复制失败', err)
					// 如果Clipboard API失败，尝试使用fallback方法
					tryFallbackCopy()
				})
		} else {
			// 不支持Clipboard API时，使用fallback方法
			tryFallbackCopy()
		}

		// fallback复制方法
		function tryFallbackCopy() {
			try {
				const textArea = document.createElement('textarea')
				textArea.value = text
				// 设置样式使元素不可见
				textArea.style.position = 'fixed'
				textArea.style.left = '-999999px'
				textArea.style.top = '-999999px'
				document.body.appendChild(textArea)
				textArea.focus()
				textArea.select()

				const successful = document.execCommand('copy')
				document.body.removeChild(textArea)

				if (successful) {
					if (onSuccess) onSuccess(successMsg)
					resolve(true)
				} else {
					if (onError) onError(errorMsg)
					reject(new Error('execCommand复制失败'))
				}
			} catch (err) {
				console.error('复制到剪贴板失败:', err)
				if (onError) onError(errorMsg)
				reject(err)
			}
		}
	})
}

const util = {
	verify,
	formatTime,
	formatLocation,
	dateUtils,
	formateDate,
	deteleObject,
	randomNum,
	pointInsideCircle,
	isSameDay,
	isNull,
	getNowDate,
	templateStaticDataMap,

	getServiceKey,
	getContentLinkedDataValue,

	classEnumMap,
	getEnumMapByCode,
	getEnumListByCode,
	getEnumListMapByCode,
	getEnumListMapForStandardByCode,
	getEnumList2MapByCode,
	getDictMapByCode,
	getDictListByCode,
	getDictListMapByCode,
	getKeyIdToMation,
	getKeyIdToKey,

	formatFileSize,
	formatTimeZhCn,

	getInPoingArr,

	getRules,

	calculationUtil,
	erpUtil,

	getFlowable,

	timeUtil,
	commonMenusUtil,

	filterImage,
	filterImageBasePath,

	filters,
	autoRandomCode,

	getTagColor,
	tagColors,
	listToTree,
	getLevelText,
	searchTree,

	skyeyeClassEnumUtil,
	skyeyeDictUtil,
	dsFormComponentUtil,
	systemCommonUtil,
	getFormItemWidth,
	getRandomValueToString,
	getRandom,
	generateMarkdownTable,
	downloadTextFile,

	copyToClipboard
}

// 注册全局方法
export function setupUtil(app) {
	// 添加到全局属性
	app.config.globalProperties.$util = util
	// 添加到全局对象
	window.$util = util
}

export default util
