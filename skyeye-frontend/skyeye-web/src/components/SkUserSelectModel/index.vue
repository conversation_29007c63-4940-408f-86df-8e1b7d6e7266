<template>
    <!-- 顶部选项卡 -->
    <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane v-for="tab in tabs" :key="tab.key" :tab="tab.title" />
    </a-tabs>

    <div class="modal-content">
        <!-- 左侧树 -->
        <div class="left-tree">
            <!-- 搜索框 -->
            <div class="search-box">
                <a-input v-model:value="searchText" placeholder="请输入关键字" allowClear>
                    <template #prefix>
                        <search-outlined />
                    </template>
                </a-input>
            </div>
            <!-- 树组件 -->
            <div class="tree-container">
                <a-tree v-model:selectedKeys="selectedKeys" v-model:expandedKeys="expandedKeys"
                    :tree-data="filteredTreeData" :fieldNames="fieldNames" :multiple="multiple" @select="handleSelect">
                    <template #title="nodeData">
                        <template v-if="nodeData.folderType == 'isPeople'">
                            <user-outlined /> {{ nodeData.name }}
                        </template>
                        <template v-else>
                            <folder-outlined /> {{ nodeData.name }}
                        </template>
                    </template>
                </a-tree>
            </div>
        </div>

        <!-- 右侧已选人员 -->
        <div class="right-selected">
            <div class="selected-header">
                <span>已选人员 ({{ tempSelectedUsers.length }})</span>
                <a-button type="link" @click="clearTempSelected">清空</a-button>
            </div>
            <div class="selected-list">
                <a-tag v-for="user in tempSelectedUsers" :key="user.id" closable @close="removeTempUser(user)"
                    class="user-tag" @click="locateUser(user)">
                    {{ user.name }}
                </a-tag>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import { SearchOutlined, UserOutlined, FolderOutlined } from '@ant-design/icons-vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Array, String],
        default: () => ''
    },
    multiple: {
        type: Boolean,
        default: true
    },
    chooseOrNotMy: {
        type: [Number, String],
        default: 1
    },
    chooseOrNotEmail: {
        type: [Number, String],
        default: 1
    },
    value: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 选项卡配置
const tabs = [
    { key: 'allPeople', title: '所有人员' },
    { key: 'accordingCompany', title: '按公司' },
    { key: 'accordingDepartment', title: '按部门' },
    { key: 'accordingJob', title: '按岗位' },
    { key: 'accordingSimpleDepartment', title: '同部门人员' },
    // { key: 'accordingGroup', title: '按联系组' }
]

const activeTab = ref('allPeople')
const searchText = ref('')
const selectedKeys = ref([])
const expandedKeys = ref([])
const treeData = ref([])
const tempSelectedUsers = ref([])

// 字段名映射
const fieldNames = {
    title: 'name',
    key: 'id',
    children: 'children'
}

// 监听value变化
watch(() => props.modelValue, (newVal) => {
    tempSelectedUsers.value = newVal
    selectedKeys.value = newVal.map(u => u.id)
}, { immediate: true })

// 监听selectedUsers变化
watch(() => tempSelectedUsers.value, (newVal) => {
    emit('update:modelValue', newVal)
})

// 过滤后的树数据
const filteredTreeData = computed(() => {
    if (!searchText.value) return treeData.value

    const search = (nodes) => {
        if (!nodes) return []

        return nodes.map(node => {
            // 创建节点的副本
            const newNode = { ...node }
            const matched = node.name.toLowerCase().includes(searchText.value.toLowerCase())
            if (node.children) {
                const filteredChildren = search(node.children)
                // 如果子节点有匹配或当前节点匹配，则保留该节点
                if (filteredChildren.length > 0 || matched) {
                    newNode.children = filteredChildren
                    return newNode
                }
                return null
            }
            // 如果是叶子节点且匹配，则保留
            return matched ? newNode : null
        }).filter(Boolean) // 过滤掉 null 值
    }

    return search([...treeData.value])
})

// 加载树数据
const loadTreeData = async (type) => {
    const params = {
        chooseOrNotMy: props.chooseOrNotMy,
        chooseOrNotEmail: props.chooseOrNotEmail
    }
    let res = []
    if (type == 'allPeople') {
        res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'commonselpeople001', params)
    } else if (type == 'accordingCompany') {
        res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'commonselpeople002', params)
    } else if (type == 'accordingDepartment') {
        res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'commonselpeople003', params)
    } else if (type == 'accordingJob') {
        res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'commonselpeople004', params)
    } else if (type == 'accordingSimpleDepartment') {
        res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'commonselpeople005', params)
    }

    res.rows.forEach(item => {
        if (item.folderType != 'isPeople') {
            item.disabled = true
        }
    })

    const tree = proxy.$util.listToTree(res.rows || [], {
        parentId: 'pId',
    })
    treeData.value = tree

    // 设置默认展开节点
    const getExpandedKeys = (nodes, level = 1, maxLevel = 3) => {
        let keys = []
        if (!nodes || level > maxLevel) return keys
        nodes.forEach(node => {
            if (node.children && node.children.length > 0 && level <= maxLevel) {
                keys.push(node.id)
                keys = keys.concat(getExpandedKeys(node.children, level + 1, maxLevel))
            }
        })
        return keys
    }
    expandedKeys.value = getExpandedKeys(tree)
}

// 处理选项卡切换
const handleTabChange = async (key) => {
    // 清空搜索和选中状态
    searchText.value = ''

    // 根据不同类型加载数据
    await loadTreeData(key)

    // 同步已选用户的选中状态
    selectedKeys.value = tempSelectedUsers.value.map(u => u.id)
}

// 处理选择
const handleSelect = (keys, { node }) => {
    // 判断是选中还是取消选中
    const isSelected = keys.includes(node.id)

    if (!props.multiple) {
        // 单选模式：直接替换所有已选用户
        tempSelectedUsers.value = [{
            id: node.id,
            name: node.name,
            email: node.email
        }]
    } else {
        // 多选模式
        if (isSelected) {
            // 选中：追加新选择的用户
            if (!tempSelectedUsers.value.find(u => u.id === node.id)) {
                tempSelectedUsers.value.push({
                    id: node.id,
                    name: node.name,
                    email: node.email
                })
            }
        } else {
            // 取消选中：移除用户
            tempSelectedUsers.value = tempSelectedUsers.value.filter(u => u.id !== node.id)
        }
    }
}

// 移除临时选择的用户（弹窗中）
const removeTempUser = (user) => {
    tempSelectedUsers.value = tempSelectedUsers.value.filter(u => u.id !== user.id)
    // 同步更新树的选中状态
    selectedKeys.value = tempSelectedUsers.value.map(u => u.id)
}

// 清空临时选择
const clearTempSelected = () => {
    tempSelectedUsers.value = []
    // 同步清空树的选中状态
    selectedKeys.value = []
}

// 定位用户
const locateUser = (user) => {
    // 查找用户所在的所有父节点
    const findParentPath = (nodes, targetId, path = []) => {
        for (const node of nodes) {
            if (node.id === targetId) {
                return path
            }
            if (node.children) {
                const found = findParentPath(node.children, targetId, [...path, node.id])
                if (found) return found
            }
        }
        return null
    }

    const parentPath = findParentPath(treeData.value, user.id)
    if (parentPath) {
        // 展开所有父节点
        expandedKeys.value = [...new Set([...expandedKeys.value, ...parentPath])]

        // 等待DOM更新后滚动到目标节点
        setTimeout(() => {
            const treeNodes = document.querySelectorAll('.ant-tree-node-content-wrapper')
            for (let node of treeNodes) {
                if (node.textContent.includes(user.name)) {
                    const container = document.querySelector('.tree-container')
                    if (container) {
                        node.scrollIntoView({ behavior: 'smooth', block: 'center' })
                        break
                    }
                }
            }
        }, 300)
    }
}

onMounted(() => {
    loadTreeData(activeTab.value)
})
</script>

<style scoped>
.modal-content {
    display: flex;
    gap: 16px;
    height: 400px;
}

.left-tree {
    width: 280px;
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    display: flex;
    flex-direction: column;
}

.search-box {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.tree-container {
    flex: 1;
    overflow: auto;
    padding: 12px;
}

.right-selected {
    flex: 1;
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    display: flex;
    flex-direction: column;
}

.selected-header {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selected-list {
    flex: 1;
    overflow: auto;
    padding: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-content: flex-start;
}

:deep(.ant-tree) {
    background: transparent;
}

.user-tag {
    cursor: pointer;
}

.user-tag:hover {
    background: #e6f7ff;
}
</style>