<template>
    <div class="sk-segmented-wrapper">
        <!-- 只读模式 -->
        <template v-if="isEdit === $config.formEditType.notEdit">
            <div class="sk-segmented-readonly">
                {{ getSelectedLabel }}
            </div>
        </template>

        <!-- 编辑模式 -->
        <template v-else>
            <a-segmented v-model:value="currentValue" :options="options" :disabled="disabled" :size="size"
                :block="block" @change="handleChange">
                <template v-if="$slots.default" #label="item">
                    <slot :item="item"></slot>
                </template>
            </a-segmented>
        </template>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
    // 当前选中的值
    modelValue: {
        type: [String, Number],
        default: ''
    },
    // 选项配置
    options: {
        type: Array,
        default: () => []
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 尺寸
    size: {
        type: String,
        default: 'middle',
        validator: (value) => ['large', 'middle', 'small'].includes(value)
    },
    // 是否撑满父容器
    block: {
        type: Boolean,
        default: false
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: () => $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const currentValue = ref(props.modelValue)

// 监听值变化
watch(
    () => props.modelValue,
    (val) => {
        currentValue.value = val
    }
)

watch(currentValue, (val) => {
    emit('update:modelValue', val)
})

// 获取选中项的标签文本
const getSelectedLabel = computed(() => {
    const selectedOption = props.options.find(opt => {
        if (typeof opt === 'object') {
            return opt.value === currentValue.value
        }
        return opt === currentValue.value
    })

    if (!selectedOption) return ''
    return typeof selectedOption === 'object' ? selectedOption.label : selectedOption
})

// 值变化事件
const handleChange = (value) => {
    currentValue.value = value
    emit('change', value)
}
</script>

<style scoped>
.sk-segmented-wrapper {
    display: inline-block;
    width: v-bind('block ? "100%" : "auto"');
}

.sk-segmented-readonly {
    min-height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 24px;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    transition: all 0.3s;
}

:deep(.ant-segmented) {
    width: v-bind('block ? "100%" : "auto"');
}

:deep(.ant-segmented-item) {
    transition: all 0.3s;
}

:deep(.ant-segmented-item-selected) {
    background: #1890ff;
    color: #fff;
}

:deep(.ant-segmented-thumb) {
    background-color: #1890ff;
}

:deep(.ant-segmented-item-selected:hover) {
    color: #fff;
}
</style>