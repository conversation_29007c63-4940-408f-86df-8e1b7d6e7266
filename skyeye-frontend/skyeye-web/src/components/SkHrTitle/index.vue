<template>
    <div class="sk-hr-title" :style="containerStyle">
        <div class="hr-line" :style="leftLineStyle"></div>
        <div class="title-content" :style="titleStyle">
            <slot></slot>
        </div>
        <div class="hr-line" :style="rightLineStyle"></div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 标题位置：左对齐、居中、右对齐
    position: {
        type: String,
        default: 'left',
        validator: (value) => ['left', 'center', 'right'].includes(value)
    },
    // 线条宽度
    lineWidth: {
        type: Number,
        default: 1
    },
    // 线条颜色
    lineColor: {
        type: String,
        default: '#1890ff'
    },
    // 字体大小
    fontSize: {
        type: Number,
        default: 16
    },
    // 字体颜色
    fontColor: {
        type: String,
        default: '#1890ff'
    },
    // 上下外边距
    margin: {
        type: Number,
        default: 16
    }
})

// 容器样式
const containerStyle = computed(() => ({
    marginTop: `${props.margin}px`,
    marginBottom: `${props.margin}px`
}))

// 标题样式
const titleStyle = computed(() => ({
    fontSize: `${props.fontSize}px`,
    color: props.fontColor,
    padding: '0 12px'
}))

// 左侧线条样式
const leftLineStyle = computed(() => {
    const baseStyle = {
        height: `${props.lineWidth}px`,
        backgroundColor: props.lineColor
    }

    switch (props.position) {
        case 'left':
            return {
                ...baseStyle,
                width: '32px'
            }
        case 'center':
            return {
                ...baseStyle,
                flex: 1
            }
        case 'right':
            return {
                ...baseStyle,
                flex: 1
            }
    }
})

// 右侧线条样式
const rightLineStyle = computed(() => {
    const baseStyle = {
        height: `${props.lineWidth}px`,
        backgroundColor: props.lineColor
    }

    switch (props.position) {
        case 'left':
            return {
                ...baseStyle,
                flex: 1
            }
        case 'center':
            return {
                ...baseStyle,
                flex: 1
            }
        case 'right':
            return {
                ...baseStyle,
                width: '32px'
            }
    }
})
</script>

<style scoped>
.sk-hr-title {
    display: flex;
    align-items: center;
    width: 100%;
}

.hr-line {
    transition: all 0.3s ease;
}

.title-content {
    white-space: nowrap;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 悬停效果 */
.sk-hr-title:hover .hr-line {
    background-color: #40a9ff;
}

.sk-hr-title:hover .title-content {
    color: #40a9ff;
}
</style>