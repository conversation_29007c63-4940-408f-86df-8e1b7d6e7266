<template>
    <div class="sk-button" :style="wrapperStyle">
        <a-button :type="type" :size="size" :loading="loading" :disabled="disabled" :danger="danger" :ghost="ghost"
            :block="block" :shape="shape" :href="href" :target="target" @click="handleClick">
            <template #icon>
                <slot name="icon"></slot>
            </template>
            <slot></slot>
        </a-button>
    </div>
</template>

<script>
import { defineComponent, computed } from 'vue';

export default defineComponent({
    name: 'SkButton',
    props: {
        // 按钮类型
        type: {
            type: String,
            default: 'default',
            validator: (value) => ['primary', 'ghost', 'dashed', 'link', 'text', 'default'].includes(value)
        },
        // 按钮大小
        size: {
            type: String,
            default: 'middle',
            validator: (value) => ['large', 'middle', 'small'].includes(value)
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 禁用状态
        disabled: {
            type: <PERSON>olean,
            default: false
        },
        // 危险按钮
        danger: {
            type: Boolean,
            default: false
        },
        // 幽灵按钮
        ghost: {
            type: Boolean,
            default: false
        },
        // 块级按钮
        block: {
            type: Boolean,
            default: false
        },
        // 按钮形状
        shape: {
            type: String,
            default: undefined,
            validator: (value) => ['default', 'circle', 'round'].includes(value)
        },
        // 链接地址
        href: {
            type: String,
            default: ''
        },
        // 链接打开方式
        target: {
            type: String,
            default: '_self'
        },
        // 添加新的 wrapperCol 属性
        wrapperCol: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['click'],
    setup(props, { emit }) {
        const wrapperStyle = computed(() => {
            const { span = 24, offset = 0, style = { marginLeft: 0, width: 'auto' } } = props.wrapperCol

            return {
                marginLeft: style?.marginLeft || `${(offset / 24) * 100}%`,
                width: style?.width || `${(span / 24) * 100}%`,
                ...style
            }
        })

        const handleClick = (e) => {
            emit('click', e);
        };

        return {
            handleClick,
            wrapperStyle
        };
    }
});
</script>

<style scoped>
.sk-button {
    display: inline-block;
}

/* 自定义主题色按钮样式 */
:deep(.ant-btn-primary) {
    background-color: var(--ant-primary-color, #1890ff);
    border-color: var(--ant-primary-color, #1890ff);
}

:deep(.ant-btn-primary:hover) {
    background-color: var(--ant-primary-color-hover, #40a9ff);
    border-color: var(--ant-primary-color-hover, #40a9ff);
}

:deep(.ant-btn-primary:active) {
    background-color: var(--ant-primary-color-active, #096dd9);
    border-color: var(--ant-primary-color-active, #096dd9);
}

/* 危险按钮样式 */
:deep(.ant-btn-danger) {
    background-color: var(--ant-error-color, #ff4d4f);
    border-color: var(--ant-error-color, #ff4d4f);
    color: #fff;
}

:deep(.ant-btn-danger:hover) {
    background-color: var(--ant-error-color-hover, #ff7875);
    border-color: var(--ant-error-color-hover, #ff7875);
}

:deep(.ant-btn-danger:active) {
    background-color: var(--ant-error-color-active, #d9363e);
    border-color: var(--ant-error-color-active, #d9363e);
}

/* 幽灵按钮样式 */
:deep(.ant-btn-ghost) {
    background-color: transparent;
    border-color: var(--ant-primary-color, #1890ff);
    color: var(--ant-primary-color, #1890ff);
}

:deep(.ant-btn-ghost:hover) {
    background-color: rgba(24, 144, 255, 0.1);
}

/* 块级按钮样式 */
:deep(.ant-btn-block) {
    width: 100%;
}

/* 圆形按钮样式 */
:deep(.ant-btn-circle) {
    min-width: 32px;
    padding: 0;
}

/* 圆角按钮样式 */
:deep(.ant-btn-round) {
    border-radius: 32px;
}
</style>