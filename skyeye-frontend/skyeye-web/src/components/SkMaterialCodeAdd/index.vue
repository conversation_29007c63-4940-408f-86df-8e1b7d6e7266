<template>
    <div>
        <SkForm ref="formRef" v-model="formData" @submit="handleSubmit" @reset="handleCancel">
            <!-- 基本信息标题 -->
            <SkHrTitle>基本信息</SkHrTitle>

            <!-- 商品明细表格 -->
            <a-form-item label="商品明细" required>
                <div class="table-container">
                    <SkDynamicTable ref="dynamicTableRef" v-model="tableData" :columns="dynamicColumns"
                        :isEdit="$config.formEditType.isEdit" @cell-change="handleCellChange">
                        <template #cell-select-materialId="{ record }">
                            <SkMaterialSelect v-model="record.materialId" :isEdit="$config.formEditType.isEdit"
                                @change="(event) => handleProductChange(event, record)" />
                        </template>
                        <template #cell-select-normsId="{ record }">
                            <SkSelect v-model="record.normsId" :options="record.normsOptions" placeholder="请选择规格" />
                        </template>
                    </SkDynamicTable>
                </div>
            </a-form-item>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import SkDynamicTable from '@/components/SkDynamicTable/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'

const props = defineProps({
    materialId: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['success', 'cancel'])

const { proxy } = getCurrentInstance()

// 表单数据
const formRef = ref(null)
const dynamicTableRef = ref(null)
const formData = reactive({})
const submitLoading = ref(false)

// 表格数据
const tableData = ref([])

// 动态表格列配置
const dynamicColumns = [
    {
        name: '商品',
        attrKey: 'materialId',
        showType: 'select',
        width: 200,
        require: ['required'],
        rules: [{ required: true, message: '请选择商品' }]
    },
    {
        name: '规格',
        attrKey: 'normsId',
        showType: 'select',
        width: 200,
        require: ['required'],
        rules: [{ required: true, message: '请选择规格' }]
    },
    {
        name: '条形码数量',
        attrKey: 'operNumber',
        showType: 'input',
        width: 150,
        align: 'center',
        require: ['required'],
        rules: [
            { required: true, message: '请输入数量' },
            { type: 'number', min: 1, max: 999, message: '数量必须在1-999之间' }
        ]
    }
]

// 处理单元格变化
const handleCellChange = ({ record, dataIndex, value }) => {
    if (dataIndex === 'operNumber') {
        const numValue = Number(value)
        if (!isNaN(numValue)) {
            record.operNumber = numValue
        }
    }
}

// 选择商品
const handleProductChange = (product, record) => {
    if (product) {
        const exists = tableData.value.some((item) =>
            item !== record &&
            item.materialId === product.id &&
            item.normsId === record.normsId
        )

        if (exists) {
            SkMessage.warning('该商品规格已经选择')
            record.materialId = ''
            return
        }

        record.materialId = product.id
        record.materialName = product.name
        record.normsOptions = (product.materialNorms || []).map(norm => ({
            label: norm.name,
            value: norm.id
        }))
        record.normsId = undefined
    }
}

// 初始化一行数据
const initRow = () => {
    return {
        key: Date.now() + Math.random().toString(36).slice(2),
        materialId: '',
        materialName: '',
        normsId: undefined,
        normsOptions: [],
        operNumber: 1
    }
}

// 提交表单时的验证
const handleSubmit = async () => {
    try {
        submitLoading.value = true

        if (!tableData.value.length) {
            SkMessage.warning('请添加商品明细')
            return
        }

        // 使用 SkDynamicTable 的验证方法
        const valid = await dynamicTableRef.value.validate()
        if (!valid) {
            return
        }

        // 额外验证所有行的条形码数量范围
        const hasInvalidNumber = tableData.value.some(row => {
            const num = Number(row.operNumber)
            return isNaN(num) || num < 1 || num > 999
        })
        if (hasInvalidNumber) {
            SkMessage.warning('条形码数量必须在1-999之间')
            return
        }

        const params = {
            list: JSON.stringify(tableData.value.map(row => ({
                materialId: row.materialId,
                normsId: row.normsId,
                operNumber: row.operNumber
            })))
        }

        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertMaterialNormsCode',
            params
        )

        SkMessage.success('保存成功')
        emit('success')
    } catch (error) {
        SkMessage.error('保存失败')
    } finally {
        submitLoading.value = false
    }
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 初始化
onMounted(() => {
    const initialRow = initRow()
    tableData.value = [initialRow]

    if (props.materialId) {
        proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryMaterialListById',
            { id: props.materialId }
        ).then(res => {
            const product = res.bean
            const row = tableData.value[0]
            row.materialId = product.id
            row.materialName = product.name
            row.normsOptions = (product.materialNorms || []).map(norm => ({
                label: norm.name,
                value: norm.id
            }))
        })
    }
})
</script>

<style scoped></style>
