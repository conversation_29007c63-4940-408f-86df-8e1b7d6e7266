<template>
    <div class="sk-interviewer-info">
        <!-- <SkHrTitle>面试者信息</SkHrTitle> -->
        <a-row>
            <!-- 面试者选择/展示 -->
            <a-col :span="24">
                <a-form-item>
                    <div v-if="isEdit === $config.formEditType.isEdit" class="input-with-icon">
                        <SkInput v-model="selectedName" :placeholder="placeholder" readonly>
                            <template #suffix>
                                <user-add-outlined class="select-icon" @click="handleSelect" />
                            </template>
                        </SkInput>
                    </div>
                    <div v-else>{{ intervieweeData.name }}</div>
                </a-form-item>
            </a-col>

            <!-- 联系方式 -->
            <a-col :span="12">
                <a-form-item label="联系方式">
                    <div>{{ intervieweeData.phone }}</div>
                </a-form-item>
            </a-col>

            <!-- 工作年限 -->
            <a-col :span="12">
                <a-form-item label="工作年限">
                    <div>{{ intervieweeData.workYears }}</div>
                </a-form-item>
            </a-col>

            <!-- 心仪岗位 -->
            <a-col :span="24">
                <a-form-item label="心仪岗位">
                    <div>{{ intervieweeData.favoriteJob }}</div>
                </a-form-item>
            </a-col>

            <!-- 基本简历 -->
            <a-col :span="24">
                <a-form-item label="基本简历">
                    <div v-html="formatTextArea(intervieweeData.basicResume)"></div>
                </a-form-item>
            </a-col>

            <!-- 附件资料 -->
            <a-col :span="24">
                <a-form-item label="附件资料">
                    <div v-if="intervieweeData.enclosureResume?.enclosureInfoList?.length" class="file-list">
                        <a v-for="(file, index) in intervieweeData.enclosureResume.enclosureInfoList" :key="index"
                            class="file-link" @click="downloadFile(file)">
                            {{ file.name }}
                        </a>
                    </div>
                </a-form-item>
            </a-col>
        </a-row>

        <!-- 选择面试者弹窗 -->
        <SkModal v-model="modalVisible" :title="'选择面试者'" width="80%">
            <ShowIndex ref="showIndexRef" :pageId="operatorParams.pageId" :params="operatorParams.params"
                @close="handleModalClick" @rowHandleSelect="handleSelectionChange">
            </ShowIndex>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, getCurrentInstance } from 'vue'
import { UserAddOutlined } from '@ant-design/icons-vue'
import SkInput from '@/components/SkInput/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkUpload from '@/components/SkUpload/index.vue'
import SkMessage from '@/components/SkMessage/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
const { proxy } = getCurrentInstance()
const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择面试者'
    },
    attrKey: {
        type: String,
        default: 'objectId'
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const modalVisible = ref(false)
const selectedName = ref('')
const intervieweeData = ref({})
const showIndexRef = ref(null)
const showValue = ref({})

// 添加必要的变量
const modalTitle = ref('')
const operatorParams = ref({})

// 修改 handleSelect 函数
const handleSelect = () => {
    modalTitle.value = '列表'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023061200001',
        params: {},
        whetherCustomerData: true,
        customerDataSaveFun: true
    }
}

// 处理选择
const handleSelectionChange = (record) => {
    if (!record) return
    showValue.value = record
    selectedName.value = record.name || ''
    intervieweeData.value = {
        id: record.id,
        name: record.name,
        phone: record.phone,
        workYears: record.workYears,
        favoriteJob: record.favoriteJob,
        basicResume: record.basicResume,
        enclosureResume: record.enclosureResume,
        fromId: record.fromId,
        fromMation: record.fromMation,
        serviceClassName: record.serviceClassName
    }

    emit('update:modelValue', record.id)
    emit('change', record)
    modalVisible.value = false
}

// 处理弹窗确认
const handleModalClick = (isSubmit) => {
    if (!isSubmit) {
        modalVisible.value = false
    }
}

// 下载处理
const downloadFile = async (file) => {
    try {
        if (!file || !file.fileAddress) {
            proxy.$message.error('文件信息不完整')
            return
        }

        // 如果是相对路径，添加基础路径
        let fileUrl = file.fileAddress
        if (!fileUrl.startsWith('http')) {
            fileUrl = proxy.$config.getConfig().fileBasePath + fileUrl
        }

        // 发起下载请求
        const response = await fetch(fileUrl)
        if (!response.ok) {
            throw new Error('文件下载失败')
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.setAttribute('download', file.name || `file_${Date.now()}${getFileExtension(fileUrl)}`)
        document.body.appendChild(link)
        link.click()

        // 清理
        window.URL.revokeObjectURL(downloadUrl)
        document.body.removeChild(link)
        proxy.$message.success('下载成功')
    } catch (error) {
        console.error('Download Error:', error)
        proxy.$message.error('下载失败')
    }
}

// 获取文件扩展名
const getFileExtension = (filename) => {
    const ext = filename.split('.').pop()
    return ext ? `.${ext}` : ''
}

const formatTextArea = (text) => {
    if (!text) return ''
    return text.replace(/\n/g, '<br>')
}

// 初始化数据
const initData = () => {
    if (props.pageType === proxy.$config.pageType.EDIT && props.formData) {
        const mationKey = props.attrKey.replace('Id', 'Mation')
        const mation = props.formData[mationKey]

        if (mation) {
            showValue.value = mation
            selectedName.value = mation.name || ''
            intervieweeData.value = {
                id: mation.id || '',
                name: mation.name || '',
                phone: mation.phone || '',
                workYears: mation.workYears || '',
                favoriteJob: mation.favoriteJob || '',
                basicResume: mation.basicResume || '',
                enclosureResume: mation.enclosureResume || '',
                fromId: mation.fromId || '',
                fromMation: mation.fromMation || '',
                serviceClassName: mation.serviceClassName || ''
            }
            emit('update:modelValue', mation.id)
        }
    }
}

// 监听 formData 变化，但避免深度监听
watch(() => props.formData, (newVal) => {
    if (newVal) {
        initData()
    }
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
    initData()
})
</script>

<style scoped>
.sk-interviewer-info {
    width: 100%;
}

.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.select-icon {
    cursor: pointer;
    color: #1890ff;
    font-size: 16px;
}
</style>
