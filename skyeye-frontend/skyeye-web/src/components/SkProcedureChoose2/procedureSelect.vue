<template>
    <div class="procedure-select">
        <!-- 提示信息 -->
        <div class="tip-box">
            <SkAlert :message="tipMessage" type="info" show-icon />
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <SkSpace>
                <SkButton type="primary" @click.prevent="fetchData">
                    <template #icon><reload-outlined /></template>
                    {{ $t('common.refresh') }}
                </SkButton>
                <SkButton type="primary" @click.prevent="handleSave" :loading="saveLoading">
                    <template #icon>
                        <SaveOutlined />
                    </template>
                    {{ $t('common.confirm') }}
                </SkButton>
            </SkSpace>
        </div>

        <!-- 表格 -->
        <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
            :row-selection="rowSelection" @change="handleTableChange">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'content'">
                    <div style="white-space: pre-wrap;">{{ record.content }}</div>
                </template>
            </template>
        </SkTable>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { ReloadOutlined, SaveOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'

const props = defineProps({
    multiple: {
        type: Boolean,
        default: false
    },
    selectedIds: {
        type: Array,
        default: () => []
    },
    modelValue: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['select', 'cancel'])
const { proxy } = getCurrentInstance()

// 表格数据
const tableData = ref([])
const loading = ref(false)
const saveLoading = ref(false)
const selectedRowKeys = ref([])
const selectedRows = ref([])

// 分页配置
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true
})

// 提示信息
const tipMessage = computed(() => {
    return `工序选择规则：1.${props.multiple ? '多选' : '单选，双击指定行数据即可选中'}；如没有查到要选择的工序，请检查工序信息是否满足当前规则。`
})

// 表格列配置
const columns = [
    {
        title: '工序名称',
        dataIndex: 'name',
        width: 120,
        align: 'left'
    },
    {
        title: '工序编号',
        dataIndex: 'number',
        width: 120,
        align: 'left'
    },
    {
        title: '工序内容',
        dataIndex: 'content',
        width: 200,
        align: 'left'
    }
]

// 选择配置
const rowSelection = computed(() => ({
    type: props.multiple ? 'checkbox' : 'radio',
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys, rows) => {
        selectedRowKeys.value = keys
        selectedRows.value = rows
    }
}))

// 设置选中状态
const setSelectedState = () => {
    if (props.selectedIds?.length && tableData.value?.length) {
        // 找出已选择的行
        const selected = tableData.value.filter(item =>
            props.selectedIds.includes(item.id)
        )
        if (selected.length > 0) {
            selectedRowKeys.value = selected.map(item => item.id)
            selectedRows.value = selected
        }
    }
}

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: pagination.value.current,
            limit: pagination.value.pageSize,
            keyword: ''
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'erpworkprocedure001',
            params
        )
        tableData.value = res.rows || []
        pagination.value.total = res.total || 0

        // 数据加载完成后设置选中状态
        setSelectedState()
    } catch (error) {
        SkMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 处理表格变化
const handleTableChange = (pag) => {
    pagination.value.current = pag.current
    pagination.value.pageSize = pag.pageSize
    fetchData()
}

// 处理保存(多选模式)
const handleSave = () => {
    if (!selectedRows.value.length) {
        SkMessage.warning('请选择工序')
        return
    }

    saveLoading.value = true
    try {
        // 使用 .value 而不是 ._rawValue
        emit('select', selectedRows.value)
    } finally {
        saveLoading.value = false
    }
}

// 监听 selectedIds 变化
watch(() => props.selectedIds, (newIds) => {
    if (newIds && tableData.value.length > 0) {
        setSelectedState()
    }
}, { immediate: true })

// 监听 tableData 变化
watch(() => tableData.value, () => {
    if (props.selectedIds?.length) {
        setSelectedState()
    }
}, { deep: true })

// 初始化
onMounted(() => {
    fetchData()
    initData()
})

// 处理工序选择
const handleProcedureSelect = (selectedProcedures) => {
    if (selectedProcedures?.length) {
        const record = selectedProcedures[0]

        // 检查是否已存在
        const exists = tableData.value.some(item => item.id === record.id)
        if (exists) {
            SkMessage.warning('该工序已存在')
            return
        }

        // 添加新工序，确保使用正确的字段名
        const newProcedure = {
            id: record.id,
            name: record.name,
            number: record.number,
            content: record.content,
            // 添加默认值
            price: '',
            quotaCapacity: '0',
            homeworkAbility: '0'
        }

        tableData.value.push(newProcedure)
        updateValue()
    }
    modalVisible.value = false
}

// 更新值
const updateValue = () => {
    const value = tableData.value.map((item, index) => ({
        procedureId: item.id,
        orderBy: index + 1,
        quotaCapacity: item.quotaCapacity,
        homeworkAbility: item.homeworkAbility,
        price: item.price,
        // 添加额外的字段，确保父组件能够正确显示数据
        procedureMation: {
            id: item.id,
            name: item.name,
            number: item.number,
            content: item.content
        }
    }))
    emit('update:modelValue', value)
    emit('change', value)
}

// 初始化数据
const initData = () => {
    if (props.modelValue?.length) {
        tableData.value = props.modelValue.map(item => ({
            id: item.procedureId || (item.procedureMation?.id || ''),
            name: item.procedureMation?.name || '',
            number: item.procedureMation?.number || '',
            content: item.procedureMation?.content || '',
            homeworkAbility: item.homeworkAbility || '0',
            quotaCapacity: item.quotaCapacity || '0',
            price: formatFloat(item.price) || ''
        }))
    }
}
</script>

<style scoped></style>