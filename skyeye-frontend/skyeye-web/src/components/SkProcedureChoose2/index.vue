<template>
    <div class="sk-procedure-choose">
        <div class="tip-box">
            <SkAlert message="拖拽表格行可修改顺序" type="info" show-icon />
        </div>

        <div class="table-operations" v-if="isEdit == $config.formEditType.isEdit">
            <SkSpace>
                <SkButton type="primary" @click.prevent="handleChooseProcedure">
                    <template #icon><plus-outlined /></template>
                    工序选择
                </SkButton>
            </SkSpace>
        </div>

        <SkForm ref="formRef" v-model="formData" :showButtons="false">
            <SkTable :key="tableKey" :columns="columns" :data-source="tableData" :pagination="false" :loading="loading"
                :row-key="record => record.id" :dragSort="true" @dragSortEnd="handleDragSort">
                <template #bodyCell="{ column, record }">
                    <!-- 工序编号列 - 只读 -->
                    <template v-if="column.dataIndex === 'number'">
                        <div>{{ record.number }}</div>
                    </template>

                    <!-- 工序名称列 - 只读 -->
                    <template v-if="column.dataIndex === 'name'">
                        <div>{{ record.name }}</div>
                    </template>

                    <!-- 加工单价列 - 可编辑 -->
                    <template v-if="column.dataIndex === 'price'">
                        <a-form-item
                            :rules="[{ required: true, message: '请填写加工单价' }, { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字' }]"
                            :preserve="false">
                            <SkInput v-model="record.price" placeholder="请填写加工单价"
                                @change="() => handleCellChange(record, 'price')" />
                        </a-form-item>
                    </template>

                    <!-- 定额能力列 - 可编辑 -->
                    <template v-if="column.dataIndex === 'quotaCapacity'">
                        <a-form-item :rules="[{ required: true, message: '请填写定额能力' }]" :preserve="false">
                            <SkInput v-model="record.quotaCapacity" placeholder="请填写定额能力"
                                @change="() => handleCellChange(record, 'quotaCapacity')" />
                        </a-form-item>
                    </template>

                    <!-- 作业人数列 - 可编辑 -->
                    <template v-if="column.dataIndex === 'homeworkAbility'">
                        <a-form-item
                            :rules="[{ required: true, message: '请填写作业人数' }, { pattern: /^\d+$/, message: '请输入整数' }]"
                            :preserve="false">
                            <SkInput v-model="record.homeworkAbility" placeholder="请填写作业人数"
                                @change="() => handleCellChange(record, 'homeworkAbility')" />
                        </a-form-item>
                    </template>
                </template>
            </SkTable>
        </SkForm>

        <!-- 工序选择弹窗 -->
        <SkModal v-model="modalVisible" title="工序选择" width="70%">
            <ProcedureSelect :multiple="true" :selected-ids="selectedIds" @select="handleProcedureSelect"
                @cancel="handleModalClose" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, computed, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import ProcedureSelect from './procedureSelect.vue'

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])
const { proxy } = getCurrentInstance()

// 格式化数字
const formatFloat = (value) => {
    if (!value && value !== 0) return ''
    const num = parseFloat(value)
    return isNaN(num) ? '' : num.toFixed(2)
}

// 表单引用
const formRef = ref(null)
const loading = ref(false)
const modalVisible = ref(false)
const tableKey = ref(0)

// 表单数据
const formData = ref({})

// 表格数据
const tableData = computed({
    get: () => Array.isArray(props.modelValue) ? props.modelValue.map(item => {
        const procedureMation = item.procedureMation || {}
        return {
            id: item.procedureId || procedureMation.id,
            name: procedureMation.name || '',
            number: procedureMation.number || '',
            content: procedureMation.content || '',
            homeworkAbility: item.homeworkAbility || '0',
            quotaCapacity: item.quotaCapacity || '0',
            price: formatFloat(item.price) || ''
        }
    }) : [],
    set: (val) => {
        const value = val.map((item, index) => ({
            procedureId: item.id,
            orderBy: index + 1,
            quotaCapacity: item.quotaCapacity || '0',
            homeworkAbility: item.homeworkAbility || '0',
            price: item.price || ''
        }))
        emit('update:modelValue', value)
    }
})

// 已选择的ID列表
const selectedIds = computed(() => tableData.value.map(item => item.id))

// 表格列配置
const columns = [
    {
        title: '工序编号',
        dataIndex: 'number',
        width: 120,
        align: 'left'
    },
    {
        title: '工序名称',
        dataIndex: 'name',
        width: 140,
        align: 'left'
    },
    {
        title: '加工单价',
        dataIndex: 'price',
        width: 150,
        align: 'left'
    },
    {
        title: '定额能力',
        dataIndex: 'quotaCapacity',
        width: 150,
        align: 'left'
    },
    {
        title: '作业人数',
        dataIndex: 'homeworkAbility',
        width: 150,
        align: 'left'
    }
]

// 打开工序选择弹窗
const handleChooseProcedure = () => {
    modalVisible.value = true
}

// 处理工序选择
const handleProcedureSelect = (selectedProcedures) => {
    if (selectedProcedures?.length) {
        const newTableData = [...tableData.value]

        for (const record of selectedProcedures) {
            if (!newTableData.some(item => item.id === record.id)) {
                newTableData.push({
                    id: record.id,
                    name: record.name,
                    number: record.number,
                    content: record.content || '',
                    price: '',
                    quotaCapacity: '0',
                    homeworkAbility: '0'
                })
            }
        }

        tableData.value = newTableData
    }
    modalVisible.value = false
}

// 处理单元格变化
const handleCellChange = (record, field) => {
    emit('change', tableData.value.map((item, index) => ({
        procedureId: item.id,
        orderBy: index + 1,
        quotaCapacity: item.quotaCapacity || '0',
        homeworkAbility: item.homeworkAbility || '0',
        price: item.price || ''
    })))
}

// 处理拖拽排序
const handleDragSort = ({ newIndex, oldIndex }) => {
    const newTableData = [...tableData.value]
    const item = newTableData.splice(oldIndex, 1)[0]
    newTableData.splice(newIndex, 0, item)
    tableData.value = newTableData
}

// 关闭弹窗
const handleModalClose = () => {
    modalVisible.value = false
}

// 表单验证
const validate = async () => {
    try {
        if (tableData.value.length === 0) {
            return {
                valid: false,
                message: '请选择至少一个工序'
            }
        }

        await formRef.value?.validate()

        for (const item of tableData.value) {
            if (!item.price || !item.quotaCapacity || !item.homeworkAbility) {
                return {
                    valid: false,
                    message: '请完善所有工序的信息'
                }
            }
        }

        const value = tableData.value.map((item, index) => ({
            procedureId: item.id,
            orderBy: index + 1,
            quotaCapacity: item.quotaCapacity || '0',
            homeworkAbility: item.homeworkAbility || '0',
            price: item.price || ''
        }))

        return {
            valid: true,
            data: value
        }
    } catch (error) {
        return {
            valid: false,
            message: error.message || '表单验证失败'
        }
    }
}

// 暴露方法给父组件
defineExpose({
    validate,
    formRef,
    getValue: () => {
        if (tableData.value.length === 0) {
            return [];
        }

        return tableData.value.map((item, index) => ({
            procedureId: item.id,
            orderBy: index + 1,
            quotaCapacity: item.quotaCapacity || '0',
            homeworkAbility: item.homeworkAbility || '0',
            price: item.price || ''
        }));
    }
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        formData.value = newVal
    }
}, { deep: true })

// 初始化
onMounted(() => {
    if (props.modelValue) {
        formData.value = props.modelValue
    }
})
</script>

<style lang="less" scoped>
.sk-procedure-choose {
    width: 100%;

    .tip-box {
        margin-bottom: 16px;
    }

    .table-operations {
        margin-bottom: 16px;
    }

    :deep(.ant-table-cell) {
        .ant-input {
            width: 100%;
        }

        .ant-form-item {
            margin-bottom: 0;
        }
    }
}
</style>
