<template>
    <div class="sk-page-selector">
        <SkInput v-model="inputValue" :placeholder="placeholder">
            <template #suffix>
                <a-dropdown :trigger="['click']" v-if="pageList.length">
                    <LinkOutlined />
                    <template #overlay>
                        <a-menu @click="handlePageSelect">
                            <a-menu-item v-for="page in pageList" :key="page.path">
                                <div class="page-item">
                                    <span class="page-name">{{ page.name }}</span>
                                    <span class="page-path">{{ page.path }}</span>
                                </div>
                            </a-menu-item>
                        </a-menu>
                    </template>
                </a-dropdown>
            </template>
        </SkInput>
    </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { LinkOutlined } from '@ant-design/icons-vue'
import SkInput from '../SkInput/index.vue'

export default defineComponent({
    name: 'sk-page-selector',

    components: {
        LinkOutlined,
        SkInput
    },

    props: {
        modelValue: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入页面路径或选择已有页面'
        }
    },

    emits: ['update:modelValue'],

    setup(props, { emit }) {
        const inputValue = ref(props.modelValue)

        // 模拟页面列表数据
        const pageList = ref([
            { name: '首页', path: '/pages/index/index' },
            { name: '分类页', path: '/pages/category/index' },
            { name: '商品详情', path: '/pages/goods/detail' },
            { name: '个人中心', path: '/pages/user/index' },
            { name: '订单列表', path: '/pages/order/list' }
        ])

        watch(() => props.modelValue, (val) => {
            inputValue.value = val
        })

        watch(inputValue, (val) => {
            emit('update:modelValue', val)
        })

        const handlePageSelect = ({ key }) => {
            inputValue.value = key
        }

        return {
            inputValue,
            pageList,
            handlePageSelect
        }
    }
})
</script>

<style lang="less" scoped>
.sk-page-selector {
    .page-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .page-name {
            font-weight: 500;
        }

        .page-path {
            font-size: 12px;
            color: #999;
        }
    }
}
</style>