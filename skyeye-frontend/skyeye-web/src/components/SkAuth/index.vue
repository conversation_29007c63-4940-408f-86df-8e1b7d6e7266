<template>
    <div class="sk-auth">
        <!-- 搜索框 -->
        <SkInput v-model="searchText" placeholder="请输入菜单名称" @change="handleSearch" allowClear />
        <!-- 表格树 -->
        <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :row-selection="rowSelection"
            :loading="loading" :pagination="false" :ready="tableReady" class="menu-table" />
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, computed } from 'vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkTable from '@/components/SkTable/index.vue'

const props = defineProps({
    id: {
        type: [String, Number],
        required: true
    },
    selectedKeys: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['change', 'submit', 'cancel'])
const { proxy } = getCurrentInstance()

// 表格相关数据
const tableRef = ref(null)
const tableData = ref([])
const originalTableData = ref([]) // 保存原始数据
const loading = ref(false)
const tableReady = ref(false)
const searchText = ref('')
const selectedRowKeys = ref(props.selectedKeys)

// 表格列配置
const columns = [
    {
        title: '菜单权限',
        dataIndex: 'name',
        key: 'name',
        width: '40%'
    },
    {
        title: '所属系统',
        dataIndex: 'sysName',
        key: 'sysName',
        width: '30%'
    },
    {
        title: '菜单类型',
        dataIndex: 'pageType',
        key: 'pageType',
        width: '30%'
    }
]

// 表格选择配置
const rowSelection = computed(() => ({
    selectedRowKeys: selectedRowKeys.value,
    checkStrictly: false, // 父子节点关联
    onChange: (keys, rows) => {
        selectedRowKeys.value = keys
        emit('change', keys)
    }
}))

// 获取菜单树数据
const getMenuTree = async () => {
    loading.value = true
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().fileBasePath + 'queryTenantAppBandMenuList'
        )
        if (res.rows) {
            const treeData = arrayToTree(res.rows)
            tableData.value = treeData
            originalTableData.value = treeData // 保存原始数据
            tableReady.value = true

            // 确保在数据加载完成后设置选中状态
            if (props.selectedKeys && Array.isArray(props.selectedKeys)) {
                selectedRowKeys.value = props.selectedKeys
            }
        }
    } catch (error) {
        SkMessage.error('获取菜单树失败')
    } finally {
        loading.value = false
    }
}
// 数组转树形结构
const arrayToTree = (items) => {
    const result = []
    const itemMap = {}

    for (const item of items) {
        itemMap[item.id] = {
            ...item,
            key: item.id, // 添加key属性
            children: []
        }
    }

    for (const item of items) {
        const id = item.id
        const pid = item.pId
        const treeItem = itemMap[id]

        if (pid === '0' || !itemMap[pid]) {
            result.push(treeItem)
        } else {
            if (!itemMap[pid].children) {
                itemMap[pid].children = []
            }
            itemMap[pid].children.push(treeItem)
        }
    }

    const removeEmptyChildren = (nodes) => {
        nodes.forEach(node => {
            if (node.children && node.children.length === 0) {
                delete node.children
            } else if (node.children) {
                removeEmptyChildren(node.children)
            }
        })
    }
    removeEmptyChildren(result)

    return result
}

// 处理搜索
const handleSearch = (e) => {
    // 获取搜索值
    const searchValue = searchText.value.trim().toLowerCase()

    if (!searchValue) {
        // 搜索值为空时恢复原始数据
        tableData.value = JSON.parse(JSON.stringify(originalTableData.value))
        return
    }

    // 深拷贝原始数据，避免修改原始数据
    const data = JSON.parse(JSON.stringify(originalTableData.value))

    const filterNode = (nodes) => {
        return nodes.filter(node => {
            // 检查当前节点是否匹配
            const isMatch =
                (node.name && node.name.toLowerCase().includes(searchValue)) ||
                (node.sysName && node.sysName.toLowerCase().includes(searchValue)) ||
                (node.pageType && node.pageType.toLowerCase().includes(searchValue))

            // 如果有子节点，递归处理
            if (node.children && node.children.length) {
                const filteredChildren = filterNode(node.children)
                node.children = filteredChildren
                return filteredChildren.length > 0 || isMatch
            }

            return isMatch
        })
    }

    // 过滤数据
    tableData.value = filterNode(data)
}

// 初始化
onMounted(() => {
    getMenuTree()
})

// 暴露方法给父组件
defineExpose({
    getSelectedKeys: () => selectedRowKeys.value
})
</script>

<style scoped></style>