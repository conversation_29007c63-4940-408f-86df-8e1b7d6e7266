<template>
    <div class="sk-textarea">
        <a-textarea v-if="isEdit == $config.formEditType.isEdit" :value="modelValue" :placeholder="placeholder"
            :disabled="disabled" :allowClear="allowClear" :autoSize="autoSize" :maxlength="maxLength"
            :showCount="getShowCount()" @change="handleChange" @pressEnter="handlePressEnter" />
        <div v-else class="textarea-text">{{ modelValue || '-' }}</div>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                {{ modelValue }}
            </template>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    placeholder: {
        type: String,
        default: '请输入'
    },
    disabled: {
        type: Boolean,
        default: false
    },
    allowClear: {
        type: Boolean,
        default: true
    },
    autoSize: {
        type: [Boolean, Object],
        default: () => ({ minRows: 3, maxRows: 6 })
    },
    maxLength: {
        type: [Number, String],
        default: 200
    },
    showCount: {
        type: Boolean,
        default: false
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'pressEnter'])

const handleChange = (e) => {
    const value = e.target.value
    emit('update:modelValue', value)
    emit('change', {
        value,
        attrKey: props.attrKey
    })
}

// 获取是否显示计数
const getShowCount = () => {
    if (props.maxLength) {
        return props.showCount
    }
    return false
}

const handlePressEnter = (e) => {
    emit('pressEnter', {
        value: props.modelValue,
        attrKey: props.attrKey
    })
}
</script>

<style scoped>
.sk-textarea {
    width: 100%;

    .sk-detail-readonly {
        white-space: normal;
    }
}
</style>