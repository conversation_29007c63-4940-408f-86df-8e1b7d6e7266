<template>
    <div class="sk-pagination">
        <a-pagination v-bind="paginationProps" @change="handleChange" @showSizeChange="handleShowSizeChange">
            <!-- 自定义总数显示 -->
            <template v-if="$slots.total" #total="{ total, range }">
                <slot name="total" :total="total" :range="range"></slot>
            </template>

            <!-- 自定义每页条数选择器 -->
            <template v-if="$slots.sizeChanger" #sizeChanger>
                <slot name="sizeChanger"></slot>
            </template>

            <!-- 自定义页码项 -->
            <template v-if="$slots.itemRender" #itemRender="{ type, page }">
                <slot name="itemRender" :type="type" :page="page"></slot>
            </template>

            <!-- 自定义选项文本 -->
            <template v-if="$slots.buildOptionText" #buildOptionText="props">
                <slot name="buildOptionText" :value="props.value" :current="props.current"></slot>
            </template>
        </a-pagination>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useSlots } from 'vue'

const slots = useSlots()
const props = defineProps({
    // 当前页数
    current: {
        type: Number,
        default: 1
    },
    // 默认当前页数
    defaultCurrent: {
        type: Number,
        default: 1
    },
    // 每页条数
    pageSize: {
        type: Number,
        default: 10
    },
    // 默认每页条数
    defaultPageSize: {
        type: Number,
        default: 10
    },
    // 数据总数
    total: {
        type: Number,
        default: 0
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 是否显示较少页面时的跳转
    hideOnSinglePage: {
        type: Boolean,
        default: false
    },
    // 是否可以快速跳转至某页
    showQuickJumper: {
        type: Boolean,
        default: false
    },
    // 是否显示页码选择器
    showSizeChanger: {
        type: Boolean,
        default: false
    },
    // 是否显示总数
    showTotal: {
        type: [Boolean, Function],
        default: false
    },
    // 是否显示较少页面时的省略号
    showLessItems: {
        type: Boolean,
        default: false
    },
    // 每页条数选项
    pageSizeOptions: {
        type: Array,
        default: () => ['10', '20', '50', '100']
    },
    // 分页大小
    size: {
        type: String,
        default: 'default',
        validator: (value) => ['small', 'default'].includes(value)
    },
    // 简单分页
    simple: {
        type: Boolean,
        default: false
    },
    // 自定义构建选项文本的函数
    buildOptionText: {
        type: Function,
        default: undefined
    },

    // 自定义选项配置
    optionsConfig: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:current', 'update:pageSize', 'change', 'showSizeChange'])

// 计算分页属性
const paginationProps = computed(() => {
    const defaultOptionsConfig = {
        showTitle: true,
        customTitle: undefined,
        width: undefined,
        placement: 'bottomLeft',
        dropdownStyle: {}
    };

    // 合并默认配置和用户配置
    const mergedOptionsConfig = {
        ...defaultOptionsConfig,
        ...(props.optionsConfig || {})
    };

    return {
        current: props.current,
        defaultCurrent: props.defaultCurrent,
        pageSize: props.pageSize,
        defaultPageSize: props.defaultPageSize,
        total: props.total,
        disabled: props.disabled,
        hideOnSinglePage: props.hideOnSinglePage,
        showQuickJumper: props.showQuickJumper,
        showSizeChanger: props.showSizeChanger,
        showTotal: typeof props.showTotal === 'function' ? props.showTotal : props.showTotal ? (total, range) => `共 ${total} 条` : undefined,
        showLessItems: props.showLessItems,
        pageSizeOptions: props.pageSizeOptions,
        size: props.size,
        simple: props.simple,
        buildOptionText: props.buildOptionText || (slots.buildOptionText ? (props) => {
            // 如果有插槽，返回一个函数来处理选项文本
            return slots.buildOptionText({ value: props.value, current: props.current })
        } : undefined),
        selectProps: {
            ...mergedOptionsConfig,
            dropdownMatchSelectWidth: mergedOptionsConfig.width !== undefined,
            dropdownStyle: {
                minWidth: mergedOptionsConfig.width,
                ...mergedOptionsConfig.dropdownStyle
            }
        }
    };
});

// 处理页码改变
const handleChange = (page, pageSize) => {
    emit('update:current', page)
    emit('update:pageSize', pageSize)
    emit('change', page, pageSize)
}

// 处理每页条数改变
const handleShowSizeChange = (current, size) => {
    emit('update:pageSize', size)
    emit('showSizeChange', current, size)
}
</script>

<style scoped>
.sk-pagination {
    display: flex;
    justify-content: flex-end;
    margin: 0;
}

:deep(.ant-pagination) {
    display: flex;
    align-items: center;
}

:deep(.ant-pagination-total-text) {
    margin-right: 8px;
}

:deep(.ant-pagination-options) {
    margin-left: 16px;
}

:deep(.ant-pagination-options-size-changer.ant-select) {
    margin-right: 8px;
    width: auto;
}

:deep(.ant-pagination-options-quick-jumper) {
    margin-left: 8px;
}

:deep(.ant-pagination-item) {
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.ant-pagination-item-active) {
    font-weight: 500;
    background: var(--ant-primary-color);
    border-color: var(--ant-primary-color);
}

:deep(.ant-pagination-disabled .ant-pagination-item-link) {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
}

:deep(.ant-select-dropdown) {

    /* 下拉框样式优化 */
    .ant-select-item {
        padding: 5px 12px;

        &-option-selected {
            font-weight: 600;
            background-color: var(--ant-primary-1);
        }

        &:hover {
            background-color: var(--ant-primary-1);
        }
    }
}
</style>