<template>
    <div class="sk-empty">
        <a-empty :image="image" :imageStyle="imageStyle" :description="description" :class="className">
            <template #image v-if="$slots.image">
                <slot name="image"></slot>
            </template>
            <template #description v-if="$slots.description">
                <slot name="description"></slot>
            </template>
            <template #default>
                <slot></slot>
            </template>
        </a-empty>
    </div>
</template>

<script setup>
const props = defineProps({
    // 设置显示图片，为 string 时表示自定义图片地址
    image: {
        type: String,
        default: undefined
    },
    // 图片样式
    imageStyle: {
        type: Object,
        default: () => ({})
    },
    // 自定义描述内容
    description: {
        type: String,
        default: undefined
    },
    // 自定义 className
    className: {
        type: String,
        default: ''
    }
})
</script>

<style lang="less" scoped>
.sk-empty {
    text-align: center;
}
</style>