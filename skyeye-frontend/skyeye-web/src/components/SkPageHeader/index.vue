<template>
    <div class="sk-page-header" :class="{ 'sk-page-header-ghost': ghost }">
        <!-- 返回区域 -->
        <div v-if="showBack" class="sk-page-header-back" @click="handleBack">
            <slot name="backIcon">
                <arrow-left-outlined />
            </slot>
            <div class="sk-page-header-back-text">
                <slot name="backText">{{ backText }}</slot>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="sk-page-header-content">
            <!-- 头部区域 -->
            <div class="sk-page-header-heading">
                <!-- 头部左侧 -->
                <div class="sk-page-header-heading-left">
                    <!-- 标题前的图标 -->
                    <span v-if="$slots.icon" class="sk-page-header-heading-icon">
                        <slot name="icon"></slot>
                    </span>
                    <!-- 标题 -->
                    <span class="sk-page-header-heading-title">
                        <slot name="title">{{ title }}</slot>
                    </span>
                    <!-- 子标题 -->
                    <span v-if="subTitle || $slots.subTitle" class="sk-page-header-heading-sub-title">
                        <slot name="subTitle">{{ subTitle }}</slot>
                    </span>
                    <!-- 标签 -->
                    <span v-if="$slots.tags" class="sk-page-header-heading-tags">
                        <slot name="tags"></slot>
                    </span>
                </div>
                <!-- 头部右侧 -->
                <div v-if="$slots.extra" class="sk-page-header-heading-extra">
                    <slot name="extra"></slot>
                </div>
            </div>

            <!-- 内容区域 -->
            <div v-if="$slots.content" class="sk-page-header-content-main">
                <slot name="content"></slot>
            </div>

            <!-- 底部区域 -->
            <div v-if="$slots.footer" class="sk-page-header-footer">
                <slot name="footer"></slot>
            </div>
        </div>

        <!-- 分割线 -->
        <div v-if="divider" class="sk-page-header-divider"></div>
    </div>
</template>

<script setup>
import { ArrowLeftOutlined } from '@ant-design/icons-vue'

const props = defineProps({
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 子标题
    subTitle: {
        type: String,
        default: ''
    },
    // 是否显示返回按钮
    showBack: {
        type: Boolean,
        default: true
    },
    // 返回按钮文本
    backText: {
        type: String,
        default: '返回'
    },
    // 是否显示分割线
    divider: {
        type: Boolean,
        default: true
    },
    // 背景是否透明
    ghost: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['back'])

// 处理返回事件
const handleBack = () => {
    emit('back')
}
</script>

<style scoped>
.sk-page-header {
    background-color: #fff;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.sk-page-header-ghost {
    background-color: transparent;
    border-bottom: none;
}

.sk-page-header-back {
    display: inline-flex;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.85);
}

.sk-page-header-back:hover {
    color: var(--ant-primary-color);
}

.sk-page-header-back-text {
    margin-left: 8px;
    font-size: 14px;
}

.sk-page-header-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sk-page-header-heading-left {
    display: flex;
    align-items: center;
    margin: 4px 0;
    overflow: hidden;
}

.sk-page-header-heading-icon {
    margin-right: 12px;
    font-size: 20px;
}

.sk-page-header-heading-title {
    margin-right: 12px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 20px;
    line-height: 32px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.sk-page-header-heading-sub-title {
    margin-right: 12px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.sk-page-header-heading-tags {
    margin: 0;
}

.sk-page-header-heading-extra {
    margin: 4px 0;
    white-space: nowrap;
}

.sk-page-header-content-main {
    margin-top: 16px;
}

.sk-page-header-footer {
    margin-top: 16px;
}

.sk-page-header-divider {
    height: 1px;
    margin: 16px 0 0;
    background-color: #f0f0f0;
}
</style>