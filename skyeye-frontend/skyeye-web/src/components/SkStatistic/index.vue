<template>
    <div class="sk-statistic-wrapper">
        <a-spin :spinning="loading">
            <a-statistic :title="title" :value="value" :precision="precision"
                :prefix="$slots.prefix ? undefined : prefix" :suffix="suffix" :value-style="valueStyle"
                :formatter="formatter" :decimal-separator="decimalSeparator" :group-separator="groupSeparator">
                <!-- 前缀插槽 -->
                <template #prefix>
                    <slot name="prefix"></slot>
                </template>

                <!-- 后缀插槽 -->
                <template v-if="$slots.suffix" #suffix>
                    <slot name="suffix"></slot>
                </template>

                <!-- 标题插槽 -->
                <template v-if="$slots.title" #title>
                    <slot name="title"></slot>
                </template>
            </a-statistic>
        </a-spin>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 数值
    value: {
        type: [Number, String],
        default: 0
    },
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 数值精度
    precision: {
        type: Number,
        default: undefined
    },
    // 前缀文本
    prefix: {
        type: String,
        default: ''
    },
    // 后缀文本
    suffix: {
        type: String,
        default: ''
    },
    // 数值样式
    valueStyle: {
        type: Object,
        default: () => ({})
    },
    // 是否加载中
    loading: {
        type: Boolean,
        default: false
    },
    // 自定义格式化函数
    formatter: {
        type: Function,
        default: undefined
    },
    // 小数点分隔符
    decimalSeparator: {
        type: String,
        default: '.'
    },
    // 千分位分隔符
    groupSeparator: {
        type: String,
        default: ','
    }
})
</script>

<style scoped>
.sk-statistic-wrapper {
    display: inline-block;
}

:deep(.ant-statistic) {
    width: 100%;
}

:deep(.ant-statistic-title) {
    margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
        'Noto Sans', sans-serif;
}

:deep(.ant-statistic-content-value) {
    display: inline-flex;
    align-items: center;
}

:deep(.ant-statistic-content-prefix),
:deep(.ant-statistic-content-suffix) {
    display: inline-flex;
    align-items: center;
}

:deep(.ant-spin-nested-loading) {
    width: 100%;
}
</style>