<template>
    <div class="sk-map-location" ref="mapRouteMarkRef">
        <!-- 选择框区域 -->
        <div class="mb-6">
            <SkForm :form="form" :modelValue="form.modelValue" :showButtons="false">
                <a-row :gutter="16">
                    <a-col :span="14">
                        <a-form-item label="学校" name="schoolId" class="map-required-field">
                            <SkSelect v-model="selectedSchool" placeholder="请选择学校" :options="schoolOptions"
                                @change="handleSchoolChange" :loading="loading.school" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="起始地点" name="startId" class="map-required-field">
                            <SkSelect v-model="selectedStartPoint" placeholder="请选择起始地点" :options="locationOptions"
                                @change="handleStartPointChange" :loading="loading.location" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="结束地点" name="endId" class="map-required-field">
                            <SkSelect v-model="selectedEndPoint" placeholder="请选择结束地点" :options="locationOptions"
                                @change="handleEndPointChange" :loading="loading.location" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </SkForm>
        </div>

        <!-- 地图容器 -->
        <div class="map-container" ref="mapContainer"></div>

        <!-- 路线点列表 -->
        <div class="route-points-panel">
            <div class="route-points-list">
                <template v-if="routeStopList.length > 0">
                    <div v-for="(point, index) in routeStopList" :key="index" class="route-point-item">
                        <span>第{{ index + 1 }}个点：[{{ point.longitude }}, {{ point.latitude }}]</span>
                        <SkButton type="link" danger @click.prevent="removeRoutePoint(index)">删除</SkButton>
                    </div>
                </template>
                <div v-else class="empty-text">暂无路线点，请在地图上点击添加</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, getCurrentInstance } from 'vue'
import SkSelect from '@/components/SkSelect/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import AMapLoader from '@amap/amap-jsapi-loader'

const mapRouteMarkRef = ref(null)
const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Object, String],
        default: () => ({
            schoolId: '',
            startId: '',
            endId: '',
            routeLength: 1
        })
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'route-change'])

const validate = () => {
    // 验证学校、起始地点和结束地点是否为空
    if (!selectedSchool.value) {
        SkMessage.error('请选择学校');
        return false;
    }
    if (!selectedStartPoint.value) {
        SkMessage.error('请选择起始地点');
        return false;
    }
    if (!selectedEndPoint.value) {
        SkMessage.error('请选择结束地点');
        return false;
    }
    return true;
};

// 将 validate 方法暴露给父组件
defineExpose({
    validate
});

// 加载状态
const loading = ref({
    school: false,
    location: false
})

// 选项数据
const schoolOptions = ref([])
const locationOptions = ref([])
const selectedSchool = ref('')
const selectedStartPoint = ref('')
const selectedEndPoint = ref('')

// 表单数据
const form = ref({
    modelValue: {
        schoolId: '',
        startId: '',
        endId: '',
        routeStopList: [],
        routeLength: 1,
        id: '' // 编辑时使用
    }
})

// 地图相关变量
const mapContainer = ref(null)
const map = ref(null)
const AMapClass = ref(null)
const startMarker = ref(null)
const endMarker = ref(null)
const startText = ref(null)
const endText = ref(null)
const polyline = ref(null)
const imageLayer = ref(null)

// 路线点列表
const routeStopList = ref([])

// 监听 props 变化，更新表单数据
watch(() => props.modelValue, (newValue) => {
    if (typeof newValue === 'string') {
        // 如果是字符串，说明是编辑模式下的 ID
        form.value.modelValue.id = newValue
    } else if (typeof newValue === 'object') {
        // 如果是对象，更新所有字段
        form.value.modelValue = {
            ...form.value.modelValue,
            ...newValue
        }
    }
}, { immediate: true })

// 更新表单数据
const updateFormData = (newData) => {
    // 确保 routeStopList 中的经纬度是数值类型
    if (newData.routeStopList) {
        newData.routeStopList = newData.routeStopList.map(point => ({
            longitude: Number(point.longitude),
            latitude: Number(point.latitude)
        }))
    }

    // 更新表单数据
    form.value.modelValue = {
        ...form.value.modelValue,
        ...newData
    }

    // 构建要发送的数据
    const formData = {
        ...form.value.modelValue,
        routeLength: 1
    }

    // 如果是编辑模式，保留 id
    if (props.isEdit === proxy.$config.formEditType.isEdit && form.value.modelValue.id) {
        formData.id = form.value.modelValue.id
    }

    // 确保 routeStopList 为空时是空数组，有值时转换为字符串
    formData.routeStopList = formData.routeStopList && formData.routeStopList.length > 0 
        ? JSON.stringify(formData.routeStopList) 
        : []
    emit('update:modelValue', formData)
}

// 修改获取学校列表的方法
const getSchoolList = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().schoolBasePath + 'queryAllSchoolList'
        )
        schoolOptions.value = res.rows.map(item => ({
            label: item.schoolName || item.name,
            value: item.schoolId || item.id,
            longitude: item.longitude || item.lng,
            latitude: item.latitude || item.lat
        }))
    } catch (error) {
        SkMessage.error('获取学校列表失败')
        schoolOptions.value = []
    }
}

// 获取地点列表
const getLocationList = async (schoolId) => {
    try {
        loading.value.location = true
        const res = await proxy.$http.get(
            proxy.$config.getConfig().schoolBasePath + 'queryTeachBuildingBySchoolId',
            { schoolId }
        )
        locationOptions.value = res.rows.map(item => ({
            label: item.name,
            value: item.id,
            longitude: item.longitude,
            latitude: item.latitude,
            ...item // 保留其他可能需要的字段
        }))
    } catch (error) {
        SkMessage.error('获取地点列表失败')
        locationOptions.value = []
    } finally {
        loading.value.location = false
    }
}

// 学校选择变化处理
const handleSchoolChange = async (value) => {
    const school = schoolOptions.value.find(item => item.value === value)
    if (school) {
        try {
            // 确保经纬度是数值类型
            const longitude = Number(school.longitude)
            const latitude = Number(school.latitude)

            // 检查经纬度是否有效
            if (isNaN(longitude) || isNaN(latitude)) {
                SkMessage.warning('学校坐标信息无效')
                return
            }

            // 更新地图中心和缩放级别
            map.value.setCenter([longitude, latitude])
            map.value.setZoom(16)

            // 获取地点列表
            await getLocationList(value)

            // 重置起终点选择
            selectedStartPoint.value = ''
            selectedEndPoint.value = ''
            startMarker.value.setPosition(null)
            startText.value.setPosition(null)
            endMarker.value.setPosition(null)
            endText.value.setPosition(null)
            routeStopList.value = []

            // 更新表单数据
            updateFormData({
                schoolId: value,
                startId: '',
                endId: '',
                routeStopList: []
            })
        } catch (error) {
            SkMessage.error('设置学校位置失败')
        }
    }
}

// 起点选择变化处理
const handleStartPointChange = (value) => {
    const location = locationOptions.value.find(item => item.value === value)
    if (location && location.longitude && location.latitude) {
        try {
            const position = new AMapClass.value.LngLat(
                Number(location.longitude),
                Number(location.latitude)
            )
            startMarker.value.setPosition(position)
            startText.value.setPosition(position)
            updateFormData({ startId: value })
        } catch (error) {
            SkMessage.error('设置起点位置失败')
        }
    } else {
        SkMessage.warning('选择的地点没有有效的位置信息')
    }
}

// 终点选择变化处理
const handleEndPointChange = (value) => {
    const location = locationOptions.value.find(item => item.value === value)
    if (location && location.longitude && location.latitude) {
        try {
            const position = new AMapClass.value.LngLat(
                Number(location.longitude),
                Number(location.latitude)
            )
            endMarker.value.setPosition(position)
            endText.value.setPosition(position)
            updateFormData({ endId: value })
        } catch (error) {
            SkMessage.error('设置终点位置失败')
        }
    } else {
        SkMessage.warning('选择的地点没有有效的位置信息')
    }
}

// 监听路线点变化
watch(routeStopList, (newValue) => {
    updateFormData({ routeStopList: newValue })
    updatePolyline()
}, { deep: true })

// 初始化地图
const initMap = async () => {
    try {
        // 设置安全密钥
        window._AMapSecurityConfig = {
            securityJsCode: proxy.$config.getConfig().securityJsCode
        }

        // 加载高德地图
        const AMap = await AMapLoader.load({
            key: proxy.$config.getConfig().skyeyeMapKey,
            version: '2.0',
            cookieEnabled: false,
            policy: 1,
            plugins: [
                'AMap.Geocoder',
                'AMap.PlaceSearch',
                'AMap.AutoComplete',
                'AMap.Scale',
                'AMap.ToolBar',
                'AMap.Bounds'
            ]
        })

        AMapClass.value = AMap

        // 创建地图实例
        map.value = new AMap.Map(mapContainer.value, {
            zoom: 13,
            center: [109.21508, 23.757652],
            viewMode: '2D',
            mapStyle: 'amap://styles/normal',
            pitch: 0,
            rotateEnable: false,
            protocol: 'https',
            showBuildingBlock: false,
            features: ['bg', 'road', 'point']
        })

        // 添加控件
        map.value.addControl(new AMap.Scale())
        map.value.addControl(new AMap.ToolBar())

        // 创建起点标记
        startMarker.value = new AMap.Marker({
            map: map.value,
            bubble: true
        })
        startText.value = new AMap.Text({
            text: "起始地点",
            offset: new AMap.Pixel(0, -20),
            map: map.value
        })

        // 创建终点标记
        endMarker.value = new AMap.Marker({
            map: map.value,
            bubble: true
        })
        endText.value = new AMap.Text({
            text: "结束地点",
            offset: new AMap.Pixel(0, -20),
            map: map.value
        })

        // 创建路线
        polyline.value = new AMap.Polyline({
            map: map.value,
            strokeColor: "#3366FF",
            strokeWeight: 5,
            strokeOpacity: 1,
            strokeStyle: "solid",
            strokeDasharray: [10, 5],
            lineJoin: "round"
        })

        // 绑定地图点击事件
        map.value.on('click', handleMapClick)

        // 如果是编辑模式且有学校数据，设置背景图
        if (props.formData?.schoolMation) {
            const schoolMation = props.formData.schoolMation
            if (schoolMation.neLatitude && schoolMation.neLongitude &&
                schoolMation.swLatitude && schoolMation.swLongitude && schoolMation.background) {
                updateImageLayer(schoolMation)
            }
        }

    } catch (error) {
        SkMessage.error('地图加载失败')
    }
}

// 处理地图点击
const handleMapClick = (e) => {
    const { lng, lat } = e.lnglat
    routeStopList.value.push({
        longitude: Number(lng),
        latitude: Number(lat)
    })
}

// 移除路线点
const removeRoutePoint = (index) => {
    routeStopList.value.splice(index, 1)
}

// 更新路线
const updatePolyline = () => {
    if (!polyline.value) return
    const path = routeStopList.value.map(point => [point.longitude, point.latitude])
    polyline.value.setPath(path)
}

// 更新图片图层
const updateImageLayer = (schoolMation) => {
    if (!map.value || !schoolMation.neLatitude || !schoolMation.neLongitude ||
        !schoolMation.swLatitude || !schoolMation.swLongitude || !schoolMation.background) {
        return
    }

    if (imageLayer.value) {
        map.value.removeLayer(imageLayer.value)
    }

    imageLayer.value = new AMapClass.value.ImageLayer({
        url: proxy.$config.getConfig().fileBasePath + schoolMation.background,
        bounds: new AMapClass.value.Bounds(
            [schoolMation.neLongitude, schoolMation.neLatitude],
            [schoolMation.swLongitude, schoolMation.swLatitude]
        )
    })

    imageLayer.value.setMap(map.value)
}

// 组件挂载时初始化
onMounted(async () => {
    try {
        // 获取学校列表
        await getSchoolList()

        // 初始化地图
        await initMap()

        // 如果是编辑模式且有表单数据，进行数据回显
        if (props.isEdit === proxy.$config.formEditType.isEdit && props.formData) {
            // 设置学校
            if (props.formData.schoolId) {
                selectedSchool.value = props.formData.schoolId
                await handleSchoolChange(props.formData.schoolId)

                // 设置起点
                if (props.formData.startId) {
                    selectedStartPoint.value = props.formData.startId
                    handleStartPointChange(props.formData.startId)
                }

                // 设置终点
                if (props.formData.endId) {
                    selectedEndPoint.value = props.formData.endId
                    handleEndPointChange(props.formData.endId)
                }

                // 设置路线点
                if (props.formData.routeStopList && props.formData.routeStopList.length > 0) {
                    routeStopList.value = props.formData.routeStopList.map(point => ({
                        longitude: Number(point.longitude),
                        latitude: Number(point.latitude)
                    }))
                    updatePolyline()
                }

                // 设置 ID
                if (props.formData.id) {
                    form.value.modelValue.id = props.formData.id
                }
            }
        }
    } catch (error) {
        SkMessage.error('初始化失败')
    }
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
    destroyMap()
})

// 清理地图实例
const destroyMap = () => {
    if (map.value) {
        map.value.off('click', handleMapClick)
        if (imageLayer.value) {
            map.value.removeLayer(imageLayer.value)
        }
        map.value.destroy()
        map.value = null
    }
}
</script>

<style scoped>

/* 添加必填标志的样式 */
.map-required-field :deep(.ant-form-item-label > label::before) {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
</style>