<template>
    <div class="icon-select">
        <template v-if="isEdit === $config.formEditType.isEdit">
            <!-- 图标类型选择 -->
            <a-form-item>
                <SkRadio v-model="iconType" :options="iconTypeOptions" @change="handleTypeChange" />
            </a-form-item>

            <!-- 图标选择 -->
            <template v-if="iconType == 1">
                <a-row>
                    <a-col :span="12">
                        <a-form-item label="图标">
                            <SkInput v-model="icon" placeholder="请选择图标" readonly>
                                <template #suffix>
                                    <plus-outlined class="add-icon" @click="showModal" />
                                </template>
                            </SkInput>
                        </a-form-item>
                    </a-col>

                    <a-col :span="12">
                        <a-form-item label="图标颜色">
                            <SkColorPicker v-model="iconColor" @change="handleColorChange" />
                        </a-form-item>
                    </a-col>

                    <a-col :span="12">
                        <a-form-item label="背景颜色">
                            <SkColorPicker v-model="iconBg" @change="handleBgChange" />
                        </a-form-item>
                    </a-col>

                    <a-col :span="12">
                        <!-- 图标预览 -->
                        <a-form-item label="预览">
                            <div class="icon-preview" v-html="previewHtml"></div>
                        </a-form-item>
                    </a-col>
                </a-row>
            </template>

            <!-- 图片上传 -->
            <template v-else>
                <a-form-item label="图片">
                    <SkUpload v-model="iconPic" :maxCount="1" :accept="$config.$fileType.imageType.join(',')" :type="12"
                        @change="handlePicChange">
                        <template #tip>
                            <div class="upload-tip">建议上传正方形图片，大小不超过2MB</div>
                        </template>
                    </SkUpload>
                </a-form-item>
            </template>
        </template>

        <template v-else>
            <template v-if="iconType == 1">
                <div class="icon-preview" v-html="previewHtml"></div>
            </template>
            <template v-else>
                <img :src="$config.getConfig().fileBasePath + iconPic" alt="图标" class="icon-pic photo-img">
            </template>
        </template>

        <!-- 图标选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择图标" width="70%" :bodyStyle="{ height: '80vh' }">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <!-- 搜索表单 -->
                    <div class="table-search">
                        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                            :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                            <template #submitIcon>
                                <search-outlined />
                            </template>
                            <a-form-item name="keyword">
                                <SkInput v-model="searchForm.keyword" placeholder="请输入图标属性" allowClear />
                            </a-form-item>
                        </SkForm>
                    </div>

                    <!-- 表格区域 -->
                    <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                        :ready="tableReady" :pagination="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'choose'">
                                <a-button type="link" size="small" @click.stop="handleSelect(record)">
                                    选择
                                </a-button>
                            </template>
                            <!-- 图标预览列 -->
                            <template v-if="column.key === 'preview'">
                                <div class="icon-preview">
                                    <i :class="['fa', record.iconClass]"></i>
                                </div>
                            </template>
                        </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance, nextTick, reactive } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkRadio from '@/components/SkRadio/index.vue'
import SkColorPicker from '@/components/SkColorPicker/index.vue'
import SkUpload from '@/components/SkUpload/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({
            iconType: '1',
            icon: '',
            iconColor: '',
            iconBg: '',
            iconPic: ''
        })
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 图标类型选项
const iconTypeOptions = [
    { label: '图标', value: '1' },
    { label: '图片', value: '2' }
]

// 图标相关数据
const iconType = ref(String(props.modelValue?.iconType || '1'))
const icon = ref(props.modelValue?.icon || '')
const iconColor = ref(props.modelValue?.iconColor || '')
const iconBg = ref(props.modelValue?.iconBg || '')
const iconPic = ref(props.modelValue?.iconPic || '')

// 预览HTML
const previewHtml = computed(() => {
    if (!icon.value) return ''
    return proxy.$util.systemCommonUtil.initIconShow({
        iconType: iconType.value,
        icon: icon.value,
        iconColor: iconColor.value,
        iconBg: iconBg.value
    })
})

// 类型变化
const handleTypeChange = () => {
    updateValue()
}

// 图标变化
const handleSelect = (record) => {
    icon.value = record.iconClass
    updateValue()
    modalVisible.value = false
}

// 颜色变化
const handleColorChange = () => {
    updateValue()
}

// 背景色变化
const handleBgChange = () => {
    updateValue()
}

// 图片变化
const handlePicChange = () => {
    updateValue()
}

// 更新值
const updateValue = () => {
    const value = {
        iconType: iconType.value,
        icon: icon.value,
        iconColor: iconColor.value,
        iconBg: iconBg.value,
        iconPic: iconPic.value
    }
    emit('update:modelValue', value)
    emit('change', value)
}

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)

const modalVisible = ref(false)

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 表格列配置
const columns = [{
    title: '选择',
    dataIndex: 'choose',
    width: 80,
    align: 'center',
    fixed: 'left'
}, {
    title: t('common.serialNum'),
    dataIndex: 'index',
    width: 80,
    align: 'center',
    fixed: 'left',
    customRender: ({ index }) => {
        // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
        return (pagination.current - 1) * pagination.pageSize + index + 1
    }
},
{
    title: '图标预览',
    dataIndex: 'preview',
    width: 100,
    align: 'center'
},
{
    title: '图标属性',
    dataIndex: 'iconClass',
    width: 80,
    align: 'left'
}]
// 搜索表单数据
const searchForm = reactive({
    keyword: '',
})

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }
        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'querySysIconList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}
// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    iconType.value = String(newVal.iconType || '1')
    icon.value = newVal.icon
    iconColor.value = newVal.iconColor
    iconBg.value = newVal.iconBg
    iconPic.value = newVal.iconPic
}, { deep: true })

onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    if (props.isEdit === $config.formEditType.isEdit) {
        await fetchData()
        updateValue()
    }
})
</script>

<style lang="less" scoped>
.icon-select {

    .icon-preview {
        :deep(.winui-icon) {
            width: 32px;
            height: 32px;
            border-radius: 4px;

            i {
                line-height: 32px;
                font-size: 16px;
            }
        }
    }

    .upload-tip {
        font-size: 12px;
        color: #999;
        margin-top: 4px;
    }
}
</style>