<template>
    <div class="sk-json-editor">
        <div class="editor-header">
            <a-radio-group v-model:value="mode" size="small">
                <a-radio-button value="text">文本模式</a-radio-button>
                <a-radio-button value="tree">树形模式</a-radio-button>
            </a-radio-group>
        </div>
        <div class="editor-content">
            <template v-if="mode === 'text'">
                <SkTextarea v-model="jsonText" :rows="4" @blur="handleTextBlur" :placeholder="'请输入合法的 JSON 格式数据'" />
            </template>
            <template v-else>
                <div class="tree-editor">
                    <div v-for="(value, key) in jsonData" :key="key" class="tree-item">
                        <div class="item-key">
                            <SkInput v-model="treeKeys[key]" @change="handleKeyChange(key)" />
                        </div>
                        <div class="item-value">
                            <SkInput v-if="typeof value !== 'object'" v-model="jsonData[key]"
                                @change="handleValueChange" />
                            <SkButton v-else @click="toggleExpand(key)">
                                {{ isExpanded[key] ? '收起' : '展开' }}
                            </SkButton>
                        </div>
                        <div v-if="typeof value === 'object' && isExpanded[key]" class="sub-items">
                            <div v-for="(subValue, subKey) in value" :key="subKey" class="tree-item">
                                <div class="item-key">
                                    <SkInput v-model="treeKeys[`${key}.${subKey}`]"
                                        @change="handleSubKeyChange(key, subKey)" />
                                </div>
                                <div class="item-value">
                                    <SkInput v-model="jsonData[key][subKey]" @change="handleValueChange" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <SkButton type="dashed" block @click="addProperty">添加属性</SkButton>
                </div>
            </template>
        </div>
        <div class="editor-error" v-if="error">
            {{ error }}
        </div>
    </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import SkTextarea from '@/components/SkTextarea/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkButton from '@/components/SkButton/index.vue'

export default defineComponent({
    name: 'sk-json-editor',

    components: {
        SkTextarea,
        SkInput,
        SkButton
    },

    props: {
        modelValue: {
            type: Object,
            default: () => ({})
        }
    },

    emits: ['update:modelValue'],

    setup(props, { emit }) {
        const mode = ref('text')
        const jsonData = ref({ ...props.modelValue })
        const jsonText = ref(JSON.stringify(props.modelValue, null, 2))
        const error = ref('')
        const treeKeys = ref({})
        const isExpanded = ref({})

        // 初始化树形编辑器的键名
        const initTreeKeys = () => {
            const keys = {}
            Object.keys(jsonData.value).forEach(key => {
                keys[key] = key
                if (typeof jsonData.value[key] === 'object') {
                    Object.keys(jsonData.value[key]).forEach(subKey => {
                        keys[`${key}.${subKey}`] = subKey
                    })
                }
            })
            treeKeys.value = keys
        }

        watch(() => props.modelValue, (val) => {
            jsonData.value = { ...val }
            jsonText.value = JSON.stringify(val, null, 2)
            initTreeKeys()
        })

        const handleTextBlur = () => {
            try {
                const parsed = JSON.parse(jsonText.value)
                jsonData.value = parsed
                emit('update:modelValue', parsed)
                error.value = ''
                initTreeKeys()
            } catch (e) {
                error.value = 'JSON 格式错误'
            }
        }

        const handleValueChange = () => {
            jsonText.value = JSON.stringify(jsonData.value, null, 2)
            emit('update:modelValue', jsonData.value)
        }

        const handleKeyChange = (oldKey) => {
            const newKey = treeKeys.value[oldKey]
            if (newKey && newKey !== oldKey) {
                const value = jsonData.value[oldKey]
                delete jsonData.value[oldKey]
                jsonData.value[newKey] = value
                handleValueChange()
            }
        }

        const handleSubKeyChange = (parentKey, oldKey) => {
            const fullKey = `${parentKey}.${oldKey}`
            const newKey = treeKeys.value[fullKey]
            if (newKey && newKey !== oldKey) {
                const value = jsonData.value[parentKey][oldKey]
                delete jsonData.value[parentKey][oldKey]
                jsonData.value[parentKey][newKey] = value
                handleValueChange()
            }
        }

        const toggleExpand = (key) => {
            isExpanded.value[key] = !isExpanded.value[key]
        }

        const addProperty = () => {
            const newKey = `property${Object.keys(jsonData.value).length + 1}`
            jsonData.value[newKey] = ''
            treeKeys.value[newKey] = newKey
            handleValueChange()
        }

        // 初始化
        initTreeKeys()

        return {
            mode,
            jsonData,
            jsonText,
            error,
            treeKeys,
            isExpanded,
            handleTextBlur,
            handleValueChange,
            handleKeyChange,
            handleSubKeyChange,
            toggleExpand,
            addProperty
        }
    }
})
</script>

<style lang="less" scoped>
.sk-json-editor {
    .editor-header {
        margin-bottom: 8px;
    }

    .editor-content {
        border: 1px solid #d9d9d9;
        border-radius: 2px;
        padding: 8px;

        .tree-editor {
            .tree-item {
                display: flex;
                gap: 8px;
                margin-bottom: 8px;

                .item-key {
                    width: 200px;
                }

                .item-value {
                    flex: 1;
                }

                .sub-items {
                    margin-left: 24px;
                    width: 100%;
                    margin-top: 8px;
                }
            }
        }
    }

    .editor-error {
        color: #ff4d4f;
        font-size: 12px;
        margin-top: 4px;
    }
}
</style>