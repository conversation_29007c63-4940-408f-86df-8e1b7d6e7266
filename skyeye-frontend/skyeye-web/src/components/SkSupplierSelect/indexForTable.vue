<template>
    <div class="sk-supplier-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else>
            {{ showValue.name }}
        </div>

        <!-- 供应商选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择供应商" width="80%" :bodyStyle="{ height: '80vh' }">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <div class="table-btn-group">
                        <SkAuthBtnGroup authPointCode="1569132969654" @change="handleChange" />
                    </div>
                    <!-- 搜索区域 -->
                    <div class="table-search">
                        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                            :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                            <template #submitIcon>
                                <search-outlined />
                            </template>
                            <a-form-item name="keyword">
                                <SkInput v-model="searchForm.keyword" placeholder="请输入名称" allowClear />
                            </a-form-item>
                        </SkForm>
                    </div>

                    <!-- 表格区域 -->
                    <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                        :ready="tableReady" :pagination="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'choose'">
                                <a-button type="link" size="small" @click.stop="handleSelect(record)">
                                    选择
                                </a-button>
                            </template>
                            <template v-if="column.dataIndex === 'enabled'">
                                <div
                                    v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['commonEnable'], 'id', record.enabled, 'name')">
                                </div>
                            </template>
                        </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkAuthBtnGroup from '@/components/SkAuthBtnGroup/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择供应商'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})

const tableReady = ref(false)

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '供应商名称',
        dataIndex: 'name',
        width: 150
    },
    {
        title: '地址',
        dataIndex: 'address',
        width: 200
    },
    {
        title: '状态',
        dataIndex: 'enabled',
        width: 100
    }
]

// 权限点处理
const authMation = ref({})
const handleChange = (key, value) => {
    authMation.value[key] = value
    pagination.current = 1
    getSupplierList()
}

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['commonEnable']);
    initEnumData.value = result
}

// 获取供应商列表
const getSupplierList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            ...searchForm,
            ...authMation.value
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'supplier001',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取供应商列表失败')
    } finally {
        loading.value = false
    }
}

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        // 确保弹窗已经渲染完成
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getSupplierList()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getSupplierList()
}

// 处理选择
const handleSelect = (record) => {
    showValue.value = record
    if (props.attrKey == 'holderId') {
        emit('update:modelValue', record.id);
    } else {
        emit('update:modelValue', record.id);
    }
    emit('change', record)
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    // 详情页面和编辑页面都会走这里
    const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
    const mation = props.formData[mationKey]
    showValue.value = mation || {}
    getInitData()
})
</script>

<style scoped>
.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}
</style>