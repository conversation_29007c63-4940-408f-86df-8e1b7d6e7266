<template>
    <div class="sk-drawer-wrapper">
        <a-drawer v-model:open="visible" :title="title" :placement="placement" :width="width" :height="height"
            :mask="mask" :maskClosable="maskClosable" :closable="closable" :keyboard="keyboard"
            :destroyOnClose="destroyOnClose" :zIndex="zIndex" :class="drawerClass" :rootClassName="rootClassName"
            :rootStyle="rootStyle" :contentWrapperStyle="contentWrapperStyle" :headerStyle="headerStyle"
            :bodyStyle="bodyStyle" :footerStyle="footerStyle" :extra="extra" @close="handleClose"
            @afterOpenChange="handleAfterOpenChange">
            <!-- 自定义标题 -->
            <template #title v-if="$slots.title">
                <slot name="title"></slot>
            </template>

            <!-- 自定义额外操作 -->
            <template #extra v-if="$slots.extra">
                <slot name="extra"></slot>
            </template>

            <!-- 主体内容 -->
            <slot></slot>

            <!-- 底部内容 -->
            <template #footer v-if="$slots.footer">
                <slot name="footer"></slot>
            </template>
        </a-drawer>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    // 是否显示抽屉
    modelValue: {
        type: Boolean,
        default: false
    },
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 抽屉方向
    placement: {
        type: String,
        default: 'right',
        validator: (value) => ['top', 'right', 'bottom', 'left'].includes(value)
    },
    // 宽度(当placement为right/left)
    width: {
        type: [String, Number],
        default: 378
    },
    // 高度(当placement为top/bottom)
    height: {
        type: [String, Number],
        default: 378
    },
    // 是否显示遮罩
    mask: {
        type: Boolean,
        default: true
    },
    // 点击遮罩是否关闭
    maskClosable: {
        type: Boolean,
        default: true
    },
    // 是否显示关闭按钮
    closable: {
        type: Boolean,
        default: true
    },
    // 是否支持键盘ESC关闭
    keyboard: {
        type: Boolean,
        default: true
    },
    // 关闭时是否销毁子元素
    destroyOnClose: {
        type: Boolean,
        default: false
    },
    // 设置层级
    zIndex: {
        type: Number,
        default: 1000
    },
    // 抽屉类名
    drawerClass: {
        type: String,
        default: ''
    },
    // 可用于设置 Drawer 最外层容器的类名
    rootClassName: {
        type: String,
        default: ''
    },
    // 可用于设置 Drawer 最外层容器的样式
    rootStyle: {
        type: Object,
        default: () => ({})
    },
    // 可用于设置 Drawer 包裹内容部分的样式
    contentWrapperStyle: {
        type: Object,
        default: () => ({})
    },
    // 抽屉头部样式
    headerStyle: {
        type: Object,
        default: () => ({})
    },
    // 抽屉内容部分样式
    bodyStyle: {
        type: Object,
        default: () => ({})
    },
    // 抽屉底部样式
    footerStyle: {
        type: Object,
        default: () => ({})
    },
    // 额外操作区域
    extra: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'close', 'afterOpenChange'])

const visible = ref(props.modelValue)

// 监听值变化
watch(
    () => props.modelValue,
    (val) => {
        visible.value = val
    }
)

watch(visible, (val) => {
    emit('update:modelValue', val)
})

// 关闭事件
const handleClose = (e) => {
    visible.value = false
    emit('close', e)
}

// 打开/关闭动画结束后的回调
const handleAfterOpenChange = (open) => {
    emit('afterOpenChange', open)
}
</script>

<style scoped></style>