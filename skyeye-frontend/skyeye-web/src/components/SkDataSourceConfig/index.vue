<template>
    <div class="sk-data-source-config">
        <SkCollapse v-model="customActive" :panels="customPanels">
            <template #dataSource>
                <a-row :gutter="16">
                    <!-- 数据来源类型选择 -->
                    <a-col :span="24">
                        <a-form-item>
                            <template #label>
                                <span>{{ config.type.label }}</span>
                                <a-tooltip v-if="config.type.help">
                                    <template #title>
                                        <div class="help-content">
                                            <div class="help-title">{{ config.type.help.title }}</div>
                                            <div class="help-text"
                                                v-html="config.type.help.content.replace(/\n/g, '<br/>')">
                                            </div>
                                        </div>
                                    </template>
                                    <component :is="config.type.help.icon" class="help-icon" />
                                </a-tooltip>
                            </template>
                            <SkSelect v-model="modelValue.type" :options="config.type.options" />
                        </a-form-item>
                    </a-col>

                    <!-- 静态数据配置 -->
                    <template v-if="modelValue.type === 'static'">
                        <a-col :span="24">
                            <a-form-item :label="config.data.label">
                                <SkButton @click="addDataItem">添加图片</SkButton>
                                <div v-for="(item, index) in modelValue.data" :key="index" class="data-item">
                                    <a-row :gutter="16">
                                        <a-col v-for="(itemProp, itemKey) in config.data.config.item.props"
                                            :key="itemKey" :span="itemProp.type === 'image' ? 8 : 12">
                                            <a-form-item :label="itemProp.label">
                                                <!-- 图片上传 -->
                                                <template v-if="itemProp.type === 'image'">
                                                    <SkUpload v-model:value="item[itemKey]" :accept="'image/*'" />
                                                </template>
                                                <!-- 页面选择器 -->
                                                <template v-else-if="itemKey === 'url'">
                                                    <SkPageSelector v-model:value="item[itemKey]" />
                                                </template>
                                                <!-- 普通输入框 -->
                                                <template v-else>
                                                    <SkInput v-model:value="item[itemKey]"
                                                        :placeholder="itemProp.placeholder" />
                                                </template>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <div class="data-item-actions">
                                        <SkButton size="small" @click="removeDataItem(index)">删除</SkButton>
                                    </div>
                                </div>
                            </a-form-item>
                        </a-col>
                    </template>

                    <!-- 接口数据配置 -->
                    <template v-else>
                        <!-- API配置 -->
                        <template v-if="modelValue.type === 'api'">
                            <a-col :span="24">
                                <a-form-item label="选择服务">
                                    <SkSelect v-model="modelValue.serviceId" :options="serviceOptions"
                                        @change="handleServiceChange" />
                                </a-form-item>
                            </a-col>

                            <a-col :span="24" v-if="selectedService?.isCustom">
                                <a-form-item label="服务地址">
                                    <SkInput v-model="modelValue.customBaseUrl"
                                        placeholder="请输入服务地址，例如：http://api.example.com" />
                                </a-form-item>
                            </a-col>

                            <a-col :span="24">
                                <a-form-item :label="config.url.label">
                                    <SkApiSelector v-model="modelValue.url" :placeholder="config.url.placeholder"
                                        :baseUrl="currentBaseUrl" />
                                </a-form-item>
                            </a-col>

                            <a-col :span="24">
                                <a-form-item :label="config.method.label">
                                    <SkSelect v-model="modelValue.method" :options="config.method.options" />
                                </a-form-item>
                            </a-col>

                            <a-col :span="24">
                                <a-form-item :label="config.headers.label">
                                    <SkJsonEditor v-model="modelValue.headers" />
                                </a-form-item>
                            </a-col>

                            <a-col :span="24">
                                <a-form-item :label="config.params.label">
                                    <SkJsonEditor v-model="modelValue.params" />
                                </a-form-item>
                            </a-col>
                        </template>

                        <!-- XML/JSON文件上传 -->
                        <template v-else-if="['xml', 'json'].includes(modelValue.type)">
                            <a-col :span="24">
                                <a-form-item>
                                    <template #label>
                                        <span>上传文件</span>
                                        <a-tooltip v-if="config.fileUpload?.help">
                                            <template #title>
                                                <div class="help-content">
                                                    <div class="help-title">{{ config.fileUpload.help.title }}</div>
                                                    <div class="help-text"
                                                        v-html="config.fileUpload.help.content.replace(/\n/g, '<br/>')">
                                                    </div>
                                                </div>
                                            </template>
                                            <component :is="config.fileUpload.help.icon" class="help-icon" />
                                        </a-tooltip>
                                    </template>
                                    <SkUpload v-model="modelValue.fileUrl"
                                        :accept="modelValue.type === 'xml' ? '.xml' : '.json'"
                                        :placeholder="`请上传${modelValue.type.toUpperCase()}文件`" :multiple="false" />
                                </a-form-item>
                            </a-col>
                        </template>

                        <!-- 通用配置项 -->
                        <a-col :span="24">
                            <a-form-item>
                                <template #label>
                                    <span>{{ config.dataPath.label }}</span>
                                    <a-tooltip v-if="config.dataPath.help">
                                        <template #title>
                                            <div class="help-content">
                                                <div class="help-title">{{ config.dataPath.help.title }}</div>
                                                <div class="help-text"
                                                    v-html="config.dataPath.help.content.replace(/\n/g, '<br/>')">
                                                </div>
                                            </div>
                                        </template>
                                        <component :is="config.dataPath.help.icon" class="help-icon" />
                                    </a-tooltip>
                                </template>
                                <SkPathSelector v-model="modelValue.dataPath"
                                    :placeholder="config.dataPath.placeholder" />
                            </a-form-item>
                        </a-col>

                        <a-col :span="24">
                            <a-form-item>
                                <template #label>
                                    <span>{{ config.transform.label }}</span>
                                    <a-tooltip v-if="config.transform.help">
                                        <template #title>
                                            <div class="help-content">
                                                <div class="help-title">{{ config.transform.help.title }}</div>
                                                <div class="help-text"
                                                    v-html="config.transform.help.content.replace(/\n/g, '<br/>')">
                                                </div>
                                            </div>
                                        </template>
                                        <component :is="config.transform.help.icon" class="help-icon" />
                                    </a-tooltip>
                                </template>
                                <SkCodeEditor v-model="modelValue.transform" :language="config.transform.language"
                                    :placeholder="config.transform.placeholder" />
                            </a-form-item>
                        </a-col>

                        <a-col :span="24">
                            <a-form-item>
                                <template #label>
                                    <span>{{ config.fieldMapping.label }}</span>
                                    <a-tooltip v-if="config.fieldMapping.help">
                                        <template #title>
                                            <div class="help-content">
                                                <div class="help-title">{{ config.fieldMapping.help.title }}</div>
                                                <div class="help-text"
                                                    v-html="config.fieldMapping.help.content.replace(/\n/g, '<br/>')">
                                                </div>
                                            </div>
                                        </template>
                                        <component :is="config.fieldMapping.help.icon" class="help-icon" />
                                    </a-tooltip>
                                </template>
                                <SkFieldMapper v-model="modelValue.fieldMapping" :config="config.fieldMapping.config" />
                            </a-form-item>
                        </a-col>
                    </template>
                </a-row>
            </template>
        </SkCollapse>
    </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { serviceList, getServiceConfig } from '@/uni-components/util/designTool'
import SkCollapse from '../SkCollapse/index.vue'
import SkButton from '../SkButton/index.vue'
import SkInput from '../SkInput/index.vue'
import SkSelect from '../SkSelect/index.vue'
import SkUpload from '../SkUpload/index.vue'
import SkPageSelector from '../SkPageSelector/index.vue'
import SkApiSelector from '../SkApiSelector/index.vue'
import SkJsonEditor from '../SkJsonEditor/index.vue'
import SkPathSelector from '../SkPathSelector/index.vue'
import SkCodeEditor from '../SkCodeEditor/index.vue'
import SkFieldMapper from '../SkFieldMapper/index.vue'

export default defineComponent({
    name: 'sk-data-source-config',

    components: {
        SkCollapse,
        SkButton,
        SkInput,
        SkSelect,
        SkUpload,
        SkPageSelector,
        SkApiSelector,
        SkJsonEditor,
        SkPathSelector,
        SkCodeEditor,
        SkFieldMapper
    },

    props: {
        modelValue: {
            type: Object,
            required: true
        },
        config: {
            type: Object,
            required: true
        }
    },

    emits: ['update:modelValue'],

    data() {
        return {
            customActive: "dataSource",
            customPanels: [
                {
                    key: 'dataSource',
                    header: '数据来源'
                }
            ]
        }
    },

    setup(props, { emit }) {
        // 转换服务列表为选择器选项格式
        const serviceOptions = computed(() => {
            return serviceList.map(service => ({
                label: service.name,
                value: service.id,
                description: service.description
            }))
        })

        // 获取当前选中的服务配置
        const selectedService = computed(() => {
            return getServiceConfig(props.modelValue.serviceId)
        })

        // 获取当前使用的baseUrl
        const currentBaseUrl = computed(() => {
            if (selectedService.value?.isCustom) {
                return props.modelValue.customBaseUrl
            }
            return selectedService.value?.baseUrl || ''
        })

        // 处理服务变更
        const handleServiceChange = (serviceId) => {
            const service = getServiceConfig(serviceId)
            emit('update:modelValue', {
                ...props.modelValue,
                serviceId,
                // 如果不是自定义服务，清空自定义baseUrl
                customBaseUrl: service.isCustom ? props.modelValue.customBaseUrl : ''
            })
        }

        // 添加数据项
        const addDataItem = () => {
            const defaultItem = {
                image: '',
                url: '',
                title: ''
            }
            const newData = [...props.modelValue.data, defaultItem]
            emit('update:modelValue', {
                ...props.modelValue,
                data: newData
            })
        }

        // 删除数据项
        const removeDataItem = (index) => {
            const newData = [...props.modelValue.data]
            newData.splice(index, 1)
            emit('update:modelValue', {
                ...props.modelValue,
                data: newData
            })
        }

        return {
            serviceOptions,
            selectedService,
            currentBaseUrl,
            handleServiceChange,
            addDataItem,
            removeDataItem
        }
    }
})
</script>

<style lang="less" scoped>
.sk-data-source-config {
    .data-item {
        margin-bottom: 16px;
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        background: #fafafa;

        &:hover {
            background: #f5f5f5;
        }

        .data-item-actions {
            margin-top: 8px;
            text-align: right;
        }
    }

    :deep(.ant-form-item) {
        margin-bottom: 16px;
    }

    :deep(.ant-form-item-label) {
        min-width: 80px;
    }

    .help-icon {
        margin-left: 4px;
        color: #999;
        cursor: help;

        &:hover {
            color: #666;
        }
    }
}

:deep(.help-content) {
    max-width: 400px;

    .help-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .help-text {
        font-size: 12px;
        white-space: pre-wrap;
        line-height: 1.5;
    }
}
</style>