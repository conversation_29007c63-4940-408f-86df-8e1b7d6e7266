<template>
    <div class="sk-tabs">
        <a-tabs v-model:activeKey="activeKey" :type="type" :tabPosition="tabPosition" :size="size"
            @change="handleChange" @edit="handleEdit">
            <a-tab-pane v-for="tab in tabs" :key="tab.key" :tab="tab.label">
                <slot :name="tab.key" :tab="tab">
                    {{ tab.content }}
                </slot>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    },
    tabs: {
        type: Array,
        default: () => []
    },
    type: {
        type: String,
        default: 'line'  // line, card, editable-card
    },
    tabPosition: {
        type: String,
        default: 'top'  // top, right, bottom, left
    },
    size: {
        type: String,
        default: 'default'  // large, default, small
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'edit'])

const activeKey = ref(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    activeKey.value = newVal
})

// 监听内部值变化
watch(activeKey, (newVal) => {
    emit('update:modelValue', newVal)
})

const handleChange = (key) => {
    emit('change', key)
}

const handleEdit = (targetKey, action) => {
    emit('edit', { targetKey, action })
}
</script>

<style scoped>
.sk-tabs {
    width: 100%;
}

:deep(.ant-tabs-left) {
    height: 100%;
}

:deep(.ant-tabs-left > .ant-tabs-nav) {
    width: 150px;
    min-height: 200px;
}

:deep(.ant-tabs-left > .ant-tabs-content-holder) {
    border-left: 1px solid #f0f0f0;
    padding-left: 16px;
    min-height: 200px;
}

:deep(.ant-tabs-tab) {
    padding: 8px 16px !important;
}
</style>