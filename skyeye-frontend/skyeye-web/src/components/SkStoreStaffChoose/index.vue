<template>
    <div class="sk-staffChoose-select">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 添加提示语 -->
            <SkAlert message="店员选择规则：1.单选，双击指定行数据即可选中。如没有查到要选择的店员，请检查店员信息是否满足当前规则。" show-icon />

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格区域 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'choose'">
                        <SkButton type="link" size="small" @click.prevent="handleSelect(record)">选择</SkButton>
                    </template>
                </template>
            </SkTable>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed, onMounted } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    storeId: {
        type: [String, Number],
        required: true
    }
})

const emit = defineEmits(['select', 'cancel'])

const { proxy } = getCurrentInstance()

// 表格数据
const tableData = ref([])
const loading = ref(false)
const selectedRowKeys = ref([])
const selectedRows = ref([])
const tableReady = ref([])
// 分页配置
const pagination = reactive({
    ...proxy.$config.pagination(),
    pageSize: 8,
    pageSizeOptions: [8, 16, 24, 32, 40, 48, 56]
})

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: '序号',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '工号',
        dataIndex: 'jobNumber',
        width: 140,
        align: 'left',
        customRender: ({ record }) => record.staffMation?.jobNumber
    },
    {
        title: '姓名',
        dataIndex: 'userName',
        width: 120,
        customRender: ({ record }) => record.staffMation?.userName
    },
    {
        title: '企业',
        dataIndex: 'companyName',
        width: 150,
        customRender: ({ record }) => record.staffMation?.companyName
    },
    {
        title: '部门',
        dataIndex: 'departmentName',
        width: 140,
        customRender: ({ record }) => record.staffMation?.departmentName
    },
    {
        title: '职位',
        dataIndex: 'jobName',
        width: 140,
        customRender: ({ record }) => record.staffMation?.jobName
    }
]

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 8),
            objectId: props.storeId
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().shopBasePath + 'storeStaff001',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 表格变化处理
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

// 选择处理
const handleSelect = (record) => {
    if (!record) {
        SkMessage.warning('请选择店员')
        return
    }

    if (!record.staffMation?.userName) {
        SkMessage.warning('店员信息不完整')
        return
    }

    selectedRowKeys.value = [record.id]
    selectedRows.value = [record]

    emit('select', record)
}

// 暴露方法给父组件
defineExpose({
    selectedRows,
    selectedRowKeys,
    fetchData
})
onMounted(async () => {
    await fetchData()
})
</script>

<style scoped>
.sk-staffChoose-select {
    padding: 16px;
}
</style>
