<template>
    <div class="sk-modal-wrapper">
        <a-modal v-model:open="visible" :title="title" :width="width" :centered="centered" :closable="closable"
            :mask="mask" :maskClosable="maskClosable" :keyboard="keyboard" :destroyOnClose="destroyOnClose"
            :class="modalClass" :wrapClassName="wrapClassName" :bodyStyle="bodyStyle" :maskStyle="maskStyle"
            :okText="showOk ? okText : null" :cancelText="showCancel ? cancelText : null" :confirmLoading="loading"
            :footer="showOk || showCancel ? undefined : null" @ok="handleOk" @cancel="handleCancel">
            <!-- 自定义标题 -->
            <template #title v-if="$slots.title">
                <slot name="title"></slot>
            </template>

            <!-- 主体内容 -->
            <slot></slot>

            <!-- 自定义页脚 -->
            <template #footer v-if="$slots.footer">
                <slot name="footer"></slot>
            </template>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    // 对话框是否可见
    modelValue: {
        type: Boolean,
        default: false
    },
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 宽度
    width: {
        type: [Number, String],
        default: "70%"
    },
    // 是否居中显示
    centered: {
        type: Boolean,
        default: true
    },
    // 是否显示右上角的关闭按钮
    closable: {
        type: Boolean,
        default: true
    },
    // 是否显示遮罩
    mask: {
        type: Boolean,
        default: true
    },
    // 点击遮罩是否关闭
    maskClosable: {
        type: Boolean,
        default: true
    },
    // 是否支持键盘 esc 关闭
    keyboard: {
        type: Boolean,
        default: true
    },
    // 关闭时销毁子元素
    destroyOnClose: {
        type: Boolean,
        default: true
    },
    // 确定按钮文字
    okText: {
        type: String,
        default: '确定'
    },
    // 取消按钮文字
    cancelText: {
        type: String,
        default: '取消'
    },
    // 确定按钮 loading
    loading: {
        type: Boolean,
        default: false
    },
    // 对话框类名
    modalClass: {
        type: String,
        default: 'default-modal'
    },
    // 对话框外层容器类名
    wrapClassName: {
        type: String,
        default: ''
    },
    // 对话框内容样式
    bodyStyle: {
        type: Object,
        default: () => ({})
    },
    // 遮罩样式
    maskStyle: {
        type: Object,
        default: () => ({})
    },
    // 是否显示确定按钮
    showOk: {
        type: Boolean,
        default: false
    },
    // 是否显示取消按钮
    showCancel: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue', 'ok', 'cancel'])

const visible = ref(props.modelValue)

// 监听值变化
watch(
    () => props.modelValue,
    (val) => {
        visible.value = val
    }
)

watch(visible, (val) => {
    emit('update:modelValue', val)
})

// 确定事件
const handleOk = (e) => {
    emit('ok', e)
}

// 取消事件
const handleCancel = (e) => {
    visible.value = false
    emit('cancel', e)
}
</script>

<style>
.full-modal {
    max-width: 100%;
}

.full-modal .ant-modal {
    max-width: 100%;
    height: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
}

.full-modal .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
    max-height: calc(100vh) !important;
}

.full-modal .ant-modal-body {
    flex: 1;
}

.default-modal .ant-modal-content {
    display: flex;
    flex-direction: column;
    max-height: calc(80vh);
}

.default-modal .ant-modal-body {
    height: 100%;
    overflow-y: auto;
}
</style>