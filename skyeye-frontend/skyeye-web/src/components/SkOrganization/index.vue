<template>
    <div class="sk-organization">
        <div><span class="required-asterisk">*</span>组织部门：</div>
        <!-- 详情模式下的显示 -->
        <div v-if="isEdit === $config.formEditType.notEdit" class="detail-view">
            <a-row>
                <a-col :span="6">
                    <div class="detail-item">
                        <span class="detail-label">公司：</span>
                        <span class="detail-value">{{ getCompanyName() }}</span>
                    </div>
                </a-col>
                <a-col :span="6">
                    <div class="detail-item">
                        <span class="detail-label">部门：</span>
                        <span class="detail-value">{{ getDepartmentName() }}</span>
                    </div>
                </a-col>
                <a-col :span="6">
                    <div class="detail-item">
                        <span class="detail-label">职位：</span>
                        <span class="detail-value">{{ getJobName() }}</span>
                    </div>
                </a-col>
                <a-col :span="6">
                    <div class="detail-item">
                        <span class="detail-label">职级：</span>
                        <span class="detail-value">{{ getJobScoreName() }}</span>
                    </div>
                </a-col>
            </a-row>
        </div>

        <!-- 编辑模式下的树形选择 -->
        <SkFlex v-else>
            <!-- 第一列：公司选择 -->
            <div class="tree-content">
                <a-tree v-model:selectedKeys="selectedCompanyKeys" :tree-data="companyData" :field-names="fieldNames"
                    @select="handleCompanySelect" :show-line="true" :show-icon="true">
                    <template #icon>
                        <folder-outlined />
                    </template>
                    <template #title="{ data }">
                        <span>{{ data.title }}</span>
                    </template>
                </a-tree>
            </div>

            <!-- 第二列：部门选择 -->
            <div class="tree-content">
                <a-tree v-if="selectedCompanyKeys.length && departmentData.length"
                    v-model:selectedKeys="selectedDeptKeys" :tree-data="departmentData" :field-names="fieldNames"
                    @select="handleDeptSelect" :show-line="true" :show-icon="true">
                    <template #icon>
                        <apartment-outlined />
                    </template>
                    <template #title="{ data }">
                        <span>{{ data.title }}</span>
                    </template>
                </a-tree>
                <div v-else-if="selectedCompanyKeys.length && !departmentData.length" class="empty-text">
                    <a-empty description="暂无部门数据" />
                </div>
                <div v-else class="empty-text">
                    <span>请先选择公司</span>
                </div>
            </div>

            <!-- 第三列：职位选择 -->
            <div class="tree-content">
                <a-tree v-if="selectedDeptKeys.length && jobData.length" v-model:selectedKeys="selectedJobKeys"
                    :tree-data="jobData" :field-names="fieldNames" @select="handleJobSelect" :show-line="true"
                    :show-icon="true">
                    <template #icon>
                        <user-outlined />
                    </template>
                    <template #title="{ data }">
                        <span>{{ data.title }}</span>
                    </template>
                </a-tree>
                <div v-else-if="selectedDeptKeys.length && !jobData.length" class="empty-text">
                    <a-empty description="暂无职位数据" />
                </div>
                <div v-else class="empty-text">
                    <span>请先选择部门</span>
                </div>
            </div>

            <!-- 第四列：职级选择 -->
            <div class="tree-content">
                <a-tree v-if="selectedJobKeys.length && jobScoreData.length" v-model:selectedKeys="selectedJobScoreKeys"
                    :tree-data="jobScoreData" :field-names="fieldNames" :show-line="true" :show-icon="true">
                    <template #icon>
                        <trophy-outlined />
                    </template>
                    <template #title="{ data }">
                        <span>{{ data.title }}</span>
                    </template>
                </a-tree>
                <div v-else-if="selectedJobKeys.length && !jobScoreData.length" class="empty-text">
                    <a-empty description="暂无职级数据" />
                </div>
                <div v-else class="empty-text">
                    <span>请先选择职位</span>
                </div>
            </div>
        </SkFlex>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watch } from 'vue'
import { FolderOutlined, ApartmentOutlined, UserOutlined, TrophyOutlined } from '@ant-design/icons-vue'
import SkFlex from '@/components/SkFlex/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { debounce } from 'lodash-es'

const { proxy } = getCurrentInstance()
const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 加载状态
const loading = ref(false)

// 各级数据
const companyData = ref([])
const departmentData = ref([])
const jobData = ref([])
const jobScoreData = ref([])

// 选中的键值
const selectedCompanyKeys = ref([])
const selectedDeptKeys = ref([])
const selectedJobKeys = ref([])
const selectedJobScoreKeys = ref([])

// 字段名称映射
const fieldNames = {
    title: 'title',
    key: 'id',
    children: 'children'
}

// 获取公司数据
const getCompanyData = async () => {
    try {
        loading.value = true;

        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'queryCompanyMationListTree'
        );

        if (res.rows?.length) {
            companyData.value = res.rows.map(item => ({
                ...item,
                id: item.id,
                title: item.name || item.title,
                isLeaf: !(item.children && item.children.length > 0)
            }));

            // 只在编辑模式下且没有选中值时默认选择第一个公司
            if (props.isEdit === proxy.$config.formEditType.isEdit && !selectedCompanyKeys.value.length) {
                selectedCompanyKeys.value = [res.rows[0].id];
            }
        } else {
            companyData.value = [];
        }
    } catch (error) {
        SkMessage.error('获取公司数据失败');
        companyData.value = [];
    } finally {
        loading.value = false;
    }
}

// 获取部门数据
const getDepartmentData = async (companyId) => {
    try {
        if (!companyId) {
            return;
        }

        loading.value = true;

        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'companydepartment006', {
            companyId: companyId.toString()
        });

        if (res.rows?.length) {
            departmentData.value = res.rows.map(item => ({
                ...item,
                id: item.id,
                title: item.name || item.title,
                isLeaf: !(item.children && item.children.length > 0)
            }));
        } else {
            departmentData.value = [];
        }
    } catch (error) {
        SkMessage.error('获取部门数据失败');
        departmentData.value = [];
    } finally {
        loading.value = false;
    }
}

// 获取职位数据
const getJobData = async (departmentId) => {
    try {
        if (!departmentId) {
            return;
        }

        loading.value = true;

        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'companyjob006', {
            departmentId: departmentId.toString()
        });

        if (res.rows?.length) {
            jobData.value = res.rows.map(item => ({
                ...item,
                id: item.id,
                title: item.name || item.title,
                isLeaf: !(item.children && item.children.length > 0)
            }));
        } else {
            jobData.value = [];
        }
    } catch (error) {
        SkMessage.error('获取职位数据失败');
        jobData.value = [];
    } finally {
        loading.value = false;
    }
}

// 获取职级数据
const getJobScoreData = async (jobId) => {
    try {
        if (!jobId) {
            return;
        }

        loading.value = true;

        const res = await proxy.$http.get(
            proxy.$config.getConfig().reqBasePath + 'companyjobscore008', {
            jobId: jobId.toString()
        });

        if (res && res.rows?.length) {
            // 处理职级数据
            jobScoreData.value = res.rows.map(item => ({
                ...item,
                id: item.id,
                title: item.name || item.title,
                isLeaf: true
            }));

            // 职级选择逻辑 - 不在这里自动选择，而是让调用方决定如何选择
        } else {
            jobScoreData.value = [];
        }
    } catch (error) {
        SkMessage.error('获取职级数据失败');
        jobScoreData.value = [];
    } finally {
        loading.value = false;
    }
}

// 处理公司选择（添加防抖）
const handleCompanySelect = debounce(async (keys, info) => {
    if (keys.length > 0) {
        const selectedNode = info.selectedNodes[0];
        // 清空其他选择
        selectedDeptKeys.value = [];
        selectedJobKeys.value = [];
        selectedJobScoreKeys.value = [];
        departmentData.value = [];
        jobData.value = [];
        jobScoreData.value = [];

        // 获取部门数据
        await getDepartmentData(selectedNode.id);
        emitValue();
    }
}, 300);

// 处理部门选择（添加防抖）
const handleDeptSelect = debounce(async (keys, info) => {
    if (keys.length > 0) {
        const selectedNode = info.selectedNodes[0];
        // 清空职位和职级选择
        selectedJobKeys.value = [];
        selectedJobScoreKeys.value = [];
        jobData.value = [];
        jobScoreData.value = [];
        // 获取职位数据
        await getJobData(selectedNode.id);
        emitValue();
    }
}, 300);

// 处理职位选择（添加防抖）
const handleJobSelect = debounce(async (keys, info) => {
    if (keys.length > 0) {
        const selectedNode = info.selectedNodes[0];

        // 保存原有的jobScoreId，用于之后的比较
        const oldJobScoreId = selectedJobScoreKeys.value[0];

        // 清空职级选择
        selectedJobScoreKeys.value = [];
        jobScoreData.value = [];

        // 获取职级数据
        await getJobScoreData(selectedNode.id);

        // 职级选择逻辑：
        // 1. 如果之前有选中的职级，并且该职级在新的职级列表中存在，则保持原有选择
        // 2. 如果是编辑模式，且formData中有jobScoreId值，尝试选择该值
        // 3. 如果以上都不满足，才选择第一个职级
        let jobScoreFound = false;

        // 检查原职级ID是否在新的职级列表中
        if (oldJobScoreId && jobScoreData.value.some(item => item.id === oldJobScoreId)) {
            selectedJobScoreKeys.value = [oldJobScoreId];
            jobScoreFound = true;
        }
        // 检查formData中的jobScoreId
        else if (props.pageType === proxy.$config.pageType.EDIT) {
            const formDataJobScoreId = !props.attrKey || props.attrKey === 'companyId'
                ? props.formData.jobScoreId
                : props.formData[`${props.attrKey}JobScoreId`];

            if (formDataJobScoreId && jobScoreData.value.some(item => item.id === formDataJobScoreId)) {
                selectedJobScoreKeys.value = [formDataJobScoreId];
                jobScoreFound = true;
            }
        }

        // 如果上述两种情况都没找到匹配的职级，且有职级数据，选择第一个
        if (!jobScoreFound && jobScoreData.value.length > 0) {
            selectedJobScoreKeys.value = [jobScoreData.value[0].id];
        }

        // 发送更新的值
        emitValue();
    }
}, 300);

// 发送选择值
const emitValue = () => {
    const attrKey = props.attrKey.replace('CompanyId', '')
    let result = {}

    // 确保有选中的公司ID
    const currentCompanyId = selectedCompanyKeys.value[0] || props.formData?.companyId || ''

    if (!attrKey || attrKey === 'companyId') {
        result = {
            companyId: selectedCompanyKeys.value[0],
            departmentId: selectedDeptKeys.value[0],
            jobId: selectedJobKeys.value[0],
            jobScoreId: selectedJobScoreKeys.value[0],
            currentCompanyId: currentCompanyId
        }
    } else {
        result = {
            [`${attrKey}CompanyId`]: selectedCompanyKeys.value[0],
            [`${attrKey}DepartmentId`]: selectedDeptKeys.value[0],
            [`${attrKey}JobId`]: selectedJobKeys.value[0],
            [`${attrKey}JobScoreId`]: selectedJobScoreKeys.value[0],
            currentCompanyId: currentCompanyId
        }
    }

    // 确保所有必需的字段都有值
    if (!result.currentCompanyId) {
        result.currentCompanyId = companyData.value[0]?.id || ''
    }

    emit('update:modelValue', result)
    emit('change', result)
}

// 添加对 formData 的监听，以便在数据变化时更新显示
watch(() => props.formData, async (newVal, oldVal) => {
    try {
        // 避免重复更新
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
            return;
        }

        if (!newVal) {
            return;
        }

        // 获取当前值
        const attrKey = props.attrKey.replace('CompanyId', '');
        const currentCompanyId = !attrKey || attrKey === 'companyId'
            ? newVal.companyId
            : newVal[`${attrKey}CompanyId`];

        // 如果当前选中的公司ID与新值相同，则不需要更新
        if (selectedCompanyKeys.value[0] === currentCompanyId) {
            return;
        }

        if (props.pageType === proxy.$config.pageType.EDIT || props.isEdit === proxy.$config.formEditType.notEdit) {
            // 获取各个层级的ID
            let companyId, departmentId, jobId, jobScoreId;

            if (!attrKey || attrKey === 'companyId') {
                companyId = newVal.companyId;
                departmentId = newVal.departmentId;
                jobId = newVal.jobId;
                jobScoreId = newVal.jobScoreId;
            } else {
                companyId = newVal[`${attrKey}CompanyId`];
                departmentId = newVal[`${attrKey}DepartmentId`];
                jobId = newVal[`${attrKey}JobId`];
                jobScoreId = newVal[`${attrKey}JobScoreId`];
            }

            // 只在值真正改变时才更新
            if (companyId && companyId !== selectedCompanyKeys.value[0]) {
                selectedCompanyKeys.value = [companyId];
                await getDepartmentData(companyId);

                if (departmentId) {
                    selectedDeptKeys.value = [departmentId];
                    await getJobData(departmentId);

                    if (jobId) {
                        selectedJobKeys.value = [jobId];
                        await getJobScoreData(jobId);

                        if (jobScoreId) {
                            selectedJobScoreKeys.value = [jobScoreId];
                        }
                    }
                }
            }
        }
    } catch (error) {
    }
}, { deep: true });

// 获取选中的公司名称
const getCompanyName = () => {
    if (selectedCompanyKeys.value.length > 0) {
        const companyId = selectedCompanyKeys.value[0];
        const company = companyData.value.find(item => item.id === companyId);
        return company ? (company.name || company.title) : '未选择';
    }
    return '未选择';
};

// 获取选中的部门名称
const getDepartmentName = () => {
    if (selectedDeptKeys.value.length > 0) {
        const deptId = selectedDeptKeys.value[0];
        const dept = departmentData.value.find(item => item.id === deptId);
        return dept ? (dept.name || dept.title) : '未选择';
    }
    return '未选择';
};

// 获取选中的职位名称
const getJobName = () => {
    if (selectedJobKeys.value.length > 0) {
        const jobId = selectedJobKeys.value[0];
        const job = jobData.value.find(item => item.id === jobId);
        return job ? (job.name || job.title) : '未选择';
    }
    return '未选择';
};

// 获取选中的职级名称
const getJobScoreName = () => {
    if (selectedJobScoreKeys.value.length > 0) {
        const jobScoreId = selectedJobScoreKeys.value[0];
        const jobScore = jobScoreData.value.find(item => item.id === jobScoreId);
        return jobScore ? (jobScore.name || jobScore.title) : '未选择';
    }
    return '未选择';
};

// 初始化数据方法，供外部调用
const initData = async () => {
    try {

        // 获取回显需要的ID
        const attrKey = props.attrKey.replace('CompanyId', '');
        let companyId, departmentId, jobId, jobScoreId;

        if (!attrKey || attrKey === 'companyId') {
            companyId = props.formData.companyId;
            departmentId = props.formData.departmentId;
            jobId = props.formData.jobId;
            jobScoreId = props.formData.jobScoreId;
        } else {
            companyId = props.formData[`${attrKey}CompanyId`];
            departmentId = props.formData[`${attrKey}DepartmentId`];
            jobId = props.formData[`${attrKey}JobId`];
            jobScoreId = props.formData[`${attrKey}JobScoreId`];
        }

        // 先加载公司数据
        await getCompanyData();

        // 清空现有数据
        selectedCompanyKeys.value = [];
        selectedDeptKeys.value = [];
        selectedJobKeys.value = [];
        selectedJobScoreKeys.value = [];

        // 如果有回显数据，设置选中状态并加载级联数据
        if (companyId) {
            selectedCompanyKeys.value = [companyId];

            // 加载并设置部门数据
            if (departmentId) {
                await getDepartmentData(companyId);
                selectedDeptKeys.value = [departmentId];

                // 加载并设置职位数据
                if (jobId) {
                    await getJobData(departmentId);
                    selectedJobKeys.value = [jobId];

                    // 加载并设置职级数据
                    if (jobScoreId) {
                        await getJobScoreData(jobId);

                        // 检查jobScoreId是否在加载的职级数据中
                        const exists = jobScoreData.value.some(item => item.id === jobScoreId);
                        if (exists) {
                            selectedJobScoreKeys.value = [jobScoreId];
                        } else {
                            // 如果有职级数据，默认选择第一个
                            if (jobScoreData.value.length > 0) {
                                selectedJobScoreKeys.value = [jobScoreData.value[0].id];
                            }
                        }
                    } else {
                        await getJobScoreData(jobId);

                        // 如果有职级数据，默认选择第一个
                        if (jobScoreData.value.length > 0) {
                            selectedJobScoreKeys.value = [jobScoreData.value[0].id];
                        }
                    }
                }
            }
        }

        // 确保触发一次值更新
        emitValue();
    } catch (error) {
        SkMessage.error('组织数据初始化失败');
    }
};

// 组件挂载时获取数据
onMounted(async () => {
    initData();
});
</script>

<style scoped>
.sk-organization {
    width: 100%;
    background: #fff;
    padding: 16px;
    border-radius: 2px;
}

.tree-content {
    flex: 1;
    min-height: 300px;
    max-height: 500px;
    overflow: auto;
    border: 1px solid #f0f0f0;
    padding: 8px;
    margin: 0 8px;
}

.empty-text {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

/* 详情模式样式 */
.detail-view {
    padding: 16px 0;
}

.detail-item {
    margin-bottom: 16px;
    line-height: 24px;
}

.detail-label {
    color: #666;
    margin-right: 8px;
    font-weight: 500;
}

.detail-value {
    color: #333;
}

.required-asterisk {
    color: #ff4d4f;
    margin-right: 4px;
    font-family: SimSun, sans-serif;
}
</style>
