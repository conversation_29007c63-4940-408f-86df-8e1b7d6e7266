<template>
    <div class="performers-wrapper">
        <team-outlined class="icon" />
        <a-popover placement="right" trigger="hover" v-if="performers && performers.length > 0">
            <template #content>
                <div class="performer-list-popover">
                    <div class="performer-list-title">{{ title }} (共{{ performers.length }}人)</div>
                    <div class="performer-list">
                        <div v-for="(person, index) in performers" :key="index" class="performer-item">
                            <a-avatar size="small" :style="{ backgroundColor: getAvatarColor(person.userId) }">
                                {{ person.userName.trim() ? person.userName.trim().charAt(0) : '?' }}
                            </a-avatar>
                            <span class="performer-name">{{ person.name.trim() }}</span>
                        </div>
                    </div>
                </div>
            </template>
            <div class="performers-container">
                <div class="performer-tags">
                    <template v-for="(person, index) in performers" :key="index">
                        <a-tag v-if="index < 3 || performers.length <= 5" :color="getTagColor(person.userId)"
                            class="performer-tag">
                            {{ person.userName.trim() }}
                        </a-tag>
                    </template>
                    <a-tag v-if="performers.length > 5" color="#f50" class="performer-tag">
                        +{{ performers.length - 3 }}人
                    </a-tag>
                </div>
            </div>
        </a-popover>
        <span class="no-performers" v-else>暂无执行人</span>
    </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { TeamOutlined, UserOutlined } from '@ant-design/icons-vue';

// 组件属性定义
const props = defineProps({
    title: {
        type: String,
        default: '执行人列表'
    },
    // 数据
    performers: {
        type: Array,
        default: () => []
    }
});

// 根据用户ID生成固定的头像颜色
const getAvatarColor = (userId) => {
    if (!userId) return '#1890ff';

    // 预定义一组好看的颜色
    const colors = [
        '#1890ff', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96',
        '#13c2c2', '#faad14', '#a0d911', '#1677ff', '#f759ab'
    ];

    // 将userId转为数字并取模得到颜色索引
    let sum = 0;
    for (let i = 0; i < String(userId).length; i++) {
        sum += String(userId).charCodeAt(i);
    }

    return colors[sum % colors.length];
}

// 标签颜色使用深色调，更加醒目
const getTagColor = (userId) => {
    if (!userId) return '#108ee9';

    // 预定义一组深色调
    const colors = [
        '#108ee9', '#389e0d', '#d46b08', '#531dab', '#c41d7f',
        '#08979c', '#d4b106', '#7cb305', '#096dd9', '#d4380d',
        '#1d39c4', '#0a7b83', '#ad4e00', '#5b8c00', '#9e1068'
    ];

    // 将userId转为数字并取模得到颜色索引
    let sum = 0;
    for (let i = 0; i < String(userId).length; i++) {
        sum += String(userId).charCodeAt(i);
    }

    return colors[sum % colors.length];
}
</script>

<style lang="less" scoped>
.performers-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 4px;
    margin-bottom: 8px;
    cursor: pointer;

    .icon {
        margin-top: 2px;
    }

    .performers-container {
        display: flex;
        flex-wrap: wrap;
        flex: 1;

        .performer-tags {
            display: flex;
            flex-wrap: wrap;

            .performer-tag {
                margin: 1px;
                font-size: 12px;
                padding: 0 4px;
                line-height: 18px;
                height: 20px;
                border-radius: 4px;
            }
        }
    }

    .no-performers {
        color: rgba(0, 0, 0, 0.45);
    }
}

.performer-list-popover {
    width: 260px;
    max-height: 300px;

    .performer-list-title {
        font-weight: bold;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 8px;
    }

    .performer-list {
        max-height: 250px;
        overflow-y: auto;

        .performer-item {
            display: flex;
            align-items: center;
            padding: 6px 0;

            &:hover {
                background-color: #f5f5f5;
            }

            .performer-name {
                margin-left: 8px;
            }
        }
    }
}
</style>