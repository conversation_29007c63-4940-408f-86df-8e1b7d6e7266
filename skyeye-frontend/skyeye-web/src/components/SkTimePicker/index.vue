<template>
    <div class="sk-time-picker">
        <a-time-picker v-if="isEdit === $config.formEditType.isEdit" :value="modelValue" :placeholder="placeholder"
            :disabled="disabled" :format="format" :size="size" :allowClear="allowClear" :hourStep="hourStep"
            :minuteStep="minuteStep" :secondStep="secondStep" @change="handleChange" />
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                {{ formatReadOnlyValue }}
            </template>
        </div>
    </div>
</template>

<script>
import { defineComponent, computed } from 'vue';
import dayjs from 'dayjs';

export default defineComponent({
    name: 'SkTimePicker',
    props: {
        modelValue: {
            type: [Object, String, Date],
            default: null
        },
        placeholder: {
            type: String,
            default: '请选择时间'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        format: {
            type: String,
            default: 'HH:mm:ss'
        },
        size: {
            type: String,
            default: 'middle',
            validator: (value) => ['large', 'middle', 'small'].includes(value)
        },
        allowClear: {
            type: Boolean,
            default: true
        },
        isEdit: {
            type: Number,
            default: $config.formEditType.isEdit
        },
        hourStep: {
            type: Number,
            default: 1
        },
        minuteStep: {
            type: Number,
            default: 1
        },
        secondStep: {
            type: Number,
            default: 1
        }
    },
    emits: ['update:modelValue', 'change'],
    setup(props, { emit }) {
        // 格式化只读模式下的时间显示
        const formatReadOnlyValue = computed(() => {
            if (!props.modelValue) return '';
            return dayjs(props.modelValue).format(props.format);
        });

        const handleChange = (time, timeString) => {
            emit('update:modelValue', time);
            emit('change', time, timeString);
        };

        return {
            formatReadOnlyValue,
            handleChange
        };
    }
});
</script>

<style scoped>
.sk-time-picker {
    display: inline-block;
}

.sk-detail-readonly {
    min-height: 32px;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.88);
}
</style>