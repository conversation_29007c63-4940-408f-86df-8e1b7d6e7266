<template>
    <div class="sk-color-picker">
        <a-popover v-if="isEdit == $config.formEditType.isEdit" trigger="click" placement="bottomLeft" :open="open"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode" @openChange="handleOpenChange">
            <template #content>
                <div class="color-panel">
                    <!-- 颜色模式切换 -->
                    <a-radio-group v-model:value="colorMode" class="mode-switch">
                        <a-radio-button value="solid">纯色</a-radio-button>
                        <a-radio-button value="gradient">渐变</a-radio-button>
                    </a-radio-group>

                    <!-- 纯色模式 -->
                    <template v-if="colorMode === 'solid'">
                        <!-- 预设颜色方案选择 -->
                        <a-select v-model:value="currentScheme" class="scheme-select">
                            <a-select-option v-for="scheme in colorSchemes" :key="scheme.name" :value="scheme.name">
                                {{ scheme.label }}
                            </a-select-option>
                        </a-select>

                        <!-- 预设颜色 -->
                        <div class="preset-colors">
                            <div v-for="color in currentSchemeColors" :key="color" class="color-item"
                                :class="{ 'active': modelValue === color }" :style="{ backgroundColor: color }"
                                @click="handleColorSelect(color)">
                                <CheckOutlined v-if="modelValue === color" class="check-icon" />
                            </div>
                        </div>

                        <!-- 透明度调节 -->
                        <div class="opacity-slider">
                            <span class="slider-label">透明度</span>
                            <a-slider v-model:value="opacity" :min="0" :max="100" :step="1"
                                @change="handleOpacityChange" />
                            <span class="slider-value">{{ opacity }}%</span>
                        </div>
                    </template>

                    <!-- 渐变模式 -->
                    <template v-else>
                        <div class="gradient-editor">
                            <!-- 渐变预览 -->
                            <div class="gradient-preview" :style="gradientStyle">
                                <div v-for="(stop, index) in gradientStops" :key="index" class="gradient-stop"
                                    :style="{ left: stop.position + '%', backgroundColor: stop.color }"
                                    @mousedown="handleStopDrag($event, index)">
                                </div>
                            </div>
                            <!-- 渐变类型选择 -->
                            <div class="gradient-type">
                                <a-radio-group v-model:value="gradientType">
                                    <a-radio-button value="linear">线性渐变</a-radio-button>
                                    <a-radio-button value="radial">径向渐变</a-radio-button>
                                </a-radio-group>
                                <!-- 渐变角度（线性渐变时显示） -->
                                <a-input-number v-if="gradientType === 'linear'" v-model:value="gradientAngle" :min="0"
                                    :max="360" addon-after="°" />
                            </div>
                        </div>
                    </template>

                    <!-- 最近使用的颜色 -->
                    <div class="recent-colors">
                        <div class="section-title">最近使用</div>
                        <div class="color-list">
                            <div v-for="color in recentColors" :key="color" class="color-item"
                                :style="{ backgroundColor: color }" @click="handleColorSelect(color)">
                            </div>
                        </div>
                    </div>

                    <!-- 自定义颜色输入 -->
                    <div class="custom-color">
                        <a-input v-model:value="customColor" placeholder="FFFFFF" @pressEnter="handleCustomColorEnter">
                            <template #prefix>#</template>
                        </a-input>
                    </div>
                </div>
            </template>

            <div class="color-trigger" @click="handleTriggerClick">
                <div class="color-preview" :style="colorPreviewStyle"></div>
                <div class="color-value">{{ colorDisplayValue }}</div>
            </div>
        </a-popover>
        <div v-else class="color-display">
            <div class="color-preview" :style="colorPreviewStyle"></div>
            <div class="color-value">{{ colorDisplayValue || '-' }}</div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { CheckOutlined } from '@ant-design/icons-vue'

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    placeholder: {
        type: String,
        default: '请选择颜色'
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const open = ref(false)
const customColor = ref('')
const colorMode = ref('solid')
const opacity = ref(100)
const currentScheme = ref('default')
const recentColors = ref([])
const gradientType = ref('linear')
const gradientAngle = ref(90)
const gradientStops = ref([
    { color: '#ff0000', position: 0 },
    { color: '#00ff00', position: 100 }
])

// 颜色方案
const colorSchemes = [
    {
        name: 'default',
        label: '默认方案',
        colors: [
            '#FF4D4F', '#FF7A45', '#FFA940', '#FFD666', '#FFF566',
            '#95DE64', '#5CDBD3', '#69C0FF', '#85A5FF', '#B37FEB',
            '#FF85C0', '#595959', '#8C8C8C', '#BFBFBF', '#F5F5F5'
        ]
    },
    {
        name: 'material',
        label: 'Material',
        colors: [
            '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
            '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
            '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800'
        ]
    },
    {
        name: 'dark',
        label: '暗色系',
        colors: [
            '#000000', '#1A1A1A', '#333333', '#4D4D4D', '#666666',
            '#808080', '#999999', '#B3B3B3', '#CCCCCC', '#E6E6E6',
            '#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7'
        ]
    }
]

// 当前方案的颜色
const currentSchemeColors = computed(() => {
    const scheme = colorSchemes.find(s => s.name === currentScheme.value)
    return scheme ? scheme.colors : []
})

// 渐变样式
const gradientStyle = computed(() => {
    if (gradientType.value === 'linear') {
        const gradient = `linear-gradient(${gradientAngle.value}deg, ${gradientStops.value.map(stop =>
            `${stop.color} ${stop.position}%`).join(', ')})`
        return { background: gradient }
    } else {
        const gradient = `radial-gradient(circle, ${gradientStops.value.map(stop =>
            `${stop.color} ${stop.position}%`).join(', ')})`
        return { background: gradient }
    }
})

// 颜色预览样式
const colorPreviewStyle = computed(() => {
    if (colorMode.value === 'solid') {
        const color = props.modelValue || '#FFFFFF'
        const alpha = opacity.value / 100
        return {
            backgroundColor: color,
            opacity: alpha
        }
    } else {
        return gradientStyle.value
    }
})

// 颜色显示值
const colorDisplayValue = computed(() => {
    if (!props.modelValue) return props.placeholder
    if (colorMode.value === 'solid') {
        return opacity.value < 100
            ? `${props.modelValue} ${opacity.value}%`
            : props.modelValue
    } else {
        return '渐变色'
    }
})

// 处理颜色选择
const handleColorSelect = (color) => {
    const finalColor = colorMode.value === 'solid' && opacity.value < 100
        ? addOpacity(color, opacity.value)
        : color
    updateColor(finalColor)
}

// 处理自定义颜色输入
const handleCustomColorEnter = () => {
    if (/^[0-9A-Fa-f]{6}$/.test(customColor.value)) {
        const color = '#' + customColor.value.toUpperCase()
        handleColorSelect(color)
        customColor.value = ''
    }
}

// 添加到最近使用
const addToRecentColors = (color) => {
    // 如果是透明度调节，不添加到最近使用
    if (color.startsWith('rgba')) {
        return
    }

    // 只添加十六进制颜色到最近使用
    if (color.startsWith('#')) {
        if (!recentColors.value.includes(color)) {
            recentColors.value.unshift(color)
            if (recentColors.value.length > 8) {
                recentColors.value.pop()
            }
        }
    }
}

// 处理渐变点拖拽
const handleStopDrag = (event, index) => {
    const startX = event.clientX
    const startPos = gradientStops.value[index].position

    const handleMouseMove = (e) => {
        const delta = e.clientX - startX
        const newPos = Math.max(0, Math.min(100, startPos + (delta / 2)))
        gradientStops.value[index].position = newPos
    }

    const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
}

// 添加透明度
const addOpacity = (color, opacity) => {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity / 100})`
}

// 处理打开状态变化
const handleOpenChange = (visible) => {
    if (!props.disabled) {
        open.value = visible
    }
}

// 处理触发器点击
const handleTriggerClick = () => {
    if (!props.disabled) {
        open.value = !open.value
    }
}

// 添加处理透明度变化的函数
const handleOpacityChange = (value) => {
    if (!props.modelValue) return

    // 如果当前是十六进制颜色，转换为 rgba
    if (props.modelValue.startsWith('#')) {
        const color = addOpacity(props.modelValue, value)
        // 透明度变化时不添加到最近使用
        emit('update:modelValue', color)
        emit('change', color)
    }
    // 如果当前是 rgba 颜色，更新透明度
    else if (props.modelValue.startsWith('rgba')) {
        const matches = props.modelValue.match(/rgba\((\d+),\s*(\d+),\s*(\d+)/)
        if (matches) {
            const [_, r, g, b] = matches
            const color = `rgba(${r}, ${g}, ${b}, ${value / 100})`
            // 透明度变化时不添加到最近使用
            emit('update:modelValue', color)
            emit('change', color)
        }
    }
}

// 添加统一的颜色更新函数
const updateColor = (color) => {
    emit('update:modelValue', color)
    emit('change', color)
    // 只有选择新颜色时才添加到最近使用
    if (!color.startsWith('rgba')) {
        addToRecentColors(color)
    }
}
</script>

<style scoped>
.sk-color-picker {
    /* 基础样式保持不变 */
    border-width: 1px;
    border-style: solid;
    border-color: #d9d9d9;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    line-height: 1.5714285714285714;
    background-color: #ffffff;
    border-radius: 4px;
}

.color-panel {
    width: 280px;
    padding: 16px;
}

.mode-switch {
    width: 100%;
    margin-bottom: 16px;
}

.scheme-select {
    width: 100%;
    margin-bottom: 12px;
}

.preset-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.color-item {
    position: relative;
    width: 100%;
    height: 24px;
    border-radius: 2px;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    transition: transform 0.2s;
}

.color-item:hover {
    transform: scale(1.1);
    z-index: 1;
}

.color-item.active {
    border: 2px solid #1890ff;
}

.opacity-slider {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;
}

:deep(.ant-slider) {
    width: 60%;
}

.slider-label,
.slider-value {
    color: rgba(0, 0, 0, 0.85);
    font-size: 12px;
}

.gradient-editor {
    margin-bottom: 16px;
}

.gradient-preview {
    height: 40px;
    border-radius: 4px;
    margin-bottom: 8px;
    position: relative;
    border: 1px solid #d9d9d9;
}

.gradient-stop {
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    transform: translate(-50%, -50%);
    top: 100%;
    cursor: pointer;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3);
}

.gradient-type {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
}

.recent-colors {
    margin-bottom: 16px;
}

.section-title {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 8px;
}

.color-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.custom-color {
    margin-top: 16px;
}

:deep(.ant-input-affix-wrapper) {
    width: 100%;
}

.check-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
}
</style>