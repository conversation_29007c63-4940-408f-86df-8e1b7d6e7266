<template>
    <div class="sk-boss-person-require">
        <template v-if="isEdit === $config.formEditType.isEdit">
            <!-- 编辑模式 -->
            <a-row>
                <a-col :span="24">
                    <a-form-item>
                        <div class="input-with-icon">
                            <SkInput v-model="selectedName" :placeholder="placeholder" readonly>
                                <template #suffix>
                                    <user-add-outlined class="select-icon" @click="handleSelect" />
                                </template>
                            </SkInput>
                        </div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="需求部门">
                        <div>{{ requireData.recruitDepartmentMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="需求岗位">
                        <div>{{ requireData.recruitJobMation?.name }}</div>
                    </a-form-item>
                </a-col>
            </a-row>
        </template>
        <template v-else>
            <!-- 详情模式 -->
            <a-row>
                <a-col :span="24">
                    <a-form-item :label="title">
                        <div>{{ requireData.oddNumber }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="需求部门">
                        <div>{{ requireData.recruitDepartmentMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="需求岗位">
                        <div>{{ requireData.recruitJobMation?.name }}</div>
                    </a-form-item>
                </a-col>
            </a-row>
        </template>

        <!-- 选择弹窗 -->
        <SkModal v-model="modalVisible" :title="'人员需求'" width="70%">
            <div>
                <!-- 提示信息 -->
                <SkAlert message="注意事项: 1. 仅加载我负责的人员需求申请信息  2. 已招聘结束的人员需求申请无法选中" show-icon />
                <!-- 刷新按钮 -->
                <div class="refresh-button">
                    <SkSpace>
                        <SkButton type="primary" @click.prevent="fetchData">
                            <template #icon>
                                <reload-outlined />
                            </template>
                            {{ $t('common.refresh') }}
                        </SkButton>
                    </SkSpace>
                </div>

                <!-- 表格 -->
                <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                    :tipInfoHeight="56" @change="handleTableChange">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'state'">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['bossPersonRequireState'], 'id', record.state, 'name')">
                            </div>
                        </template>
                        <template v-else-if="column.key === 'action'">
                            <SkSpace>
                                <a v-if="record.state !== 'endRecruitment'" @click="handleRequireSelect(record)">选择</a>
                                <span v-else class="disabled-link">选择</span>
                            </SkSpace>
                        </template>
                    </template>
                </SkTable>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watch, reactive } from 'vue'
import { UserAddOutlined, SyncOutlined } from '@ant-design/icons-vue'
import SkInput from '@/components/SkInput/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择人员需求'
    },
    attrKey: {
        type: String,
        default: 'objectId'
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])
const { proxy } = getCurrentInstance()

const modalVisible = ref(false)
const selectedName = ref('')
const requireData = ref({})
const loading = ref(false)
const tableData = ref([])

// 表格列配置
const columns = [
    {
        title: '选择',
        key: 'action',
        width: 80,
        align: 'center'
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        width: 150,
        align: 'left'
    },
    {
        title: '需求部门',
        dataIndex: ['recruitDepartmentMation', 'name'],
        width: 150,
        align: 'left'
    },
    {
        title: '需求岗位',
        dataIndex: ['recruitJobMation', 'name'],
        width: 150,
        align: 'left'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 100,
        align: 'left'
    },
    {
        title: '薪资范围',
        dataIndex: 'wages',
        width: 120,
        align: 'left'
    },
    {
        title: '需求人数',
        dataIndex: 'recruitNum',
        width: 100,
        align: 'center'
    },
    {
        title: '已招聘人数',
        dataIndex: 'recruitedNum',
        width: 100,
        align: 'center'
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120,
        align: 'left'
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'left'
    },

]

// 分页配置
const pagination = reactive({
    ...proxy.$config.pagination(),
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '30'],
    defaultPageSize: 5,
    showTotal: (total) => `全部 ${total} 条`
})

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['bossPersonRequireState'])
    initEnumData.value = result
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 5)
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'queryMyChargePersonRequireList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 处理表格变化
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

// 处理选择按钮点击
const handleSelect = async () => {
    modalVisible.value = true
    await getInitData()
    fetchData()
}

// 处理需求选择
const handleRequireSelect = (record) => {
    if (!record) return

    requireData.value = record
    selectedName.value = record.oddNumber || ''

    emit('update:modelValue', record.id)
    emit('change', record)
    modalVisible.value = false
}

// 初始化数据
const initData = () => {
    if (props.pageType === proxy.$config.pageType.EDIT && props.formData) {
        const mationKey = props.attrKey.replace('Id', 'Mation')
        const mation = props.formData[mationKey]

        if (mation) {
            requireData.value = mation
            selectedName.value = mation.oddNumber || ''
            emit('update:modelValue', mation.id)
        }
    }
}

// 监听formData变化
watch(() => props.formData, (newVal) => {
    if (newVal) {
        initData()
    }
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
    initData()
})
</script>

<style scoped>
.sk-boss-person-require {
    width: 100%;
}

.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.select-icon {
    cursor: pointer;
    color: #1890ff;
    font-size: 16px;
}

.disabled-link {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
}
</style>
