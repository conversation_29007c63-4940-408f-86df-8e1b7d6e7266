<template>
    <div class="sk-region-select">
        <SkSpace>
            <!-- 省份选择 -->
            <SkSelect v-model="selectedProvince" :loading="loadingProvince" :options="provinceOptions"
                :formData="formData" :placeholder="t('common.selectProvince')" :isEdit="isEdit"
                @change="handleProvinceChange" />

            <!-- 城市选择 -->
            <SkSelect v-model="selectedCity" :loading="loadingCity" :options="cityOptions" :formData="formData"
                :placeholder="t('common.selectCity')" :isEdit="isEdit" @change="handleCityChange" />

            <!-- 区县选择 -->
            <SkSelect v-model="selectedDistrict" :loading="loadingDistrict" :options="districtOptions"
                :formData="formData" :placeholder="t('common.selectDistrict')" :isEdit="isEdit"
                @change="handleDistrictChange" />

            <!-- 乡镇选择 -->
            <SkSelect v-model="selectedTown" :loading="loadingTown" :options="townOptions" :formData="formData"
                :placeholder="t('common.selectTown')" :isEdit="isEdit" @change="handleTownChange" />
        </SkSpace>

        <!-- 详细地址 -->
        <div class="address-detail">
            <SkInput v-model="detailAddress" :placeholder="t('common.inputDetailAddress')" :isEdit="isEdit"
                @change="handleDetailAddressChange" :maxLength="100" :showCount="true" />
        </div>
    </div>
</template>

<script setup>
import { ref, watch, getCurrentInstance, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import SkSpace from '@/components/SkSpace/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkInput from '@/components/SkInput/index.vue'

const { t } = useI18n()
const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Object, String],
        default: () => ({})
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: () => $config.formEditType.isEdit // 默认可编辑
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 选中的值
const selectedProvince = ref('')
const selectedCity = ref('')
const selectedDistrict = ref('')
const selectedTown = ref('')
const detailAddress = ref('')

// 选项数据
const provinceOptions = ref([])
const cityOptions = ref([])
const districtOptions = ref([])
const townOptions = ref([])

// 加载状态
const loadingProvince = ref(false)
const loadingCity = ref(false)
const loadingDistrict = ref(false)
const loadingTown = ref(false)

// 获取区域数据
const getRegionData = async (parentId = '0') => {
    try {
        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'queryAreaListByPId',
            { pId: parentId }
        )
        return res.rows || []
    } catch (error) {
        console.error('获取区域数据失败:', error)
        return []
    }
}

// 初始化省份数据
const initProvinceData = async () => {
    loadingProvince.value = true
    try {
        const data = await getRegionData()
        provinceOptions.value = data.map(item => ({
            label: item.name,
            value: item.id,
            data: item
        }))
    } finally {
        loadingProvince.value = false
    }
}

// 处理省份变化
const handleProvinceChange = async (value, option, attrKey, isInit = false) => {
    if (!isInit) {
        selectedCity.value = ''
        selectedDistrict.value = ''
        selectedTown.value = ''
        districtOptions.value = []
        townOptions.value = []
    }

    if (value) {
        loadingCity.value = true
        try {
            const data = await getRegionData(value)
            cityOptions.value = data.map(item => ({
                label: item.name,
                value: item.id,
                data: item
            }))
        } finally {
            loadingCity.value = false
        }
    }

    if (!isInit) {
        updateValue()
    }
}

// 处理城市变化
const handleCityChange = async (value, option, attrKey, isInit = false) => {
    if (!isInit) {
        selectedDistrict.value = ''
        selectedTown.value = ''
        townOptions.value = []
    }

    if (value) {
        loadingDistrict.value = true
        try {
            const data = await getRegionData(value)
            districtOptions.value = data.map(item => ({
                label: item.name,
                value: item.id,
                data: item
            }))
        } finally {
            loadingDistrict.value = false
        }
    }

    if (!isInit) {
        updateValue()
    }
}

// 处理区县变化
const handleDistrictChange = async (value, option, attrKey, isInit = false) => {
    if (!isInit) {
        selectedTown.value = ''
    }

    if (value) {
        loadingTown.value = true
        try {
            const data = await getRegionData(value)
            townOptions.value = data.map(item => ({
                label: item.name,
                value: item.id,
                data: item
            }))
        } finally {
            loadingTown.value = false
        }
    }

    if (!isInit) {
        updateValue()
    }
}

// 处理乡镇变化
const handleTownChange = (value, option, attrKey, isInit = false) => {
    updateValue()
}

// 处理详细地址变化
const handleDetailAddressChange = () => {
    updateValue()
}

// 更新值
const updateValue = () => {
    const value = {
        provinceId: selectedProvince.value,
        cityId: selectedCity.value,
        areaId: selectedDistrict.value,
        townshipId: selectedTown.value,
        absoluteAddress: detailAddress.value
    }

    emit('update:modelValue', value)
    emit('change', value)
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        selectedProvince.value = newVal.provinceId
        selectedCity.value = newVal.cityId
        selectedDistrict.value = newVal.areaId
        selectedTown.value = newVal.townshipId
        detailAddress.value = newVal.absoluteAddress

        // 如果有省份ID,则加载对应的城市数据
        if (newVal.provinceId) {
            handleProvinceChange(newVal.provinceId, null, null, true)
        }
        // 如果有城市ID,则加载对应的区县数据  
        if (newVal.cityId) {
            handleCityChange(newVal.cityId, null, null, true)
        }
        // 如果有区县ID,则加载对应的乡镇数据
        if (newVal.areaId) {
            handleDistrictChange(newVal.areaId, null, null, true)
        }
    }
}, { deep: true, immediate: true })

// 初始化
onMounted(() => {
    if (!proxy.$util.isNull(props.modelValue)) {
        const value = {
            provinceId: props.formData.provinceId,
            cityId: props.formData.cityId,
            areaId: props.formData.areaId,
            townshipId: props.formData.townshipId,
            absoluteAddress: props.formData.absoluteAddress
        }

        emit('update:modelValue', value)
    }
    initProvinceData()
})
</script>

<style scoped>
.sk-region-select {
    width: 100%;

    :deep(.sk-select-wrapper) {
        min-width: 140px;
    }
}

.address-detail {
    margin-top: 16px;
}
</style>