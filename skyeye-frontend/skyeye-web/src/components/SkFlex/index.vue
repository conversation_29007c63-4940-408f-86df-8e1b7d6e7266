<template>
    <a-flex :vertical="vertical" :wrap="wrap" :justify="justify" :align="align" :gap="gap" :component="component">
        <slot></slot>
    </a-flex>
</template>

<script setup>
defineProps({
    // 是否垂直排列
    vertical: {
        type: Boolean,
        default: false
    },
    // 是否自动换行
    wrap: {
        type: String,
        default: 'nowrap'
    },
    // 水平排列方式
    justify: {
        type: String,
        default: 'normal'
    },
    // 垂直对齐方式
    align: {
        type: String,
        default: 'normal'
    },
    // 间距大小
    gap: {
        type: [Number, String],
        default: undefined
    },
    // 自定义元素类型
    component: {
        type: String,
        default: 'div'
    }
})
</script>