<template>
    <div class="sk-dict-wrapper">
        <div v-if="isEdit == $config.formEditType.isEdit">
            <SkRadio v-if="showType === 'radio' && isShow" v-model="value" :options="dataList" @change="handleChange" />
            <SkSelect v-if="showType === 'select' && isShow" v-model="value" :options="dataList"
                @change="handleChange" />
            <SkTreeSelect v-if="showType === 'radioTree' && isShow" v-model="value" :tree-data="treeData"
                @change="handleChange" :field-names="fieldNames" />
            <SkTree v-if="showType === 'selectTree' && isShow" :data="treeData" @select="handleSelect"
                :fieldNames="skTreeFieldNames" :is-edit="isEdit" />
        </div>
        <div v-else-if="isEdit == $config.formEditType.notEdit">
            {{ showValue }}
        </div>
    </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import SkRadio from '@/components/SkRadio/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkTreeSelect from '@/components/SkTreeSelect/index.vue'
import SkTree from '@/components/SkTree/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Array, String, Object]
    },

    // 属性key
    attrKey: {
        type: String,
        default: ''
    },

    // 显示类型
    showType: {
        type: String,
        default: ''
    },

    // 数据字典类型
    objectType: {
        type: String,
        default: ''
    },

    // 页面类型
    pageType: {
        type: String,
        default: ''
    },

    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },

    formData: {},

    attrDefinitionCustom: {
        type: Object,
        default: function () {
            return {}
        }
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const value = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

const isShow = ref(false)
const dataList = ref([])
const treeData = ref([])
const fieldNames = ref({
    label: 'name',
    value: 'id',
    children: 'children'
})

const skTreeFieldNames = ref({
    title: 'name',
    key: 'id',
    children: 'children'
})
const showValue = ref('')

const handleChange = (value) => {
    emit('change', value)
}

const handleSelect = (selectIds) => {
    value.value = selectIds.length > 0 ? selectIds[selectIds.length - 1] : ''
    handleChange(value.value);
}

// 递归处理树节点
const processTreeNodes = (nodes) => {
    if (!nodes) return []
    return nodes.map(node => {
        const processedNode = { ...node }
        // 如果 nocheck 为 true，设置 disabled
        if (node.nocheck === true) {
            processedNode.disabled = true
        }
        // 递归处理子节点
        if (node.children && node.children.length > 0) {
            processedNode.children = processTreeNodes(node.children)
        }
        return processedNode
    })
}

// 获取数据
const fetchData = async () => {
    // 如果是不可编辑的，则先从formData中获取数据，如果获取不到，则调用接口获取数据
    if (props.isEdit == $config.formEditType.notEdit) {
        // 不可编辑
        const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
        showValue.value = props.formData[keyMation]?.dictName || ''
        if (!proxy.$util.isNull(showValue.value)) {
            return
        }
    }
    var params = {
        dictTypeCode: props.objectType
    }
    const res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + "queryDictDataListByDictTypeCode", params)
    if (props.isEdit == $config.formEditType.isEdit) {
        const _dataList = []
        res.rows.forEach((item, index) => {
            _dataList.push({
                label: item.name,
                value: item.id,
                isDefault: item.isDefault
            })
        })
        if (props.showType == 'radioTree' || props.showType == 'selectTree') {
            treeData.value = processTreeNodes([].concat(res.treeRows))
        }
        if (props.pageType == proxy.$config.pageType.ADD) {
            if (proxy.$util.isNull(props.modelValue)) {
                const num1 = _dataList.findIndex(v => v.isDefault == 1);
                if (num1 >= 0) {
                    value.value = _dataList[num1].value
                    handleChange(_dataList[num1].value)
                }
            }
        }

        dataList.value = _dataList
        isShow.value = true
    } else {
        showValue.value = res.rows.find(item => item.id == props.modelValue)?.name || ''
    }
}

onMounted(async () => {
    await fetchData()
})

</script>

<style scoped></style>