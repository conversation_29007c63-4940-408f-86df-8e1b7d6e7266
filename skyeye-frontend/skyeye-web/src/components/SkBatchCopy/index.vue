<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" @submit="handleSubmit" @reset="handleCancel" submitText="获取">
            <!-- 基本信息标题 -->
            <SkHrTitle>基本信息</SkHrTitle>

            <!-- 商品选择 -->
            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="商品">
                        <SkMaterialSelect v-model="formData.materialId" :isEdit="$config.formEditType.isEdit"
                            @change="handleMaterialChange" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="规格">
                        <SkSelect v-model="formData.normsId" :options="normsOptions" placeholder="请选择规格"
                            :disabled="!formData.materialId" allowClear />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="仓库">
                        <SkSelect v-model="formData.depotId" :options="depotOptions" placeholder="请选择仓库" allowClear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="状态">
                        <SkRadio v-model="formData.inDepot" :options="statusOptions" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="获取数量" required>
                        <SkInputNumber v-model="formData.number" placeholder="请输入获取编码的数量" :min="1" :max="99" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-form-item label="条形码">
                <a-textarea v-model:value="formData.barCode" :rows="8" :readonly="true" />
                <div class="mt-8">
                    <SkSpace>
                        <SkButton type="primary" @click.prevent="handleCopy">复制</SkButton>
                        <SkButton @click.prevent="handleClear">清空</SkButton>
                    </SkSpace>
                </div>
            </a-form-item>

            <a-form-item label="获取结果">
                <div class="text-primary">{{ tips }}</div>
            </a-form-item>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInputNumber from '@/components/SkInputNumber/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkRadio from '@/components/SkRadio/index.vue'

const props = defineProps({
    materialId: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['cancel'])

const { proxy } = getCurrentInstance()


// 表单数据
const formRef = ref(null)
let formData = reactive({
    materialId: props.materialId || '',
    normsId: undefined,
    depotId: undefined,
    inDepot: undefined,
    number: 1,
    barCode: ''
})

// 规格选项
const normsOptions = ref([])
// 仓库选项
const depotOptions = ref([])
const tips = ref('')

// 状态选项
const statusOptions = ref([])

// 获取仓库列表
const getDepotList = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryAllStoreHouseList'
        )
        depotOptions.value = (res.rows || []).map(item => ({
            label: item.name,
            value: item.id
        }))
    } catch (error) {
        SkMessage.error('获取仓库列表失败')
    }
}

// 获取状态枚举数据
const getStatusOptions = async () => {
    try {
        const result = await proxy.$util.getEnumListMapByCode(['materialNormsCodeInDepot'])
        if (result.materialNormsCodeInDepot) {
            statusOptions.value = Object.values(result.materialNormsCodeInDepot).map(item => ({
                label: item.name,
                value: String(item.id)
            }))
            // 如果有默认值，设置默认值
            const defaultOption = statusOptions.value.find(item => item.isDefault)
            if (defaultOption) {
                formData.inDepot = defaultOption.value
            }
        }
    } catch (error) {
        SkMessage.error('获取状态列表失败')
    }
}

// 处理商品变化
const handleMaterialChange = async (material) => {
    formData.normsId = undefined
    normsOptions.value = []

    if (material?.id) {
        try {
            const res = await proxy.$http.get(
                proxy.$config.getConfig().erpBasePath + 'queryNormsListByMaterialId',
                { materialId: material.id }
            )
            normsOptions.value = (res.rows || []).map(item => ({
                label: item.name,
                value: item.id
            }))
        } catch (error) {
            SkMessage.error('获取规格列表失败')
        }
    }
}

// 复制条形码
const handleCopy = async () => {
    if (!formData.barCode) {
        SkMessage.warning('没有可复制的内容')
        return
    }
    proxy.$util.copyToClipboard(formData.barCode,{
        onSuccess: () => SkMessage.success('复制成功'),
        onError: () => SkMessage.error('复制失败')
    }) 
}

// 清空条形码
const handleClear = () => {
    formData.barCode = ''
    tips.value = ''
}

// 提交表单
const handleSubmit = async () => {
    try {
        const params = {
            materialId: formData.materialId,
            normsId: formData.normsId,
            inDepot: formData.inDepot,
            number: formData.number,
            limit: formData.number,
            depotId: formData.depotId,
            page: 1
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryNormsBarCodeList',
            params
        )

        formData.barCode = (res.rows || []).join('\n')
        tips.value = `共计获取${res.total}个条形码`
    } catch (error) {
        SkMessage.error('获取条形码失败')
    }
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 初始化
onMounted(async () => {
    // 获取状态列表
    await getStatusOptions()
    // 获取仓库列表
    await getDepotList()

    if (props.materialId) {
        // 如果传入了materialId，加载对应的规格列表
        const material = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryMaterialListById',
            { id: props.materialId }
        )
        if (material.bean) {
            await handleMaterialChange(material.bean)
        }
    }
})
</script>

<style scoped></style>
