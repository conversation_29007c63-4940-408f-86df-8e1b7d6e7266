<template>
    <a-col :span="span" :offset="offset" :pull="pull" :push="push" :xs="xs" :sm="sm" :md="md" :lg="lg" :xl="xl"
        :xxl="xxl" :flex="flex">
        <slot></slot>
    </a-col>
</template>

<script setup>
defineProps({
    span: {
        type: Number,
        default: undefined
    },
    offset: {
        type: Number,
        default: undefined
    },
    pull: {
        type: Number,
        default: undefined
    },
    push: {
        type: Number,
        default: undefined
    },
    xs: {
        type: [Number, Object],
        default: undefined
    },
    sm: {
        type: [Number, Object],
        default: undefined
    },
    md: {
        type: [Number, Object],
        default: undefined
    },
    lg: {
        type: [Number, Object],
        default: undefined
    },
    xl: {
        type: [Number, Object],
        default: undefined
    },
    xxl: {
        type: [Number, Object],
        default: undefined
    },
    flex: {
        type: [Number, String],
        default: undefined
    }
})
</script>