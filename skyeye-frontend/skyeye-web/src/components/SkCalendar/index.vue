<template>
    <div class="sk-calendar">
        <a-calendar :value="currentDate" :mode="currentMode" :fullscreen="fullscreen" :locale="locale"
            @select="handleSelect" @panelChange="handlePanelChange">
            <!-- 自定义日期单元格 -->
            <template v-if="$slots.dateCellRender" #dateCellRender="dateCell">
                <slot name="dateCellRender" v-bind="dateCell"></slot>
            </template>

            <!-- 自定义月份单元格 -->
            <template v-if="$slots.monthCellRender" #monthCellRender="monthCell">
                <slot name="monthCellRender" v-bind="monthCell"></slot>
            </template>

            <!-- 自定义头部 -->
            <template v-if="$slots.headerRender" #headerRender="headerCell">
                <slot name="headerRender" v-bind="headerCell"></slot>
            </template>
        </a-calendar>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

const props = defineProps({
    // 当前日期
    value: {
        type: [Date, String, Object],
        default: () => new Date()
    },
    // 显示模式
    mode: {
        type: String,
        default: 'month',
        validator: (value) => ['month', 'year'].includes(value)
    },
    // 是否全屏显示
    fullscreen: {
        type: Boolean,
        default: true
    },
    // 国际化配置
    locale: {
        type: Object,
        default: () => ({
            lang: {
                locale: 'zh_CN',
                month: '月',
                year: '年',
                ...dayjs.locale('zh-cn')
            }
        })
    }
})

const emit = defineEmits(['select', 'panelChange', 'update:value', 'update:mode'])

// 内部状态
const currentMode = ref(props.mode)

// 将日期值转换为 dayjs 对象
const currentDate = computed(() => {
    if (props.value instanceof Date) {
        return dayjs(props.value)
    }
    if (typeof props.value === 'string') {
        return dayjs(props.value)
    }
    if (dayjs.isDayjs(props.value)) {
        return props.value
    }
    return dayjs()
})

// 监听模式变化
watch(() => props.mode, (newMode) => {
    currentMode.value = newMode
})

// 处理日期选择
const handleSelect = (date) => {
    emit('select', date)
    emit('update:value', date)
}

// 处理面板切换
const handlePanelChange = (date, mode) => {
    currentMode.value = mode
    emit('panelChange', { date, mode })
    emit('update:mode', mode)
}
</script>

<style scoped>
.sk-calendar {
    width: 100%;
}

:deep(.ant-picker-calendar) {
    background: #fff;
}

:deep(.ant-picker-calendar-header) {
    padding: 12px;
}

:deep(.ant-picker-calendar-date) {
    height: 100%;
    margin: 0;
    padding: 4px 8px;
}

:deep(.ant-picker-calendar-date-value) {
    margin-bottom: 0;
    line-height: 24px;
}

:deep(.ant-picker-calendar-date-content) {
    height: calc(100% - 24px);
    overflow: hidden;
}

:deep(.ant-picker-calendar-full .ant-picker-panel) {
    background: #fff;
}

:deep(.ant-picker-calendar-date.ant-picker-calendar-date-today) {
    border-color: var(--ant-primary-color);
}

:deep(.ant-picker-calendar-date.ant-picker-calendar-date-selected) {
    background: var(--ant-primary-1);
}

:deep(.ant-picker-calendar-date:hover) {
    background: var(--ant-primary-1);
}
</style>