<template>
    <div class="sk-input-wrapper">
        <a-input v-if="isEdit === $config.formEditType.isEdit" v-bind="$attrs" :value="modelValue" :type="type"
            :placeholder="placeholder" :disabled="disabled" :maxlength="maxLength" :show-count="getShowCount()"
            :size="size" :allow-clear="allowClear" :status="validateStatus" @change="handleChange" :title="modelValue"
            @pressEnter="handlePressEnter" @focus="handleFocus" @blur="handleBlur">
            <!-- 前缀图标插槽 -->
            <template v-if="$slots.prefix" #prefix>
                <slot name="prefix"></slot>
            </template>
            <!-- 后缀图标插槽 -->
            <template v-if="$slots.suffix" #suffix>
                <slot name="suffix"></slot>
            </template>
            <!-- 前置标签插槽 -->
            <template v-if="$slots.addonBefore" #addonBefore>
                <slot name="addonBefore"></slot>
            </template>
            <!-- 后置标签插槽 -->
            <template v-if="$slots.addonAfter" #addonAfter>
                <slot name="addonAfter"></slot>
            </template>
        </a-input>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <span :title="modelValue">{{ modelValue }}</span>
        </div>
        <!-- 校验提示信息 -->
        <div v-if="validateMessage" :class="['sk-input-validate-message', validateStatus]">
            <exclamation-circle-outlined v-if="validateStatus === 'error'" />
            <info-circle-outlined v-else-if="validateStatus === 'warning'" />
            {{ validateMessage }}
        </div>
        <!-- 帮助信息 -->
        <div v-if="help && helpShow" class="sk-input-help">
            <info-circle-outlined />
            {{ help }}
        </div>
    </div>
</template>

<script setup>
import { InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'

defineOptions({
    name: 'SkInput'
})

const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    },
    type: {
        type: String,
        default: 'text',
        validator: (value) => ['text', 'password', 'number'].includes(value)
    },
    placeholder: {
        type: String,
        default: '请输入'
    },
    disabled: {
        type: Boolean,
        default: false
    },
    maxLength: {
        type: [Number, String],
        default: 150
    },
    showCount: {
        type: Boolean,
        default: false
    },
    allowClear: {
        type: Boolean,
        default: false
    },
    size: {
        type: String,
        default: 'middle',
        validator: (value) => ['large', 'middle', 'small'].includes(value)
    },
    help: {
        type: String,
        default: ''
    },
    helpShow: {
        type: Boolean,
        default: false
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    validateStatus: {
        type: String,
        default: '',
        validator: (value) => ['', 'success', 'warning', 'error', 'validating'].includes(value)
    },
    validateMessage: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'pressEnter', 'focus', 'blur', 'clear'])

// 处理输入变化
const handleChange = (e) => {
    const value = e.target?.value ?? e
    emit('update:modelValue', value)
    emit('change', { value, attrKey: props.attrKey })
}

// 获取是否显示计数
const getShowCount = () => {
    if (props.maxLength) {
        return props.showCount
    }
    return false
}

// 处理回车按键
const handlePressEnter = (e) => {
    emit('pressEnter', { value: e.target.value, attrKey: props.attrKey })
}

// 处理获得焦点
const handleFocus = (e) => {
    emit('focus', { value: e.target.value, attrKey: props.attrKey })
}

// 处理失去焦点
const handleBlur = (e) => {
    emit('blur', { value: e.target.value, attrKey: props.attrKey })
}
</script>

<style lang="less" scoped>
.sk-input-wrapper {
    display: inline-flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;

    :deep(.ant-input) {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .sk-input-validate-message {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;

        &.error {
            color: #ff4d4f;
        }

        &.warning {
            color: #faad14;
        }

        &.success {
            color: #52c41a;
        }
    }

    .sk-input-help {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
    }

    :deep(.ant-input-group-wrapper) {
        width: 100%;
    }

    :deep(.ant-input-affix-wrapper) {
        width: 100%;
    }
}
</style>