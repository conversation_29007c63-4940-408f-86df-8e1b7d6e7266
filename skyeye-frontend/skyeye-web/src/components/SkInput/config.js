export default {
    type: 'input',
    icon: 'EditOutlined',
    props: {
        label: '',
        placeholder: '',
        required: false,
        attrKey: '',
        width: '100%'
    },
    propsConfig: {
        attrKey: {
            type: 'select',
            label: '绑定属性',
            options: [],
            config: {
                labelField: 'name',
                valueField: 'attrKey'
            }
        },
        label: {
            type: 'string',
            label: '标签文本'
        },
        placeholder: {
            type: 'string',
            label: '占位提示'
        },
        help: {
            type: 'string',
            label: '备注'
        },
        className: {
            type: 'string',
            label: 'class属性'
        },
        require: {
            type: 'selectMultiple',
            label: '限制条件'
        },
        width: {
            type: 'select',
            label: '宽度',
            options: [
                { label: '25%（1/4列）', value: '25%' },
                { label: '33%（1/3列）', value: '33%' },
                { label: '50%（1/2列）', value: '50%' },
                { label: '75%（3/4列）', value: '75%' },
                { label: '100%（整行）', value: '100%' }
            ]
        }
    }
} 