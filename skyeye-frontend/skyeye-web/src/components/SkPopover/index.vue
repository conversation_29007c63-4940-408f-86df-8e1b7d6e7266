<template>
    <div class="sk-popover-wrapper">
        <a-popover v-model:open="open" :title="$slots.title ? undefined : title"
            :content="$slots.content ? undefined : content" :placement="placement" :trigger="trigger"
            :overlayClassName="overlayClassName" :mouseEnterDelay="mouseEnterDelay" :mouseLeaveDelay="mouseLeaveDelay"
            :overlayStyle="overlayStyle" :destroyTooltipOnHide="destroyTooltipOnHide" :align="align"
            :arrowPointAtCenter="arrowPointAtCenter" :autoAdjustOverflow="autoAdjustOverflow"
            @openChange="handleOpenChange">
            <template #title>
                <slot name="title"></slot>
            </template>

            <template #content>
                <slot name="content"></slot>
            </template>

            <span ref="triggerRef" :class="{ 'sk-popover-disabled': disabled }">
                <slot></slot>
            </span>
        </a-popover>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 内容
    content: {
        type: String,
        default: ''
    },
    // 气泡框位置
    placement: {
        type: String,
        default: 'top',
        validator: (value) => [
            'top', 'left', 'right', 'bottom',
            'topLeft', 'topRight', 'bottomLeft', 'bottomRight',
            'leftTop', 'leftBottom', 'rightTop', 'rightBottom'
        ].includes(value)
    },
    // 触发方式
    trigger: {
        type: String,
        default: 'hover',
        validator: (value) => ['hover', 'focus', 'click', 'contextmenu'].includes(value)
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 自定义浮层类名
    overlayClassName: {
        type: String,
        default: ''
    },
    // 鼠标移入后延时多少才显示
    mouseEnterDelay: {
        type: Number,
        default: 0.1
    },
    // 鼠标移出后延时多少才隐藏
    mouseLeaveDelay: {
        type: Number,
        default: 0.1
    },
    // 卡片样式
    overlayStyle: {
        type: Object,
        default: () => ({})
    },
    // 隐藏时是否销毁
    destroyTooltipOnHide: {
        type: Boolean,
        default: false
    },
    // 该值为 true 时，箭头将指向目标元素的中心
    arrowPointAtCenter: {
        type: Boolean,
        default: false
    },
    // 气泡被遮挡时自动调整位置
    autoAdjustOverflow: {
        type: Boolean,
        default: true
    },
    // 对齐方式
    align: {
        type: Object,
        default: () => ({})
    },
    // 受控显示
    modelValue: {
        type: Boolean,
        default: undefined
    }
})

const emit = defineEmits(['update:modelValue', 'openChange'])

const open = ref(false)
const triggerRef = ref(null)

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (val) => {
        if (val !== undefined) {
            open.value = val
        }
    },
    { immediate: true }
)

// 监听 open 变化
watch(open, (val) => {
    emit('update:modelValue', val)
})

// 可见性变化事件
const handleOpenChange = (val) => {
    if (props.disabled) return
    open.value = val
    emit('openChange', val)
}
</script>

<style scoped>
.sk-popover-wrapper {
    display: inline-block;
}

.sk-popover-disabled {
    cursor: not-allowed;
}

:deep(.ant-popover-disabled) {
    cursor: not-allowed;
}

:deep(.ant-popover) {
    max-width: 500px;
}

:deep(.ant-popover-inner-content) {
    max-height: 300px;
    overflow: auto;
}
</style>