<template>
    <div class="workday-row">
        <div class="workday-tags">
            <span v-for="(day, index) in weekDays" :key="index" class="workday-tag" :class="getDayClass(day.value)">
                {{ day.label }}
            </span>
        </div>
    </div>
</template>

<script setup>
import { defineProps, watch, ref } from 'vue'

// 组件属性定义
const props = defineProps({
    type: {
        type: Number,
        required: true
    },
    checkWorkTimeWeekList: {
        type: Array,
        default: () => []
    }
})

// 本地工作日状态
const localWorkDays = ref([])

// 星期几的配置
const weekDays = [
    { label: '一', value: '1' },
    { label: '二', value: '2' },
    { label: '三', value: '3' },
    { label: '四', value: '4' },
    { label: '五', value: '5' },
    { label: '六', value: '6' },
    { label: '日', value: '7' }
]

// 获取工作日标签的样式类名
const getDayClass = (value) => {
    // 查找当前日期的工作状态
    const workDay = localWorkDays.value.find(day => day.value === value)
    if (!workDay) return 'workday-tag rest'

    // 根据工作状态返回对应的样式
    if (workDay.isWork === true) return 'workday-tag work'
    if (workDay.isWork === false) return 'workday-tag rest'
    if (workDay.isWork === 'alternate') return 'workday-tag alternate'

    return 'workday-tag rest'
}

// 处理工作日状态
const handleWorkDayStatus = () => {
    switch (props.type) {
        case 1: // 单休
            resetSingleBreak()
            break
        case 2: // 双休
            resetWeekend()
            break
        case 3: // 单双休
            resetSingleAndDoubleBreak()
            break
        case 4: // 自定义
            resetCustomizeDay()
            break
    }
}

// 单休（周一至周六上班，周日休息）
const resetSingleBreak = () => {
    localWorkDays.value = weekDays.map(day => ({
        ...day,
        isWork: day.value !== '7'
    }))
}

// 大小周（周一至周五工作，周末休息）
const resetWeekend = () => {
    localWorkDays.value = weekDays.map(day => ({
        ...day,
        isWork: !['6', '7'].includes(day.value)
    }))
}

// 单双休
const resetSingleAndDoubleBreak = () => {
    localWorkDays.value = weekDays.map(day => {
        const weekValue = day.value
        let workStatus
        if (weekValue <= '5') {
            workStatus = true
        } else if (weekValue === '6') {
            workStatus = 'alternate'
        } else {
            workStatus = false
        }
        return {
            ...day,
            isWork: workStatus
        }
    })
}

// 自定义工作日
const resetCustomizeDay = () => {
    localWorkDays.value = weekDays.map(day => ({
        ...day,
        isWork: false
    }))

    if (!props.checkWorkTimeWeekList) return

    props.checkWorkTimeWeekList.forEach(workDay => {
        const targetDay = localWorkDays.value.find(day => day.value === String(workDay.weekNumber))
        if (targetDay) {
            targetDay.isWork = workDay.type === 1 ? true : 'alternate'
        }
    })
}

// 监听类型变化
watch(() => props.type, () => {
    handleWorkDayStatus()
}, { immediate: true })
</script>

<style scoped>
</style>
