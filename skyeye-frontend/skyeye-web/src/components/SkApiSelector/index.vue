<template>
    <div class="sk-api-selector">
        <SkInput v-model="inputValue" :placeholder="placeholder" class="api-input">
            <template #suffix>
                <a-dropdown :trigger="['click']" v-if="apiList.length">
                    <LinkOutlined />
                    <template #overlay>
                        <a-menu @click="handleApiSelect">
                            <a-menu-item v-for="api in apiList" :key="api.url">
                                <div class="api-item">
                                    <span class="api-name">{{ api.name }}</span>
                                    <span class="api-url">{{ api.url }}</span>
                                </div>
                            </a-menu-item>
                        </a-menu>
                    </template>
                </a-dropdown>
            </template>
        </SkInput>
    </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { LinkOutlined } from '@ant-design/icons-vue'
import SkInput from '../SkInput/index.vue'

export default defineComponent({
    name: 'sk-api-selector',

    components: {
        LinkOutlined,
        SkInput
    },

    props: {
        modelValue: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入接口地址或选择已有接口'
        }
    },

    emits: ['update:modelValue'],

    setup(props, { emit }) {
        const inputValue = ref(props.modelValue)

        // 模拟API列表数据
        const apiList = ref([
            { name: '轮播图列表', url: '/api/banner/list' },
            { name: '商品列表', url: '/api/goods/list' },
            { name: '分类列表', url: '/api/category/list' }
        ])

        watch(() => props.modelValue, (val) => {
            inputValue.value = val
        })

        watch(inputValue, (val) => {
            emit('update:modelValue', val)
        })

        const handleApiSelect = ({ key }) => {
            inputValue.value = key
        }

        return {
            inputValue,
            apiList,
            handleApiSelect
        }
    }
})
</script>

<style lang="less" scoped>
.sk-api-selector {
    .api-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .api-name {
            font-weight: 500;
        }

        .api-url {
            font-size: 12px;
            color: #999;
        }
    }
}
</style>