<template>
    <div class="sk-tag-wrapper" v-show="modelValue">
        <a-tag :color="color" :closable="closable" :style="tagStyle" @close="handleClose">
            <template v-if="$slots.icon">
                <span class="tag-icon">
                    <slot name="icon"></slot>
                </span>
            </template>
            <slot></slot>
        </a-tag>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    // 标签颜色
    color: {
        type: String,
        default: ''
    },
    // 是否可关闭
    closable: {
        type: Boolean,
        default: false
    },
    // 是否显示标签（v-model）
    modelValue: {
        type: Boolean,
        default: true
    },
    // 标签样式
    tagStyle: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['close', 'update:modelValue'])

// 关闭标签事件
const handleClose = (e) => {
    emit('update:modelValue', false)
    emit('close', e)
}
</script>

<style scoped>
.sk-tag-wrapper {
    display: inline-block;
}

:deep(.ant-tag) {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
    font-size: 14px;
    line-height: 20px;
}

.tag-icon {
    display: inline-flex;
    align-items: center;
    margin-right: 4px;
}

:deep(.anticon) {
    font-size: 14px;
    line-height: 0;
}
</style>