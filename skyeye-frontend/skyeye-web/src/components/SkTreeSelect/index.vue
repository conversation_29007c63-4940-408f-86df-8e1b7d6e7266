<template>
    <div class="sk-tree-select-wrapper">
        <a-tree-select v-if="isEdit === $config.formEditType.isEdit" v-bind="$attrs" :value="modelValue"
            :tree-data="treeData" :disabled="disabled" :show-search="showSearch" :placeholder="placeholder"
            :tree-default-expand-all="treeDefaultExpandAll" :tree-checkable="treeCheckable" :allow-clear="allowClear"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :load-data="loadData"
            :get-popup-container="(triggerNode) => triggerNode.parentNode || document.body" @change="handleChange"
            @search="handleSearch" @select="handleSelect" :field-names="fieldNames">
            <template v-if="$slots.title" #title="titleProps">
                <slot name="title" v-bind="titleProps"></slot>
            </template>
        </a-tree-select>
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                {{ getSelectedLabel }}
            </template>
        </div>
        <div v-if="help" class="sk-tree-select-help">
            <info-circle-outlined />
            {{ help }}
        </div>
    </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

defineOptions({
    name: 'SkTreeSelect'
})

const props = defineProps({
    modelValue: {
        type: [String, Number, Array],
        default: undefined
    },
    treeData: {
        type: Array,
        default: () => []
    },
    disabled: {
        type: Boolean,
        default: false
    },
    showSearch: {
        type: Boolean,
        default: false
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    treeDefaultExpandAll: {
        type: Boolean,
        default: false
    },
    treeCheckable: {
        type: Boolean,
        default: false
    },
    allowClear: {
        type: Boolean,
        default: true
    },
    fieldNames: {
        type: Object,
        default: () => ({
            label: 'title',
            value: 'value',
            children: 'children'
        })
    },
    help: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    loadData: {
        type: Function,
        default: null
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'search', 'select'])

// 获取选中项的标签
const getSelectedLabel = computed(() => {
    const findLabel = (data, value) => {
        for (const item of data) {
            if (item[props.fieldNames.value] === value) return item[props.fieldNames.label]
            if (item[props.fieldNames.children]) {
                const label = findLabel(item[props.fieldNames.children], value)
                if (label) return label
            }
        }
        return ''
    }

    if (!props.modelValue) return ''
    if (Array.isArray(props.modelValue)) {
        return props.modelValue
            .map(value => findLabel(props.treeData, value))
            .filter(Boolean)
            .join('、')
    }
    return findLabel(props.treeData, props.modelValue)
})

// 处理选择变化
const handleChange = (value, label, extra) => {
    emit('update:modelValue', value)
    emit('change', { value, label, extra })
}

// 处理搜索
const handleSearch = (value) => {
    emit('search', value)
}

// 处理选择
const handleSelect = (value, node, extra) => {
    emit('select', { value, node, extra })
}
</script>

<style lang="less" scoped>
.sk-tree-select-wrapper {
    display: inline-flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;

    :deep(.ant-tree-select) {
        width: 100%;
    }

    .sk-tree-select-help {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
    }
}
</style>