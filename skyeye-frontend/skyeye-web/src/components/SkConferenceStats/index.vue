<template>
    <div class="conference-stats">
        <a-collapse>
            <a-collapse-panel key="1" header="会议状态">
                <div class="stats-item">
                    <span class="label">会议时长：</span>
                    <span>{{ formatDuration(stats.duration) }}</span>
                </div>
                <div class="stats-item">
                    <span class="label">参会人数：</span>
                    <span>{{ stats.participants.size }}</span>
                </div>
            </a-collapse-panel>

            <a-collapse-panel key="2" header="网络状态">
                <div class="stats-item">
                    <span class="label">网络质量：</span>
                    <a-tag :color="getNetworkQualityColor()">{{ getNetworkQualityText() }}</a-tag>
                </div>
                <div class="stats-item">
                    <span class="label">上行带宽：</span>
                    <span>{{ formatBandwidth(stats.bandwidth.upload) }}</span>
                </div>
                <div class="stats-item">
                    <span class="label">下行带宽：</span>
                    <span>{{ formatBandwidth(stats.bandwidth.download) }}</span>
                </div>
            </a-collapse-panel>

            <a-collapse-panel key="3" header="视频状态">
                <div class="stats-item">
                    <span class="label">分辨率：</span>
                    <span>{{ stats.videoStats.resolution }}</span>
                </div>
                <div class="stats-item">
                    <span class="label">帧率：</span>
                    <span>{{ stats.videoStats.frameRate }} fps</span>
                </div>
                <div class="stats-item">
                    <span class="label">丢包率：</span>
                    <span>{{ stats.videoStats.packetLoss.toFixed(2) }}%</span>
                </div>
            </a-collapse-panel>

            <a-collapse-panel key="4" header="音频状态">
                <div class="stats-item">
                    <span class="label">比特率：</span>
                    <span>{{ formatBitrate(stats.audioStats.bitrate) }}</span>
                </div>
                <div class="stats-item">
                    <span class="label">丢包率：</span>
                    <span>{{ stats.audioStats.packetLoss.toFixed(2) }}%</span>
                </div>
                <div class="stats-item">
                    <span class="label">音量：</span>
                    <a-progress :percent="stats.audioStats.audioLevel * 100" size="small" />
                </div>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    stats: {
        type: Object,
        required: true
    }
})

// 添加错误处理
const formatDuration = (ms) => {
    try {
        const seconds = Math.floor(ms / 1000)
        const minutes = Math.floor(seconds / 60)
        const hours = Math.floor(minutes / 60)
        return `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
    } catch (error) {
        return '00:00:00'
    }
}

// 格式化带宽
const formatBandwidth = (bps) => {
    if (bps < 1000000) {
        return `${(bps / 1000).toFixed(1)} Kbps`
    }
    return `${(bps / 1000000).toFixed(1)} Mbps`
}

// 格式化比特率
const formatBitrate = (bps) => {
    return `${(bps / 1000).toFixed(0)} kbps`
}

// 获取网络质量颜色
const getNetworkQualityColor = () => {
    try {
        const { good = 0, medium = 0, poor = 0 } = props.stats.networkQuality || {}
        const total = good + medium + poor
        if (!total) return 'gray'
        if (poor / total > 0.3) return 'red'
        if (medium / total > 0.5) return 'orange'
        return 'green'
    } catch (error) {
        return 'gray'
    }
}

// 获取网络质量文本
const getNetworkQualityText = () => {
    const color = getNetworkQualityColor()
    switch (color) {
        case 'red': return '差'
        case 'orange': return '一般'
        case 'green': return '良好'
    }
}

// 添加默认值处理
const safeStats = computed(() => ({
    duration: props.stats?.duration || 0,
    participants: props.stats?.participants || new Map(),
    networkQuality: props.stats?.networkQuality || { good: 0, medium: 0, poor: 0 },
    bandwidth: props.stats?.bandwidth || { upload: 0, download: 0 },
    videoStats: props.stats?.videoStats || {
        resolution: 'N/A',
        frameRate: 0,
        packetLoss: 0,
        jitter: 0
    },
    audioStats: props.stats?.audioStats || {
        bitrate: 0,
        packetLoss: 0,
        jitter: 0,
        audioLevel: 0
    }
}))
</script>

<style lang="less" scoped>
.conference-stats {
    padding: 16px;

    .stats-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .label {
            width: 80px;
            color: #666;
        }
    }
}
</style>