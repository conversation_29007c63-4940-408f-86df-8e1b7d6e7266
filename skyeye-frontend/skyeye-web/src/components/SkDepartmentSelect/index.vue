<template>
    <div class="sk-department-select">
        <SkTreeSelect v-if="isEdit == $config.formEditType.isEdit" v-model="selectedValue" :tree-data="treeData"
            :field-names="fieldNames" @change="handleChange" />
        <div v-else>
            {{ showValue?.name }}
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import SkTreeSelect from '@/components/SkTreeSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: String,
        default: () => ''
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },

    formData: {},
})

const emit = defineEmits(['update:modelValue', 'change'])

// 树形数据
const treeData = ref([])
// 选中值
const selectedValue = ref(props.modelValue)
// 显示值
const showValue = ref({})

// 字段映射
const fieldNames = {
    label: 'name',
    value: 'id',
    children: 'children'
}

// 监听选中值变化
watch(selectedValue, (value) => {
    emit('update:modelValue', value)
    emit('change', value, props.attrKey)
})

// 选择变化处理
const handleChange = ({ value }) => {
    selectedValue.value = value
}

// 获取部门树数据
const getDepartmentTree = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().reqBasePath + 'queryDepartmentListByCurrentUserBelong'
        )
        const tree = proxy.$util.listToTree(res.rows || [], {
            parentId: 'pId',
        })
        treeData.value = tree
    } catch (error) {
        proxy.$message.error('获取部门数据失败')
    }
}

// 组件挂载时获取数据
onMounted(() => {
    if (props.isEdit == $config.formEditType.isEdit) {
        if (props.pageType == proxy.$config.pageType.ADD) {
            selectedValue.value = proxy.$config.getCurrentUser()?.departmentId;
        }
        getDepartmentTree()
    } else {
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation
    }
})
</script>

<style scoped>
.sk-department-select {
    width: 100%;
}
</style>