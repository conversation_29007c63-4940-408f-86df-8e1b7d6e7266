<template>
    <a-row :gutter="gutter" :justify="justify" :align="align" :wrap="wrap">
        <slot></slot>
    </a-row>
</template>

<script setup>
defineProps({
    // 栅格间隔
    gutter: {
        type: [Number, Object, Array],
        default: 0
    },
    // 水平排列方式
    justify: {
        type: String,
        default: 'start'
    },
    // 垂直对齐方式
    align: {
        type: String,
        default: 'top'
    },
    // 是否自动换行
    wrap: {
        type: Boolean,
        default: true
    }
})
</script>