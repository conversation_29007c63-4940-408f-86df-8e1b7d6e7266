<template>
    <div class="sk-member-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else>
            {{ showValue.name }}
        </div>

        <!-- 会员选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择会员" width="70%" :bodyStyle="{ height: '80vh' }">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <!-- 提示信息 -->
                    <div class="tip-info">
                        <div class="tip-title">会员选择规则：</div>
                        <div>1. 单选，点击选择按钮即可选中</div>
                        <div>2. 只有启用状态的会员可以选择</div>
                        <div>如没有查到要选择的会员，请检查会员信息是否满足当前规则。</div>
                    </div>

                    <!-- 搜索区域 -->
                    <div class="table-search">
                        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                            :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                            <template #submitIcon>
                                <search-outlined />
                            </template>
                            <a-form-item name="keyword">
                                <SkInput v-model="searchForm.keyword" placeholder="请输入联系电话" allowClear />
                            </a-form-item>
                        </SkForm>
                    </div>

                    <!-- 表格区域 -->
                    <SkTable :tipInfoHeight="56"  ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                        :ready="tableReady" :pagination="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'choose'">
                                <a-button type="link" size="small" @click.stop="handleSelect(record)"
                                    :disabled="record.enabled === 2">
                                    选择
                                </a-button>
                            </template>
                            <template v-if="column.dataIndex === 'enabled'">
                                <div
                                    v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['commonEnable'], 'id', record.enabled, 'name')">
                                </div>
                            </template>
                        </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择会员'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})

const tableReady = ref(false)

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '会员称呼',
        dataIndex: 'name',
        width: 140,
        fixed: 'left'
    },
    {
        title: '联系电话',
        dataIndex: 'phone',
        width: 100,
        align: 'center'
    },
    {
        title: '电子邮箱',
        dataIndex: 'email',
        width: 120,
    },
    {
        title: '地址',
        dataIndex: 'address',
        width: 100,
    },
    {
        title: '状态',
        dataIndex: 'enabled',
        width: 80,
        align: 'center'
    }
]

// 分页配置
const pagination = reactive(proxy.$config.pagination({
    pageSize: 8,
    pageSizeOptions: [8, 16, 24, 32, 40, 48, 56]
}))

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['commonEnable']);
    initEnumData.value = result
}

// 获取会员列表
const getMemberList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 8),
            ...searchForm
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().shopBasePath + 'member001',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取会员列表失败')
    } finally {
        loading.value = false
    }
}

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getMemberList()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 8)
    }
    getMemberList()
}

// 处理选择
const handleSelect = (record) => {
    if (record.enabled === 2) {
        return
    }
    showValue.value = record
    if (props.attrKey == 'holderId') {
        emit('update:modelValue', {
            holderId: record.id,
            holderKey: record.serviceClassName || 'com.skyeye.service.impl.MemberServiceImpl'
        });
    } else {
        emit('update:modelValue', {
            objectId: record.id,
            objectKey: record.serviceClassName || 'com.skyeye.service.impl.MemberServiceImpl'
        });
    }
    emit('change', record, props.attrKey)
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    if (props.pageType == proxy.$config.pageType.EDIT) {
        // 详情页面和编辑页面都会走这里
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation || {}
        if (mation?.id) {
            const idKey = proxy.$util.getKeyIdToKey(props.attrKey)
            const serviceClassName = props.formData[idKey]
            if (props.attrKey == 'holderId') {
                emit('update:modelValue', {
                    holderId: showValue.value.id,
                    holderKey: serviceClassName || 'com.skyeye.service.impl.MemberServiceImpl'
                });
            } else {
                emit('update:modelValue', {
                    objectId: showValue.value.id,
                    objectKey: serviceClassName || 'com.skyeye.service.impl.MemberServiceImpl'
                });
            }
        }
    }
    if (props.isEdit == proxy.$config.formEditType.isEdit) {
        getInitData()
    }
})
</script>

<style scoped>
.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}

.tip-info {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 2px;
}

.tip-info .tip-title {
    font-weight: bold;
    margin-bottom: 8px;
}

.tip-info div {
    line-height: 1.5;
}
</style>
