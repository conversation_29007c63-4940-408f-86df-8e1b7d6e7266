<template>
    <div class="modal-content">
        <!-- 步骤条 -->
        <div class="step-container">
            <SkSteps v-model:current="currentStep" :steps="steps" :showActions="false">
                <template #content="{ current }">
                    <div class="step-content">
                        <!-- 第一步：选择商品 -->
                        <div v-if="current === 0">
                            <div class="container-manage">
                                <div class="left-tree">
                                    <SkCard title="商品分类" :bordered="false">
                                        <div class="list-tree">
                                            <SkDict v-model="treeSelectData" objectType="ERP_MATERIAL_CATEGORY"
                                                showType="selectTree" @change="handleSearch" />
                                        </div>
                                    </SkCard>
                                </div>

                                <div class="right-content">
                                    <SkCard ref="cardRef" :bordered="false">
                                        <!-- 搜索区域 -->
                                        <div class="table-search">
                                            <SkForm layout="inline" v-model="searchForm" @submit="handleSearch"
                                                @reset="handleSearch" :submitText="$t('common.search')"
                                                :resetText="$t('common.reset')" :isFormSubmit="false">
                                                <template #submitIcon>
                                                    <SearchOutlined />
                                                </template>
                                                <a-form-item name="keyword">
                                                    <SkInput v-model="searchForm.keyword" placeholder="请输入商品名称、型号"
                                                        allowClear />
                                                </a-form-item>
                                            </SkForm>
                                        </div>

                                        <!-- 表格区域 -->
                                        <SkTable ref="tableRef" :columns="columns" :data-source="tableData"
                                            :loading="loading" :ready="tableReady" :pagination="pagination"
                                            :row-selection="{
                                                selectedRowKeys: selectedRowKeys,
                                                onChange: onSelectChange,
                                                type: 'checkbox'
                                            }" @change="handleTableChange">
                                            <template #bodyCell="{ column, record }">
                                                <template v-if="column.dataIndex === 'categoryId'">
                                                    {{
                                                        initDictData['ERP_MATERIAL_CATEGORY']?.[record.categoryId]
                                                    }}
                                                </template>
                                                <template v-if="column.dataIndex === 'fromType'">
                                                    <div
                                                        v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialFromType'], 'id', record.fromType, 'name')">
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'type'">
                                                    <div
                                                        v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialType'], 'id', record.type, 'name')">
                                                    </div>
                                                </template>
                                                <template v-if="column.dataIndex === 'unit'">
                                                    <div
                                                        v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialUnit'], 'id', record.unit, 'name')">
                                                    </div>
                                                </template>
                                            </template>
                                        </SkTable>
                                    </SkCard>
                                </div>
                            </div>
                            <div class="step-footer">
                                <SkSpace>
                                    <SkButton type="primary" @click.prevent="nextStep">下一步</SkButton>
                                </SkSpace>
                            </div>
                        </div>

                        <!-- 第二步：选择规格 -->
                        <div v-if="current === 1">
                            <SkForm ref="formRef" :model="formState" :showButtons="false">
                                <SkTable :columns="specColumns" :data-source="selectedMaterials" :pagination="false">
                                    <template #bodyCell="{ column, record }">
                                        <template v-if="column.key === 'type'">
                                            <SkSelect v-model="record.fromType" :options="fromTypeOptions" disabled>
                                                <template #option="{ value }">
                                                    {{ value === 1 ? '自产' : value === 2 ? '外购' : '' }}
                                                </template>
                                            </SkSelect>
                                        </template>
                                        <template v-else-if="column.key === 'norms'">
                                            <SkSelect v-model="record.normsId"
                                                :options="formatNormsOptions(record.materialNorms)" placeholder="请选择"
                                                @change="(value) => handleNormsChange(value, record)" />
                                        </template>
                                        <template v-else-if="column.key === 'bom'">
                                            <SkSelect v-model="record.bomId" :options="record.bomList"
                                                placeholder="请选择" />
                                        </template>
                                    </template>
                                </SkTable>
                            </SkForm>
                            <div class="step-footer">
                                <SkSpace>
                                    <SkButton @click.prevent="prevStep">上一步</SkButton>
                                    <SkButton type="primary" @click.prevent="handleConfirm">确定</SkButton>
                                </SkSpace>
                            </div>
                        </div>
                    </div>
                </template>
            </SkSteps>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import SkCard from '@/components/SkCard/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkDict from '@/components/SkDict/index.vue'
import SkSteps from '@/components/SkSteps/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 定义组件的属性和事件
const props = defineProps({
    modelValue: {
        type: [String, Object, Array],
        default: () => []
    },
})

// 定义组件的事件
const emit = defineEmits(['confirm', 'cancel', 'close', 'update:modelValue'])

const currentStep = ref(0)

// 步骤配置
const steps = [
    {
        title: '选择商品'
    },
    {
        title: '选择规格'
    }
]

// 步骤控制
const selectedMaterials = ref([])

// 表格配置
const columns = [
    { title: '商品名称', dataIndex: 'name', key: 'name', width: 150 },
    { title: '型号', dataIndex: 'model', key: 'model', width: 150 },
    { title: '所属分类', dataIndex: 'categoryId', key: 'categoryId', width: 100 },
    { title: '产品来源', dataIndex: 'fromType', key: 'fromType', width: 100 },
    { title: '产品类型', dataIndex: 'type', key: 'type', width: 100 },
    { title: '产品规格类型', dataIndex: 'unit', key: 'unit', width: 100 }
]

const specColumns = [
    { title: '商品名称', dataIndex: 'name', key: 'name' },
    { title: '型号', dataIndex: 'model', key: 'model' },
    { title: '商品来源', dataIndex: 'type', key: 'type' },
    { title: '规格选择', dataIndex: 'norms', key: 'norms' },
    { title: 'BOM方案选择', dataIndex: 'bom', key: 'bom' }
]

const formState = reactive({
    materials: {}
})
const selectedRowKeys = ref([])
const loading = ref(false)
const tableData = ref([])
const tableReady = ref(false)
const searchForm = reactive({ keyword: '' })
const pagination = reactive(proxy.$config.pagination())
const treeSelectData = ref('')
const initDictData = ref({})
const initEnumData = ref({})
const formRef = ref(null)

// 商品来源选项
const fromTypeOptions = [
    { label: '自产', value: 1 },
    { label: '外购', value: 2 }
]

// 获取初始化数据
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['materialFromType', 'materialType', 'materialUnit'])
    initEnumData.value = enumResult
    let dictResult = await proxy.$util.getDictListMapByCode(['ERP_MATERIAL_CATEGORY'])
    initDictData.value = dictResult
}

// 获取商品列表
const getMaterialList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            ...searchForm,
            categoryId: treeSelectData.value
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'material010',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取商品列表失败')
    } finally {
        loading.value = false
    }
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getMaterialList()
}

// 格式化规格选项
const formatNormsOptions = (norms) => {
    if (!norms || !Array.isArray(norms)) return []
    return norms.map(item => ({
        label: item.name,
        value: item.id
    }))
}

// 处理规格变化
const handleNormsChange = async (value, record) => {
    // 直接更新记录的 normsId
    record.normsId = value

    // 更新表单状态
    if (!formState.materials[record.id]) {
        formState.materials[record.id] = {}
    }
    formState.materials[record.id].normsId = value

    // 清空 BOM 列表
    record.bomList = []
    record.bomId = undefined

    if (!value) return

    try {
        // 请求 BOM 列表数据
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryBomListByNormsId',
            { normsId: value }
        )

        // 转换数据格式
        record.bomList = Array.isArray(res) ? res.map(item => ({
            label: item.name,
            value: item.id
        })) : []

    } catch (error) {
        SkMessage.error('获取BOM列表失败')
    }
}

// 处理选择变化
const onSelectChange = (keys, rows) => {
    selectedRowKeys.value = keys
    selectedMaterials.value = rows
    console.log('selectedMaterials', selectedMaterials.value)

    // 初始化表单状态
    selectedMaterials.value.forEach(item => {
        if (!item.materialNorms) {
            item.materialNorms = []
        }
        // 初始化该行的规格状态
        if (!formState.materials[item.id]) {
            formState.materials[item.id] = {}
        }
        formState.materials[item.id].normsId = item.normsId || undefined
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getMaterialList()
}

// 步骤控制
const nextStep = () => {
    if (selectedMaterials.value.length === 0) {
        message.error('请选择商品')
        return
    }
    currentStep.value++
}

const prevStep = () => {
    currentStep.value--
}

// 取消选择
// const handleCancel = () => {
//     emit('cancel')

//     // 重置状态
//     selectedRowKeys.value = []
//     selectedMaterials.value = []
//     currentStep.value = 0
//     searchForm.keyword = ''
//     treeSelectData.value = ''
//     tableData.value = []
//     pagination.current = 1
//     pagination.total = 0
//     formState.materials = {}
// }

// 确认选择
const handleConfirm = async () => {
    try {
        // 检查是否有选择规格
        let checkTable = false

        // 构建提交数据
        const proList = []

        // 遍历选中的商品
        for (const item of selectedMaterials.value) {
            // 添加到提交列表
            proList.push({
                materialId: item.id,
                bomId: item.bomId || '',
                normsId: item.normsId || ''
            })

            // 检查规格是否已选择
            if (!item.normsId) {
                checkTable = true
                message.error('请选择商品规格')
                return
            }
        }

        // 如果所有规格都已选择，则提交数据
        if (!checkTable) {
            const params = {
                proList: JSON.stringify(proList)
            }
            
            try {
                // 发送请求
                const res = await proxy.$http.post(
                    proxy.$config.getConfig().erpBasePath + 'material015',
                    params
                )
                
                // 发送确认事件
                if (res && res.rows) {
                    console.log('发送数据到父组件:', res.rows)
                    emit('confirm', res.rows)
                    
                    // 更新 modelValue
                    emit('update:modelValue', res.rows)
                } else {
                    console.error('API返回数据格式不正确:', res)
                    message.error('返回数据格式不正确')
                }
                
                // 显示成功提示
                message.success('保存成功')

                // 通知父组件关闭弹窗
                emit('close')

                // 清空选择状态
                selectedRowKeys.value = []
                selectedMaterials.value = []
                currentStep.value = 0

                // 清空表单状态
                formState.materials = {}
            } catch (apiError) {
                console.error('API请求失败:', apiError)
                message.error('保存失败: ' + (apiError.message || '未知错误'))
            }
        }
    } catch (error) {
        console.error('保存失败:', error)
        message.error('保存失败: ' + (error.message || '未知错误'))
    }
}

// 组件挂载时初始化
onMounted(() => {
    getInitData()
    tableReady.value = true
    handleSearch()
})
</script>

<style scoped>
.modal-content {
    min-height: 400px;
}

.step-container {
    margin-bottom: 24px;
}

.step-content {
    min-height: 200px;
    margin-top: 24px;
}

.step-footer {
    margin-top: 24px;
    text-align: right;
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
}

.container-manage {
    display: flex;
    gap: 20px;
}

.left-tree {
    flex: 1;
}

.right-content {
    flex: 2;
}

.list-tree {
    padding: 8px;
    height: calc(100% - 48px);
    overflow: auto;
}

:deep(.ant-card-body) {
    padding: 0;
}
</style>
