<template>
    <div class="material-choose">
        <!-- 选择子件按钮 (仅在可编辑模式下显示) -->
        <div class="choose-btn" v-if="isEdit">
            <SkButton type="primary" @click.prevent="showChooseModal">
                <template #icon>
                    <PlusOutlined />
                </template>
                选择子件
            </SkButton>
        </div>

        <!-- 已选商品表格 -->
        <div class="material-table">
            <SkTable :columns="selectedColumns" :data-source="selectedList" :pagination="false" bordered>
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.key === 'action'">
                        <SkButton type="link" danger @click.prevent="removeMaterial(record)">删除</SkButton>
                    </template>
                    <template v-if="column.key === 'craft'">
                        <SkSelect 
                            v-model:value="record.craftId" 
                            :options="craftOptions"
                            placeholder="请选择工艺"
                            style="width: 100%"
                            @change="(value) => handleCraftChange(value, record, index)"
                        />
                    </template>
                    <template v-if="column.key === 'quantity'">
                        <SkInputNumber 
                            v-model:value="record.quantity" 
                            :min="1" 
                            style="width: 100%"
                            @change="(value) => handleQuantityChange(value, record, index)"
                        />
                    </template>
                    <template v-if="column.key === 'remark'">
                        <SkInput 
                            v-model:value="record.remark" 
                            placeholder="请输入备注"
                            style="width: 100%"
                            @change="(e) => handleRemarkChange(e, record, index)"
                        />
                    </template>
                </template>
            </SkTable>
        </div>

        <!-- 选择商品弹窗 -->
        <SkModal v-model="modalVisible" title="选择商品" width="80%" :bodyStyle="{ height: '80vh' }" @cancel="handleCancel">
            <SelectMaterials @confirm="handleMaterialsConfirm" @cancel="handleCancel" @close="closeModal" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, getCurrentInstance, watch, onMounted, computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkButton from '@/components/SkButton/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkInputNumber from '@/components/SkInputNumber/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SelectMaterials from './selectMaterials.vue'
import SkModal from '@/components/SkModal/index.vue'

const { proxy } = getCurrentInstance()

// 定义组件的属性和事件
const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    // 添加编辑模式属性
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

// 定义组件的事件
const emit = defineEmits(['update:modelValue', 'change'])

// 已选商品列表
const selectedList = ref([])

// 弹窗可见性
const modalVisible = ref(false)

// 工艺选项
const craftOptions = ref([])

// 根据编辑模式动态计算列配置
const selectedColumns = computed(() => {
    const columns = [
        { title: '商品名称', dataIndex: 'name', key: 'name', width: 150 },
        { title: '规格', dataIndex: 'model', key: 'model', width: 150 },
        { title: '工艺', dataIndex: 'craft', key: 'craft', width: 150 },
        { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100 },
        { title: '单价', dataIndex: 'price', key: 'price', width: 100 },
        { title: '金额', dataIndex: 'amount', key: 'amount', width: 100 },
        { title: '商品来源', dataIndex: 'fromType', key: 'fromType', width: 100, 
          customRender: ({ text }) => text === 1 ? '自产' : text === 2 ? '外购' : '' },
        { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 }
    ]
    
    // 仅在可编辑模式下添加操作列
    if (props.isEdit) {
        columns.push({ title: '操作', key: 'action', width: 80, fixed: 'right' })
    }
    
    return columns
})

// 监听 modelValue 变化
watch(() => props.modelValue,
    (newVal) => {
        if (newVal) {
            console.log('newVal', newVal)
            // 处理回显数据
            if (Array.isArray(newVal) && newVal.length > 0) {
                // 检查是否是BOM子件数据格式
                if (newVal[0].materialMation || newVal[0].normsMation) {
                    // 转换数据格式以适应表格
                    const formattedMaterials = newVal.map(item => {
                        return {
                            id: item.id || item.materialMation.id,
                            materialId: item.materialId || item.materialMation.id,
                            name: item.materialMation.name || '',
                            model: item.materialMation.model || '',
                            fromType: item.materialMation.fromType || 1,
                            normsId: item.normsId || item.normsMation.id || '',
                            bomId: item.bomId || '',
                            quantity: item.needNum || 1, // 使用需求数量
                            price: item.normsMation.retailPrice || item.allPrice || 0,
                            amount: (item.normsMation.retailPrice || item.allPrice || 0) * (item.needNum || 1),
                            remark: item.materialMation.remark || '',
                            craftId: item.wayProcedureMation.id, // 工艺ID
                            craft: item.wayProcedureMation.name // 工艺名称
                        }
                    })
                    console.log('formattedMaterials', formattedMaterials)
                    selectedList.value = formattedMaterials
                } else {
                    // 如果是普通数据格式，直接使用
                    selectedList.value = newVal
                }
            } else {
                selectedList.value = []
            }
            
            console.log('处理后的selectedList.value', selectedList.value)
        }
    },
    { immediate: true, deep: true }
)

// 加载工艺数据
const loadCraftData = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryAllPublishProcedureList'
        )
        
        // 检查响应格式，提取 rows 数组
        if (res && res.rows && Array.isArray(res.rows)) {
            // 转换数据格式为下拉框选项
            craftOptions.value = res.rows.map(item => ({
                label: item.name,
                value: item.id
            }))
        } else {
            console.warn('工艺数据返回格式不正确:', res)
            craftOptions.value = [] // 确保是空数组
        }
    } catch (error) {
        console.error('加载工艺数据失败:', error)
        craftOptions.value = [] // 确保是空数组
    }
}

// 处理工艺变化
const handleCraftChange = (value, record, index) => {
    // 更新工艺ID
    record.craftId = value
    
    // 更新工艺名称
    const selectedCraft = craftOptions.value.find(item => item.value === value)
    record.craft = selectedCraft ? selectedCraft.label : ''
    
    // 通知父组件数据已更新
    emit('update:modelValue', selectedList.value)
    emit('change', selectedList.value)
}

// 处理数量变化
const handleQuantityChange = (value, record, index) => {
    // 更新数量
    record.quantity = value
    
    // 计算金额
    record.amount = (record.price || 0) * value
    
    // 通知父组件数据已更新
    emit('update:modelValue', selectedList.value)
    emit('change', selectedList.value)
}

// 处理备注变化
const handleRemarkChange = (e, record, index) => {
    // 更新备注
    record.remark = e.target.value
    
    // 通知父组件数据已更新
    emit('update:modelValue', selectedList.value)
    emit('change', selectedList.value)
}

// 处理材料选择确认
const handleMaterialsConfirm = async (materials) => {
    // 确保工艺数据已加载
    if (craftOptions.value.length === 0) {
        await loadCraftData()
    }
    
    if (Array.isArray(materials)) {
        // 转换数据格式以适应表格
        const formattedMaterials = materials.map(item => {
            // 从 materialMation 和 normsMation 中提取数据
            const materialData = item.materialMation || {}
            const normsData = item.normsMation || {}
            
            return {
                id: item.id || materialData.id,
                materialId: materialData.id,
                name: materialData.name,
                model: materialData.model,
                fromType: materialData.fromType,
                normsId: item.normsId || normsData.id,
                bomId: item.bomId,
                // 其他可能需要的字段
                quantity: item.needNum || 1, // 使用需求数量
                price: normsData.retailPrice || item.allPrice || 0,
                amount: (normsData.retailPrice || item.allPrice || 0) * (item.needNum || 1),
                remark: materialData.remark || '',
                craftId: '', // 工艺ID
                craft: '' // 工艺名称
            }
        })

        selectedList.value = formattedMaterials
        
        // 通知父组件数据已更新
        emit('update:modelValue', JSON.stringify(selectedList.value))
        emit('change', selectedList.value)
        console.log(JSON.stringify(selectedList.value))
       
    }
}

// 显示选择弹窗
const showChooseModal = async () => {
    // 确保工艺数据已加载
    if (craftOptions.value.length === 0) {
        await loadCraftData()
    }
    
    modalVisible.value = true
}

// 取消选择
const handleCancel = () => {
    modalVisible.value = false
}

// 移除已选商品
const removeMaterial = (record) => {
    selectedList.value = selectedList.value.filter(item => item.id !== record.id)
    
    // 通知父组件数据已更新
    emit('update:modelValue',JSON.stringify(selectedList.value))
    emit('change', selectedList.value)
    console.log(JSON.stringify(selectedList.value))
}

// 关闭弹窗
const closeModal = () => {
    modalVisible.value = false
}

// 组件挂载时加载工艺数据
onMounted(() => {
    loadCraftData()
})
</script>

<style scoped>
.material-choose {
    padding: 20px;
}

.choose-btn {
    margin-bottom: 16px;
}

.material-container {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
}

.material-table {
    flex: 1;
}
</style>
