<template>
    <div class="sk-list-wrapper">
        <a-list class="sk-list" :grid="grid" :data-source="dataSource" :loading="loading" :pagination="pagination"
            :bordered="bordered" :split="split">
            <!-- 列表项 -->
            <template #renderItem="{ item }">
                <a-list-item>
                    <slot name="item" :item="item">
                        {{ item }}
                    </slot>
                </a-list-item>
            </template>

            <!-- 空状态 -->
            <template v-if="$slots.empty" #empty>
                <slot name="empty">
                    <a-empty />
                </slot>
            </template>

            <!-- 加载更多 -->
            <template v-if="$slots.loadMore" #loadMore>
                <slot name="loadMore" />
            </template>

            <!-- 头部 -->
            <template v-if="$slots.header" #header>
                <slot name="header" />
            </template>

            <!-- 底部 -->
            <template v-if="$slots.footer" #footer>
                <slot name="footer" />
            </template>
        </a-list>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    // 数据源
    dataSource: {
        type: Array,
        default: () => []
    },
    // 是否显示加载状态
    loading: {
        type: Boolean,
        default: false
    },
    // 分页配置
    pagination: {
        type: [Object, Boolean],
        default: false
    },
    // 列表栅格配置
    column: {
        type: Number,
        default: 1
    },
    // 是否显示边框
    bordered: {
        type: Boolean,
        default: false
    },
    // 是否显示分割线
    split: {
        type: Boolean,
        default: true
    }
});

// 计算栅格配置
const grid = computed(() => {
    if (props.column <= 1) return null;
    return {
        gutter: 16,
        column: props.column,
        xs: 1,
        sm: props.column > 2 ? 2 : props.column,
        md: props.column > 3 ? 3 : props.column,
        lg: props.column,
        xl: props.column
    };
});
</script>

<style scoped></style>