<template>
    <div class="sk-ueditor">
        <div v-if="isEdit === $config.formEditType.isEdit">
            <Toolbar class="editor-toolbar" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
            <Editor style="height: 350px;" class="editor-wrapper" v-model="content" :defaultConfig="editorConfig"
                :mode="mode" @onCreated="handleCreated" />
        </div>
        <div v-else v-html="content"></div>
    </div>
</template>

<script setup>
import { shallowRef, getCurrentInstance, onBeforeUnmount, computed } from 'vue'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    config: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const content = computed({
    set(newHtml) {
        if (newHtml?.replace(/<[^>]*>/g, '').trim() === '') {
            emit('update:modelValue', '')
            return
        }
        // 双向绑定
        emit('update:modelValue', proxy.$util.filterImageBasePath(newHtml))
    },
    get() {
        if (props.modelValue?.replace(/<[^>]*>/g, '').trim() === '') {
            return ''
        }
        return proxy.$util.filterImage(props.modelValue)
    }
})
const mode = 'default'

// 工具栏配置
const toolbarConfig = {
    toolbarKeys: [
        // 基础样式
        'bold',
        'italic',
        'underline',
        'through',
        '|',
        // 字号和字体
        'fontSize',
        'fontFamily',
        '|',
        // 文字颜色和背景色
        'color',
        'bgColor',
        '|',
        // 对齐方式
        'indent',
        'delIndent',
        'headerSelect',
        '|',
        // 列表
        'bulletedList',
        'numberedList',
        '|',
        // 插入功能
        'uploadImage',
        'uploadVideo',
        'insertTable',
        'insertLink',
        {
            key: 'group-more-style',
            title: '更多',
            iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
            menuKeys: [
                'blockquote',
                'codeBlock',
                'undo',
                'redo',
                'fullScreen'
            ]
        }
    ]
}

// 编辑器配置
const editorConfig = {
    placeholder: '请输入内容...',
    autoFocus: false,
    height: 350,
    MENU_CONF: {
        uploadImage: {
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024,
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            // 自定义上传参数
            async customUpload(file, insertFn) {
                try {
                    const formData = new FormData()
                    formData.append('file', file)
                    formData.append('type', '36')

                    const res = await proxy.$http.post(proxy.$config.getConfig().reqBasePath + 'common003', formData)
                    // 获取文件访问路径
                    insertFn(proxy.$config.getConfig().fileBasePath + res.bean.picUrl, file.name)
                } catch (error) {
                    proxy.$message.error('图片上传失败')
                }
            }
        },
        uploadVideo: {
            fieldName: 'file',
            maxFileSize: 100 * 1024 * 1024, // 最大100M
            maxNumberOfFiles: 1,
            allowedFileTypes: ['.mp4', '.avi', '.mov'],
            // 自定义上传参数
            async customUpload(file, insertFn) {
                try {
                    const formData = new FormData()
                    formData.append('file', file)
                    formData.append('type', '36')

                    const res = await proxy.$http.post(proxy.$config.getConfig().reqBasePath + 'common003', formData)
                    // 获取文件访问路径并插入视频
                    const videoUrl = proxy.$config.getConfig().fileBasePath + res.bean.picUrl
                    insertFn(videoUrl)
                } catch (error) {
                    proxy.$message.error('视频上传失败')
                }
            }
        },
        insertTable: {
            maxRow: 20,
            maxCol: 6
        },
        fontSize: {
            options: ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px']
        }
    },
    ...props.config
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

// 初始化完成时的回调
const handleCreated = (editor) => {
    editorRef.value = editor
}
</script>

<style scoped>
.sk-ueditor {
    width: 100%;
    border: 1px solid #ccc;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    z-index: 6;
}

.editor-toolbar {
    border-bottom: 1px solid #ccc;
    box-sizing: border-box;
}

.editor-wrapper {
    overflow-y: auto;
}

:deep(.sk-detail-readonly img) {
    max-width: 100% !important;
}
</style>