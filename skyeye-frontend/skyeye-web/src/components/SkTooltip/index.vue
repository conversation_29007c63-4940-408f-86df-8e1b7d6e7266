<template>
    <div class="sk-tooltip-wrapper" :class="{ 'sk-tooltip-disabled': disabled }">
        <a-tooltip v-model:open="open" :placement="placement" :color="color" :mouse-enter-delay="mouseEnterDelay"
            :mouse-leave-delay="mouseLeaveDelay" :overlay-style="overlayStyle" :overlay-class-name="overlayClassName"
            :arrow-point-at-center="arrowPointAtCenter" :destroy-tooltip-on-hide="destroyTooltipOnHide"
            @openChange="handleOpenChange">
            <!-- 使用 title 插槽或 title 属性 -->
            <template #title>
                <slot name="title">{{ title }}</slot>
            </template>

            <!-- 默认插槽 -->
            <slot></slot>
        </a-tooltip>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    // 提示文字
    title: {
        type: String,
        default: ''
    },
    // 气泡框位置
    placement: {
        type: String,
        default: 'top'
    },
    // 背景颜色
    color: {
        type: String,
        default: ''
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 鼠标移入后延时多少才显示
    mouseEnterDelay: {
        type: Number,
        default: 0.1
    },
    // 鼠标移出后延时多少才隐藏
    mouseLeaveDelay: {
        type: Number,
        default: 0.1
    },
    // 卡片样式
    overlayStyle: {
        type: Object,
        default: () => ({})
    },
    // 卡片类名
    overlayClassName: {
        type: String,
        default: ''
    },
    // 箭头是否指向目标元素中心
    arrowPointAtCenter: {
        type: Boolean,
        default: false
    },
    // 隐藏时是否销毁
    destroyTooltipOnHide: {
        type: Boolean,
        default: false
    },
    // 受控显示
    modelValue: {
        type: Boolean,
        default: undefined
    }
})

const emit = defineEmits(['update:modelValue', 'openChange'])

const open = ref(false)

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (val) => {
        if (val !== undefined) {
            open.value = val
        }
    },
    { immediate: true }
)

// 监听 open 变化
watch(open, (val) => {
    emit('update:modelValue', val)
})

// 显示状态变化事件
const handleOpenChange = (val) => {
    if (props.disabled) return
    open.value = val
    emit('openChange', val)
}
</script>

<style scoped>
.sk-tooltip-wrapper {
    display: inline-block;
}

.sk-tooltip-disabled {
    cursor: not-allowed;
}

:deep(.ant-tooltip) {
    max-width: 500px;
}

:deep(.ant-tooltip-inner) {
    min-height: 32px;
    padding: 6px 12px;
    color: #fff;
    text-align: left;
    text-decoration: none;
    word-wrap: break-word;
    background-color: rgba(0, 0, 0, 0.75);
    border-radius: 2px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

:deep(.ant-tooltip-arrow) {
    position: absolute;
    display: block;
    width: 13px;
    height: 13px;
    overflow: hidden;
    background: transparent;
    pointer-events: none;
}
</style>