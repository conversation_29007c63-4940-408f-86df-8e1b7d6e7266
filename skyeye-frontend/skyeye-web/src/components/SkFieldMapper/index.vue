<template>
    <div class="sk-field-mapper">
        <div v-for="(field, key) in config" :key="key" class="field-item">
            <div class="field-label">{{ field.label }}</div>
            <SkInput v-model="mappings[key]" :placeholder="field.placeholder" @change="handleChange" />
        </div>
    </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import SkInput from '../SkInput/index.vue'

export default defineComponent({
    name: 'sk-field-mapper',

    components: {
        SkInput
    },

    props: {
        modelValue: {
            type: Object,
            required: true
        },
        config: {
            type: Object,
            required: true
        }
    },

    emits: ['update:modelValue'],

    setup(props, { emit }) {
        const mappings = ref({ ...props.modelValue })

        watch(() => props.modelValue, (val) => {
            mappings.value = { ...val }
        })

        const handleChange = () => {
            emit('update:modelValue', { ...mappings.value })
        }

        return {
            mappings,
            handleChange
        }
    }
})
</script>

<style lang="less" scoped>
.sk-field-mapper {
    .field-item {
        margin-bottom: 16px;

        .field-label {
            margin-bottom: 8px;
            color: rgba(0, 0, 0, 0.85);
        }
    }
}
</style>