<template>
    <div class="sk-rate">
        <!-- 编辑模式 -->
        <template v-if="isEdit === $config.formEditType.isEdit">
            <div class="rate-container">
                <!-- 标签 -->
                <span v-if="label" class="rate-label">{{ label }}</span>
                <div class="rate-content">
                    <!-- 评分主体 -->
                    <a-rate :value="modelValue" :count="count" :allow-half="allowHalf" :disabled="disabled"
                        :tooltips="tooltips" @update:value="handleChange" @hoverChange="handleHoverChange">
                        <template #character>
                            <template v-if="character">{{ character }}</template>
                            <StarFilled v-else style="color: inherit;" />
                        </template>
                    </a-rate>
                    <!-- 显示分数 -->
                    <span v-if="showValue" class="rate-text">
                        {{ modelValue || defaultValue }}{{ unit }}
                    </span>
                </div>
            </div>
        </template>
        <!-- 查看模式 -->
        <template v-else>
            <div class="view-mode">
                <span v-if="label" class="view-label">{{ label }}：</span>
                <a-rate :value="modelValue" :count="count" disabled>
                    <template #character>
                        <template v-if="character">{{ character }}</template>
                        <StarFilled v-else style="color: inherit;" />
                    </template>
                </a-rate>
                <span v-if="showValue" class="view-value">
                    {{ modelValue || defaultValue }}{{ unit }}
                </span>
            </div>
        </template>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { StarFilled } from '@ant-design/icons-vue'

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0
    },
    label: {
        type: String,
        default: ''
    },
    count: {
        type: Number,
        default: 5
    },
    allowHalf: {
        type: Boolean,
        default: false
    },
    character: {
        type: String,  // 只允许字符串类型
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    },
    showValue: {
        type: Boolean,
        default: true
    },
    unit: {
        type: String,
        default: '分'
    },
    tooltips: {
        type: Array,
        default: () => []
    },
    defaultValue: {
        type: [Number, String],
        default: '-'
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'hoverChange'])

// 处理值变化
const handleChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
}

// 处理悬停变化
const handleHoverChange = (value) => {
    emit('hoverChange', value)
}
</script>

<style scoped>
.sk-rate {
    width: 100%;
}

.rate-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rate-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

.rate-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rate-text {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    min-width: 45px;
}

.view-mode {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    line-height: 32px;
}

.view-label {
    color: rgba(0, 0, 0, 0.85);
}

.view-value {
    color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-rate) {
    color: #fadb14;
    font-size: 20px;
    line-height: 20px;
}

:deep(.ant-rate-star) {
    margin-right: 8px;
}

:deep(.anticon) {
    color: inherit;
}
</style>