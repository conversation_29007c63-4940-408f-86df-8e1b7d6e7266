<template>
    <div class="sk-divider" :class="dividerClasses">
        <!-- 分割线内容 -->
        <div v-if="$slots.default" class="sk-divider-inner-text">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { computed, useSlots } from 'vue'

const props = defineProps({
    // 水平还是垂直类型
    type: {
        type: String,
        default: 'horizontal',
        validator: (value) => ['horizontal', 'vertical'].includes(value)
    },
    // 分割线标题的位置
    orientation: {
        type: String,
        default: 'center',
        validator: (value) => ['left', 'right', 'center'].includes(value)
    },
    // 是否虚线
    dashed: {
        type: Boolean,
        default: false
    },
    // 是否使用普通文字样式
    plain: {
        type: Boolean,
        default: false
    },
    // 自定义样式
    style: {
        type: Object,
        default: () => ({})
    }
})

// 获取插槽
const slots = useSlots()

// 计算分割线的类名
const dividerClasses = computed(() => ({
    [`sk-divider-${props.type}`]: true,
    [`sk-divider-with-text`]: !!slots.default,
    [`sk-divider-with-text-${props.orientation}`]: !!slots.default,
    'sk-divider-dashed': props.dashed,
    'sk-divider-plain': props.plain
}))
</script>

<style scoped>
.sk-divider {
    box-sizing: border-box;
    margin: 24px 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.sk-divider-vertical {
    position: relative;
    top: -0.06em;
    display: inline-block;
    height: 0.9em;
    margin: 0 8px;
    vertical-align: middle;
    border-top: 0;
    border-left: 1px solid rgba(0, 0, 0, 0.06);
}

.sk-divider-horizontal {
    display: flex;
    clear: both;
    width: 100%;
    min-width: 100%;
    margin: 24px 0;
}

.sk-divider-horizontal.sk-divider-with-text {
    display: flex;
    margin: 16px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
    white-space: nowrap;
    text-align: center;
    border-top: 0;
    border-top-color: rgba(0, 0, 0, 0.06);
}

.sk-divider-horizontal.sk-divider-with-text::before,
.sk-divider-horizontal.sk-divider-with-text::after {
    position: relative;
    top: 50%;
    width: 50%;
    border-top: 1px solid transparent;
    border-top-color: inherit;
    border-bottom: 0;
    transform: translateY(50%);
    content: '';
}

.sk-divider-horizontal.sk-divider-with-text-left::before {
    width: 5%;
}

.sk-divider-horizontal.sk-divider-with-text-left::after {
    width: 95%;
}

.sk-divider-horizontal.sk-divider-with-text-right::before {
    width: 95%;
}

.sk-divider-horizontal.sk-divider-with-text-right::after {
    width: 5%;
}

.sk-divider-inner-text {
    display: inline-block;
    padding: 0 1em;
}

.sk-divider-dashed {
    background: none;
    border-color: rgba(0, 0, 0, 0.06);
    border-style: dashed;
    border-width: 1px 0 0;
}

.sk-divider-plain.sk-divider-with-text {
    color: rgba(0, 0, 0, 0.45);
    font-weight: normal;
    font-size: 14px;
}

.sk-divider-horizontal.sk-divider-with-text.sk-divider-dashed::before,
.sk-divider-horizontal.sk-divider-with-text.sk-divider-dashed::after {
    border-style: dashed none none;
}
</style>