<template>
    <div class="sk-breadcrumb">
        <a-breadcrumb :separator="separator" :routes="routes">
            <!-- 自动生成面包屑项 -->
            <template v-if="items && items.length">
                <a-breadcrumb-item v-for="item in items" :key="item.path || item.title">
                    <template v-if="item.path">
                        <router-link v-if="item.path" :to="item.path">{{ item.title }}</router-link>
                        <span v-else>{{ item.title }}</span>
                    </template>
                    <template v-else>
                        <span>{{ item.title }}</span>
                    </template>
                </a-breadcrumb-item>
            </template>

            <!-- 默认插槽 -->
            <template v-else>
                <slot></slot>
            </template>

            <!-- 分隔符插槽 -->
            <template v-if="$slots.separator" #separator>
                <slot name="separator"></slot>
            </template>
        </a-breadcrumb>
    </div>
</template>

<script setup>
const props = defineProps({
    // 分隔符
    separator: {
        type: String,
        default: '/'
    },
    // 路由配置
    routes: {
        type: Array,
        default: () => []
    },
    // 面包屑项配置
    items: {
        type: Array,
        default: () => []
    }
})
</script>

<style scoped>
.sk-breadcrumb {
    line-height: 1.5715;
}

:deep(.ant-breadcrumb) {
    color: rgba(0, 0, 0, 0.45);
}

:deep(.ant-breadcrumb a) {
    color: rgba(0, 0, 0, 0.45);
    transition: color 0.3s;
}

:deep(.ant-breadcrumb a:hover) {
    color: var(--ant-primary-color);
}

:deep(.ant-breadcrumb > span:last-child) {
    color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-breadcrumb-separator) {
    margin: 0 8px;
    color: rgba(0, 0, 0, 0.45);
}

:deep(.ant-breadcrumb-link) {
    display: inline-flex;
    align-items: center;
}

:deep(.ant-breadcrumb-link .anticon) {
    margin-right: 4px;
}
</style>