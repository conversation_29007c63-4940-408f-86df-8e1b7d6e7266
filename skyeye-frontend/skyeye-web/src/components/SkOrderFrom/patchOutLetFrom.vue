<template>
    <div class="sk-depot-out-from">
        <div v-if="isEdit === $config.formEditType.notEdit && !$util.isNull(modelValue)">
            <div>{{ initEnumData[fromTypeId] }} {{ data?.oddNumber }}</div>
        </div>
        <div v-else-if="isEdit === $config.formEditType.isEdit">
            该组件暂不支持新增时的选择操作。
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: String,
        default: () => ''
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    }
})

// 数据
const data = ref({})
const fromTypeId = ref('')

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumMapByCode('patchOutLetFromType');
    initEnumData.value = result
}

onMounted(() => {
    getInitData()
    if (!proxy.$util.isNull(props.modelValue)) {
        fromTypeId.value = props.formData?.fromTypeId
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        data.value = props.formData[mationKey]
    }
})
</script>

<style scoped>
.sk-depot-out-from {
    width: 100%;
}
</style>