<template>
    <div class="sk-checkbox-wrapper">
        <template v-if="isEdit === $config.formEditType.isEdit">
            <a-checkbox-group v-if="options && options.length" :value="modelValue" :options="options"
                :disabled="disabled" @change="handleGroupChange" />
            <a-checkbox v-else v-model:checked="checked" :disabled="disabled" @change="handleSingleChange">
                <slot></slot>
            </a-checkbox>
        </template>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                <template v-if="options && options.length">
                    {{ getSelectedLabels }}
                </template>
                <template v-else>
                    <slot></slot>
                </template>
            </template>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 绑定值
    modelValue: {
        type: [Boolean, Array],
        default: undefined
    },
    // 选项数组
    options: {
        type: Array,
        default: undefined
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 占位符
    placeholder: {
        type: String,
        default: '请选择'
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 计算单个checkbox的选中状态
const checked = computed({
    get: () => props.modelValue === true,
    set: (val) => {
        emit('update:modelValue', val)
    }
})

// 获取选中项的标签（多选）
const getSelectedLabels = computed(() => {
    if (!Array.isArray(props.modelValue) || !props.options) return ''
    return props.modelValue
        .map(value => {
            const selected = props.options.find(item => item.value === value)
            return selected ? selected.label : ''
        })
        .filter(Boolean)
        .join('、')
})

// 处理多选框组值变化
const handleGroupChange = (checkedValues) => {
    emit('update:modelValue', checkedValues)
    emit('change', checkedValues)
}

// 处理单个多选框值变化
const handleSingleChange = (e) => {
    const value = e.target.checked
    emit('update:modelValue', value)
    emit('change', value)
}
</script>

<style lang="less" scoped>
.sk-checkbox-wrapper {
    :deep(.ant-checkbox-group) {
        width: 100%;
    }
}
</style>