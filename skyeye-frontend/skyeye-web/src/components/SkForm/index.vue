<template>
    <div class="sk-form">
        <a-form ref="formRef" :model="modelValue" :label-col="labelCol" :wrapper-col="wrapperCol" :rules="rules"
            :layout="layout" :label-align="labelAlign" :validate-on-rule-change="validateOnRuleChange"
            :scroll-to-first-error="scrollToFirstError" :size="size"
            :disabled="disabled || isEdit === $config.formEditType.notEdit" :colon="colon" v-bind="$attrs">
            <slot></slot>

            <!-- 默认的表单按钮组 -->
            <template v-if="showButtons && isEdit === $config.formEditType.isEdit">
                <a-form-item :wrapper-col="buttonWrapperCol" :class="!isFormSubmit ? 'form-search-btn' : ''">
                    <a-space>
                        <a-button v-if="showReset" @click="handleReset">
                            <template #icon>
                                <slot name="resetIcon"></slot>
                            </template>
                            {{ resetText }}
                        </a-button>
                        <a-button v-if="flowabled" :loading="submitLoading" @click="handleSubmit(1)">
                            <template #icon>
                                <slot name="draftIcon"></slot>
                            </template>
                            {{ draftText }}
                        </a-button>
                        <a-button type="primary" :loading="submitLoading" @click="handleSubmit(2)">
                            <template #icon>
                                <slot name="submitIcon"></slot>
                            </template>
                            {{ submitText }}
                        </a-button>
                        <slot name="buttons"></slot>
                    </a-space>
                </a-form-item>
            </template>
        </a-form>
    </div>
</template>

<script>
import { defineComponent, ref, watch, onMounted, onUnmounted, getCurrentInstance } from 'vue';

export default defineComponent({
    name: 'SkForm',
    props: {
        modelValue: {
            type: Object,
            required: true
        },
        rules: {
            type: Object,
            default: () => ({})
        },
        layout: {
            type: String,
            default: 'horizontal',
            validator: (value) => ['horizontal', 'vertical', 'inline'].includes(value)
        },
        labelCol: {
            type: Object,
            default: () => ({
                style: {
                    width: '120px'
                }
            })
        },
        wrapperCol: {
            type: Object,
            default: () => ({
                style: {
                    width: 'calc(100% - 120px)'
                }
            })
        },
        buttonWrapperCol: {
            type: Object,
            default: () => $config.buttonWrapperCol
        },
        labelAlign: {
            type: String,
            default: 'right',
            validator: (value) => ['left', 'right'].includes(value)
        },
        validateOnRuleChange: {
            type: Boolean,
            default: true
        },
        scrollToFirstError: {
            type: [Boolean, Object],
            default: true
        },
        size: {
            type: String,
            default: 'middle',
            validator: (value) => ['small', 'middle', 'large'].includes(value)
        },
        disabled: {
            type: Boolean,
            default: false
        },
        colon: {
            type: Boolean,
            default: true
        },
        showButtons: {
            type: Boolean,
            default: true
        },
        showReset: {
            type: Boolean,
            default: true
        },
        draftText: {
            type: String,
            default: '保存草稿'
        },
        submitText: {
            type: String,
            default: '提交'
        },
        resetText: {
            type: String,
            default: '取消'
        },
        isEdit: {
            type: Number,
            default: $config.formEditType.isEdit
        },
        submitLoading: {
            type: Boolean,
            default: false
        },
        // 是否启用回车提交
        enterSubmit: {
            type: Boolean,
            default: true
        },
        // 是否启用表单提交，表单提交的按钮样式和搜索框的按钮样式会有所不同
        isFormSubmit: {
            type: Boolean,
            default: true
        },
        // 是否开启工作流
        flowabled: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update:modelValue', 'submit', 'reset', 'finish', 'finishFailed'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()
        const formRef = ref();

        // 表单验证
        const validate = async () => {
            try {
                // 先验证基本表单
                await formRef.value?.validate()

                // 获取所有动态表格组件的验证结果
                if (props.modelValue) {  // 使用 modelValue，因为这是我们定义的 prop
                    const dynamicTableRefs = Object.keys(props.modelValue).filter(key => {
                        // 检查是否存在对应的表格组件引用
                        const tableRef = proxy.$refs[`table_${key}`]
                        return tableRef
                    })

                    // 验证所有动态表格
                    for (const key of dynamicTableRefs) {
                        const tableRef = proxy.$refs[`table_${key}`]
                        if (tableRef && Array.isArray(tableRef)) {
                            for (const ref of tableRef) {
                                if (ref?.validate) {
                                    const { valid } = await ref.validate()
                                    if (!valid) {
                                        return Promise.reject('表格验证失败')
                                    }
                                }
                            }
                        }
                    }
                }

                return Promise.resolve()
            } catch (error) {
                return Promise.reject(error)
            }
        }

        // 验证特定字段
        const validateFields = async (fields) => {
            try {
                const values = await formRef.value.validateFields(fields);
                return [null, values];
            } catch (error) {
                return [error, null];
            }
        };

        // 清除验证
        const clearValidate = (fields) => {
            formRef.value.clearValidate(fields);
        };

        // 重置表单
        const resetFields = () => {
            formRef.value.resetFields();
        };

        // 滚动到字段
        const scrollToField = (field, options) => {
            formRef.value.scrollToField(field, options);
        };

        // 处理回车提交
        const handleKeyup = (e) => {
            try {
                // 确保所有条件都存在后再进行判断
                if (e.key === 'Enter' &&
                    props.enterSubmit &&
                    proxy &&
                    proxy.$config &&
                    props.isEdit === proxy.$config.formEditType.isEdit) {

                    // 如果当前焦点在 textarea，富文本编辑器，代码编辑器中，不触发提交
                    if (e.target.tagName.toLowerCase() === 'textarea' ||
                        e.target.closest('.w-e-text-container') ||
                        e.target.closest('.editor-wrapper') ||
                        e.target.closest('.sk-code-editor')) {
                        return;
                    }

                    // 阻止默认行为
                    e.preventDefault();

                    // 检查表单引用是否存在
                    if (!formRef.value) {
                        console.warn('Form reference is not available');
                        return;
                    }

                    handleSubmit();
                }
            } catch (error) {
                console.error('处理回车事件时出错:', error);
            }
        };

        // 提交处理
        const handleSubmit = async (submitType) => {
            try {
                if (!formRef.value) {
                    console.warn('Form reference is not available');
                    return;
                }

                // 尝试验证表单
                try {
                    const values = await formRef.value.validate();
                    emit('submit', values, submitType);
                    emit('finish', values);
                } catch (validationError) {
                    emit('finishFailed', validationError);
                }
            } catch (error) {
                console.error('表单提交时出错:', error);
            }
        };

        // 重置处理
        const handleReset = () => {
            formRef.value.resetFields();
            emit('reset');
        };

        // 监听表单数据变化
        watch(
            () => props.modelValue,
            (newVal) => {
                emit('update:modelValue', newVal);
            },
            { deep: true }
        );

        // 在组件挂载时添加事件监听
        onMounted(() => {
            if (props.enterSubmit) {
                // 使用事件委托，只监听表单内的回车事件
                formRef.value?.$el.addEventListener('keyup', handleKeyup);
            }
        });

        // 在组件卸载时移除事件监听
        onUnmounted(() => {
            if (props.enterSubmit && formRef.value?.$el) {
                formRef.value.$el.removeEventListener('keyup', handleKeyup);
            }
        });

        // 暴露方法
        expose({
            validate,
            validateFields,
            clearValidate,
            resetFields,
            scrollToField,
            submit: handleSubmit,
            reset: handleReset
        });

        return {
            formRef,
            handleSubmit,
            handleReset
        };
    }
});
</script>

<style scoped>
.sk-form {
    width: 100%;
}

.form-search-btn :deep(.ant-col) {
    margin-left: 0px !important;
}
</style>