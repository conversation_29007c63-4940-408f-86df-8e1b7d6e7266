<template>
    <div class="sk-switch-wrapper">
        <!-- 编辑模式 -->
        <template v-if="isEdit === $config.formEditType.isEdit">
            <a-switch v-bind="$attrs" :checked="modelValue" :disabled="disabled" :loading="loading" :size="size"
                :checked-children="checkedText" :un-checked-children="uncheckedText" @change="handleChange" />
        </template>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                {{ modelValue ? (checkedText || '是') : (uncheckedText || '否') }}
            </template>
        </div>
        <!-- 帮助信息 -->
        <div v-if="help" class="sk-switch-help">
            <info-circle-outlined />
            {{ help }}
        </div>
    </div>
</template>

<script setup>
import { InfoCircleOutlined } from '@ant-design/icons-vue'

defineOptions({
    name: 'SkSwitch'
})

defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    loading: {
        type: Boolean,
        default: false
    },
    size: {
        type: String,
        default: 'default',
        validator: (value) => ['small', 'default'].includes(value)
    },
    checkedText: {
        type: [String, Boolean, Number],
        default: ''
    },
    uncheckedText: {
        type: [String, Boolean, Number],
        default: ''
    },
    help: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const handleChange = (checked) => {
    emit('update:modelValue', checked)
    emit('change', checked)
}
</script>

<style lang="less" scoped>
.sk-switch-wrapper {
    display: inline-flex;
    flex-direction: column;
    gap: 4px;

    .sk-switch-help {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
    }
}
</style>