<template>
    <div class="sk-anchor" :class="{ 'sk-anchor-fixed': affix }">
        <a-anchor :affix="affix" :bounds="bounds" :offset-top="offsetTop" :target-offset="targetOffset"
            :get-container="getContainer" :getCurrentAnchor="getCurrentAnchor" :show-ink="showInk" :items="anchorItems"
            @click="handleClick" @change="handleChange">
            <!-- 自定义标题插槽 -->
            <template v-if="$slots.title" #title>
                <slot name="title"></slot>
            </template>
        </a-anchor>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 是否固定模式
    affix: {
        type: Boolean,
        default: true
    },
    // 锚点区域边界
    bounds: {
        type: Number,
        default: 5
    },
    // 距离窗口顶部达到指定偏移量后触发
    offsetTop: {
        type: Number,
        default: 0
    },
    // 锚点滚动偏移量
    targetOffset: {
        type: Number,
        default: undefined
    },
    // 指定滚动容器
    getContainer: {
        type: Function,
        default: () => window
    },
    // 自定义高亮的锚点
    getCurrentAnchor: {
        type: Function,
        default: undefined
    },
    // 是否显示小圆点
    showInk: {
        type: Boolean,
        default: true
    },
    // 锚点配置项
    items: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['click', 'change'])

// 处理点击事件
const handleClick = (e, link) => {
    emit('click', { e, link })
}

// 处理锚点改变事件
const handleChange = (currentActiveLink) => {
    emit('change', currentActiveLink)
}

// 转换锚点配置为 Ant Design Vue 4.x 的格式
const anchorItems = computed(() => {
    return props.items.map(item => ({
        key: item.key || item.href,
        href: item.href,
        title: item.title,
        target: item.target,
        children: item.children ? item.children.map(child => ({
            key: child.key || child.href,
            href: child.href,
            title: child.title,
            target: child.target
        })) : undefined
    }))
})
</script>

<style scoped>
.sk-anchor {
    position: relative;
    width: 100%;
}

.sk-anchor-fixed {
    /* 移除之前的固定定位样式 */
}

:deep(.ant-anchor) {
    padding-left: 4px;
}

:deep(.ant-anchor-wrapper) {
    background-color: transparent;
    max-width: 100%;
}

:deep(.ant-anchor-ink) {
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
}

:deep(.ant-anchor-ink-ball) {
    width: 8px;
    height: 8px;
    background-color: #fff;
    border: 2px solid var(--ant-primary-color);
    border-radius: 8px;
    transition: top 0.3s ease-in-out;
}

:deep(.ant-anchor-link) {
    padding: 4px 0 4px 16px;
    line-height: 1.5;
}

:deep(.ant-anchor-link-title) {
    color: rgba(0, 0, 0, 0.85);
    transition: all 0.3s;
}

:deep(.ant-anchor-link-title:hover) {
    color: var(--ant-primary-color);
}

:deep(.ant-anchor-link-title-active) {
    color: var(--ant-primary-color);
}

:deep(.ant-anchor-link-active > .ant-anchor-link-title) {
    color: var(--ant-primary-color);
}
</style>