<template>
    <div class="sk-slider">
        <!-- 编辑模式 -->
        <template v-if="isEdit === $config.formEditType.isEdit">
            <div class="slider-container">
                <!-- 标签和值的显示 -->
                <div class="slider-header" v-if="label || showValue">
                    <span v-if="label" class="slider-label">{{ label }}</span>
                    <span v-if="showValue" class="slider-value">{{ modelValue }}{{ unit }}</span>
                </div>
                <!-- 滑块主体 -->
                <div class="slider-content">
                    <a-slider :value="modelValue" :min="min" :max="max" :step="step" :disabled="disabled"
                        :tooltip="tooltipConfig" :marks="marks" :included="included" :range="range" :reverse="reverse"
                        :vertical="vertical" @update:value="handleChange" @afterChange="handleAfterChange" />
                    <!-- 输入框 -->
                    <a-input-number v-if="showInput" :value="modelValue" :min="min" :max="max" :step="step"
                        :disabled="disabled" :controls="false" class="slider-input" @change="handleInputChange" />
                </div>
            </div>
        </template>
        <!-- 查看模式 -->
        <template v-else>
            <div class="view-mode">
                <span v-if="label" class="view-label">{{ label }}：</span>
                <span class="view-value">{{ modelValue }}{{ unit }}</span>
            </div>
        </template>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    modelValue: {
        type: [Number, Array],
        default: 0
    },
    label: {
        type: String,
        default: ''
    },
    min: {
        type: Number,
        default: 0
    },
    max: {
        type: Number,
        default: 100
    },
    step: {
        type: Number,
        default: 1
    },
    unit: {
        type: String,
        default: ''
    },
    showValue: {
        type: Boolean,
        default: true
    },
    showTooltip: {
        type: Boolean,
        default: true
    },
    showInput: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    marks: {
        type: Object,
        default: () => ({})
    },
    included: {
        type: Boolean,
        default: true
    },
    range: {
        type: Boolean,
        default: false
    },
    reverse: {
        type: Boolean,
        default: false
    },
    vertical: {
        type: Boolean,
        default: false
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'afterChange'])

// Tooltip 配置
const tooltipConfig = {
    open: undefined,  // 让 tooltip 自动控制显示/隐藏
    formatter: (value) => `${value}${props.unit}`,
    placement: 'top',
    getPopupContainer: (triggerNode) => triggerNode.parentNode
}

// 处理滑块值变化
const handleChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
}

// 处理输入框值变化
const handleInputChange = (value) => {
    // 确保值在范围内
    const validValue = Math.min(Math.max(value, props.min), props.max)
    emit('update:modelValue', validValue)
    emit('change', validValue)
}

// 处理拖动结束
const handleAfterChange = (value) => {
    emit('afterChange', value)
}
</script>

<style scoped>
.sk-slider {
    width: 100%;
}

.slider-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
}

.slider-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

.slider-value {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    min-width: 45px;
    text-align: right;
}

.slider-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.slider-input {
    width: 90px;
}

:deep(.ant-slider) {
    flex: 1;
    margin: 0;
}

:deep(.ant-slider-vertical) {
    height: 300px;
    margin: 6px 16px;
}

.view-mode {
    font-size: 14px;
    line-height: 32px;
    display: flex;
    gap: 8px;
}

.view-label {
    color: rgba(0, 0, 0, 0.85);
}

.view-value {
    color: rgba(0, 0, 0, 0.65);
}
</style>