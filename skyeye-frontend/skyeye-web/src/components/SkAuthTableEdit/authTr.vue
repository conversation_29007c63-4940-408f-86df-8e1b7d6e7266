<template>
    <template v-for="(item, index) in data" :key="index">
        <tr :class="['auth-tr', { 'auth-tr-hover': !item.children?.length }]">
            <!-- 第一列 -->
            <td class="auth-td fixed-column">
                <div class="name-cell" :style="{ paddingLeft: zIndex * 20 + 'px' }">
                    <span v-if="item.children?.length > 0" class="expand-icon" @click="toggleCollapse(item)">
                        <RightOutlined v-if="item.isCollapsed" />
                        <DownOutlined v-else />
                    </span>
                    <span :class="['cell-text', { 'is-parent': item.children?.length }]">
                        {{ item[columns[0].prop] }}
                    </span>
                </div>
            </td>

            <!-- 其他列 -->
            <td v-for="(column, colIndex) in columns.slice(1)" :key="colIndex" class="auth-td">
                <div class="auth-cell">
                    <template v-if="!showContent">
                        <a-checkbox-group v-model:value="item[column.prop]"
                            @change="(value) => changeData(item, column.prop, value)">
                            <a-checkbox :value="1" />
                        </a-checkbox-group>
                    </template>
                    <span v-else>{{ item[column.prop] }}</span>
                </div>
            </td>

            <!-- 操作列 -->
            <td v-if="showOperator" class="auth-td">
                <div class="operator-cell">
                    <SkUserSelect v-if="zIndex === 0" v-model="formData.userList" :formData="formData"
                        :objectId="item.id" :chooseOrNotEmail="2" :chooseOrNotMy="2" :multiple="true"
                        :showChooseUser="false" @change="handleSave" />
                    <a-button type="primary" danger size="small" @click="handleRemove(item.id)">移除</a-button>
                </div>
            </td>
        </tr>

        <AuthTr v-if="item.children?.length > 0 && !item.isCollapsed" :data="item.children" :columns="columns"
            :z-index="zIndex + 1" :show-content="showContent" :show-operator="showOperator" @save="handleSave"
            @remove="handleRemove" />
    </template>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { RightOutlined, DownOutlined } from '@ant-design/icons-vue'
import SkUserSelect from '@/components/SkUserSelect/index.vue'

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    columns: {
        type: Array,
        required: true
    },
    zIndex: {
        type: Number,
        default: 0
    },
    showContent: {
        type: Boolean,
        default: false
    },
    showOperator: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['save', 'remove'])

const formData = ref({
    userList: []
})

// 初始化折叠状态
onMounted(() => {
    props.data?.forEach(item => {
        item.isCollapsed = false
    })
})

// 切换折叠状态
const toggleCollapse = (item) => {
    item.isCollapsed = !item.isCollapsed
}

// 处理保存
const handleSave = (selectedMationList, selectIdList, objectId) => {
    emit('save', objectId, selectedMationList, selectIdList)
}

// 处理移除
const handleRemove = (id) => {
    emit('remove', id)
}

// 处理数据变化
const changeData = (item, key, value) => {
    const newValue = value.length > 0 ? value[0] : null
    item[key] = [newValue]

    // 更新子项
    if (item.children?.length > 0) {
        item.children.forEach(child => {
            child[key] = [newValue ?? 0]
        })
    }
}
</script>

<style scoped>
.auth-tr {
    transition: all 0.3s ease;
}

.auth-tr-hover:hover {
    background-color: #fafafa;
}

.auth-td {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    white-space: nowrap;
}

.name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expand-icon {
    cursor: pointer;
    color: #595959;
    display: inline-flex;
    align-items: center;
    transition: transform 0.3s;
}

.expand-icon:hover {
    color: #1890ff;
}

.cell-text {
    color: rgba(0, 0, 0, 0.85);
}

.is-parent {
    font-weight: 500;
}

.auth-cell {
    display: flex;
    align-items: center;
}

.operator-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fixed-column {
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: #fff;
    border-right: 1px solid #f0f0f0;
}
</style>
