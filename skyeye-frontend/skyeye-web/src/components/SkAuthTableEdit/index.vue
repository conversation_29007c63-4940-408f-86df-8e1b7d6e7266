<template>
    <div class="sk-auth-table">
        <!-- 顶部操作区 -->
        <div v-if="showOperator" class="top-actions">
            <a-button type="primary" size="small" @click="chooseRole">选择角色</a-button>
        </div>

        <!-- 表格区域 -->
        <div class="table-wrapper">
            <table class="auth-table">
                <colgroup>
                    <col style="width: 200px;" />
                    <col v-for="(_, index) in columns.slice(1)" :key="index" style="width: 120px;" />
                    <col v-if="showOperator" style="width: 150px;" />
                </colgroup>
                <thead>
                    <tr>
                        <th class="auth-th fixed-column">{{ columns[0].label }}</th>
                        <th v-for="(column, index) in columns.slice(1)" :key="index" class="auth-th">
                            {{ column.label }}
                        </th>
                        <th v-if="showOperator" class="auth-th">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <AuthTr :data="data" :columns="columns" :show-content="showContent" :show-operator="showOperator"
                        @save="handleSave" @remove="handleRemove" />
                </tbody>
            </table>
        </div>

        <!-- 角色选择弹窗 -->
        <SkModal v-model="modalVisible" :showOk="true" :showCancel="true" title="选择角色" :width="450" @ok="saveRole"
            @cancel="modalVisible = false">
            <SkSelect v-model="chooseRoleId" style="width: 100%" placeholder="请选择角色" :options="roleList" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import AuthTr from './authTr.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    columns: {
        type: Array,
        required: true
    },
    showContent: {
        type: Boolean,
        default: false
    },
    showOperator: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['save', 'saveRole', 'remove'])

const modalVisible = ref(false)
const roleList = ref([])
const chooseRoleId = ref('')

// 打开角色选择
const chooseRole = async () => {
    let result = await proxy.$util.getDictListMapByCode(['TEAM_ROLE']);

    for (let key in result['TEAM_ROLE']) {
        roleList.value.push({
            label: result['TEAM_ROLE'][key],
            value: key
        })
    }
    modalVisible.value = true
}

// 保存选择的角色
const saveRole = () => {
    const chooseRole = roleList.value.find(role => role.value === chooseRoleId.value)
    emit('saveRole', chooseRole)
    modalVisible.value = false
}

// 处理保存
const handleSave = (objectId, selectedMationList, selectIdList) => {
    emit('save', objectId, selectedMationList, selectIdList)
}

// 处理移除
const handleRemove = (id) => {
    emit('remove', id)
}
</script>

<style scoped>
.sk-auth-table {
    position: relative;
    width: 100%;
}

.top-actions {
    margin-bottom: 16px;
}

.table-wrapper {
    width: 100%;
    overflow-x: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
}

.auth-table {
    width: auto;
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: #fff;
}

.auth-th {
    padding: 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
}

.fixed-column {
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: #fafafa;
    border-right: 1px solid #f0f0f0;
}

.auth-th:first-child {
    border-top-left-radius: 2px;
}

.auth-th:last-child {
    border-top-right-radius: 2px;
}
</style>
