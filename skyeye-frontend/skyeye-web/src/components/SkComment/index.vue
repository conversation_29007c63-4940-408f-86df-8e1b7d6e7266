<template>
    <div class="sk-comment">
        <a-comment :author="author" :avatar="avatar" :content="content" :datetime="datetime">
            <!-- 操作区域 -->
            <template #actions>
                <slot name="actions">
                    <span v-if="showDefaultActions" key="like" @click="handleLike">
                        <template v-if="action === 'liked'">
                            <like-filled :style="{ color: '#1890ff' }" />
                        </template>
                        <template v-else>
                            <like-outlined />
                        </template>
                        <span class="action-text">{{ likes }}</span>
                    </span>
                    <span v-if="showDefaultActions" key="dislike" @click="handleDislike">
                        <template v-if="action === 'disliked'">
                            <dislike-filled :style="{ color: '#1890ff' }" />
                        </template>
                        <template v-else>
                            <dislike-outlined />
                        </template>
                        <span class="action-text">{{ dislikes }}</span>
                    </span>
                    <span v-if="showDefaultActions" key="reply" @click="handleReply">
                        <message-outlined />
                        <span class="action-text">回复</span>
                    </span>
                </slot>
            </template>

            <!-- 头像 -->
            <template v-if="$slots.avatar" #avatar>
                <slot name="avatar"></slot>
            </template>

            <!-- 作者 -->
            <template v-if="$slots.author" #author>
                <slot name="author"></slot>
            </template>

            <!-- 内容 -->
            <template v-if="$slots.content" #content>
                <slot name="content"></slot>
            </template>

            <!-- 时间 -->
            <template v-if="$slots.datetime" #datetime>
                <slot name="datetime"></slot>
            </template>

            <!-- 嵌套评论 -->
            <template v-if="$slots.default">
                <slot></slot>
            </template>

            <!-- 回复框 -->
            <div v-if="showReplyBox" class="reply-box">
                <a-comment>
                    <template #avatar>
                        <a-avatar :src="currentUserAvatar" :alt="currentUserName" />
                    </template>
                    <template #content>
                        <a-form-item>
                            <a-textarea v-model:value="replyContent" :rows="3" />
                            <a-button type="primary" @click="submitReply" :loading="submitting">
                                回复
                            </a-button>
                        </a-form-item>
                    </template>
                </a-comment>
            </div>
        </a-comment>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
    LikeOutlined,
    LikeFilled,
    DislikeOutlined,
    DislikeFilled,
    MessageOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
    // 作者名称
    author: {
        type: String,
        default: ''
    },
    // 头像地址
    avatar: {
        type: String,
        default: ''
    },
    // 评论内容
    content: {
        type: String,
        default: ''
    },
    // 评论时间
    datetime: {
        type: [String, Object],
        default: ''
    },
    // 是否显示默认操作按钮
    showDefaultActions: {
        type: Boolean,
        default: true
    },
    // 当前用户头像
    currentUserAvatar: {
        type: String,
        default: ''
    },
    // 当前用户名称
    currentUserName: {
        type: String,
        default: ''
    },
    // 初始点赞数
    initialLikes: {
        type: Number,
        default: 0
    },
    // 初始踩数
    initialDislikes: {
        type: Number,
        default: 0
    }
})

const emit = defineEmits(['like', 'dislike', 'reply', 'submit'])

// 状态管理
const action = ref('')
const likes = ref(props.initialLikes)
const dislikes = ref(props.initialDislikes)
const showReplyBox = ref(false)
const replyContent = ref('')
const submitting = ref(false)

// 处理点赞
const handleLike = () => {
    if (action.value === 'liked') {
        likes.value -= 1
        action.value = ''
    } else {
        likes.value += 1
        if (action.value === 'disliked') {
            dislikes.value -= 1
        }
        action.value = 'liked'
    }
    emit('like', { likes: likes.value, action: action.value })
}

// 处理踩
const handleDislike = () => {
    if (action.value === 'disliked') {
        dislikes.value -= 1
        action.value = ''
    } else {
        dislikes.value += 1
        if (action.value === 'liked') {
            likes.value -= 1
        }
        action.value = 'disliked'
    }
    emit('dislike', { dislikes: dislikes.value, action: action.value })
}

// 处理回复
const handleReply = () => {
    showReplyBox.value = !showReplyBox.value
    emit('reply', showReplyBox.value)
}

// 提交回复
const submitReply = async () => {
    if (!replyContent.value.trim()) return
    submitting.value = true
    try {
        emit('submit', replyContent.value)
        replyContent.value = ''
        showReplyBox.value = false
    } finally {
        submitting.value = false
    }
}
</script>

<style scoped>
.sk-comment {
    width: 100%;
}

.action-text {
    padding-left: 4px;
    color: rgba(0, 0, 0, 0.45);
}

.reply-box {
    margin-top: 16px;
}

:deep(.ant-comment-actions) {
    margin-top: 8px;
}

:deep(.ant-comment-content-detail) {
    margin-bottom: 8px;
}

:deep(.ant-form-item) {
    margin-bottom: 0;
}

:deep(.ant-form-item .ant-btn) {
    margin-top: 8px;
}
</style>