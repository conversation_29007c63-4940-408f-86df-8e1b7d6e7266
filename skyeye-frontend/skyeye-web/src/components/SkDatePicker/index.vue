<template>
    <div class="sk-date-picker-wrapper">
        <!-- 日期范围选择器 -->
        <a-range-picker v-if="isEdit === $config.formEditType.isEdit && range" v-model:value="rangeValue"
            :format="realFormat" :show-time="realShowTime" :placeholder="rangePlaceholder" :allowClear="allowClear"
            :disabled="disabled" :inputReadOnly="inputReadOnly" :status="status" :presets="presetList"
            :locale="mergedLocale" @change="handleChange" @ok="handleOk" @openChange="handleOpenChange"
            @focus="handleFocus" @blur="handleBlur" @panelChange="handlePanelChange">
            <template v-if="$slots.suffixIcon" #suffixIcon>
                <slot name="suffixIcon"></slot>
            </template>
            <template v-if="$slots.renderExtraFooter" #renderExtraFooter>
                <slot name="renderExtraFooter"></slot>
            </template>
        </a-range-picker>
        <!-- 普通日期选择器 -->
        <a-date-picker v-else-if="isEdit === $config.formEditType.isEdit" v-model:value="dateValue" :format="realFormat"
            :show-time="realShowTime" :picker="realPicker" :mode="mode" :disabled-date="disabledDate"
            :disabled-time="disabledTime" :show-today="showToday" :show-now="showNow" :placeholder="placeholder"
            :allowClear="allowClear" :disabled="disabled" :inputReadOnly="inputReadOnly" :status="status"
            :locale="mergedLocale" @change="handleChange" @ok="handleOk" @openChange="handleOpenChange"
            @focus="handleFocus" @blur="handleBlur" @panelChange="handlePanelChange">
            <template v-if="$slots.suffixIcon" #suffixIcon>
                <slot name="suffixIcon"></slot>
            </template>
            <template v-if="$slots.renderExtraFooter" #renderExtraFooter>
                <slot name="renderExtraFooter"></slot>
            </template>
        </a-date-picker>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="range">
                {{ modelValue ? modelValue[0] : '' }} ~ {{ modelValue ? modelValue[1] : '' }}
            </template>
            <template v-else>
                {{ modelValue }}
            </template>
        </div>
    </div>
</template>

<script setup>
import { computed, watch, ref, onMounted } from 'vue'
import dayjs from 'dayjs'
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    // 绑定值
    modelValue: {
        type: [String, Object, Array],
        default: undefined
    },
    // 是否为日期范围选择
    range: {
        type: Boolean,
        default: false
    },
    // 展示的日期格式
    format: {
        type: [String, Array, Function],
        default: undefined
    },
    // 是否显示时间选择
    showTime: {
        type: [Boolean, Object],
        default: false
    },
    // 设置选择器类型，可选值：date、week、month、quarter、year
    picker: {
        type: String,
        default: 'date'
    },
    // 面板的模式
    mode: {
        type: String,
        default: undefined
    },
    // 不可选择的日期
    disabledDate: {
        type: Function,
        default: undefined
    },
    // 不可选择的时间
    disabledTime: {
        type: Function,
        default: undefined
    },
    // 是否展示"今天"按钮
    showToday: {
        type: Boolean,
        default: true
    },
    // 当设置 showTime 时，面板是否显示"此刻"按钮
    showNow: {
        type: Boolean,
        default: undefined
    },
    // 输入框提示文字
    placeholder: {
        type: [String, Array],
        default: () => '请选择'
    },
    // 是否显示清除按钮
    allowClear: {
        type: Boolean,
        default: true
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 设置输入框为只读
    inputReadOnly: {
        type: Boolean,
        default: false
    },
    // 验证状态
    status: {
        type: String,
        default: undefined
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },
    // 预设范围
    presets: {
        type: Object,
        default: undefined
    },
    // 属性key
    attrKey: {
        type: String,
        default: undefined
    },
    // 表单数据
    formData: {
        type: Object,
        default: undefined
    },
    // 页面类型
    pageType: {
        type: String,
        default: undefined
    },
    // 分钟步长
    minuteStep: {
        type: Number,
        default: 1
    }
})

const emit = defineEmits([
    'update:modelValue',
    'change',
    'ok',
    'openChange',
    'focus',
    'blur',
    'panelChange'
])

// 计算日期值
const dateValue = computed({
    get: () => {
        if (!props.modelValue) return undefined
        let date = dayjs(props.modelValue)
        // 如果是 HH:mm 格式，转换为完整的日期时间字符串
        if (/^\d{2}:\d{2}$/.test(props.modelValue)) {
            const today = dayjs().format('YYYY-MM-DD')
            date = dayjs(`${today} ${props.modelValue}`)
        }
        return date.isValid() ? date : undefined
    },
    set: (val) => {
        emit('update:modelValue', val ? val.format(realFormat.value) : undefined)
    }
})

// 计算日期范围值
const rangeValue = computed({
    get: () => {
        if (!props.modelValue || !Array.isArray(props.modelValue)) return []
        return props.modelValue.map(date => {
            if (!date) return undefined
            const parsedDate = dayjs(date)
            return parsedDate.isValid() ? parsedDate : undefined
        })
    },
    set: (val) => {
        emit('update:modelValue', val ? val.map(date => date ? date.format(realFormat.value) : undefined) : undefined)
    }
})

// 处理日期变化
const handleChange = (date, dateString) => {
    emit('change', date, dateString)
}

// 处理确定按钮点击
const handleOk = (date) => {
    emit('ok', date)
}

// 处理面板打开/关闭
const handleOpenChange = (open) => {
    emit('openChange', open)
}

// 处理获得焦点
const handleFocus = (e) => {
    emit('focus', e)
}

// 处理失去焦点
const handleBlur = (e) => {
    emit('blur', e)
}

// 处理面板视图变化
const handlePanelChange = (value, mode) => {
    emit('panelChange', value, mode)
}

// 计算日期范围的占位符
const rangePlaceholder = computed(() => {
    if (Array.isArray(props.placeholder)) {
        return props.placeholder
    }
    return [t('common.datePicker.startDate'), t('common.datePicker.endDate')]
})

// 计算预设范围列表
const presetList = computed(() => {
    if (!props.presets) return []
    return Object.entries(props.presets).map(([label, value]) => ({
        label,
        value
    }))
})

const realPicker = ref('')
const realShowTime = ref(false)
const realFormat = ref('')
const initPicker = (newVal) => {
    if (newVal === 'year') {
        realPicker.value = 'year'
        realFormat.value = 'YYYY'
    } else if (newVal === 'month') {
        realPicker.value = 'month'
        realFormat.value = 'YYYY-MM'
    } else if (newVal === 'date') {
        realFormat.value = 'YYYY-MM-DD'
    } else if (newVal === 'datetime') {
        realShowTime.value = true
        realFormat.value = 'YYYY-MM-DD HH:mm:ss'
    } else if (newVal === 'time') {
        realFormat.value = 'HH:mm:ss'
    } else if (newVal === 'timeminute') {
        realFormat.value = 'HH:mm'
        realPicker.value = 'time'
        realShowTime.value = {
            format: 'HH:mm',
            minuteStep: props.minuteStep
        }
    }
}

// 监听picker变化
watch(() => props.picker, (newVal) => {
    initPicker(newVal)
})

onMounted(() => {
    initPicker(props.picker)
})

// 扩展 locale 配置
const mergedLocale = computed(() => ({
    ...zhCN,
    lang: {
        ...zhCN.lang,
        ok: t('common.confirm'),
        today: t('common.datePicker.today'),
        now: t('common.datePicker.now'),
        timeSelect: t('common.datePicker.selectTime'),
        dateSelect: t('common.datePicker.selectDate'),
        monthSelect: t('common.datePicker.selectMonth'),
        yearSelect: t('common.datePicker.selectYear'),
        decadeSelect: t('common.datePicker.selectDecade'),
        shortWeekDays: [
            t('common.datePicker.weeks.sun'),
            t('common.datePicker.weeks.mon'),
            t('common.datePicker.weeks.tue'),
            t('common.datePicker.weeks.wed'),
            t('common.datePicker.weeks.thu'),
            t('common.datePicker.weeks.fri'),
            t('common.datePicker.weeks.sat')
        ],
        shortMonths: [
            t('common.datePicker.months.jan'),
            t('common.datePicker.months.feb'),
            t('common.datePicker.months.mar'),
            t('common.datePicker.months.apr'),
            t('common.datePicker.months.may'),
            t('common.datePicker.months.jun'),
            t('common.datePicker.months.jul'),
            t('common.datePicker.months.aug'),
            t('common.datePicker.months.sep'),
            t('common.datePicker.months.oct'),
            t('common.datePicker.months.nov'),
            t('common.datePicker.months.dec')
        ],
        weekDays: [
            t('common.datePicker.fullWeeks.sun'),
            t('common.datePicker.fullWeeks.mon'),
            t('common.datePicker.fullWeeks.tue'),
            t('common.datePicker.fullWeeks.wed'),
            t('common.datePicker.fullWeeks.thu'),
            t('common.datePicker.fullWeeks.fri'),
            t('common.datePicker.fullWeeks.sat')
        ]
    }
}))
</script>

<style lang="less" scoped>
.sk-date-picker-wrapper {
    :deep(.ant-picker) {
        width: 100%;
    }
}
</style>