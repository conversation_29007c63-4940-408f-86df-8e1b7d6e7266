<template>
    <div :class="['sk-skeleton-wrapper', className]">
        <a-skeleton v-bind="$attrs" :loading="loading" :avatar="avatar" :title="title" :paragraph="paragraph"
            :active="active" :round="round">
            <template v-if="!loading">
                <slot></slot>
            </template>
        </a-skeleton>
    </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'SkSkeleton',
    props: {
        className: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: true
        },
        avatar: {
            type: [Boolean, Object],
            default: false
        },
        title: {
            type: [Boolean, Object],
            default: true
        },
        paragraph: {
            type: [Boolean, Object],
            default: true
        },
        active: {
            type: Boolean,
            default: false
        },
        round: {
            type: Boolean,
            default: false
        }
    }
})
</script>

<style lang="less" scoped>
.sk-skeleton-wrapper {

    // 自定义样式
    :deep(.ant-skeleton-header) {
        padding-right: 16px;
    }

    :deep(.ant-skeleton-content) {
        .ant-skeleton-title {
            margin-top: 0;
        }
    }

    &.ant-skeleton-with-avatar {
        :deep(.ant-skeleton-content) {
            .ant-skeleton-title {
                margin-top: 12px;
            }
        }
    }

    :deep(.ant-skeleton-avatar) {
        &.ant-skeleton-avatar-circle {
            border-radius: 50%;
        }
    }
}
</style>