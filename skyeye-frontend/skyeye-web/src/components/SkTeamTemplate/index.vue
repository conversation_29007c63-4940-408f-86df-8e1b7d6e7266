<template>
    <div class="sk-team-template-wrapper">
        <SkSelect v-if="isEdit === $config.formEditType.isEdit" v-model="value" :attrKey="attrKey" :formData="formData"
            :isEdit="isEdit" :pageType="pageType" :preAttribute="preAttribute" :options="dataList"
            @change="handleChange" :fieldNames="fieldNames" />
        <div v-else>
            <div>{{ showData?.name }}</div>
        </div>
    </div>
</template>

<script setup>
import { computed, getCurrentInstance, watch, ref, onMounted } from 'vue'
import SkSelect from '@/components/SkSelect/index.vue'

const { proxy } = getCurrentInstance()

const emit = defineEmits(['update:modelValue', 'change'])

const props = defineProps({
    // 绑定值
    modelValue: {
        type: [String, Number, Array],
        default: undefined
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },
    // 属性键
    attrKey: {
        type: String,
        default: undefined
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    },
    // 前置属性
    preAttribute: {
        type: String,
        default: ''
    },
    // 团队对象类型
    teamObjectType: {
        type: String,
        default: ''
    }
})

// 使用计算属性处理 v-model
const value = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

// 处理值变化
const handleChange = (value, option) => {
    emit('change', value, option)
}

const fieldNames = ref({
    label: 'name',
    value: 'id',
    children: 'children'
})
const dataList = ref([])
const showView = async () => {
    let res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'queryEnableTeamTemplateList', {
        objectType: props.teamObjectType
    })
    dataList.value = res.rows
}

const showData = ref({})
const getShowData = async () => {
    if (props.modelValue) {
        let res = await proxy.$http.get(proxy.$config.getConfig().reqBasePath + 'queryTeamTemplateById', {
            id: props.modelValue
        })
        showData.value = res.bean
    }
}

// 监听 teamObjectType 变化
watch(
    () => props.teamObjectType,
    (newVal) => {
        if (newVal) {
            showView()
        }
    },
    {
        immediate: true,
        deep: true
    }
)

onMounted(() => {
    if (props.isEdit == proxy.$config.formEditType.notEdit) {
        getShowData()
    }
})
</script>

<style scoped></style>