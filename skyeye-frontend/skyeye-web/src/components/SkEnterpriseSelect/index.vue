<template>
    <div class="sk-enterprise-select">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 提示信息 -->
            <SkAlert :message="alertMessage" type="info" show-icon />

            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入企业名称" allowClear />
                    </a-form-item>
                </SkForm>
                <a-form-item-rest>
                    <SkButton type="primary" @click.prevent="handleConfirm" :loading="loading"
                        :disabled="!selectedRowKeys.length">
                        <template #icon>
                            <SaveOutlined />
                        </template>
                        {{ $t('common.confirm') }}
                    </SkButton>
                </a-form-item-rest>
            </div>

            <!-- 操作按钮 -->
            <a-form-item-rest>
                <SkButton type="primary" @click.prevent="fetchData">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    {{ $t('common.refresh') }}
                </SkButton>
            </a-form-item-rest>

            <!-- 表格区域 -->
            <a-form-item-rest>
                <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                    :ready="tableReady" @change="handleTableChange" :tipInfoHeight="56" :row-selection="{
                        type: props.multiple ? 'checkbox' : 'radio',
                        selectedRowKeys: selectedRowKeys,
                        onChange: handleSelectionChange,
                        preserveSelectedRowKeys: true
                    }" :row-key="(record) => record.id || record.key">
                </SkTable>
            </a-form-item-rest>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick, computed } from 'vue'
import { SearchOutlined, ReloadOutlined, SaveOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
const { t } = useI18n();
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Object, Array],
        default: () => []
    },
    multiple: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits([ 'cancel', 'change', 'update:modelValue'])

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据相关
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())
const selectedRows = ref([])
const selectedRowKeys = ref([])

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '企业名称',
        dataIndex: 'name',
        width: 300,
        fixed: 'left'
    },
    {
        title: '部门数',
        dataIndex: 'departmentNum',
        width: 100,
        align: 'center'
    },
    {
        title: '员工数',
        dataIndex: 'userNum',
        width: 100,
        align: 'center'
    }
]

// 获取表格数据
const fetchData = async () => {
    try {
        loading.value = true
        const params = {
            page: pagination.current,
            limit: pagination.pageSize,
            keyword: searchForm.keyword.trim() || ''
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'companymation010',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取企业列表失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 重置处理
const handleReset = () => {
    searchForm.keyword = ''
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 处理选择变化
const handleSelectionChange = (keys, rows) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
}

// 处理确认
const handleConfirm = async () => {
    try {
        if (!selectedRows.value.length) {
            SkMessage.warning('请选择企业')
            return
        }

        // 为每个企业添加serviceClassName
        const selectedCompanies = selectedRows.value.map(company => ({
            ...company,
            serviceClassName: 'companyMation'
        }))

        emit('change', selectedCompanies)
        emit('update:modelValue', props.multiple ? selectedCompanies : selectedCompanies[0])
        SkMessage.success('保存成功')
    } catch (error) {
        SkMessage.error('保存失败')
    }
}

// 提示信息
const alertMessage = computed(() => {
    const selectionType = props.multiple ? '多选' : '单选'
    return `企业选择规则：1.${selectionType}；如没有查到要选择的企业，请检查企业信息是否满足当前规则。2.选择后请点击确认按钮完成选择。`
})

// 初始化
onMounted(async () => {
    tableReady.value = true
    handleSearch()
})

// 暴露方法给父组件
defineExpose({
    selectedRows,
    selectedRowKeys,
    clearSelection: () => {
        selectedRows.value = []
        selectedRowKeys.value = []
    }
})
</script>

<style scoped>
.table-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
</style>
