<template>
    <a-spin v-bind="$attrs" :class="['sk-spin', className]" :spinning="spinning" :delay="delay" :size="size" :tip="tip"
        :indicator="indicator" :wrapperClassName="wrapperClassName">
        <template v-if="$slots.indicator" #indicator>
            <slot name="indicator"></slot>
        </template>
        <template v-if="$slots.default">
            <slot></slot>
        </template>
    </a-spin>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'SkSpin',
    props: {
        className: {
            type: String,
            default: ''
        },
        spinning: {
            type: Boolean,
            default: true
        },
        delay: {
            type: Number,
            default: 0
        },
        size: {
            type: String,
            default: 'default'
        },
        tip: {
            type: String,
            default: ''
        },
        indicator: {
            type: Object,
            default: undefined
        },
        wrapperClassName: {
            type: String,
            default: ''
        }
    }
})
</script>

<style lang="less" scoped>
.sk-spin {

    // 自定义样式
    &.ant-spin {
        color: #1890ff;
    }

    :deep(.ant-spin-text) {
        margin-top: 8px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    :deep(.ant-spin-nested-loading) {
        position: relative;
    }

    :deep(.ant-spin-container) {
        position: relative;
        transition: opacity 0.3s;

        &.ant-spin-blur {
            clear: both;
            overflow: hidden;
            opacity: 0.5;
            user-select: none;
            pointer-events: none;
        }
    }
}
</style>