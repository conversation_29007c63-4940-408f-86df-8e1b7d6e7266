<template>
    <div class="sk-rich-editor" :style="{ height: height + 'px' }">
        <div style="border: 1px solid #ccc; border-radius: 4px; height: 100%;">
            <!-- wangEditor工具栏 -->
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="mergedToolbarConfig"
                :mode="mode" />
            <!-- wangEditor编辑器 -->
            <Editor :style="{ height: (height - 42) + 'px', overflow: 'hidden' }" v-model="editorContent"
                :defaultConfig="mergedEditorConfig" :mode="mode" @onCreated="handleCreated" />
        </div>
    </div>
</template>

<script setup>
import { computed, shallowRef, onBeforeUnmount, watch, ref } from 'vue'

// 引入wangEditor
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

const props = defineProps({
    // 编辑器内容，支持v-model双向绑定
    modelValue: {
        type: String,
        default: ''
    },
    // 编辑器高度
    height: {
        type: Number,
        default: 300
    },
    // 编辑器模式
    mode: {
        type: String,
        default: 'default'
    },
    // 占位符文本
    placeholder: {
        type: String,
        default: '请输入内容...'
    },
    // 是否只读
    readOnly: {
        type: Boolean,
        default: false
    },
    // 上传API地址
    uploadUrl: {
        type: String,
        default: '/api/upload'
    },
    // 自定义工具栏配置
    toolbarConfig: {
        type: Object,
        default: () => ({})
    },
    // 自定义编辑器配置
    editorConfig: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'created', 'change'])

// 编辑器内容双向绑定
const editorContent = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

// 编辑器实例，必须用shallowRef
const editorRef = shallowRef()

// 默认工具栏配置
const defaultToolbarConfig = {
    excludeKeys: []
}

// 合并自定义工具栏配置
const mergedToolbarConfig = computed(() => {
    return { ...defaultToolbarConfig, ...props.toolbarConfig }
})

// 默认编辑器配置
const defaultEditorConfig = computed(() => ({
    placeholder: props.placeholder,
    readOnly: props.readOnly,
    MENU_CONF: {
        uploadImage: {
            server: props.uploadUrl,
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024, // 10M
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            metaWithUrl: true,
            withCredentials: false,
            headers: {
                Authorization: 'Bearer token'
            },
            customInsert(res, insertFn) {
                // res 即服务端的返回结果
                const url = res.data?.url || ''
                if (url) {
                    // 将图片插入到编辑器
                    insertFn(url)
                }
            }
        }
    }
}))

// 合并自定义编辑器配置
const mergedEditorConfig = computed(() => {
    // 深度合并MENU_CONF配置
    const mergedConfig = { ...defaultEditorConfig.value, ...props.editorConfig }
    if (props.editorConfig.MENU_CONF && defaultEditorConfig.value.MENU_CONF) {
        mergedConfig.MENU_CONF = {
            ...defaultEditorConfig.value.MENU_CONF,
            ...props.editorConfig.MENU_CONF
        }
    }
    return mergedConfig
})

// 监听编辑器内容变化
watch(() => editorRef.value?.getHtml(), (html) => {
    if (html !== undefined && html !== editorContent.value) {
        emit('change', html)
    }
})

// 组件销毁时，也销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

// 编辑器创建完成时的回调
const handleCreated = (editor) => {
    editorRef.value = editor
    emit('created', editor)
}

// 暴露方法给父组件
defineExpose({
    // 获取编辑器实例
    getEditorInstance: () => editorRef.value,
    // 清空内容
    clearContent: () => {
        if (editorRef.value) {
            editorRef.value.clear()
        }
    },
    // 插入HTML
    insertHtml: (html) => {
        if (editorRef.value) {
            editorRef.value.insertHtml(html)
        }
    }
})
</script>

<style scoped>
@import '@/styles/common.css';

.sk-rich-editor {
    width: 100%;
}
</style>