<template>
    <div class="sk-card" ref="cardRef" :class="[
        `sk-card-${size}`,
        {
            'sk-card-bordered': bordered,
            'sk-card-hoverable': hoverable,
            'sk-card-loading': loading
        }
    ]">
        <a-card :title="title" :bordered="bordered" :hoverable="hoverable" :loading="loading" :size="size"
            :bodyStyle="computedBodyStyle" :headStyle="headStyle">
            <!-- 自定义标题 -->
            <template v-if="$slots.title" #title>
                <slot name="title"></slot>
            </template>

            <!-- 自定义额外内容 -->
            <template v-if="$slots.extra" #extra>
                <slot name="extra"></slot>
            </template>

            <!-- 自定义封面 -->
            <template v-if="$slots.cover" #cover>
                <slot name="cover"></slot>
            </template>

            <!-- 默认内容 -->
            <slot></slot>

            <!-- 操作区域 -->
            <template v-if="$slots.actions" #actions>
                <slot name="actions"></slot>
            </template>
        </a-card>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
    // 卡片标题
    title: {
        type: String,
        default: ''
    },
    // 是否有边框
    bordered: {
        type: Boolean,
        default: true
    },
    // 是否可悬浮
    hoverable: {
        type: Boolean,
        default: false
    },
    // 加载状态
    loading: {
        type: Boolean,
        default: false
    },
    // 卡片尺寸
    size: {
        type: String,
        default: 'default',
        validator: (value) => ['small', 'default', 'large'].includes(value)
    },
    // 内容区域样式
    bodyStyle: {
        type: Object,
        default: () => ({})
    },
    // 标题区域样式
    headStyle: {
        type: Object,
        default: () => ({})
    },
    bodyOverflow: {
        type: String,
        default: 'auto'
    }
})

const cardRef = ref(null)
let resizeObserver = null

// 计算卡片内容区域高度
const computedBodyStyle = computed(() => {
    if (!cardRef.value) {
        return props.bodyStyle
    }

    const cardHeight = cardRef.value.offsetHeight || 0
    const headerHeight = cardRef.value.querySelector('.ant-card-head')?.offsetHeight || 0
    const actionsHeight = cardRef.value.querySelector('.ant-card-actions')?.offsetHeight || 0
    const coverHeight = cardRef.value.querySelector('.ant-card-cover')?.offsetHeight || 0
    const paddingHeight = 1  // 边框高度

    // 计算内容区域可用高度
    const contentHeight = cardHeight - headerHeight - actionsHeight - coverHeight - paddingHeight

    return {
        ...props.bodyStyle,
        height: contentHeight > 0 ? `${contentHeight}px` : undefined,
        overflow: props.bodyOverflow
    }
})

// 监听容器尺寸变化
onMounted(() => {
    nextTick(() => {
        if (cardRef.value) {
            resizeObserver = new ResizeObserver(() => {
                nextTick()
            })
            resizeObserver.observe(cardRef.value)
        }
    })
})

// 组件卸载时清理
onUnmounted(() => {
    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }
})
</script>

<style scoped>
.sk-card {
    width: 100%;
    height: 100%;
}

.sk-card .ant-card {
    height: 100%;
}

.sk-card-default :deep(.ant-card-head) {
    min-height: 36px;
    padding: 0px;
    font-size: 16px;
}

.sk-card-default :deep(.ant-card-body) {
    padding: 0px;
}

.sk-card-small :deep(.ant-card-head) {
    min-height: 36px;
    padding: 0 12px;
    font-size: 14px;
}

.sk-card-small :deep(.ant-card-head-title),
.sk-card-small :deep(.ant-card-extra) {
    padding: 8px 0;
}

.sk-card-small :deep(.ant-card-body) {
    padding: 12px;
}

.sk-card-large :deep(.ant-card-head) {
    min-height: 60px;
    padding: 0 24px;
    font-size: 16px;
}

.sk-card-large :deep(.ant-card-head-title),
.sk-card-large :deep(.ant-card-extra) {
    padding: 16px 0;
}

.sk-card-large :deep(.ant-card-body) {
    padding: 24px;
}

.sk-card :deep(.ant-card-actions) {
    background: #fafafa;
}

.sk-card :deep(.ant-card-actions > li) {
    margin: 12px 0;
}

.sk-card :deep(.ant-card-cover) {
    overflow: hidden;
}

.sk-card :deep(.ant-card-cover img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sk-card-hoverable:hover {
    box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
        0 3px 6px 0 rgba(0, 0, 0, 0.12),
        0 5px 12px 4px rgba(0, 0, 0, 0.09);
    transition: box-shadow 0.3s;
}

.sk-card-loading :deep(.ant-card-loading-content) {
    padding: 24px;
}

.sk-card-loading :deep(.ant-card-loading-block) {
    height: 14px;
    margin: 4px 0;
    background: linear-gradient(90deg, rgba(207, 216, 220, 0.2), rgba(207, 216, 220, 0.4), rgba(207, 216, 220, 0.2));
    background-size: 600% 600%;
    border-radius: 2px;
    animation: card-loading 1.4s ease infinite;
}

@keyframes card-loading {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}
</style>