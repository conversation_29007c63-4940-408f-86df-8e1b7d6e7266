<template>
    <a-progress v-bind="$attrs" :class="['sk-progress', className]" :percent="percent" :status="status"
        :stroke-color="strokeColor" :type="type" :show-info="showInfo" :stroke-width="strokeWidth"
        :stroke-linecap="strokeLinecap" :trailColor="trailColor" :size="size" :steps="steps" :format="customFormat">
    </a-progress>
</template>

<script>
import { defineComponent, h } from 'vue'

export default defineComponent({
    name: 'SkProgress',
    props: {
        className: {
            type: String,
            default: ''
        },
        percent: {
            type: Number,
            default: 0
        },
        status: {
            type: String,
            default: undefined
        },
        strokeColor: {
            type: [String, Object],
            default: undefined
        },
        type: {
            type: String,
            default: 'line'
        },
        showInfo: {
            type: Boolean,
            default: true
        },
        strokeWidth: {
            type: Number,
            default: undefined
        },
        strokeLinecap: {
            type: String,
            default: 'round'
        },
        trailColor: {
            type: String,
            default: undefined
        },
        size: {
            type: String,
            default: 'default'
        },
        steps: {
            type: Number,
            default: undefined
        }
    },
    setup(props, { slots }) {
        const customFormat = (percent) => {
            if (slots.format) {
                return h('div', {}, [slots.format({ percent })])
            }
            return `${percent}%`
        }

        return {
            customFormat
        }
    }
})
</script>

<style lang="less" scoped>
.sk-progress {
    &.ant-progress-circle {
        .ant-progress-text {
            color: rgba(0, 0, 0, 0.85);
        }
    }

    &.ant-progress-status-success {
        .ant-progress-text {
            color: #52c41a;
        }
    }

    &.ant-progress-status-exception {
        .ant-progress-text {
            color: #ff4d4f;
        }
    }
}
</style>