<template>
    <div class="sk-auth-btn-group">
        <sk-segmented v-model="value" :options="options" @change="handleChange">
            <template #default="{ item }">
                <div style="padding: 4px 0">
                    <div style="margin-top: 4px">{{ item.label }}</div>
                </div>
            </template>
        </sk-segmented>
    </div>
</template>

<script setup>
import SkSegmented from '@/components/SkSegmented/index.vue'
import { ref, onMounted, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
    // 权限点
    authPointCode: {
        type: String,
        default: ''
    }
})

const value = ref('')
const options = ref([])

const emit = defineEmits(['change'])

const handleChange = (value) => {
    emit('change', value.split('==')[0].trim(), value.split('==')[1].trim())
}

// 组件挂载时获取数据
onMounted(() => {
    const authList = proxy.$config.loadAuthBtnGroup(props.authPointCode)
    options.value = authList
    if (authList.length > 0) {
        value.value = authList[0].value
        emit('change', authList[0].value.split('==')[0].trim(), authList[0].value.split('==')[1].trim())
    }
})
</script>