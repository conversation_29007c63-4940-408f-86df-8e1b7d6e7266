<template>
    <div class="sk-avatar" :class="[`sk-avatar-${shape}`, `sk-avatar-${size}`]">
        <a-avatar :src="src" :alt="alt" :size="size" :shape="shape" :gap="gap" :draggable="draggable"
            @error="handleError" @click="handleClick">
            <!-- 自定义图标 -->
            <template v-if="$slots.icon" #icon>
                <slot name="icon"></slot>
            </template>
            <template v-else-if="icon" #icon>
                <component :is="icon" />
            </template>

            <!-- 默认内容 -->
            <slot></slot>
        </a-avatar>

        <!-- 徽标 -->
        <template v-if="badge">
            <a-badge :count="badge.count" :dot="badge.dot" :color="badge.color" :offset="badge.offset">
                <template v-if="$slots.badge" #count>
                    <slot name="badge"></slot>
                </template>
            </a-badge>
        </template>
    </div>
</template>

<script setup>
import { UserOutlined } from '@ant-design/icons-vue'

const props = defineProps({
    // 图片类头像的资源地址
    src: {
        type: String,
        default: ''
    },
    // 图像无法显示时的替代文本
    alt: {
        type: String,
        default: 'avatar'
    },
    // 设置头像的图标类型
    icon: {
        type: [Object, Function],
        default: () => UserOutlined
    },
    // 设置头像的大小
    size: {
        type: [Number, String],
        default: 'default',
        validator: (value) => {
            if (typeof value === 'string') {
                return ['large', 'default', 'small'].includes(value)
            }
            return typeof value === 'number'
        }
    },
    // 指定头像的形状
    shape: {
        type: String,
        default: 'circle',
        validator: (value) => ['circle', 'square'].includes(value)
    },
    // 字符类型距离左右两侧边界单位像素
    gap: {
        type: Number,
        default: 4
    },
    // 图片是否允许拖动
    draggable: {
        type: Boolean,
        default: false
    },
    // 徽标配置
    badge: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['error', 'click'])

// 处理图片加载错误
const handleError = (e) => {
    emit('error', e)
}

// 处理点击事件
const handleClick = (e) => {
    emit('click', e)
}
</script>

<style scoped>
.sk-avatar {
    display: inline-block;
    position: relative;
}

.sk-avatar :deep(.ant-avatar) {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 大小样式 */
.sk-avatar-large :deep(.ant-avatar) {
    font-size: 24px;
}

.sk-avatar-small :deep(.ant-avatar) {
    font-size: 14px;
}

/* 形状样式 */
.sk-avatar-square :deep(.ant-avatar) {
    border-radius: 2px;
}

/* 徽标样式 */
.sk-avatar :deep(.ant-badge) {
    position: absolute;
    top: 0;
    right: 0;
}

/* 图片样式 */
.sk-avatar :deep(.ant-avatar-image) {
    background: transparent;
}

.sk-avatar :deep(.ant-avatar-image img) {
    object-fit: cover;
}

/* 图标样式 */
.sk-avatar :deep(.ant-avatar-icon) {
    font-size: inherit;
}
</style>