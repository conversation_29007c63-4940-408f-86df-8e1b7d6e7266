<template>
    <div class="sk-image">
        <a-image :src="src" :width="width" :height="height" :alt="alt" :fallback="fallback" :placeholder="placeholder"
            :preview="preview" :previewMask="previewMask" :rootClassName="rootClassName" @error="handleError">
            <template #placeholder v-if="$slots.placeholder">
                <slot name="placeholder"></slot>
            </template>
            <template #previewMask v-if="$slots.previewMask">
                <slot name="previewMask"></slot>
            </template>
        </a-image>
    </div>
</template>

<script setup>
defineProps({
    // 图片地址
    src: {
        type: String,
        required: true
    },
    // 图片宽度
    width: {
        type: [Number, String],
        default: undefined
    },
    // 图片高度
    height: {
        type: [Number, String],
        default: undefined
    },
    // 图片描述
    alt: {
        type: String,
        default: ''
    },
    // 加载失败时显示的图片
    fallback: {
        type: String,
        default: undefined
    },
    // 加载时显示的图片
    placeholder: {
        type: String,
        default: undefined
    },
    // 预览参数，为 false 时禁用预览
    preview: {
        type: [Boolean, Object],
        default: true
    },
    // 自定义预览遮罩
    previewMask: {
        type: [Boolean, Function],
        default: undefined
    },
    // 根节点 class
    rootClassName: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['error'])

// 图片加载错误事件
const handleError = (e) => {
    emit('error', e)
}
</script>

<style lang="less" scoped>
.sk-image {
    display: inline-block;
}
</style>