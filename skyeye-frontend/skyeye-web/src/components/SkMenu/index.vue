<template>
    <div class="sk-menu">
        <a-menu v-bind="menuProps" @click="handleMenuClick" @openChange="handleOpenChange" @select="handleSelect">
            <!-- 递归渲染菜单项 -->
            <template v-for="item in normalizedItems" :key="item.key">
                <!-- 分割线 -->
                <a-menu-divider v-if="item.type === 'divider'" />

                <!-- 子菜单 -->
                <a-sub-menu v-else-if="item.children" :key="item.key">
                    <template #title>
                        <component v-if="item.icon" :is="item.icon" />
                        <span>{{ item.label }}</span>
                    </template>
                    <template v-for="child in item.children" :key="child.key">
                        <a-menu-item v-if="!child.children" :disabled="child.disabled">
                            <template #icon v-if="child.icon">
                                <component :is="child.icon" />
                            </template>
                            {{ child.label }}
                        </a-menu-item>
                        <a-sub-menu v-else :key="child.key">
                            <template #title>
                                <component v-if="child.icon" :is="child.icon" />
                                <span>{{ child.label }}</span>
                            </template>
                            <a-menu-item v-for="subChild in child.children" :key="subChild.key"
                                :disabled="subChild.disabled">
                                <template #icon v-if="subChild.icon">
                                    <component :is="subChild.icon" />
                                </template>
                                {{ subChild.label }}
                            </a-menu-item>
                        </a-sub-menu>
                    </template>
                </a-sub-menu>

                <!-- 普通菜单项 -->
                <a-menu-item v-else :key="item.key" :disabled="item.disabled">
                    <template #icon v-if="item.icon">
                        <component :is="item.icon" />
                    </template>
                    {{ item.label }}
                </a-menu-item>
            </template>
        </a-menu>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 菜单项配置
    items: {
        type: Array,
        default: () => []
    },
    // 菜单类型
    mode: {
        type: String,
        default: 'vertical',
        validator: (value) => ['vertical', 'horizontal', 'inline'].includes(value)
    },
    // 当前选中的菜单项 key 数组
    selectedKeys: {
        type: Array,
        default: () => []
    },
    // 当前展开的 SubMenu 菜单项 key 数组
    openKeys: {
        type: Array,
        default: () => []
    },
    // 是否收起状态
    collapsed: {
        type: Boolean,
        default: false
    },
    // 主题
    theme: {
        type: String,
        default: 'light',
        validator: (value) => ['light', 'dark'].includes(value)
    },
    // 是否允许选中
    selectable: {
        type: Boolean,
        default: true
    },
    // 是否允许多选
    multiple: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['click', 'openChange', 'select'])

// 处理菜单项，确保每个项都有必要的属性
const normalizedItems = computed(() => {
    const normalizeItem = (item) => {
        if (item.type === 'divider') {
            return { type: 'divider', key: `divider-${Math.random()}` }
        }

        const normalized = {
            key: item.key || `item-${Math.random()}`,
            label: item.label || '',
            icon: item.icon,
            disabled: item.disabled || false,
            danger: item.danger || false
        }

        if (item.children) {
            normalized.children = item.children.map(child => normalizeItem(child))
        }

        return normalized
    }

    return props.items.map(normalizeItem)
})

// 菜单属性
const menuProps = computed(() => ({
    mode: props.mode,
    theme: props.theme,
    selectedKeys: props.selectedKeys,
    openKeys: props.openKeys,
    inlineCollapsed: props.collapsed,
    multiple: props.multiple,
    selectable: props.selectable
}))

// 处理菜单点击
const handleMenuClick = ({ key, keyPath, item, domEvent }) => {
    emit('click', { key, keyPath, item, domEvent })
}

// 处理子菜单展开/收起
const handleOpenChange = (openKeys) => {
    emit('openChange', openKeys)
}

// 处理菜单选中
const handleSelect = ({ key, keyPath, selectedKeys, item, domEvent }) => {
    emit('select', { key, keyPath, selectedKeys, item, domEvent })
}
</script>

<style scoped>
.sk-menu {
    width: 100%;
}

:deep(.ant-menu) {
    border-right: none;
}

:deep(.ant-menu-item),
:deep(.ant-menu-submenu-title) {
    display: flex;
    align-items: center;
}

:deep(.ant-menu-item .anticon),
:deep(.ant-menu-submenu-title .anticon) {
    margin-right: 10px;
}

:deep(.ant-menu-item-disabled),
:deep(.ant-menu-submenu-disabled) {
    cursor: not-allowed;
}

:deep(.ant-menu-item-danger) {
    color: #ff4d4f;
}

:deep(.ant-menu-inline-collapsed) {
    width: 80px;
}

:deep(.ant-menu-inline-collapsed .ant-menu-item),
:deep(.ant-menu-inline-collapsed .ant-menu-submenu-title) {
    padding: 0 24px !important;
}
</style>