<template>
    <div class="sk-auth-table">
        <div class="table-wrapper">
            <table class="auth-table">
                <colgroup>
                    <col style="width: 200px;" />
                    <col v-for="(_, index) in columns.slice(1)" :key="index" style="width: 120px;" />
                </colgroup>
                <thead>
                    <tr>
                        <th class="auth-th fixed-column">{{ columns[0].label }}</th>
                        <th v-for="(column, index) in columns.slice(1)" :key="index" class="auth-th">
                            {{ column.label }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <AuthTr :data="data" :columns="columns" :show-content="showContent" />
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup>
import AuthTr from './authTr.vue'

defineProps({
    data: {
        type: Array,
        default: () => []
    },
    columns: {
        type: Array,
        required: true
    },
    showContent: {
        type: <PERSON>olean,
        default: false
    }
})
</script>

<style scoped>
.sk-auth-table {
    position: relative;
    width: 100%;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
}

.table-wrapper {
    width: 100%;
    overflow-x: auto;
    /* 隐藏滚动条但保持功能 */
    scrollbar-width: thin;
    scrollbar-color: #d9d9d9 #fafafa;
}

.table-wrapper::-webkit-scrollbar {
    height: 6px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: #fafafa;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
}

.auth-table {
    width: max-content;
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: #fff;
}

.auth-th {
    padding: 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
}

.fixed-column {
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: #fafafa;
    border-right: 1px solid #f0f0f0;
}

.auth-th:first-child {
    border-top-left-radius: 2px;
}

.auth-th:last-child {
    border-top-right-radius: 2px;
}
</style>