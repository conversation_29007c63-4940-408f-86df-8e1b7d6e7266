<template>
    <template v-for="(item, index) in data" :key="index">
        <tr :class="['auth-tr', { 'auth-tr-hover': !item.children?.length }]">
            <td class="auth-td fixed-column">
                <div class="name-cell" :style="{ paddingLeft: zIndex * 20 + 'px' }">
                    <span v-if="item.children?.length > 0" class="expand-icon" @click="toggleCollapse(item)">
                        <RightOutlined v-if="item.isCollapsed" />
                        <DownOutlined v-else />
                    </span>
                    <span :class="['cell-text', { 'is-parent': item.children?.length }]">
                        {{ item[columns[0].prop] }}
                    </span>
                </div>
            </td>
            <td v-for="(column, colIndex) in columns.slice(1)" :key="colIndex" class="auth-td">
                <div class="auth-cell">
                    <template v-if="!showContent">
                        <CheckOutlined v-if="item[column.prop] == 1" class="success-icon" />
                        <CloseOutlined v-else class="error-icon" />
                    </template>
                    <span v-else>{{ item[column.prop] }}</span>
                </div>
            </td>
        </tr>
        <AuthTr v-if="item.children?.length > 0 && !item.isCollapsed" :data="item.children" :columns="columns"
            :z-index="zIndex + 1" :show-content="showContent" />
    </template>
</template>

<script setup>
import { onMounted } from 'vue'
import { CheckOutlined, CloseOutlined, RightOutlined, DownOutlined } from '@ant-design/icons-vue'

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    columns: {
        type: Array,
        required: true
    },
    zIndex: {
        type: Number,
        default: 0
    },
    showContent: {
        type: Boolean,
        default: false
    }
})

// 初始化折叠状态
onMounted(() => {
    props.data?.forEach(item => {
        item.isCollapsed = false
    })
})

// 切换折叠状态
const toggleCollapse = (item) => {
    item.isCollapsed = !item.isCollapsed
}
</script>

<style scoped>
.auth-tr {
    transition: all 0.3s ease;
}

.auth-tr-hover:hover {
    background-color: #fafafa;
}

.auth-td {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    white-space: nowrap;
}

.name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expand-icon {
    cursor: pointer;
    color: #595959;
    display: inline-flex;
    align-items: center;
    transition: transform 0.3s;
}

.expand-icon:hover {
    color: #1890ff;
}

.cell-text {
    color: rgba(0, 0, 0, 0.85);
}

.is-parent {
    font-weight: 500;
}

.auth-cell {
    display: flex;
    align-items: center;
}

.success-icon {
    color: #52c41a;
    font-size: 16px;
}

.error-icon {
    color: #ff4d4f;
    font-size: 16px;
}

.fixed-column {
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: #fff;
    border-right: 1px solid #f0f0f0;
}
</style>