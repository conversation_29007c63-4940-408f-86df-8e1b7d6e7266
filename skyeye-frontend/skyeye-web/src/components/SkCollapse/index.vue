<template>
    <div class="sk-collapse">
        <!-- 编辑模式 -->
        <template v-if="isEdit === $config.formEditType.isEdit">
            <div class="collapse-container">
                <!-- 标签 -->
                <div v-if="label" class="collapse-label">{{ label }}</div>
                <!-- 折叠面板主体 -->
                <a-collapse v-model:activeKey="activeKeys" :bordered="bordered" :accordion="accordion" :ghost="ghost"
                    :expandIconPosition="expandIconPosition" @change="handleChange">
                    <template v-for="(panel, index) in panels" :key="panel.key || index">
                        <a-collapse-panel :header="panel.header" :disabled="panel.disabled" :showArrow="panel.showArrow"
                            :extra="panel.extra" :collapsible="panel.collapsible" :forceRender="panel.forceRender">
                            <!-- 自定义展开图标 -->
                            <template #expandIcon v-if="panel.expandIcon">
                                <component :is="panel.expandIcon" />
                            </template>

                            <!-- 自定义额外内容 -->
                            <template #extra v-if="panel.extra">
                                <div class="panel-extra">
                                    {{ panel.extra }}
                                </div>
                            </template>

                            <!-- 面板内容 -->
                            <template v-if="panel.children">
                                <!-- 嵌套面板 -->
                                <a-collapse v-model:activeKey="panel.activeKeys" :bordered="false" :ghost="true"
                                    :accordion="panel.accordion" @change="(keys) => handleNestedChange(panel.key, keys)">
                                    <a-collapse-panel v-for="child in panel.children" :key="child.key"
                                        :header="child.header" :disabled="child.disabled" :showArrow="child.showArrow"
                                        :extra="child.extra">
                                        <slot :name="child.key" v-if="$slots[child.key]"></slot>
                                        <template v-else>{{ child.content }}</template>
                                    </a-collapse-panel>
                                </a-collapse>
                            </template>
                            <template v-else>
                                <!-- 插槽内容 -->
                                <slot :name="panel.key" v-if="$slots[panel.key]"></slot>
                                <!-- 默认内容 -->
                                <template v-else>{{ panel.content }}</template>
                            </template>
                        </a-collapse-panel>
                    </template>
                </a-collapse>
            </div>
        </template>
        <!-- 查看模式 -->
        <template v-else>
            <div class="view-mode">
                <div v-if="label" class="view-label">{{ label }}：</div>
                <div class="view-content">
                    <template v-for="(panel, index) in panels" :key="panel.key || index">
                        <div v-if="activeKeys.includes(panel.key)" class="view-panel">
                            <div class="view-panel-header">
                                {{ panel.header }}
                                <span v-if="panel.extra" class="view-panel-extra">{{ panel.extra }}</span>
                            </div>
                            <div class="view-panel-content">
                                <template v-if="panel.children">
                                    <div v-for="child in panel.children" :key="child.key" class="nested-panel">
                                        <div class="nested-panel-header">{{ child.header }}</div>
                                        <div class="nested-panel-content">
                                            <slot :name="child.key" v-if="$slots[child.key]"></slot>
                                            <template v-else>{{ child.content }}</template>
                                        </div>
                                    </div>
                                </template>
                                <template v-else>
                                    <slot :name="panel.key" v-if="$slots[panel.key]"></slot>
                                    <template v-else>{{ panel.content }}</template>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    modelValue: {
        type: [String, Array],
        default: () => []
    },
    label: {
        type: String,
        default: ''
    },
    panels: {
        type: Array,
        default: () => []
    },
    accordion: {
        type: Boolean,
        default: false
    },
    bordered: {
        type: Boolean,
        default: true
    },
    ghost: {
        type: Boolean,
        default: false
    },
    expandIconPosition: {
        type: String,
        default: 'start'
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 使用计算属性处理双向绑定
const activeKeys = computed({
    get: () => {
        // 如果是字符串，转换为数组
        return Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
    },
    set: (value) => {
        // 如果原始值是字符串且是手风琴模式，返回单个值
        const newValue = props.accordion ? value[value.length - 1] : value
        emit('update:modelValue', newValue)
    }
})

// 处理面板展开/收起
const handleChange = (keys) => {
    // 如果是手风琴模式，只取最后一个值
    const newValue = props.accordion ? keys[keys.length - 1] : keys
    emit('update:modelValue', newValue)
    emit('change', newValue)
}

// 处理嵌套面板的变化
const handleNestedChange = (parentKey, keys) => {
    emit('change', { parentKey, keys })
}
</script>

<style scoped>
.sk-collapse {
    width: 100%;
}

.collapse-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.collapse-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

.view-mode {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.view-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

.view-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.view-panel {
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.view-panel-header {
    padding: 12px 16px;
    background-color: #fafafa;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.view-panel-extra {
    font-weight: normal;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

.view-panel-content {
    padding: 16px;
    background-color: #fff;
}

:deep(.ant-collapse) {
    background: #fff;
}

:deep(.ant-collapse-header) {
    align-items: center !important;
}

:deep(.ant-collapse-content) {
    color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-collapse-ghost) {
    background: transparent;
}

.panel-extra {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

/* 嵌套面板样式 */
:deep(.ant-collapse .ant-collapse) {
    border: none;
    background: transparent;
}

:deep(.ant-collapse .ant-collapse .ant-collapse-item) {
    border-bottom: none;
}

:deep(.ant-collapse .ant-collapse .ant-collapse-header) {
    padding-left: 32px;
}

:deep(.ant-collapse .ant-collapse .ant-collapse-content) {
    border-top: none;
}

/* 添加嵌套面板样式 */
.nested-panel {
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 2px;
}

.nested-panel:last-child {
    margin-bottom: 0;
}

.nested-panel-header {
    padding: 8px 16px;
    background-color: #fafafa;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
}

.nested-panel-content {
    padding: 12px 16px;
    background-color: #fff;
}
</style>