<template>
    <div class="sk-dict-wrapper">
        <div v-if="isEdit == $config.formEditType.isEdit">
            <SkTreeSelect v-model="value" :tree-data="processedTreeData" @change="handleChange"
                :field-names="fieldNames" />
        </div>
        <div v-else-if="isEdit == $config.formEditType.notEdit">
            {{ showValue }}
        </div>
    </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import SkTreeSelect from '@/components/SkTreeSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Array, String, Object]
    },

    // 属性key
    attrKey: {
        type: String,
        default: ''
    },

    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },

    objectId: {
        type: String,
        default: ''
    },

    objectKey: {
        type: String,
        default: ''
    },

    // 是否显示根节点
    showRoot: {
        type: Boolean,
        default: true
    },

    // 根节点名称
    rootName: {
        type: String,
        default: '全部'
    },

    formData: {}
})

const emit = defineEmits(['update:modelValue', 'change'])

const value = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

const treeData = ref([])
const fieldNames = ref({
    label: 'name',
    value: 'id',
    children: 'children'
})

const showValue = ref('')

// 处理树数据，添加根节点
const processedTreeData = computed(() => {
    if (props.showRoot) {
        const rootNode = {
            [fieldNames.value.value]: '0',
            [fieldNames.value.label]: props.rootName,
            [fieldNames.value.children]: treeData.value
        }
        return [rootNode]
    }
    return treeData.value
})

const handleChange = (value) => {
    emit('change', value)
}

// 获取数据
const fetchData = async () => {
    if (props.isEdit == $config.formEditType.isEdit) {
        var params = {
            objectId: props.objectId,
            objectKey: props.objectKey,
            addOrUser: false
        }
        const res = await proxy.$http.post(proxy.$config.getConfig().reqBasePath + "queryCatalogForTree", params)
        treeData.value = [].concat(res.rows)

        // 检查modelValue是否存在于数据中
        if (props.modelValue) {
            const valueExists = checkValueExistsInTree(treeData.value, props.modelValue)
            if (!valueExists) {
                // 如果modelValue不存在于数据中，则设置为null
                emit('update:modelValue', null)
            }
        }
    } else {
        const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
        showValue.value = props.formData[keyMation]?.name || ''
    }
}

// 检查值是否存在于树中的递归函数
const checkValueExistsInTree = (tree, value) => {
    if (!tree || !value) return false

    // 检查是否是数组类型的值（多选情况）
    if (Array.isArray(value)) {
        // 对于数组，检查每个值是否至少有一个存在
        return value.some(item => checkValueExistsInTree(tree, item))
    }

    // 处理值可能是对象的情况
    const valueId = typeof value === 'object' ? value.id || value.value : value

    // 检查当前层级
    for (const node of tree) {
        // 检查当前节点
        if (node.id === valueId) {
            return true
        }

        // 递归检查子节点
        if (node.children && node.children.length > 0) {
            if (checkValueExistsInTree(node.children, value)) {
                return true
            }
        }
    }

    return false
}

onMounted(() => {
    fetchData()
})

</script>

<style scoped></style>