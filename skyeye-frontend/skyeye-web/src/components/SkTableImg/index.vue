<template>
    <div class="sk-table-img">
        <img :src="$config.getConfig().fileBasePath + imgPath" class="photo-img"
            :style="{ height: height, width: width }" @click="handlePreview">
    </div>
</template>

<script setup>
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
    height: {
        type: String,
        default: '21px'
    },

    width: {
        type: String,
        default: '21px'
    },

    imgPath: {
        type: String,
        default: ''
    }
})

const handlePreview = () => {
    proxy.$config.previewFile('图片', props.imgPath)
}

</script>

<style scoped></style>