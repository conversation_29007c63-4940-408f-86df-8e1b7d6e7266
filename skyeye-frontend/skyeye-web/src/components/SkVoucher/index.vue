<template>
    <div class="sk-voucher-wrapper">
        <SkDynamicTable ref="tableRef" v-model="voucherItems" :columns="columns" :isEdit="isEdit" :showAdd="true"
            :showDelete="true" @add="handleAdd" @delete="handleDelete">
            <!-- 自定义凭证选择组件 -->
            <template #cell-chooseInput-voucherId="{ record, index }">
                <SkInput v-model="(record.voucherMation || {}).name" readonly :placeholder="'请选择凭证'" :isEdit="isEdit">
                    <template #suffix>
                        <plus-outlined class="add-icon" v-if="isEdit !== $config.formEditType.notEdit"
                            @click="() => handleChooseVoucher(record)" />
                    </template>
                </SkInput>
            </template>
            <!-- 自定义会计科目选择组件 -->
            <template #cell-chooseInput-subjectId="{ record, index }">
                <SkInput v-model="(record.subjectMation || {}).name" readonly :placeholder="'请选择会计科目'" :isEdit="isEdit">
                    <template #suffix>
                        <plus-outlined class="add-icon" v-if="isEdit !== $config.formEditType.notEdit"
                            @click="() => handleChooseSubject(record)" />
                    </template>
                </SkInput>
            </template>
            <!-- 自定义借方金额输入组件 -->
            <template #cell-money-debitAmount="{ record, index }">
                <MoneyInput v-model="record.debitAmount" :max="*********.99" :isEdit="isEdit"
                    @change="(value) => handleMoneyChange(value, record, 'debit')" />
            </template>
            <!-- 自定义贷方金额输入组件 -->
            <template #cell-money-creditAmount="{ record, index }">
                <MoneyInput v-model="record.creditAmount" :max="*********.99" :isEdit="isEdit"
                    @change="(value) => handleMoneyChange(value, record, 'credit')" />
            </template>
        </SkDynamicTable>

        <!-- 合计行 -->
        <div class="voucher-total">
            <div class="total-label">合计：</div>
            <div class="total-debit">
                <MoneyInput :modelValue="totalDebit" :isEdit=$config.formEditType.notEdit :max="*********.99" />
            </div>
            <div class="total-credit">
                <MoneyInput :modelValue="totalCredit" :isEdit=$config.formEditType.notEdit :max="*********.99" />
            </div>
        </div>

        <!-- 选择弹窗 -->
        <SkModal v-model="modalVisible" :title="modalTitle" width="70%" @cancel="handleModalCancel">
            <IfsVoucherListChoose v-if="modalType === 'voucher'" @confirm="handleVoucherConfirm"
                @cancel="handleModalCancel" />
            <IfsAccountSubjectListChoose v-if="modalType === 'subject'" @confirm="handleSubjectConfirm"
                @cancel="handleModalCancel" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkDynamicTable from '@/components/SkDynamicTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import IfsVoucherListChoose from './ifsVoucherListChoose.vue'
import IfsAccountSubjectListChoose from './ifsAccountSubjectListChoose.vue'
import MoneyInput from './moneyInput.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Array, String],
        default: () => []
    },
    width: {
        type: String,
        default: ''
    },
    title: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue','change'])

// 表格引用
const tableRef = ref(null)

// 凭证项数据
const voucherItems = computed({
    get: () => {
        let items = props.modelValue || []
        
        // 如果 modelValue 是字符串，尝试解析为 JSON
        if (typeof items === 'string') {
            try {
                items = JSON.parse(items);
            } catch (error) {
                items = [];
            }
        }
        
        // 确保 items 是数组
        if (!Array.isArray(items)) {
            items = [];
        }
        
        // 处理每一项的借贷方金额
        return items.map(item => {
            // 如果已经处理过的数据，直接返回
            if (item._processed) {
                return item
            }
            const processedItem = { ...item, _processed: true }
            // 如果是从后端获取的数据（包含 directionType 和 eachAmount）
            if (item.directionType && item.eachAmount) {
                if (item.directionType === 1) {
                    // 借方
                    processedItem.debitAmount = item.eachAmount
                    processedItem.creditAmount = '0'
                } else if (item.directionType === 2) {
                    // 贷方
                    processedItem.creditAmount = item.eachAmount
                    processedItem.debitAmount = '0'
                }
            } else {
                // 确保金额字段存在默认值
                processedItem.debitAmount = processedItem.debitAmount || '0'
                processedItem.creditAmount = processedItem.creditAmount || '0'
            }
            return processedItem
        })
    },
    set: (val) => {
        // 在设置值时，将金额转换回后端需要的格式
        const processedItems = val.map(item => {
            // 如果已经处理过，直接返回原始数据
            if (item._processed) {
                const { _processed, ...rest } = item
                return rest
            }
            const processedItem = { ...item }
            // 根据借贷方金额设置 directionType 和 eachAmount
            if (Number(item.debitAmount) > 0) {
                processedItem.directionType = 1
                processedItem.eachAmount = item.debitAmount
            } else if (Number(item.creditAmount) > 0) {
                processedItem.directionType = 2
                processedItem.eachAmount = item.creditAmount
            } else {
                // 如果都为0，保持原有的 directionType
                processedItem.directionType = item.directionType || 1
                processedItem.eachAmount = '0'
            }
            return processedItem
        })
        emit('update:modelValue', JSON.stringify(processedItems))
        emit('change', processedItems)
    }
})

// 表格列配置
const columns = [
    {
        title: '摘要',
        attrKey: 'remark',
        name: '摘要',
        width: 200,
        showType: 'textarea',
        rules: [{ required: true, message: '请输入摘要' }],
        placeholder: '请输入摘要',
        defaultValue: ''
    },
    {
        title: '凭证',
        attrKey: 'voucherId',
        name: '凭证',
        width: 240,
        showType: 'chooseInput',
        rules: [{ required: true, message: '请选择凭证' }],
        placeholder: '请选择凭证',
        defaultValue: '',
        getConfig: (record) => ({
            isEdit: props.isEdit,
            defaultMation: {
                id: '',
                name: ''
            }
        })
    },
    {
        title: '会计科目',
        attrKey: 'subjectId',
        name: '会计科目',
        width: 240,
        showType: 'chooseInput',
        rules: [{ required: true, message: '请选择会计科目' }],
        placeholder: '请选择会计科目',
        defaultValue: '',
        getConfig: (record) => ({
            isEdit: props.isEdit,
            defaultMation: {
                id: '',
                name: ''
            }
        })
    },
    {
        title: '借方金额',
        attrKey: 'debitAmount',
        name: '借方金额',
        width: 218,
        showType: 'money',
        defaultValue: ''
    },
    {
        title: '贷方金额',
        attrKey: 'creditAmount',
        name: '贷方金额',
        width: 218,
        showType: 'money',
        defaultValue: ''
    }
]

// 计算借方合计
const totalDebit = computed(() => {
    if (!Array.isArray(voucherItems.value)) return '0.00';
    return voucherItems.value.reduce((sum, item) => {
        const amount = item.directionType === 1 ?
            Number(item.eachAmount || item.debitAmount || 0) : 0
        return sum + amount
    }, 0).toFixed(2)
})

// 计算贷方合计
const totalCredit = computed(() => {
    if (!Array.isArray(voucherItems.value)) return '0.00';
    return voucherItems.value.reduce((sum, item) => {
        const amount = item.directionType === 2 ?
            Number(item.eachAmount || item.creditAmount || 0) : 0
        return sum + amount
    }, 0).toFixed(2)
})

// 弹窗控制
const modalVisible = ref(false)
const modalType = ref('')
const currentRecord = ref(null)

// 计算弹窗标题
const modalTitle = computed(() => {
    return modalType.value === 'voucher' ? '选择凭证' : '选择会计科目'
})

// 处理新增行
const handleAdd = () => {
    const newRow = {
        id: Date.now() + Math.random().toString(36).slice(2),
        remark: '',
        voucherId: '',
        voucherMation: {
            id: '',
            name: ''
        },
        subjectId: '',
        subjectMation: {
            id: '',
            name: ''
        },
        debitAmount: '0',
        creditAmount: '0',
        directionType: 1,
        eachAmount: '0'
    }
    // 直接通过emit更新modelValue，避免修改computed属性
    const newItems = [...voucherItems.value, newRow]
    emit('update:modelValue', JSON.stringify(newItems))
    emit('change', newItems)
}

// 处理删除行
const handleDelete = (keys) => {
    if (voucherItems.value.length <= 2) {
        SkMessage.warning('最少需要保留两行数据')
        return
    }
    // 直接通过emit更新modelValue，避免修改computed属性
    const newItems = voucherItems.value.filter(item => !keys.includes(item.id))
    emit('update:modelValue', JSON.stringify(newItems))
    emit('change', newItems)
}

// 处理金额变化
const handleMoneyChange = (value, record, type) => {
    const isDebit = type === 'debit'

    if (value) {
        // 检查输入值是否小于0
        if (Number(value) < 0) {
            SkMessage.warning(`${isDebit ? '借' : '贷'}方金额不能小于0`)
            record[isDebit ? 'debitAmount' : 'creditAmount'] = '0'
            record.eachAmount = '0'
            return
        }

        // 计算新的总金额
        let newTotal = voucherItems.value.reduce((sum, item) => {
            if (item.id === record.id) return sum
            return sum + Number(isDebit ? item.debitAmount : item.creditAmount || 0)
        }, 0)
        newTotal += Number(value)

        // 检查是否超出上限
        if (newTotal > *********.99) {
            SkMessage.warning(`${isDebit ? '借' : '贷'}方金额合计不能超过999,999,999.99`)
            record[isDebit ? 'debitAmount' : 'creditAmount'] = '0'
            record.eachAmount = '0'
            return
        }

        // 金额在限制范围内，更新数据
        const newItems = voucherItems.value.map(item => {
            if (item.id === record.id) {
                return {
                    ...item,
                    [isDebit ? 'creditAmount' : 'debitAmount']: '0',
                    [isDebit ? 'debitAmount' : 'creditAmount']: value,
                    directionType: isDebit ? 1 : 2,
                    eachAmount: value
                }
            }
            return item
        })
        
        emit('update:modelValue', JSON.stringify(newItems))
        emit('change', newItems)
    } else {
        const newItems = voucherItems.value.map(item => {
            if (item.id === record.id) {
                return {
                    ...item,
                    [isDebit ? 'debitAmount' : 'creditAmount']: '0',
                    eachAmount: '0'
                }
            }
            return item
        })
        
        emit('update:modelValue', JSON.stringify(newItems))
        emit('change', newItems)
    }
}

// 处理选择凭证
const handleChooseVoucher = (record) => {
    currentRecord.value = record
    modalType.value = 'voucher'
    modalVisible.value = true
}

// 处理选择会计科目
const handleChooseSubject = (record) => {
    currentRecord.value = record
    modalType.value = 'subject'
    modalVisible.value = true
}

// 处理凭证选择确认
const handleVoucherConfirm = (selectedVoucher) => {
    if (currentRecord.value) {
        // 创建新的数组以触发更新
        const newItems = voucherItems.value.map(item => {
            if (item.id === currentRecord.value.id) {
                return {
                    ...item,
                    voucherId: selectedVoucher.id,
                    voucherMation: {
                        id: selectedVoucher.id,
                        name: selectedVoucher.name
                    },
                    // 同步凭证的摘要信息
                    remark: selectedVoucher.remark || item.remark
                }
            }
            return item
        })
        emit('update:modelValue', JSON.stringify(newItems))
        emit('change', newItems)
    }
    modalVisible.value = false
    currentRecord.value = null
}

// 处理会计科目选择确认
const handleSubjectConfirm = (selectedSubject) => {
    if (currentRecord.value) {
        // 创建新的数组以触发更新
        const newItems = voucherItems.value.map(item => {
            if (item.id === currentRecord.value.id) {
                return {
                    ...item,
                    subjectId: selectedSubject.id,
                    subjectMation: {
                        id: selectedSubject.id,
                        name: selectedSubject.name
                    }
                }
            }
            return item
        })
        emit('update:modelValue', JSON.stringify(newItems))
        emit('change', newItems)
    }
    modalVisible.value = false
    currentRecord.value = null
}

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
    currentRecord.value = null
}

// 表单验证
const validate = async () => {
    return await tableRef.value?.validate()
}

// 初始化函数，确保至少有一行数据
const initializeData = () => {
    if (!voucherItems.value || voucherItems.value.length === 0) {
        handleAdd();
    }
}

// 在组件挂载后初始化数据
onMounted(() => {
    initializeData();
})

defineExpose({
    validate
})
</script>

<style scoped>
.sk-voucher-wrapper {
    width: 100%;
}

.voucher-total {
    display: flex;
    margin-top: 16px;
    border: 1px solid #ccc;
    background-color: #fafafa;
}

.total-label {
    padding: 8px 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    border-right: 1px solid #ccc;
}

.total-debit,
.total-credit {
    width: 218px;
    padding: 8px 16px;
    text-align: right;
    font-weight: bold;
    border-right: 1px solid #ccc;
}

.total-credit {
    border-right: none;
}

:deep(.ant-input-number) {
    width: 100%;
}

:deep(.money-input) {
    text-align: right;
    font-family: 'tahoma';
    font-weight: bold;
    letter-spacing: 11px;
}
</style>
