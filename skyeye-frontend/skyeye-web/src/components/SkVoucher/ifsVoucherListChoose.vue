<template>
    <div class="sk-voucher-select">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 提示信息 -->
            <SkAlert message="凭证选择规则：点击选中凭证后，点击确定按钮进行选择。" type="info" show-icon />

            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入名称" allowClear />
                    </a-form-item>
                </SkForm>
                <a-form-item-rest>
                    <SkButton type="primary" @click.prevent="handleConfirm" :loading="loading"
                        :disabled="!selectedRowKey">
                        <template #icon>
                            <SaveOutlined />
                        </template>
                        {{ $t('common.confirm') }}
                    </SkButton>
                </a-form-item-rest>
            </div>

            <!-- 操作按钮 -->
            <a-form-item-rest>
                <SkButton type="primary" @click.prevent="fetchData">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    {{ $t('common.refresh') }}
                </SkButton>
            </a-form-item-rest>

            <!-- 表格区域 -->
            <a-form-item-rest>
                <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                    :ready="tableReady" @change="handleTableChange" :tipInfoHeight="56" :row-selection="{
                        type: 'radio',
                        selectedRowKeys: [selectedRowKey],
                        onChange: handleSelectionChange,
                        getCheckboxProps: (record) => ({
                            disabled: record.state !== 1
                        })
                    }" :row-key="record => record.id">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'path'">
                            <SkTableImg :imgPath="record.path" height="30px" width="30px" />
                        </template>
                        <template v-if="column.dataIndex === 'state'">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['voucherState'], 'id', record.state, 'name')">
                            </div>
                        </template>
                    </template>
                </SkTable>
            </a-form-item-rest>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'
import { SearchOutlined, ReloadOutlined, SaveOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkTableImg from '@/components/SkTableImg/index.vue'

const { proxy } = getCurrentInstance()

const emit = defineEmits(['confirm', 'cancel'])

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据相关
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())
const selectedRowKey = ref('')

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['voucherState'])
    initEnumData.value = result
}

// 表格列配置
const columns = [
    {
        title: '序号',
        type: 'index',
        width: 80,
        align: 'center',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '名称',
        dataIndex: 'name',
        width: 150
    },
    {
        title: '展示',
        dataIndex: 'path',
        width: 80,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 80,
        align: 'center'
    }
]

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'ifsVoucher001',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 重置处理
const handleReset = () => {
    searchForm.keyword = ''
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 处理选择变化
const handleSelectionChange = (selectedKeys) => {
    selectedRowKey.value = selectedKeys[0] || ''
}

// 处理确认
const handleConfirm = () => {
    const selectedRecord = tableData.value.find(item => item.id === selectedRowKey.value)
    if (selectedRecord) {
        emit('confirm', selectedRecord)
    }
}

// 初始化
onMounted(async () => {
    await getInitData()
    nextTick(() => {
        tableReady.value = true
        fetchData()
    })

})
</script>

<style scoped>
.table-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
</style>
