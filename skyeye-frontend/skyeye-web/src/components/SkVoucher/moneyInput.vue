<template>
    <div class="money-input-wrapper">
        <!-- 只读模式 -->
        <div v-if="isEdit === $config.formEditType.notEdit" class="money-display sk-detail-readonly">
            <div class="money-grid">
                <div v-for="(unit, index) in units" :key="unit" class="money-unit">
                    <span class="unit-label">{{ unit }}</span>
                    <span class="unit-value">{{ displayValues[index] || '0' }}</span>
                </div>
            </div>
        </div>
        <!-- 编辑模式 -->
        <div v-else>
            <div v-show="!isEditing" class="money-display" @click="startEdit">
                <div class="money-grid">
                    <div v-for="(unit, index) in units" :key="unit" class="money-unit">
                        <span class="unit-label">{{ unit }}</span>
                        <span class="unit-value">{{ displayValues[index] || '0' }}</span>
                    </div>
                </div>
            </div>
            <div v-show="isEditing" class="money-edit">
                <SkInput ref="inputRef" v-model="inputValue" @blur="handleBlur" @change="handleInput"
                    @pressEnter="handleBlur" :placeholder="placeholder" :isEdit="$config.formEditType.isEdit" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import SkInput from '@/components/SkInput/index.vue'

const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: '0.00'
    },
    placeholder: {
        type: String,
        default: '请输入金额'
    },
    max: {
        type: Number,
        default: 999999999.99
    },
    min: {
        type: Number,
        default: 0
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 金额单位
const units = ['亿', '千', '百', '十', '万', '千', '百', '十', '元', '角', '分']

const isEditing = ref(false)
const inputValue = ref('')
const inputRef = ref(null)

// 将数字转换为显示值数组
const displayValues = computed(() => {
    try {
        // 处理空值或无效值的情况
        let value = props.modelValue
        if (!value && value !== 0) {
            return Array(11).fill('0')
        }

        // 将字符串形式的金额转换为数字并格式化（以分为单位）
        const numValue = Number(value) / 100
        if (isNaN(numValue)) {
            return Array(11).fill('0')
        }

        // 将数字转换为固定两位小数的字符串
        const numStr = numValue.toFixed(2)
        const [intPart, decPart] = numStr.split('.')
        
        // 处理整数部分，补齐9位
        const intDigits = intPart.padStart(9, '0').split('')
        // 处理小数部分
        const decDigits = decPart.split('')

        return [...intDigits, ...decDigits]
    } catch (error) {
        return Array(11).fill('0')
    }
})

// 开始编辑
const startEdit = () => {
    if (props.isEdit === $config.formEditType.notEdit) return
    
    // 将分转换为元显示
    const value = props.modelValue ? (Number(props.modelValue) / 100).toFixed(2) : '0.00'
    inputValue.value = value
    isEditing.value = true
    
    nextTick(() => {
        const inputElement = inputRef.value?.$el?.querySelector('input')
        if (inputElement) {
            inputElement.focus()
            inputElement.select()
        }
    })
}

// 处理输入
const handleInput = (e) => {
    let value = e.value || ''
    
    // 只允许输入数字和小数点
    value = value.replace(/[^\d.]/g, '')
    
    // 处理多个小数点的情况
    const parts = value.split('.')
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
    }
    
    // 限制小数点后两位
    if (value.includes('.')) {
        const [int, dec] = value.split('.')
        value = int + '.' + dec.slice(0, 2)
    }

    // 限制整数部分不超过9位
    if (!value.includes('.')) {
        value = value.slice(0, 9)
    } else {
        const [int] = value.split('.')
        if (int.length > 9) {
            value = int.slice(0, 9) + value.slice(int.length)
        }
    }

    inputValue.value = value
}

// 处理失焦
const handleBlur = () => {
    let value = inputValue.value || '0'
    let numValue = Number(value)
    
    if (isNaN(numValue)) {
        numValue = 0
    }
    
    if (numValue > props.max) {
        numValue = props.max
    }
    if (numValue < props.min) {
        numValue = props.min
    }

    // 转换为分
    const cents = Math.round(numValue * 100)
    const newValue = cents.toString()
    
    // 如果值发生变化才触发更新
    if (newValue !== props.modelValue) {
        emit('update:modelValue', newValue)
        emit('change', newValue)
    }

    isEditing.value = false
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    if (newVal !== undefined) {
        // 如果不在编辑状态，更新显示值
        if (!isEditing.value) {
            const value = newVal ? (Number(newVal) / 100).toFixed(2) : '0.00'
            inputValue.value = value
        }
    }
}, { immediate: true })

// 监听编辑状态变化
watch(() => props.isEdit, () => {
    isEditing.value = false
}, { immediate: true })
</script>

<style scoped>
.money-input-wrapper {
    width: 100%;
    position: relative;
}

.money-display {
    cursor: text;
    min-height: 32px;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    background-color: #fff;
}

.money-display:hover {
    border-color: #40a9ff;
}

.sk-detail-readonly {
    border: none;
    background-color: transparent;
    cursor: default;
}

.sk-detail-readonly:hover {
    border-color: transparent;
}

.money-grid {
    display: grid;
    grid-template-columns: repeat(11, 1fr);
    gap: 1px;
    text-align: center;
}

.money-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.unit-label {
    font-size: 12px;
    color: #999;
}

.unit-value {
    font-size: 14px;
    min-height: 20px;
    color: #333;
}

.money-edit {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

:deep(.money-edit .ant-input) {
    text-align: right;
    font-family: 'tahoma';
    font-weight: bold;
}
</style>