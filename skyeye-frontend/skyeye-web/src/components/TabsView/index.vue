<template>
  <div class="tabs-view-container">
    <a-tabs v-model:activeKey="activeKey" type="editable-card" :hide-add="true" @edit="onEdit" @change="onChange">
      <a-tab-pane v-for="tab in visitedTabs" :key="tab.path" :closable="tab.path !== '/dashboard'">
        <template #tab>
          <a-dropdown :trigger="['contextmenu']">
            <div class="tab-item">
              <component :is="getIcon(tab.meta?.icon)" class="tab-icon" />
              <span>{{ tab.meta?.title || '未命名' }}</span>
            </div>
            <template #overlay>
              <a-menu @click="({ key }) => handleContextMenu(key, tab)">
                <a-menu-item key="refresh">
                  <reload-outlined /> 刷新页面
                </a-menu-item>
                <a-menu-item key="close" :disabled="tab.path === '/dashboard'">
                  <close-outlined /> 关闭标签
                </a-menu-item>
                <a-menu-item key="closeOthers" :disabled="visitedTabs.length <= 1">
                  <close-circle-outlined /> 关闭其他
                </a-menu-item>
                <a-menu-item key="closeRight" :disabled="isLastTab(tab)">
                  <vertical-right-outlined /> 关闭右侧
                </a-menu-item>
                <a-menu-item key="closeAll" :disabled="visitedTabs.length <= 1">
                  <minus-circle-outlined /> 关闭所有
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, watch, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ReloadOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  VerticalRightOutlined,
  MinusCircleOutlined,
  AppstoreOutlined,
  DashboardOutlined,
  UserOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { nextTick } from 'vue'

const router = useRouter()
const route = useRoute()
const activeKey = ref('/dashboard')
const visitedTabs = ref([
  {
    path: '/dashboard',
    name: 'Dashboard',
    meta: { title: '仪表盘', icon: 'DashboardOutlined' }
  }
])

// 注入 removeCachedView 方法
const removeCachedView = inject('removeCachedView')

// 图标映射
const iconMap = {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  AppstoreOutlined
}

// 获取图标组件
const getIcon = (iconName) => {
  return iconName ? iconMap[iconName] || AppstoreOutlined : AppstoreOutlined
}

// 添加标签
const addTab = (route) => {
  const { name, path, meta, query, fullPath } = route
  // 对于动态标题的路由，使用query中的title
  const title = query?.title || meta?.title || name

  // 使用 fullPath 作为 key，这样可以区分不同参数的同一路由
  if (!visitedTabs.value.find(tab => tab.path === fullPath)) {
    visitedTabs.value.push({
      path: fullPath, // 使用 fullPath 作为路径
      name,
      query, // 保存 query 参数
      meta: {
        title: title || '未命名',
        icon: meta?.icon || 'AppstoreOutlined'
      }
    })
  }
  activeKey.value = fullPath
}

// 修改 watch
watch(
  () => route.fullPath,
  () => {
    if (route.name) {
      addTab(route)
      activeKey.value = route.fullPath // 使用 fullPath
    }
  },
  { immediate: true }
)

// 关闭标签
const closeTab = async (targetPath) => {
  if (targetPath === '/dashboard') return // 防止关闭仪表盘标签

  const tabs = visitedTabs.value
  let activeIndex = tabs.findIndex(tab => tab.path === activeKey.value)

  // 移除目标标签
  visitedTabs.value = tabs.filter(tab => tab.path !== targetPath)

  // 如果关闭的是当前激活的标签，需要激活其他标签
  if (targetPath === activeKey.value) {
    activeIndex = activeIndex >= visitedTabs.value.length ? visitedTabs.value.length - 1 : activeIndex
    const nextTab = visitedTabs.value[activeIndex]
    activeKey.value = nextTab.path
    try {
      await router.replace({
        path: nextTab.path.split('?')[0], // 获取基本路径
        query: nextTab.query // 使用保存的 query 参数
      })
    } catch (error) {
      console.error('Tab navigation error:', error)
    }
  }

  // 清除组件缓存
  const route = router.resolve(targetPath)
  if (route.name && removeCachedView) {
    removeCachedView(route.name)
  }
}

// 处理标签编辑（关闭）
const onEdit = (targetKey, action) => {
  if (action === 'remove') {
    closeTab(targetKey)
  }
}

// 标签切换
const onChange = async (key) => {
  try {
    if (key) {
      const tab = visitedTabs.value.find(tab => tab.path === key)
      if (tab) {
        await router.replace({
          path: tab.path.split('?')[0], // 获取基本路径
          query: tab.query // 使用保存的 query 参数
        })
      }
    }
  } catch (error) {
    console.error('Tab change error:', error)
  }
}

// 判断是否为最后一个标签
const isLastTab = (tab) => {
  const index = visitedTabs.value.findIndex(t => t.path === tab.path)
  return index === visitedTabs.value.length - 1
}

// 处理右键菜单
const handleContextMenu = async (key, tab) => {
  switch (key) {
    case 'refresh':
      const route = router.resolve(tab.path)
      if (route.name && removeCachedView) {
        removeCachedView(route.name)
        nextTick(() => {
          router.replace({
            path: tab.path.split('?')[0],
            query: tab.query
          })
        })
      }
      break
    case 'close':
      closeTab(tab.path)
      break
    case 'closeOthers':
      visitedTabs.value = visitedTabs.value.filter(
        t => t.path === tab.path || t.path === '/dashboard'
      )
      if (activeKey.value !== tab.path) {
        activeKey.value = tab.path
        router.push(tab.path)
      }
      break
    case 'closeRight':
      const index = visitedTabs.value.findIndex(t => t.path === tab.path)
      visitedTabs.value = visitedTabs.value.slice(0, index + 1)
      if (!visitedTabs.value.find(t => t.path === activeKey.value)) {
        activeKey.value = tab.path
        router.push(tab.path)
      }
      break
    case 'closeAll':
      visitedTabs.value = visitedTabs.value.filter(t => t.path === '/dashboard')
      activeKey.value = '/dashboard'
      router.push('/dashboard')
      break
  }
}
</script>

<style scoped>
.tab-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  user-select: none;
}

.tab-icon {
  font-size: 14px;
}
</style>