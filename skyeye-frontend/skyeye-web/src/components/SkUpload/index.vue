<template>
    <div class="sk-upload-wrapper">
        <a-upload-dragger v-if="isEdit === $config.formEditType.isEdit && drag" v-bind="$attrs" :file-list="fileList"
            :before-upload="handleBeforeUpload" :custom-request="customRequest" :max-count="maxCount" :accept="accept"
            :multiple="multiple" :disabled="disabled" :show-upload-list="showUploadList" @change="handleChange"
            @preview="handlePreview" @remove="handleRemove" @drop="handleDrop">
            <template v-if="$slots.dragArea">
                <slot name="dragArea"></slot>
            </template>
            <template v-else>
                <p class="ant-upload-drag-icon">
                    <inbox-outlined />
                </p>
                <p class="ant-upload-text">{{ dragText }}</p>
                <p class="ant-upload-hint">{{ dragHint }}</p>
            </template>
        </a-upload-dragger>

        <a-upload v-else-if="isEdit === $config.formEditType.isEdit" v-bind="$attrs" :file-list="fileList"
            :before-upload="handleBeforeUpload" :custom-request="customRequest" :max-count="maxCount" :accept="accept"
            :multiple="multiple" :disabled="disabled" :list-type="listType" :show-upload-list="showUploadList"
            @change="handleChange" @preview="handlePreview" @remove="handleRemove">
            <template v-if="$slots.uploadButton">
                <slot name="uploadButton"></slot>
            </template>
            <template v-else>
                <div v-if="listType === 'picture-card' && !isMaxCount" class="sk-upload-trigger">
                    <plus-outlined />
                    <div class="ant-upload-text">{{ uploadText }}</div>
                </div>
                <a-button v-else-if="!isMaxCount" :disabled="disabled">
                    <template #icon>
                        <upload-outlined />
                    </template>
                    {{ uploadText }}
                </a-button>
            </template>

            <template v-if="$slots.tip" #tip>
                <slot name="tip"></slot>
            </template>

            <template v-if="$slots.itemRender" #itemRender="itemRenderProps">
                <slot name="itemRender" v-bind="itemRenderProps"></slot>
            </template>
        </a-upload>

        <div v-else class="sk-upload-readonly">
            <div v-if="fileList && fileList.length" class="sk-upload-list" :class="listType">
                <template v-if="listType === 'picture-card' || listType === 'picture'">
                    <div v-for="file in fileList" :key="file.uid" class="sk-upload-item">
                        <img :src="file.url || file.thumbUrl" :alt="file.name" @click="handlePreview(file)" />
                        <div class="file-actions">
                            <eye-outlined class="action-icon" title="预览" @click.stop="handlePreview(file)" />
                            <download-outlined class="action-icon" title="下载" @click.stop="handleDownload(file)" />
                        </div>
                        <div v-if="file.status === 'uploading'" class="upload-progress">
                            <a-progress :percent="file.percent" size="small" :show-info="false" status="active" />
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div v-for="file in fileList" :key="file.uid" class="sk-upload-item">
                        <file-outlined />
                        <span class="file-name line-1" :title="file.name">{{ file.name }}</span>
                        <div class="file-actions">
                            <eye-outlined class="action-icon" title="预览" @click.stop="handlePreview(file)" />
                            <download-outlined class="action-icon" title="下载" @click.stop="handleDownload(file)" />
                        </div>
                        <div v-if="file.status === 'uploading'" class="upload-progress">
                            <a-progress :percent="file.percent" size="small" :show-info="false" status="active" />
                        </div>
                    </div>
                </template>
            </div>
            <div v-else class="sk-upload-placeholder">{{ placeholder }}</div>
        </div>

        <div v-if="help" class="sk-upload-help">
            <info-circle-outlined />
            {{ help }}
        </div>

        <a-modal v-model:open="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
            <div class="preview-content">
                <template v-if="isImageFile">
                    <img :src="previewUrl" :alt="previewTitle" style="width: 100%" />
                </template>
                <template v-else>
                    <div class="preview-file">
                        <file-outlined />
                        <span>{{ previewTitle }}</span>
                    </div>
                </template>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted } from 'vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import {
    PlusOutlined,
    UploadOutlined,
    FileOutlined,
    InfoCircleOutlined,
    InboxOutlined,
    EyeOutlined,
    DownloadOutlined
} from '@ant-design/icons-vue'

const { proxy } = getCurrentInstance()

defineOptions({
    name: 'SkUpload'
})

const props = defineProps({
    modelValue: {
        type: [Array, String, Object],
        default: () => ''
    },
    maxCount: {
        type: Number,
        default: 1
    },
    // 接受上传的文件类型
    accept: {
        type: String,
        default: ''
    },
    multiple: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    listType: {
        type: String,
        default: 'text', // text, picture, picture-card
        validator: (value) => ['text', 'picture', 'picture-card'].includes(value)
    },
    uploadText: {
        type: String,
        default: '上传'
    },
    help: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    placeholder: {
        type: String,
        default: '暂无文件'
    },
    maxSize: {
        type: Number,
        default: 2 // 默认2MB
    },
    allowedTypes: {
        type: Array,
        default: () => []
    },
    showUploadList: {
        type: [Boolean, Object],
        default: true
    },
    drag: {
        type: Boolean,
        default: false
    },
    dragText: {
        type: String,
        default: '点击或拖拽文件到此区域上传'
    },
    dragHint: {
        type: String,
        default: '支持单个或批量上传'
    },
    // 上传地址
    action: {
        type: String,
        default: $config.getConfig().reqBasePath + '/common003'
    },
    // 上传方法
    method: {
        type: String,
        default: 'POST'
    },
    // 请求头
    headers: {
        type: Object,
        default: () => ({
            "requestType": "1",
            "userToken": $config.getUserToken()
        })
    },
    // 请求参数
    data: {
        type: Object,
        default: () => ({})
    },
    // 请求参数名
    name: {
        type: String,
        default: 'file'
    },
    // 是否携带凭证
    withCredentials: {
        type: Boolean,
        default: false
    },
    // 上传类型
    type: {
        type: [String, Number],
        default: '8'
    },
    // 是否是附件
    isEnclosure: {
        type: Boolean,
        default: false
    },
    // 自定义属性
    attrDefinitionCustom: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'preview', 'remove', 'drop'])

const previewVisible = ref(false)
const previewUrl = ref('')
const previewTitle = ref('')

// 是否达到最大上传数量
const isMaxCount = computed(() => {
    return props.maxCount > 0 && fileList.value.length >= props.maxCount
})

// 是否为图片文件
const isImageFile = computed(() => {
    if (!previewUrl.value) return false
    return proxy.$config.$fileType.imageType.some(ext =>
        previewUrl.value.toLowerCase().endsWith(ext) ||
        previewUrl.value.startsWith('data:image/')
    )
})

// 检查文件类型
const checkFileType = (file) => {
    if (!props.allowedTypes.length) return true
    const fileType = file.type || ''
    return props.allowedTypes.some(type => fileType.startsWith(type))
}

// 上传前检查
const handleBeforeUpload = (file) => {
    // 检查文件大小
    const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
    if (!isLtMaxSize) {
        SkMessage.error(`文件必须小于 ${props.maxSize}MB!`)
        return false
    }

    // 检查文件类型是否在accept中
    const fileExt = file.name.split('.').pop()
    if (props.accept && !props.accept.includes(fileExt)) {
        SkMessage.error('不支持的文件类型!')
        return false
    }

    // 检查文件类型
    if (!checkFileType(file)) {
        SkMessage.error('不支持的文件类型!')
        return false
    }

    return true
}

// 自定义上传
const customRequest = ({ file, onSuccess, onError, onProgress }) => {
    if (proxy.$util.isNull(props.type)) {
        SkMessage.error('上传类型不能为空')
        return
    }
    if (props.action !== '/api/upload') {
        const formData = new FormData()
        formData.append(props.name, file)
        formData.append('type', props.type)

        Object.keys(props.data).forEach(key => {
            formData.append(key, props.data[key])
        })

        const xhr = new XMLHttpRequest()
        xhr.open(props.method, props.action, true)

        Object.keys(props.headers).forEach(key => {
            xhr.setRequestHeader(key, props.headers[key])
        })

        xhr.withCredentials = props.withCredentials

        xhr.upload.addEventListener('progress', e => {
            if (e.lengthComputable) {
                const percent = Math.round((e.loaded * 100) / e.total)
                onProgress({ percent })
            }
        })

        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                onSuccess(JSON.parse(xhr.response))
            } else {
                onError(new Error('上传失败'))
            }
        })

        xhr.addEventListener('error', () => {
            onError(new Error('上传失败'))
        })

        xhr.send(formData)
        return xhr
    }

    try {
        let percent = 0
        const interval = setInterval(() => {
            percent += 10
            onProgress({ percent })
            if (percent >= 100) {
                clearInterval(interval)
                const url = URL.createObjectURL(file)
                onSuccess({ url })
            }
        }, 100)
    } catch (error) {
        onError(error)
    }
}

const fileList = ref([])
// 处理文件变化
const handleChange = async (info) => {
    // 处理上传中的状态
    if (info.file.status === 'uploading') {
        fileList.value = info.fileList
        emit('change', info)
        return
    }

    // 处理上传完成的状态
    if (info.file.status === 'done') {
        if (info.file.response?.returnCode !== 0) {
            // 上传失败，从列表中移除
            fileList.value = fileList.value.filter(item => item.uid !== info.file.uid)
            SkMessage.error(info.file.response?.message || '上传失败')
            return
        }

        const fileItem = {
            uid: info.file.uid,
            name: info.file.name,
            url: proxy.$config.getConfig().fileBasePath + info.file.response?.bean?.picUrl,
            percent: info.file.percent,
            status: info.file.status
        }

        if (props.isEnclosure) {
            // 附件上传
            const enclosureId = await handleSaveEnclosureChange(info)
            fileItem.enclosureId = enclosureId
        }
        // 更新文件列表中对应的文件信息
        const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
        if (index > -1) {
            fileList.value[index] = fileItem
        } else {
            fileList.value.push(fileItem)
        }

        if (props.isEnclosure) {
            // 附件上传，获取集合中的附件id转为集合
            const enclosureInfo = fileList.value.filter(item => item.enclosureId).map(item => item.enclosureId).join(',')
            emit('update:modelValue', JSON.stringify({ enclosureInfo: enclosureInfo }))
        } else {
            // 非附件上传
            const fileUrlList = fileList.value.map(item => item.url.replace(proxy.$config.getConfig().fileBasePath, '')).join(',')
            emit('update:modelValue', fileUrlList)
        }
        emit('change', info)
    }

    // 处理上传失败的状态
    if (info.file.status === 'error') {
        fileList.value = fileList.value.filter(item => item.uid !== info.file.uid)
        SkMessage.error('上传失败')
    }
}

const handleSaveEnclosureChange = async (info) => {
    const response = info.file.response
    var params = {
        name: response.bean.fileName,
        path: response.bean.picUrl,
        type: response.bean.fileExtName,
        size: response.bean.size,
        sizeType: response.bean.fileSizeType,
        catalog: '0'
    };
    const res = await proxy.$http.post(proxy.$config.getConfig().reqBasePath + 'createEnclosure', params)
    return res.bean.id
}

// 处理预览
const handlePreview = (file) => {
    const fileName = file.name
    const fileUrl = file.url?.replace(proxy.$config.getConfig().fileBasePath, '') ||
        file.response?.bean?.picUrl

    proxy.$config.previewFile(fileName, fileUrl)
    emit('preview', file)
}

// 添加下载处理
const handleDownload = (file) => {
    const fileName = file.name
    const fileUrl = file.url?.replace(proxy.$config.getConfig().fileBasePath, '') ||
        file.response?.bean?.picUrl

    proxy.$config.downloadFile(fileName, fileUrl)
}

// 处理移除
const handleRemove = (file) => {
    fileList.value.splice(fileList.value.indexOf(file), 1)
    if (props.isEnclosure) {
        // 附件上传，获取集合中的附件id
        const enclosureInfo = fileList.value.filter(item => item.enclosureId).map(item => item.enclosureId).join(',')
        emit('update:modelValue', JSON.stringify({ enclosureInfo: enclosureInfo }))
    } else {
        // 非附件上传，获取文件路径
        const fileUrlList = fileList.value.map(item => item.url.replace(proxy.$config.getConfig().fileBasePath, '')).join(',')
        emit('update:modelValue', fileUrlList)
    }
    emit('remove', file)
}

// 处理预览取消
const handleCancel = () => {
    previewVisible.value = false
}

// 处理拖拽
const handleDrop = (e) => {
    emit('drop', e)
}

onMounted(async () => {
    if (proxy.$util.isNull(props.modelValue)) {
        if (props.isEnclosure) {
            emit('update:modelValue', JSON.stringify({ enclosureInfo: null }))
        }
        return
    }
    const _fileList = []
    if (props.isEnclosure) {
        if (props.modelValue.enclosureInfoList) {
            props.modelValue.enclosureInfoList.forEach(item => {
                _fileList.push({
                    uid: item.id,
                    name: item.name,
                    url: proxy.$config.getConfig().fileBasePath + item.fileAddress,
                    enclosureId: item.id,
                    percent: 100,
                    status: 'done'
                })
            })
        }
    } else {
        const res = await proxy.$http.post(proxy.$config.getConfig().reqBasePath + 'queryFileListByPath', {
            path: props.modelValue
        })
        if (res.rows.length > 0) {
            res.rows.forEach(item => {
                _fileList.push({
                    uid: item.id,
                    name: item.name,
                    url: proxy.$config.getConfig().fileBasePath + item.path,
                    percent: 100,
                    status: 'done'
                })
            })
        } else {
            props.modelValue.split(',').forEach((item, index) => {
                _fileList.push({
                    uid: index,
                    // 获取最后一个/后面的文件名
                    name: item.substring(item.lastIndexOf('/') + 1),
                    url: proxy.$config.getConfig().fileBasePath + item,
                    percent: 100,
                    status: 'done'
                })
            })
        }
    }
    fileList.value = _fileList
    if (props.isEnclosure) {
        // 附件上传，获取集合中的附件id转为集合
        const enclosureInfo = fileList.value.filter(item => item.enclosureId).map(item => item.enclosureId).join(',')
        emit('update:modelValue', JSON.stringify({ enclosureInfo: enclosureInfo }))
    } else {
        // 非附件上传，获取文件路径
        const fileUrlList = fileList.value.map(item => item.url.replace(proxy.$config.getConfig().fileBasePath, '')).join(',')
        emit('update:modelValue', fileUrlList)
    }
})
</script>

<style lang="less" scoped>
.sk-upload-wrapper {
    display: inline-flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;

    .sk-upload-trigger {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #666;
    }

    .sk-upload-readonly {
        .sk-upload-list {
            &.picture-card {
                display: grid;
                grid-template-columns: repeat(auto-fill, 104px);
                gap: 8px;

                .sk-upload-item {
                    position: relative;
                    width: 104px;
                    height: 104px;
                    border: 1px solid #d9d9d9;
                    border-radius: 2px;
                    padding: 4px;
                    cursor: pointer;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .upload-progress {
                        position: absolute;
                        bottom: 4px;
                        left: 4px;
                        right: 4px;
                        background: rgba(255, 255, 255, 0.8);
                        padding: 4px;
                    }

                    .file-actions {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        display: none;
                        gap: 8px;
                        background: rgba(0, 0, 0, 0.45);
                        padding: 8px;
                        border-radius: 4px;
                    }

                    &:hover .file-actions {
                        display: flex;
                    }

                    .action-icon {
                        color: #fff;
                        font-size: 16px;
                        cursor: pointer;

                        &:hover {
                            color: #1890ff;
                        }
                    }
                }
            }

            &.picture {
                .sk-upload-item {
                    position: relative;
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;
                    padding: 4px;
                    border: 1px solid #d9d9d9;
                    border-radius: 2px;

                    img {
                        width: 48px;
                        height: 48px;
                        object-fit: cover;
                        margin-right: 8px;
                    }

                    .upload-progress {
                        flex: 1;
                        margin: 0 8px;
                    }

                    .file-actions {
                        display: flex;
                        gap: 8px;
                        margin-left: auto;
                    }

                    .action-icon {
                        color: rgba(0, 0, 0, 0.45);
                        font-size: 16px;
                        cursor: pointer;

                        &:hover {
                            color: #1890ff;
                        }
                    }
                }
            }

            .sk-upload-item {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;

                .file-name {
                    color: rgba(0, 0, 0, 0.85);
                }

                .upload-progress {
                    flex: 1;
                    margin-left: 8px;
                }

                .file-actions {
                    display: flex;
                    gap: 8px;
                    margin-left: auto;
                }

                .action-icon {
                    color: rgba(0, 0, 0, 0.45);
                    font-size: 16px;
                    cursor: pointer;

                    &:hover {
                        color: #1890ff;
                    }
                }
            }
        }

        .sk-upload-placeholder {
            color: rgba(0, 0, 0, 0.25);
            padding: 4px 11px;
            background-color: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
        }
    }

    .sk-upload-help {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
    }

    :deep(.ant-upload-drag) {
        padding: 16px;

        .ant-upload-drag-icon {
            margin-bottom: 16px;

            .anticon {
                color: #40a9ff;
                font-size: 48px;
            }
        }

        .ant-upload-text {
            margin-bottom: 8px;
            color: rgba(0, 0, 0, 0.85);
        }

        .ant-upload-hint {
            color: rgba(0, 0, 0, 0.45);
        }
    }
}

.preview-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;

    .preview-file {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: #666;

        .anticon {
            font-size: 48px;
        }
    }
}
</style>