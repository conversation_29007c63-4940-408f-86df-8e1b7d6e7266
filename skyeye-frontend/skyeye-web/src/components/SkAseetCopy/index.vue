<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" :rules="formRules" @submit="handleSubmit" @reset="$emit('cancel')"
            :submitText="$t('common.gain')" :resetText="$t('common.cancel')" :showReset="true" :showButtons="true">
            <SkHrTitle>基本信息</SkHrTitle>
            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="资产" name="assetId">
                        <SkAssetSelect v-model="formData.assetId" :formData="formData" :isEdit="1" attrKey="assetId"
                            @change="handleAssetChange" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="状态" name="state" required>
                        <SkSelect v-model="formData.state" placeholder="请选择" :options="stateOptions" allowClear
                            :rules="[{ required: true, message: '请选择状态' }]" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="获取数量" name="number" required>
                        <SkInputNumber v-model="formData.number" placeholder="请输入获取编码的数量" :min="1" :max="99" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-form-item label="条形码">
                <a-textarea v-model:value="formData.barCode" :rows="8" readonly :placeholder="'获取的条形码将显示在这里...'" />
                <div class="mt-8">
                    <SkSpace>
                        <SkButton type="primary" @click.prevent="handleCopy">
                            <template #icon><copy-outlined /></template>
                            复制
                        </SkButton>
                        <SkButton @click.prevent="handleClear">
                            <template #icon><delete-outlined /></template>
                            清空
                        </SkButton>
                    </SkSpace>
                </div>
            </a-form-item>

            <a-form-item label="获取结果">
                <div v-if="resultTips" class="text-primary">{{ resultTips }}</div>
            </a-form-item>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { CopyOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkAssetSelect from '@/components/SkAssetSelect/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkInputNumber from '@/components/SkInputNumber/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    assetId: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['cancel'])

const formRef = ref(null)

const stateOptions = [
    { value: 'unUse', label: '未使用' },
    { value: 'unPut', label: '未入库' }
]

const formData = reactive({
    assetId: props.assetId,
    assetName: '',
    state: '',
    number: '',
    barCode: '',
    modelValue: {}
})

const formRules = {
    state: [{ required: true, message: '请选择状态' }],
    number: [
        { required: true, message: '请输入获取数量' },
        { type: 'number', message: '请输入有效的数字' },
    ]
}

const resultTips = ref('')

// 处理资产选择变化
const handleAssetChange = async (asset) => {
    if (asset) {
        formData.assetName = asset.name || ''
    } else {
        formData.assetName = ''
    }
}

// 复制条形码
const handleCopy = async () => {
    if (!formData.barCode) {
        SkMessage.warning('没有可复制的内容')
        return
    }
    proxy.$util.copyToClipboard(formData.barCode, {
        onSuccess: () => SkMessage.success('复制成功'),
        onError: () => SkMessage.error('复制失败')
    })
}

// 清空条形码
const handleClear = () => {
    formData.barCode = ''
    resultTips.value = ''
}

// 提交获取条形码
const handleSubmit = async () => {
    await formRef.value.validate()
    const params = {
        assetId: formData.assetId,
        state: formData.state,
        limit: formData.number,
        number: formData.number,
        page: 1
    }
    const res = await proxy.$http.post(
        proxy.$config.getConfig().admBasePath + 'queryAssetReportCodeList',
        params
    )
    formData.barCode = res.rows.join('\n')
    resultTips.value = `共计获取${res.total}个条形码`
}
</script>

<style scoped>
.text-primary {
    color: var(--ant-primary-color);
}

:deep(.ant-form-item) {
    margin-bottom: 24px;
}
</style>
