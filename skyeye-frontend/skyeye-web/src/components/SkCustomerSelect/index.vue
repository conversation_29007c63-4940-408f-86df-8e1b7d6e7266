<template>
    <div class="sk-customer-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else>
            {{ showValue.name }}
        </div>

        <!-- 客户选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择客户" width="80%" :bodyStyle="{ height: '80vh' }">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <div class="table-btn-group">
                        <SkAuthBtnGroup authPointCode="1570455037177" @change="handleChange" />
                    </div>
                    <!-- 搜索区域 -->
                    <div class="table-search">
                        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                            :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                            <template #submitIcon>
                                <search-outlined />
                            </template>
                            <a-form-item name="keyword">
                                <SkInput v-model="searchForm.keyword" placeholder="请输入名称" allowClear />
                            </a-form-item>
                        </SkForm>
                    </div>

                    <!-- 表格区域 -->
                    <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                        :ready="tableReady" :pagination="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'choose'">
                                <a-button type="link" size="small" @click.stop="handleSelect(record)">
                                    选择
                                </a-button>
                            </template>
                            <template v-if="column.dataIndex === 'typeId'">
                                {{ initDictData['CRM_CUSTOMER_TYPE'][record.typeId] }}
                            </template>
                            <template v-if="column.dataIndex === 'fromId'">
                                {{ initDictData['CRM_CUSTOMER_FROM'][record.fromId] }}
                            </template>
                            <template v-if="column.dataIndex === 'industryId'">
                                {{ initDictData['CRM_CUSTOMER_INDUSTRY'][record.industryId] }}
                            </template>
                        </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkAuthBtnGroup from '@/components/SkAuthBtnGroup/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择客户'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})

const tableReady = ref(false)

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '客户名称',
        dataIndex: 'name',
        width: 150
    },
    {
        title: '客户分类',
        dataIndex: 'typeId',
        width: 120
    },
    {
        title: '客户来源',
        dataIndex: 'fromId',
        width: 120
    },
    {
        title: '所属行业',
        dataIndex: 'industryId',
        width: 180
    }
]

// 权限点处理
const authMation = ref({})
const handleChange = (key, value) => {
    authMation.value[key] = value
    pagination.current = 1
    getCustomerList()
}

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化数据字典数据
const initDictData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getDictListMapByCode(['CRM_CUSTOMER_TYPE', 'CRM_CUSTOMER_FROM', 'CRM_CUSTOMER_INDUSTRY']);
    initDictData.value = result
}

// 获取客户列表
const getCustomerList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            ...searchForm,
            ...authMation.value
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'customer001',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取客户列表失败')
    } finally {
        loading.value = false
    }
}

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getCustomerList()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getCustomerList()
}

// 处理选择
const handleSelect = (record) => {
    showValue.value = record
    emit('update:modelValue', {
        holderId: record.id,
        holderKey: record.serviceClassName
    })
    emit('change', record)
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    if (props.pageType == proxy.$config.pageType.EDIT) {
        // 详情页面和编辑页面都会走这里
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation || {}
        if (showValue.value.id) {
            const idKey = proxy.$util.getKeyIdToKey(props.attrKey)
            const serviceClassName = props.formData[idKey]
            emit('update:modelValue', {
                holderId: showValue.value.id,
                holderKey: serviceClassName
            })
        }
    }
    if (props.isEdit == proxy.$config.formEditType.isEdit) {
        getInitData()
    }
})
</script>

<style scoped>
.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}
</style>