<template>
    <div class="sk-dynamic-table" @click.stop>
        <SkForm ref="formRef" v-model="tableData" :showButtons="false">
            <div class="table-operations" v-if="showToolbar && isEdit == $config.formEditType.isEdit">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="handleAdd" v-if="showAdd">
                        <template #icon><plus-outlined /></template>
                        新增
                    </SkButton>
                    <SkButton danger @click.prevent="handleBatchDelete" v-if="showDelete"
                        :disabled="!selectedRowKeys.length">
                        <template #icon><delete-outlined /></template>
                        删除
                    </SkButton>
                    <slot name="toolbar"></slot>
                </SkSpace>
            </div>

            <SkTable :key="tableKey" :row-selection="showRowSelection?.value" :columns="innerColumns"
                :pagination="false" bordered @click.stop :data-source="tableData" @change="handleTableChange"
                :modalPadding="false" :row-key="rowKey" :style="{ height: '300px' }">
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.type">
                        <a-form-item :name="[index, column.dataIndex]" :rules="column.rules" :preserve="false"
                            :class="isEdit === $config.formEditType.notEdit ? 'detail-form-item' : ''">
                            <!-- 自定义组件插槽 -->
                            <slot v-if="hasCustomSlot(column.type, column.dataIndex)"
                                :name="`cell-${column.type}-${column.dataIndex}`" :record="record" :index="index"
                                :column="column">
                            </slot>
                            <template v-else>
                                <!-- 输入框 -->
                                <SkInput v-if="column.type === 'input'" v-model="record[column.dataIndex]" :attrKey="column.dataIndex"
                                    :placeholder="column.placeholder" :isEdit="column.getConfig(record).isEdit"
                                    @change="() => handleCellChange(record, column.dataIndex, column)" :maxLength="150"
                                    :showCount="true" />

                                <!-- 下拉框 -->
                                <SkSelect v-else-if="column.type === 'select'" v-model="record[column.dataIndex]"
                                    :placeholder="column.placeholder" :pageType="pageType"
                                    :attrDefinitionCustom="column.getConfig(record)"
                                    :isEdit="column.getConfig(record).isEdit" :attrKey="column.dataIndex"
                                    :formData="record"
                                    @change="(selValue, selOption) => handleCellChange(record, column.dataIndex, column, selValue, selOption)">
                                </SkSelect>

                                <!-- 文本框 -->
                                <SkTextarea v-else-if="column.type === 'textarea'" v-model="record[column.dataIndex]"
                                    :placeholder="column.placeholder" :isEdit="column.getConfig(record).isEdit"
                                    @change="() => handleCellChange(record, column.dataIndex, column)"
                                    :maxLength="100000" />

                                <!-- 文本详情 -->
                                <div v-else-if="column.type === 'detail'">
                                    {{ record[column.dataIndex] }}
                                </div>

                                <!-- 详情模式下，选择组件需要特殊处理 -->
                                <template
                                    v-if="isEdit === $config.formEditType.notEdit && column.type === 'chooseInput'">
                                    {{ $util.isNull(record[column.dataIndex]) ? '' :
                                        (record[$util.getKeyIdToMation(column.dataIndex)]?.name ||
                                            record[$util.getKeyIdToMation(column.dataIndex)]?.dictName) }}
                                </template>
                            </template>
                        </a-form-item>
                    </template>
                    <template v-else>
                        <slot :name="column.dataIndex" :record="record" :index="index">
                            {{ record[column.dataIndex] }}
                        </slot>
                    </template>
                </template>
            </SkTable>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, useSlots, watch, nextTick } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import SkInput from '@/components/SkInput/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkTextarea from '@/components/SkTextarea/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const formRef = ref(null)

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    columns: {
        type: Array,
        required: true
    },
    showToolbar: {
        type: Boolean,
        default: true
    },
    showAdd: {
        type: Boolean,
        default: true
    },
    showDelete: {
        type: Boolean,
        default: true
    },
    scroll: {
        type: Object,
        default: () => ({ x: true })
    },
    pageType: {
        type: String,
        default: ''
    },
    // 动态表单的属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 是否可以编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    // 行key
    rowKey: {
        type: String,
        default: 'id'
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'add', 'delete', 'cell-change'])

const selectedRowKeys = ref([])

// 内部数据
const tableData = computed({
    get: () => Array.isArray(props.modelValue) ? props.modelValue : [],
    set: (val) => emit('update:modelValue', val)
})

// 表格选择配置
const rowSelection = computed(() => ({
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys) => {
        selectedRowKeys.value = keys
    }
}))

// 处理列配置
const innerColumns = computed(() => {
    if (props.isEdit == proxy.$config.formEditType.isEdit) {
        // 编辑模式下，设置列的 required 属性
        props.columns.forEach(column => {
            column.required = column.require?.indexOf('required') >= 0 ? true : false
        })
    }

    const list = props.columns.map(column => ({
        align: column.align || 'center',
        width: column.width < 160 ? 160 : column.width,
        dataIndex: column.attrKey,
        pIdDataIndex: props.attrKey,
        type: column.showType,
        title: column.name,
        customHeaderCell: () => ({
            class: column.required ? 'required' : ''
        }),
        rules: proxy.$util.getRules(column),
        getConfig: (record) => {
            const baseConfig = column[`${column.attrKey}_config`] || record[`${column.attrKey}_config`] || {}
            // 根据组件类型返回不同的配置
            switch (column.showType) {
                case 'select':
                    // 下拉框需要额外的配置
                    return {
                        isEdit: props.isEdit,
                        dataType: column.dataType || 0,
                        defaultData: column.defaultData || [],
                        businessApi: column.businessApi || {},
                        objectId: column.objectId || '',
                        ...baseConfig  // 合并其他配置
                    }
                default:
                    // 其他类型只需要基本配置
                    return {
                        isEdit: props.isEdit,
                        ...baseConfig
                    }
            }
        }
    }))
    return list
})

const tableKey = ref(0)

// 监听 columns 变化
watch(
    () => props.columns,
    () => {
        // 强制表格重新渲染
        tableKey.value += 1
    },
    { deep: true }
)

// 表单验证
const validate = async () => {
    if (!formRef.value) {
        return {
            valid: true,
            data: tableData.value || []
        }
    }

    try {
        await formRef.value.validate()
        return {
            valid: true,
            data: tableData.value
        }
    } catch (error) {
        return {
            valid: false,
            data: tableData.value
        }
    }
}

// 新增行
const handleAdd = async (e) => {
    e?.preventDefault()
    e?.stopPropagation()
    // 只有当表格有数据时才验证
    if (tableData.value.length > 0) {
        const valid = await validate()
        if (!valid) {
            SkMessage.error('请填写必填项')
            return
        }
    }
    const newRow = {
        id: Date.now() + Math.random().toString(36).slice(2),
        key: Date.now() + Math.random().toString(36).slice(2)
    }

    // 初始化每列的默认值
    props.columns.forEach(column => {
        if (column.dataIndex) {
            newRow[column.dataIndex] = column.defaultValue !== undefined ? column.defaultValue : null
        }
    })
    tableData.value = [...tableData.value, newRow]
    emit('add', newRow)
}

// 批量删除
const handleBatchDelete = (e) => {
    e?.preventDefault()
    e?.stopPropagation()
    if (!selectedRowKeys.value.length) return
    tableData.value = tableData.value.filter(item => !selectedRowKeys.value.includes(item[props.rowKey]))
    emit('delete', selectedRowKeys.value)
    selectedRowKeys.value = []
    SkMessage.success('删除成功')
}

// 单元格变化
const handleCellChange = (record, dataIndex, column, selValue, selOption) => {
    const value = record[dataIndex]
    if (column.type === 'select') {
        const keyMation = proxy.$util.getKeyIdToMation(dataIndex)
        record[keyMation] = selOption
    }
    // 触发事件
    emit('cell-change', {
        record,
        dataIndex,
        value,
        column
    })
}

// 表格变化
const handleTableChange = (...args) => {
    emit('change', ...args)
}

const slots = useSlots()

const hasCustomSlot = computed(() => (type, dataIndex) => {
    const slotName = `cell-${type}-${dataIndex}`
    return !!slots[slotName]
})

// 修改单行的列配置
const updateTableColumns = (record, updates) => {
    if (!record || !updates) {
        console.warn('updateTableColumns: record or updates is null/undefined')
        return
    }

    let hasChanges = false
    Object.keys(updates).forEach(key => {
        const currentConfig = record[`${key}_config`] || {}
        const newConfig = updates[key]

        // 合并配置，以新配置为主
        const mergedConfig = {
            ...currentConfig,  // 保留旧配置
            ...newConfig      // 新配置覆盖旧配置
        }

        // 比较合并后的配置是否与当前配置不同
        if (!isConfigEqual(currentConfig, mergedConfig)) {
            record[`${key}_config`] = mergedConfig
            hasChanges = true
        }
    })

    // 只有在配置发生变化时才重新渲染
    if (hasChanges) {
        tableKey.value += 1
    }
}

// 修改所有行的列配置
const updateAllRowsColumns = (updates) => {
    if (!updates) {
        console.warn('updateAllRowsColumns: updates is null/undefined')
        return
    }

    let hasChanges = false
    props.columns.forEach(column => {
        Object.keys(updates).forEach(key => {
            const currentConfig = column[`${key}_config`] || {}
            const newConfig = updates[key]

            // 合并配置，以新配置为主
            const mergedConfig = {
                ...currentConfig,  // 保留旧配置
                ...newConfig      // 新配置覆盖旧配置
            }

            // 比较合并后的配置是否与当前配置不同
            if (!isConfigEqual(currentConfig, mergedConfig)) {
                column[`${key}_config`] = mergedConfig
                hasChanges = true
            }
        })
    })
    // 只有在配置发生变化时才重新渲染
    if (hasChanges) {
        tableKey.value += 1
    }
}

// 比较两个配置对象是否相等
const isConfigEqual = (config1, config2) => {
    // 如果两个对象引用相同，直接返回 true
    if (config1 === config2) return true

    // 如果其中一个是 null/undefined，另一个不是，返回 false
    if (!config1 || !config2) return false

    const keys1 = Object.keys(config1)
    const keys2 = Object.keys(config2)

    // 如果键的数量不同，返回 false
    if (keys1.length !== keys2.length) return false

    // 比较每个键的值
    return keys1.every(key => {
        // 如果值是对象，递归比较
        if (typeof config1[key] === 'object' && config1[key] !== null) {
            return isConfigEqual(config1[key], config2[key])
        }
        // 否则直接比较值
        return config1[key] === config2[key]
    })
}

const showAdd = ref(props.showAdd)

// 修改 showAdd 的值
const updateShowAdd = (value) => {
    showAdd.value = value
    // 强制重新渲染表格
    nextTick(() => {
        tableKey.value += 1
    })
}

const showDelete = ref(props.showDelete)

// 修改 showDelete 的值
const updateShowDelete = (value) => {
    showDelete.value = value
    // 强制重新渲染表格
    nextTick(() => {
        tableKey.value += 1
    })
}

// 计算是否显示多选框
const showRowSelection = computed(() => {
    return props.isEdit === proxy.$config.formEditType.isEdit ? rowSelection : null
})

// 导出验证方法
defineExpose({
    validate,
    formRef,
    updateTableColumns,
    updateShowAdd,
    updateAllRowsColumns,
    updateShowDelete
})
</script>

<style lang="less" scoped>
.sk-dynamic-table {
    max-height: 400px;

    .sk-form {
        height: 100%;

        :deep(.ant-form) {
            height: 100%;
        }

        .detail-form-item {
            margin-bottom: 0 !important;
        }
    }

    :deep(.ant-table-cell) {
        vertical-align: middle;
    }

    :deep(.ant-table-thead > tr > th) {
        &.required::before {
            display: inline-block;
            margin-right: 12px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*' !important;
        }
    }
}
</style>