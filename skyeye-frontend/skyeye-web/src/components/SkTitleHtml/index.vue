<template>
    <view>
        {{ modelValue }}
    </view>
</template>

<script setup>
import { onMounted, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue'])

onMounted(() => {
    if (props.pageType == proxy.$config.pageType.ADD) {
        let nowDate = proxy.$util.getNowDate()
        let userMation = proxy.$config.getCurrentUser()
        const str = props.modelValue + nowDate + '-' + userMation.userName;
        emit("update:modelValue", str)
    }
})
</script>