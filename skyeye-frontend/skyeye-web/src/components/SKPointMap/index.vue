<template>
    <div class="sk-map-location">
        <!-- 图片上传区域 -->
        <div class="mb-8">
            <SkForm :form="mapFormData" :modelValue="mapFormData" :showButtons="false">
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item label="图片" required>
                            <SkUpload v-model="mapFormData.background" :key="mapFormData.background"
                                v-model:fileList="mapFormData.fileList" :max-count="1" accept=".jpg,.jpeg,.png"
                                :list-type="'picture-card'" :show-upload-list="true"
                                :help="'请上传地图图片，支持jpg、jpeg、png格式'" @change="handleUploadChange"
                                @remove="handleRemove" />
                        </a-form-item>
                    </a-col>
                </a-row>
            </SkForm>
        </div>

        <!-- 地图控制区域 -->
        <div class="mb-8">
            <SkForm :form="mapFormData" :modelValue="mapFormData" :showButtons="false">
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item label="地图定位" required>
                            <SkButton :type="clickNum === 1 ? 'primary' : 'default'"
                                @click.prevent="handlePointClick(1)" class="mr-8">
                                第一个点
                            </SkButton>
                            <SkButton :type="clickNum === 2 ? 'primary' : 'default'"
                                @click.prevent="handlePointClick(2)">
                                第二个点
                            </SkButton>
                        </a-form-item>
                    </a-col>
                </a-row>
            </SkForm>
        </div>

        <!-- 地图容器 -->
        <div class="map-container" ref="mapContainer"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, getCurrentInstance, computed, nextTick } from 'vue'
import SkForm from '@/components/SkForm/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkUpload from '@/components/SkUpload/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import AMapLoader from '@amap/amap-jsapi-loader'

const mapContainer = ref(null)
const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Object, String],
        default: () => ({
            latitude: '',
            longitude: '',
            name: '',
            background: '',
            neLongitude: '',
            neLatitude: '',
            swLongitude: '',
            swLatitude: ''
        })
    },
    formData: {
        type: Object,
        default: () => ({})
    },
})
const emit = defineEmits(['update:modelValue'])

// 地图相关变量
const map = ref(null)
const AMapClass = ref(null)
const markerOne = ref(null)
const markerTwo = ref(null)
const textOne = ref(null)
const textTwo = ref(null)
const imageLayer = ref(null)
const clickNum = ref(1)

// 表单数据
const mapFormData = ref({
    background: '',
    fileList: []
})

// 计算属性处理 modelValue
const computedModelValue = computed(() => {
    if (typeof props.modelValue === 'string') {
        return {
            background: '',
            neLongitude: '',
            neLatitude: '',
            swLongitude: '',
            swLatitude: ''
        }
    }
    return props.modelValue
})

// 初始化地图
const initMap = async () => {
    try {
        // 设置安全密钥
        window._AMapSecurityConfig = {
            securityJsCode: proxy.$config.getConfig().securityJsCode
        }

        // 加载高德地图
        const AMap = await AMapLoader.load({
            key: proxy.$config.getConfig().skyeyeMapKey,
            version: '2.0',
            plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.Geolocation', 'AMap.Bounds']
        })

        AMapClass.value = AMap

        // 创建地图实例
        map.value = new AMap.Map(mapContainer.value, {
            zoom: computedModelValue.value.longitude && computedModelValue.value.latitude ? 16 : 5,
            center: computedModelValue.value.longitude && computedModelValue.value.latitude ?
                [computedModelValue.value.longitude, computedModelValue.value.latitude] :
                [113.65, 34.76],
            viewMode: '2D',
            mapStyle: 'amap://styles/normal',
            pitch: 0,
            rotateEnable: false,
            protocol: 'https',
            showBuildingBlock: false,
            features: ['bg', 'road', 'point']
        })

        // 添加控件
        map.value.addControl(new AMap.Scale({
            position: 'LB'
        }))
        map.value.addControl(new AMap.ToolBar({
            position: 'RB'
        }))

        // 如果有中心点坐标，添加中心标记并定位
        if (computedModelValue.value.longitude && computedModelValue.value.latitude) {
            const centerMarker = new AMap.Marker({
                position: [computedModelValue.value.longitude, computedModelValue.value.latitude],
                title: computedModelValue.value.name || '中心点'
            })
            centerMarker.setMap(map.value)

            // 设置地图中心点和缩放级别
            map.value.setCenter([computedModelValue.value.longitude, computedModelValue.value.latitude])
            map.value.setZoom(16)
        }

        // 创建标记点
        markerOne.value = new AMap.Marker({
            map: map.value,
            draggable: true,
            cursor: 'move',
            position: computedModelValue.value.neLongitude && computedModelValue.value.neLatitude ?
                [computedModelValue.value.neLongitude, computedModelValue.value.neLatitude] : null
        })
        textOne.value = new AMap.Text({
            text: "第一个点",
            offset: new AMap.Pixel(0, 0),
            map: map.value,
            position: computedModelValue.value.neLongitude && computedModelValue.value.neLatitude ?
                [computedModelValue.value.neLongitude, computedModelValue.value.neLatitude] : null
        })

        markerTwo.value = new AMap.Marker({
            map: map.value,
            draggable: true,
            cursor: 'move',
            position: computedModelValue.value.swLongitude && computedModelValue.value.swLatitude ?
                [computedModelValue.value.swLongitude, computedModelValue.value.swLatitude] : null
        })
        textTwo.value = new AMap.Text({
            text: "第二个点",
            offset: new AMap.Pixel(0, 0),
            map: map.value,
            position: computedModelValue.value.swLongitude && computedModelValue.value.swLatitude ?
                [computedModelValue.value.swLongitude, computedModelValue.value.swLatitude] : null
        })

        // 绑定地图点击事件和标记点拖拽事件
        map.value.on('click', handleMapClick)
        markerOne.value.on('dragend', handleMarkerOneDragEnd)
        markerTwo.value.on('dragend', handleMarkerTwoDragEnd)

        // 如果有初始值，更新地图显示
        if (computedModelValue.value.neLongitude && computedModelValue.value.neLatitude &&
            computedModelValue.value.swLongitude && computedModelValue.value.swLatitude &&
            computedModelValue.value.background) {
            updateImageLayer()
        }
    } catch (error) {
        SkMessage.error('地图加载失败')
    }
}

// 处理标记点拖拽结束
const handleMarkerOneDragEnd = async () => {
    const position = markerOne.value.getPosition()
    const lng = position.getLng()
    const lat = position.getLat()
    const newValue = { ...computedModelValue.value }
    newValue.neLongitude = lng
    newValue.neLatitude = lat
    textOne.value.setPosition([lng, lat])
    emit('update:modelValue', newValue)
    await nextTick()
    updateImageLayer()
}

const handleMarkerTwoDragEnd = async () => {
    const position = markerTwo.value.getPosition()
    const lng = position.getLng()
    const lat = position.getLat()
    const newValue = { ...computedModelValue.value }
    newValue.swLongitude = lng
    newValue.swLatitude = lat
    textTwo.value.setPosition([lng, lat])
    emit('update:modelValue', newValue)
    await nextTick()
    updateImageLayer()
}

// 处理地图点击
const handleMapClick = async (e) => {
    const { lng, lat } = e.lnglat
    const newValue = { ...computedModelValue.value }

    if (clickNum.value === 1) {
        markerOne.value.setPosition(e.lnglat)
        textOne.value.setPosition(e.lnglat)
        newValue.neLongitude = lng
        newValue.neLatitude = lat
        newValue.background = mapFormData.value.background
    } else if (clickNum.value === 2) {
        markerTwo.value.setPosition(e.lnglat)
        textTwo.value.setPosition(e.lnglat)
        newValue.swLongitude = lng
        newValue.swLatitude = lat
        newValue.background = mapFormData.value.background
    }
    emit('update:modelValue', newValue)
    await nextTick()
    updateImageLayer()
}

// 处理点选择
const handlePointClick = (num) => {
    clickNum.value = num
}

// 处理删除
const handleRemove = () => {
    // 清除图片
    mapFormData.value.background = ''
    mapFormData.value.fileList = []

    // 更新 modelValue
    const newValue = { ...computedModelValue.value }
    newValue.background = ''
    emit('update:modelValue', newValue)

    // 移除图层
    if (imageLayer.value && map.value) {
        map.value.removeLayer(imageLayer.value)
        imageLayer.value = null
    }

    SkMessage.success('图片已删除')
}

// 处理上传变化
const handleUploadChange = (info) => {
    const { file } = info

    if (file.status === 'done') {
        const picUrl = file.response?.bean?.picUrl || ''
        // 更新表单数据
        mapFormData.value.background = picUrl

        // 构建完整的文件URL
        const fileUrl = proxy.$config.getConfig().fileBasePath + picUrl

        // 更新文件列表
        mapFormData.value.fileList = [{
            uid: file.uid,
            name: file.name,
            status: 'done',
            url: fileUrl,
            thumbUrl: fileUrl,
            response: file.response
        }]

        // 更新 modelValue
        const newValue = { ...computedModelValue.value }
        newValue.background = picUrl
        emit('update:modelValue', newValue)

        updateImageLayer()
        SkMessage.success('上传成功')
    } else if (file.status === 'error') {
        SkMessage.error('上传失败')
        mapFormData.value.fileList = []
    }
}

// 更新图片图层
const updateImageLayer = () => {
    if (!map.value || !computedModelValue.value.neLongitude || !computedModelValue.value.neLatitude ||
        !computedModelValue.value.swLongitude || !computedModelValue.value.swLatitude ||
        !mapFormData.value.background) {
        return
    }

    if (imageLayer.value) {
        map.value.removeLayer(imageLayer.value)
    }

    imageLayer.value = new AMapClass.value.ImageLayer({
        url: proxy.$config.getConfig().fileBasePath + mapFormData.value.background,
        bounds: new AMapClass.value.Bounds(
            [computedModelValue.value.neLongitude, computedModelValue.value.neLatitude],
            [computedModelValue.value.swLongitude, computedModelValue.value.swLatitude]
        )
    })

    imageLayer.value.setMap(map.value)
}

// 清理地图实例
const destroyMap = () => {
    if (map.value) {
        map.value.off('click', handleMapClick)
        map.value.destroy()
        map.value = null
    }
}

// 获取地图值
const getPointMapValue = () => {
    return {
        firstLongitude: computedModelValue.value.neLongitude,
        firstLatitude: computedModelValue.value.neLatitude,
        secondLongitude: computedModelValue.value.swLongitude,
        secondLatitude: computedModelValue.value.swLatitude,
        pic: mapFormData.value.background
    }
}

// 获取地图结果值
const getPointMapResultValue = () => {
    return {
        neLongitude: computedModelValue.value.neLongitude,
        neLatitude: computedModelValue.value.neLatitude,
        swLongitude: computedModelValue.value.swLongitude,
        swLatitude: computedModelValue.value.swLatitude,
        background: mapFormData.value.background
    }
}

const validate = () => {
    if (!computedModelValue.value.background) {
        SkMessage.error('请上传图片');
        return false;
    }
    if (!computedModelValue.value.neLongitude) {
        SkMessage.error('请选择第一个点');
        return false;
    }
    if (!computedModelValue.value.swLongitude) {
        SkMessage.error('请选择第二个点');
        return false;
    }
    return true;
};

// 暴露方法给父组件
defineExpose({
    getPointMapValue,
    getPointMapResultValue,
    validate
})

// 组件挂载时初始化
onMounted(async () => {
    initMap()
    if (props.formData) {
        const newValue = {
            latitude: proxy.$util.isNull(props.formData?.latitude) ? '' : props.formData?.latitude,
            longitude: proxy.$util.isNull(props.formData?.longitude) ? '' : props.formData?.longitude,
            name: proxy.$util.isNull(props.formData?.name) ? '' : props.formData?.name,
            background: proxy.$util.isNull(props.formData?.background) ? '' : props.formData?.background,
            neLongitude: proxy.$util.isNull(props.formData?.neLongitude) ? '' : props.formData?.neLongitude,
            neLatitude: proxy.$util.isNull(props.formData?.neLatitude) ? '' : props.formData?.neLatitude,
            swLongitude: proxy.$util.isNull(props.formData?.swLongitude) ? '' : props.formData?.swLongitude,
            swLatitude: proxy.$util.isNull(props.formData?.swLatitude) ? '' : props.formData?.swLatitude
        }
        emit('update:modelValue', newValue)
    }
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
    destroyMap()
})

// 监听值变化
watch(() => computedModelValue.value, (newValue) => {
    if (newValue.background !== mapFormData.value.background) {
        mapFormData.value.background = newValue.background
    }
}, { deep: true })
</script>

<style scoped></style>
