<template>
    <div class="sk-radio-wrapper">
        <template v-if="isEdit === $config.formEditType.isEdit">
            <a-radio-group v-if="dataList && dataList.length" v-model:value="value" :options="dataList"
                :disabled="disabled" :name="attrKey" :button-style="buttonStyle" :size="size" :optionType="optionType"
                @change="handleGroupChange" :field-names="fieldNames" />
        </template>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                <template v-if="dataList && dataList.length">
                    {{ getSelectedLabel }}
                </template>
                <template v-else>
                    <slot></slot>
                </template>
            </template>
        </div>
    </div>
</template>

<script setup>
import { computed, getCurrentInstance, ref, watch } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    // 绑定值
    modelValue: {
        type: [String, Number, Boolean],
        default: undefined
    },
    // 选项数组
    options: {
        type: Array,
        default: undefined
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 按钮风格样式
    buttonStyle: {
        type: String,
        default: 'outline'
    },
    // 大小
    size: {
        type: String,
        default: undefined
    },
    // 选项类型
    optionType: {
        type: String,
        default: 'default'
    },
    // 占位符
    placeholder: {
        type: String,
        default: '请选择'
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },
    // 属性键
    attrKey: {
        type: String,
        default: undefined
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    },
    // 添加 fieldNames 属性
    fieldNames: {
        type: Object,
        default: () => ({
            label: 'label',
            value: 'value',
            options: 'options'
        })
    },
    // 自定义属性
    attrDefinitionCustom: {
        type: Object,
        default: () => ({})
    },
    // 前置属性
    preAttribute: {
        type: String,
        default: ''
    },
    // 对象ID
    objectId: {
        type: String,
        default: ''
    },
    // 对象键
    objectKey: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 使用计算属性处理 v-model
const value = computed({
    get: () => {
        if (proxy.$util.isNull(props.modelValue)) {
            return undefined
        }
        return props.modelValue.toString()
    },
    set: (val) => {
        emit('update:modelValue', val)
    }
})

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newVal) => {
        if (proxy.$util.isNull(newVal)) {
            // 不需要在这里设置value.value，因为computed会自动处理
            return
        }
    },
    { immediate: true }
)

const dataList = ref([])

// 是否是首次加载数据
const isFirstLoad = ref(false)
// 监听动态字段变化
watch(
    () => JSON.parse(JSON.stringify(props.formData)),
    async (newVal, oldVal) => {
        if (props.attrDefinitionCustom?.dataType === 4) {
            const params = props.attrDefinitionCustom?.businessApi?.params || {}
            let isChange = false
            for (const [key, value] of Object.entries(params)) {
                if (value && value.startsWith('${') && value.endsWith('}')) {
                    const field = value.slice(2, -1);
                    const fieldParts = field.split('.');
                    const oldValue = getNestedValue(oldVal, fieldParts);
                    const newValue = getNestedValue(newVal, fieldParts);
                    // 使用严格相等比较，null 和 undefined 都会被认为是变化
                    if (!Object.is(newValue, oldValue)) {
                        isChange = true
                        break;
                    }
                }
            }
            if (isChange) {
                if (isFirstLoad.value) {
                    // 不是首次加载时，重置选中值
                    value.value = undefined
                } else {
                    isFirstLoad.value = true
                }
                await showView()
            }
        }
    },
    { deep: true, flush: 'post' }
)

// 监听 options 变化
watch(
    () => props.options,
    (newVal) => {
        if (newVal && newVal.length > 0) {
            // 循环处理每个选项，将value转为字符串
            newVal.forEach(item => {
                if (item[props.fieldNames.value] !== undefined) {
                    item[props.fieldNames.value] = item[props.fieldNames.value].toString()
                }
            })
            dataList.value = newVal
        } else {
            dataList.value = []
        }
    },
    {
        immediate: true,
        deep: true
    }
)

// 获取选中项的标签
const getSelectedLabel = computed(() => {
    if (!dataList.value || !dataList.value.length) return ''
    const selected = dataList.value.find(item =>
        item[props.fieldNames.value] === (props.modelValue ? props.modelValue.toString() : props.modelValue)
    )
    return selected ? selected[props.fieldNames.label] : ''
})

const showView = async () => {
    const customAttr = props.attrDefinitionCustom
    const dataType = customAttr.dataType
    const _dataList = []

    if (dataType == 1) {
        // 自定义
        var obj = proxy.$util.isNull(customAttr.defaultData) ? [] : customAttr.defaultData;
        if (typeof obj == 'string') {
            obj = JSON.parse(obj);
        }
        obj.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id
            newItem["item"] = item
            _dataList.push(newItem)
        })
        dataList.value = _dataList
    } else if (dataType == 2) {
        // 枚举
        if (proxy.$util.isNull(customAttr.objectId)) {
            return
        }
        const rows = await proxy.$util.getEnumListByCode(customAttr.objectId)
        rows.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id.toString()
            newItem["item"] = item
            newItem["isDefault"] = item.isDefault
            _dataList.push(newItem)
        })
        if (props.pageType == proxy.$config.pageType.ADD) {
            if (proxy.$util.isNull(props.modelValue)) {
                const num1 = _dataList.findIndex(v => v.isDefault);
                if (num1 >= 0) {
                    value.value = _dataList[num1].value
                    handleChange(_dataList[num1].value)
                }
            }
        }
        dataList.value = _dataList
    } else if (dataType == 3) {
        // 数据字典
        const rows = await proxy.$util.getDictListByCode(customAttr.objectId)
        rows.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id
            newItem["item"] = item
            newItem["isDefault"] = item.isDefault
            _dataList.push(newItem)
        })
        if (props.pageType == proxy.$config.pageType.ADD) {
            if (proxy.$util.isNull(props.modelValue)) {
                const num1 = _dataList.findIndex(v => v.isDefault);
                if (num1 >= 0) {
                    value.value = _dataList[num1].value
                    handleChange(_dataList[num1].value)
                }
            }
        }
        dataList.value = _dataList
    } else if (dataType == 4) {
        // 自定义接口
        const businessApi = customAttr.businessApi;
        const service = proxy.$util.getServiceKey(businessApi.serviceStr);
        const params = {};
        for (const key in businessApi.params) {
            const value = businessApi.params[key]
            if (!proxy.$util.isNull(value)) {
                // 判断value是否是${}包裹的
                if (value.startsWith('${') && value.endsWith('}')) {
                    const field = value.slice(2, -1);
                    const fieldParts = field.split('.');
                    const realValue = getNestedValue(props.formData, fieldParts);
                    params[key] = realValue;
                } else {
                    if (key == 'objectId') {
                        params[key] = props.objectId
                    } else {
                        params[key] = value;
                    }
                }
            } else {
                if (!proxy.$util.isNull(props.preAttribute) && !proxy.$util.isNull(props.formData)) {
                    const preAttributeVal = props.formData[props.preAttribute];
                    params[key] = preAttributeVal;
                }
            }
        }

        const url = proxy.$config.getConfig()[service] + businessApi.api;
        const res = await proxy.$http.request({
            url: url, method: businessApi.method,
            [businessApi.method.toLowerCase() === 'get' ? 'params' : 'data']: params
        })
        res.rows.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id
            newItem["item"] = item
            _dataList.push(newItem)
        })
        dataList.value = _dataList
    } else {
        dataList.value = props.options
    }
}

// 监听 attrDefinitionCustom 变化
watch(
    () => props.attrDefinitionCustom,
    async (newVal) => {
        await showView()
    },
    {
        immediate: true,
        deep: true
    }
)

// 处理单选框组值变化
const handleGroupChange = (e) => {
    const value = e.target.value
    emit('update:modelValue', value)
    emit('change', value, props.attrKey)
}
</script>

<style lang="less" scoped>
.sk-radio-wrapper {
    :deep(.ant-radio-group) {
        width: 100%;
    }

    :deep(.ant-radio-button-wrapper) {
        text-align: center;
    }
}
</style>