<template>
    <div class="sk-alert-wrapper" v-if="visible">
        <a-alert :message="message" :description="description" :type="type" :show-icon="showIcon" :closable="closable"
            :banner="banner" @close="handleClose">
            <!-- 自定义图标 -->
            <template #icon v-if="$slots.icon">
                <slot name="icon"></slot>
            </template>

            <!-- 自定义消息 -->
            <template #message v-if="$slots.message">
                <slot name="message"></slot>
            </template>

            <!-- 自定义描述 -->
            <template #description v-if="$slots.description">
                <slot name="description"></slot>
            </template>

            <!-- 自定义操作 -->
            <template #action v-if="$slots.action">
                <slot name="action"></slot>
            </template>
        </a-alert>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    // 警告提示内容
    message: {
        type: String,
        default: ''
    },
    // 警告提示的辅助性文字介绍
    description: {
        type: String,
        default: ''
    },
    // 指定警告提示的样式
    type: {
        type: String,
        default: 'info',
        validator: (value) => ['success', 'info', 'warning', 'error'].includes(value)
    },
    // 是否显示图标
    showIcon: {
        type: Boolean,
        default: false
    },
    // 是否显示关闭按钮
    closable: {
        type: Boolean,
        default: false
    },
    // 是否用作顶部公告
    banner: {
        type: Boolean,
        default: false
    },
    // 是否显示（支持 v-model）
    modelValue: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['update:modelValue', 'close'])

const visible = ref(props.modelValue)

// 监听值变化
watch(
    () => props.modelValue,
    (val) => {
        visible.value = val
    }
)

watch(visible, (val) => {
    emit('update:modelValue', val)
})

// 关闭事件
const handleClose = (e) => {
    visible.value = false
    emit('close', e)
}
</script>

<style scoped>
.sk-alert-wrapper {
    margin-bottom: 16px;
}

:deep(.ant-alert) {
    font-size: 14px;
}

:deep(.ant-alert-message) {
    color: inherit;
}

:deep(.ant-alert-description) {
    color: inherit;
    font-size: 14px;
}

:deep(.ant-alert-with-description .ant-alert-message) {
    font-weight: 500;
    margin-bottom: 8px;
}

:deep(.ant-alert-icon) {
    font-size: 16px;
    margin-top: 4px;
}

:deep(.ant-alert-with-description .ant-alert-icon) {
    font-size: 24px;
    margin-top: 6px;
}

:deep(.anticon) {
    vertical-align: middle;
}
</style>