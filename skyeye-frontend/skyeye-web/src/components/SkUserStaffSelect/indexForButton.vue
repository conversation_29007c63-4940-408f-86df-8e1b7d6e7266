<template>
    <div>
        <!-- 选择按钮 -->
        <SkButton type="primary" @click.prevent="handleButtonClick">
            <template #icon>
                <plus-outlined />
            </template>
            {{ buttonText || $t('common.add') }}
        </SkButton>

        <!-- 选择弹窗 -->
        <SkModal v-if="canShowModal" v-model="modalVisible" :title="modalTitle" width="70%" @cancel="handleCancel">
            <SkUserStaffSelect ref="staffSelectRef" :multiple="multiple" :pageType="pageType" :attrKey="attrKey"
                :formData="formData" @change="handleStaffSelect" @cancel="handleCancel" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkButton from '@/components/SkButton/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkUserStaffSelect from './index.vue'

const props = defineProps({
    buttonText: {
        type: String,
        default: ''
    },
    modalTitle: {
        type: String,
        default: '选择员工'
    },
    multiple: {
        type: Boolean,
        default: true
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['success', 'click'])

const modalVisible = ref(false)
const canShowModal = ref(false)
const staffSelectRef = ref(null)

// 处理按钮点击
const handleButtonClick = () => {
    // 先重置状态
    canShowModal.value = false
    modalVisible.value = false

    // 触发click事件，传入一个回调函数来接收检查结果
    emit('click', (canShow) => {
        if (canShow !== false) {
            canShowModal.value = true
            modalVisible.value = true
        }
    })
}

// 处理取消
const handleCancel = () => {
    modalVisible.value = false
    canShowModal.value = false
    staffSelectRef.value?.clearSelection()
}

// 处理员工选择
const handleStaffSelect = (selectedStaff) => {
    emit('success', selectedStaff)
    modalVisible.value = false
    canShowModal.value = false
}
</script>

<style scoped></style>
