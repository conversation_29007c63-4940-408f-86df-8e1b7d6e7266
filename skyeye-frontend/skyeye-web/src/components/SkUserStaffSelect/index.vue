<template>
    <div class="sk-staffChoose-select">
        <SkCard ref="cardRef" :bordered="false">
            <SkAlert :message="alertMessage" show-icon />
            <!-- 搜索区域 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入名称" allowClear />
                    </a-form-item>
                </SkForm>
                <a-form-item-rest>
                    <SkButton type="primary" @click.prevent="handleSave" :loading="saveLoading">
                        <template #icon>
                            <SaveOutlined />
                        </template>
                        {{ $t('common.confirm') }}
                    </SkButton>
                </a-form-item-rest>
            </div>

            <!-- 表格区域 -->
            <a-form-item-rest>
                <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                    :row-key="record => record.id" :row-selection="{
                        type: props.multiple ? 'checkbox' : 'radio',
                        selectedRowKeys: selectedRowKeys,
                        onChange: handleSelectionChange,
                        onSelect: handleSelect,
                        onSelectAll: handleSelectAll,
                        preserveSelectedRowKeys: true,
                        getCheckboxProps: (record) => ({
                            disabled: record.state === 2
                        })
                    }" @change="handleTableChange">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'userPhoto'">
                            <SkTableImg :imgPath="record.userPhoto" />
                        </template>
                        <template v-if="column.dataIndex === 'userName'">
                            {{ record.jobNumber }} {{ record.userName }}
                        </template>
                        <template v-if="column.dataIndex === 'type'">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['userStaffType'], 'id', record.type, 'name')">
                            </div>
                        </template>
                        <template v-if="column.dataIndex === 'userSex'">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['sexEnum'], 'id', record.userSex, 'name')">
                            </div>
                        </template>
                        <template v-if="column.dataIndex === 'state'">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['userStaffState'], 'id', record.state, 'name')">
                            </div>
                        </template>
                    </template>
                </SkTable>
            </a-form-item-rest>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, computed, watch } from 'vue'
import { SearchOutlined, SaveOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'
import SkTableImg from '@/components/SkTableImg/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkButton from '@/components/SkButton/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    multiple: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['select', 'selectAll', 'selectionChange', 'update:modelValue', 'change'])

// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})
const selectedRows = ref([])
const selectedRowKeys = ref([])

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            if (Array.isArray(newVal)) {
                // 多选模式
                selectedRows.value = newVal
                selectedRowKeys.value = newVal.map(item => item.id)
            } else {
                // 单选模式
                selectedRows.value = [newVal]
                selectedRowKeys.value = [newVal.id]
            }
        } else {
            selectedRows.value = []
            selectedRowKeys.value = []
        }
    },
    { immediate: true }
)

// 表格列定义
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '姓名',
        dataIndex: 'userName',
        width: 150,
        align: 'left',
        fixed: 'left'
    },
    {
        title: '类型',
        dataIndex: 'type',
        width: 90,
        align: 'left'
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        width: 170,
        align: 'left'
    },
    {
        title: '头像',
        dataIndex: 'userPhoto',
        width: 60,
        align: 'center'
    },
    {
        title: '身份证',
        dataIndex: 'userIdCard',
        width: 160,
        align: 'center'
    },
    {
        title: '性别',
        dataIndex: 'userSex',
        width: 60,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 220,
        align: 'center'
    },
    {
        title: '公司信息',
        align: 'center',
        children: [{
            title: '公司',
            dataIndex: 'companyName',
            width: 120,
        },
        {
            title: '部门',
            dataIndex: 'departmentName',
            width: 120,
        },
        {
            title: '职位',
            dataIndex: 'jobName',
            width: 120,
        }]
    },

    {
        title: '手机号',
        dataIndex: 'phone',
        width: 100,
        align: 'center'
    },
    {
        title: '家庭电话',
        dataIndex: 'homePhone',
        width: 100,
        align: 'center'
    },
    {
        title: 'QQ',
        dataIndex: 'qq',
        width: 100,
    }
]
// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['userStaffType', 'sexEnum', 'userStaffState']);
    initEnumData.value = result
}

// 保存相关的状态和方法
const saveLoading = ref(false)

const handleSave = async () => {
    try {
        if (!selectedRows.value.length) {
            SkMessage.warning('请选择员工')
            return
        }
        saveLoading.value = true
        emit('change', selectedRows.value)
        SkMessage.success('保存成功')

    } catch (error) {
        SkMessage.error('保存失败')
    } finally {
        saveLoading.value = false
    }
}

// 获取员工列表
const getSupplierList = async () => {
    try {
        loading.value = true
        const params = {
            page: pagination.current,
            limit: pagination.pageSize,
            keyword: searchForm.keyword.trim() || '',
            dynamicCondition: []
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'querySysUserStaffList',
            params
        );
        tableData.value = res.rows
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取员工列表失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getSupplierList()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getSupplierList()
}

// 处理选择
const handleSelect = (record, selected) => {
    // 如果状态为2，不允许选择
    if (record.state === 2) {
        return;
    }

    if (!props.multiple) {
        // 单选模式
        selectedRows.value = selected ? [record] : []
        selectedRowKeys.value = selected ? [record.id] : []
    } else {
        // 多选模式
        if (selected) {
            // 添加新选择的记录
            if (!selectedRows.value.find(row => row.id === record.id)) {
                selectedRows.value = [...selectedRows.value, record]
                selectedRowKeys.value = selectedRows.value.map(row => row.id)
            }
        } else {
            // 移除取消选择的记录
            selectedRows.value = selectedRows.value.filter(row => row.id !== record.id)
            selectedRowKeys.value = selectedRows.value.map(row => row.id)
        }
    }
    emit('select', record, selected)
}

// 处理全选
const handleSelectAll = (selected, rows) => {
    if (!Array.isArray(rows)) {
        rows = []
    }

    // 过滤掉状态为2的行
    const validRows = rows.filter(row => row.state !== 2)

    if (selected) {
        // 先确保历史数据是有效的数组
        const validHistoryRows = selectedRows.value.filter(row => row && row.id && row.state !== 2)

        // 创建一个 Set 来存储已选择的 ID，用于去重
        const selectedIdsSet = new Set(validHistoryRows.map(row => row.id))

        // 过滤并添加新选中的有效行数据
        const validNewRows = validRows.filter(row => row && row.id && !selectedIdsSet.has(row.id))

        // 合并历史数据和新数据
        const allRows = [...validHistoryRows, ...validNewRows]

        // 更新选中状态
        selectedRows.value = allRows
        selectedRowKeys.value = allRows.map(row => row.id)
    } else {
        // 获取当前页的有效ID集合
        const currentPageIds = new Set(validRows.filter(row => row && row.id).map(row => row.id))

        // 只保留不在当前页面的选中数据
        selectedRows.value = selectedRows.value.filter(row => row && row.id && !currentPageIds.has(row.id))
        selectedRowKeys.value = selectedRows.value.map(row => row.id)
    }

    emit('selectAll', selectedRows.value)
}

const handleSelectionChange = (keys, rows) => {
    // 过滤掉状态为2的行
    const validRows = rows.filter(row => row.state !== 2)

    // 获取当前页面的所有ID
    const currentPageIds = new Set(tableData.value.map(item => item.id))

    // 保留不在当前页面的历史选中数据
    const historyRows = selectedRows.value.filter(row => !currentPageIds.has(row.id))

    // 当前页面选中的数据
    const currentPageRows = validRows.filter(row => currentPageIds.has(row.id))

    // 合并历史数据和当前页面选中的数据
    const allRows = [...historyRows, ...currentPageRows]

    // 更新选中状态
    selectedRows.value = allRows
    selectedRowKeys.value = allRows.map(row => row.id)

    emit('selectionChange', selectedRows.value)
}

// 提示信息
const alertMessage = computed(() => {
    const selectionType = props.multiple ? '多选' : '单选'
    return `员工选择规则：1.${selectionType}；如没有查到要选择的员工，请检查员工信息是否满足当前规则。2.选择后请点击确认按钮完成选择。`
})

// 组件挂载时获取数据
onMounted(async () => {
    if (props.isEdit == proxy.$config.formEditType.isEdit) {
        await getInitData()
        handleSearch()
    }
})

// 暴露方法给父组件
defineExpose({
    selectedRows,
    selectedRowKeys,
    clearSelection: () => {
        selectedRows.value = []
        selectedRowKeys.value = []
    }
})
</script>

<style scoped>
.table-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
</style>