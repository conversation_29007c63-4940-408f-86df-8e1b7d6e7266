<template>
    <div class="sk-user-select">
        <!-- 输入框 -->
        <div class="selected-tags" v-if="isEdit == $config.formEditType.isEdit">
            <a-form-item-rest>
                <a-tag v-if="showValue?.length > 0" v-for="user in showValue" :key="user.id" closable
                    @close="removeUser(user)">
                    {{ user.name }}
                </a-tag>
                <a-button type="link" @click="showModal" v-if="isEdit == $config.formEditType.isEdit">
                    <plus-outlined /> 添加员工
                </a-button>
            </a-form-item-rest>
        </div>
        <div v-else>
            <a-tag v-for="user in showValue" :key="user.id">
                {{ user.name }}
            </a-tag>
        </div>

        <!-- 选择弹窗 -->
        <SkModal v-model="modalVisible" :title="modalTitle" width="70%" @cancel="handleCancel">
            <SkUserStaffSelect ref="staffSelectRef" :multiple="multiple" :pageType="pageType" :attrKey="attrKey"
                :formData="formData" @change="handleStaffSelect" @cancel="handleCancel" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkUserStaffSelect from './index.vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Array],
        default: ''
    },
    modalTitle: {
        type: String,
        default: '选择员工'
    },
    multiple: {
        type: Boolean,
        default: true
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    placeholder: {
        type: String,
        default: '请选择员工'
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'success', 'click'])

const modalVisible = ref(false)
const staffSelectRef = ref(null)

// 移除已选用户（标签区域）
const removeUser = (user) => {
    if (props.isEdit === $config.formEditType.isEdit) {
        showValue.value = showValue.value.filter(u => u.id !== user.id)
        if (props.attrKey) {
            const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
            if (props.multiple) {
                // 多选，获取已选用户的id
                const userIds = showValue.value.map(u => u.id)
                emit('update:modelValue', JSON.stringify(userIds))
                props.formData[keyMation] = showValue.value
            } else {
                // 单选，获取已选用户的id
                const userIds = selectedUsers.value.map(u => u.id)
                emit('update:modelValue', userIds.length > 0 ? userIds[0] : null)
                props.formData[keyMation] = selectedUsers.value ? selectedUsers.value[0] : null
            }
        }
        emit('change', selectedUsers.value)
    }
}
// 显示值
const showValue = ref([])

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
}

// 处理取消
const handleCancel = () => {
    modalVisible.value = false
    staffSelectRef.value?.clearSelection()
}

// 处理员工选择 selectedStaff必定是一个集合
const handleStaffSelect = (selectedStaff) => {
    showValue.value = selectedStaff
    if (!props.multiple) {
        const chooseStaff = selectedStaff[0]
        emit('update:modelValue', chooseStaff.id)
    } else {
        const staffIds = selectedStaff.map(v => v.id)
        emit('update:modelValue', JSON.stringify(staffIds))
    }
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    // 详情页面和编辑页面都会走这里
    const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
    const mation = props.formData[mationKey]
    if (!props.multiple) {
        // 单选
        if (proxy.$util.isNull(props.modelValue)) {
            return
        }
        showValue.value = [mation]
    } else {
        // 多选
        if (proxy.$util.isNull(props.modelValue) || props.modelValue == '[]' || props.modelValue.length == 0) {
            emit('update:modelValue', JSON.stringify([]))
            return
        }
        showValue.value = mation || []
        emit('update:modelValue', JSON.stringify(props.modelValue))
    }
})
</script>

<style scoped>
.sk-user-select {
    width: 100%;
}

.sk-user-select .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    min-height: 32px;
    padding: 4px 11px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
}

.sk-user-select .selected-tags:hover {
    border-color: #40a9ff;
}

.sk-user-select :deep(.ant-tree) {
    background: transparent;
}
</style>
