<template>
    <div class="sk-project-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else>
            {{ showValue.name }}
        </div>

        <!-- 项目选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择项目" width="80%" :bodyStyle="{ height: '80vh' }">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <SkAlert message="只有【执行中】状态的项目可以选择" show-icon />
                    <div class="table-btn-group">
                        <SkAuthBtnGroup authPointCode="1574672406390" @change="handleChange" />
                    </div>
                    <!-- 搜索区域 -->
                    <div class="table-search">
                        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                            :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                            <template #submitIcon>
                                <search-outlined />
                            </template>
                            <a-form-item name="keyword">
                                <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
                            </a-form-item>
                        </SkForm>
                    </div>

                    <!-- 表格区域 -->
                    <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                        :ready="tableReady" :pagination="pagination" @change="handleTableChange" :tipInfoHeight="56">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'oddNumber'">
                                <a @click="handleProjectDetail(record)">
                                    {{ record.oddNumber }}
                                </a>
                            </template>
                            <template v-if="column.dataIndex === 'choose' && record.state == 'executing'">
                                <a-button type="link" size="small" @click.stop="handleSelect(record)">
                                    选择
                                </a-button>
                            </template>
                            <template v-if="column.dataIndex === 'state'">
                                <div
                                    v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['projectStateEnum'], 'id', record.state, 'name')">
                                </div>
                            </template>
                        </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>

        <SkModal v-model="projectModalVisible" title="项目详情" width="80%" :bodyStyle="{ height: '80vh' }">
            <ShowIndex pageId="FP2023080300003" :params="params" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkAuthBtnGroup from '@/components/SkAuthBtnGroup/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择项目'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})

const tableReady = ref(false)

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        width: 200
    },
    {
        title: '项目单号',
        dataIndex: 'numberCode',
        width: 200
    },
    {
        title: '项目名称',
        dataIndex: 'name',
        width: 300
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 180
    },
    { title: proxy.$t('common.createName'), dataIndex: 'createName', width: 140 },
    { title: proxy.$t('common.createTime'), dataIndex: 'createTime', width: 150, align: 'center' },
    { title: proxy.$t('common.lastUpdateName'), dataIndex: 'lastUpdateName', width: 140 },
    { title: proxy.$t('common.lastUpdateTime'), dataIndex: 'lastUpdateTime', width: 150, align: 'center' }
]

// 权限点处理
const authMation = ref({})
const handleChange = (key, value) => {
    authMation.value[key] = value
    pagination.current = 1
    getProjectList()
}

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['projectStateEnum']);
    initEnumData.value = result
}

// 获取项目列表
const getProjectList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            ...searchForm,
            ...authMation.value
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().projectBasePath + 'queryProProjectList',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取项目列表失败')
    } finally {
        loading.value = false
    }
}

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getProjectList()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getProjectList()
}

// 处理选择
const handleSelect = (record) => {
    showValue.value = record
    const params = {}
    params[props.attrKey] = record.id
    emit('update:modelValue', params)
    emit('change', record)
    modalVisible.value = false
}

const projectModalVisible = ref(false)
const params = ref({})
const handleProjectDetail = (record) => {
    params.value = {
        id: record.id
    }
    projectModalVisible.value = true
}

// 组件挂载时获取数据
onMounted(async () => {
    if (props.pageType == proxy.$config.pageType.EDIT) {
        // 详情页面和编辑页面都会走这里
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation || {}
        if (showValue.value.id) {
            const params = {}
            params[props.attrKey] = showValue.value.id
            emit('update:modelValue', params)
        }
    }
    getInitData()
})
</script>

<style scoped>
.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}
</style>