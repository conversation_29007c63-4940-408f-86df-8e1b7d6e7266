<template>
    <div class="sk-badge">
        <!-- 编辑模式 -->
        <template v-if="isEdit === $config.formEditType.isEdit">
            <div class="badge-container">
                <!-- 标签 -->
                <span v-if="label" class="badge-label">{{ label }}</span>
                <div class="badge-content">
                    <!-- 徽标主体 -->
                    <a-badge
                        :count="dot ? '' : badgeValue"
                        :dot="dot"
                        :overflowCount="overflowCount"
                        :showZero="showZero"
                        :status="status"
                        :color="color"
                        :text="text"
                        :size="size"
                        :offset="offset"
                        :numberStyle="numberStyle"
                    >
                        <!-- 默认插槽内容 -->
                        <slot>
                            <div v-if="defaultContent" class="default-content">
                                {{ defaultContent }}
                            </div>
                        </slot>
                    </a-badge>
                    <!-- 输入框 -->
                    <a-input-number
                        v-if="showInput && !dot && !status"
                        v-model:value="badgeValue"
                        :min="0"
                        :max="overflowCount"
                        :disabled="disabled"
                        class="badge-input"
                        @change="handleInputChange"
                    />
                </div>
            </div>
        </template>
        <!-- 查看模式 -->
        <template v-else>
            <div class="view-mode">
                <span v-if="label" class="view-label">{{ label }}：</span>
                <a-badge
                    :count="dot ? '' : badgeValue"
                    :dot="dot"
                    :overflowCount="overflowCount"
                    :showZero="showZero"
                    :status="status"
                    :color="color"
                    :text="text"
                    :size="size"
                    :offset="offset"
                    :numberStyle="numberStyle"
                >
                    <slot>
                        <div v-if="defaultContent" class="default-content">
                            {{ defaultContent }}
                        </div>
                    </slot>
                </a-badge>
            </div>
        </template>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    modelValue: {
        type: [Number, String],
        default: 0
    },
    label: {
        type: String,
        default: ''
    },
    overflowCount: {
        type: Number,
        default: 99
    },
    showZero: {
        type: Boolean,
        default: false
    },
    dot: {
        type: Boolean,
        default: false
    },
    status: {
        type: String,
        default: ''  // success, processing, default, error, warning
    },
    color: {
        type: String,
        default: ''
    },
    text: {
        type: String,
        default: ''
    },
    size: {
        type: String,
        default: 'default'  // default, small
    },
    offset: {
        type: Array,
        default: () => []
    },
    numberStyle: {
        type: Object,
        default: () => ({})
    },
    showInput: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    defaultContent: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 使用计算属性处理双向绑定
const badgeValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

// 处理输入框值变化
const handleInputChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
}
</script>

<style scoped>
.sk-badge {
    width: 100%;
}

.badge-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.badge-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

.badge-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.badge-input {
    width: 90px;
}

.default-content {
    width: 42px;
    height: 42px;
    background: #eee;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
}

.view-mode {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    line-height: 32px;
}

.view-label {
    color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-badge) {
    line-height: 1;
}

:deep(.ant-badge-count) {
    z-index: 1;
}

:deep(.ant-badge-status-dot) {
    width: 6px;
    height: 6px;
}

:deep(.ant-badge-status-text) {
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-badge-dot) {
    width: 6px;
    height: 6px;
}
</style>