<template>
    <div class="sk-account-select">
        <SkSelect v-if="isEdit == $config.formEditType.isEdit && isShow" v-model="selectedValue" :options="listData"
            :field-names="fieldNames" @change="handleChange" />
        <div v-else>
            {{ showValue?.name }}
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import SkSelect from '@/components/SkSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: String,
        default: () => ''
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },

    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 数据
const listData = ref([])
// 选中值
const selectedValue = ref(props.modelValue)
// 显示值
const showValue = ref({})

// 字段映射
const fieldNames = {
    label: 'name',
    value: 'id',
    children: 'children'
}

// 监听选中值变化
watch(selectedValue, (value) => {
    emit('update:modelValue', value)
    emit('change', value)
})

// 选择变化处理
const handleChange = (value) => {
    selectedValue.value = value
}

const isShow = ref(false)

// 获取账户树数据
const getAccountTree = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().ifsBasePath + 'account009'
        )
        listData.value = res.rows
        isShow.value = true
    } catch (error) {
        proxy.$message.error('获取账户数据失败')
    }
}

// 组件挂载时获取数据
onMounted(async () => {
    if (props.isEdit == $config.formEditType.isEdit) {
        if (props.pageType == proxy.$config.pageType.ADD) {
            selectedValue.value = proxy.$config.getCurrentUser()?.accountId
        }
        await getAccountTree()
    } else {
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation
    }
})
</script>

<style scoped>
.sk-account-select {
    width: 100%;
}
</style>