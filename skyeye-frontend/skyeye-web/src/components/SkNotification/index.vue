<template>
    <div>
        <!-- 移除 a-notification 组件的模板部分，因为我们直接使用 notification API -->
    </div>
</template>

<script>
import { notification } from 'ant-design-vue'
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'SkNotification',
    props: {
        className: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        // 配置全局样式
        notification.config({
            placement: 'topRight',
            duration: 4.5
        })

        const open = (config) => {
            const finalConfig = {
                ...config,
                class: [config.class, 'sk-notification', props.className].filter(Boolean).join(' ')
            }
            notification.open(finalConfig)
        }

        const success = (config) => {
            notification.success({
                ...config,
                class: [config?.class, 'sk-notification', props.className].filter(Boolean).join(' ')
            })
        }

        const error = (config) => {
            notification.error({
                ...config,
                class: [config?.class, 'sk-notification', props.className].filter(Boolean).join(' ')
            })
        }

        const info = (config) => {
            notification.info({
                ...config,
                class: [config?.class, 'sk-notification', props.className].filter(Boolean).join(' ')
            })
        }

        const warning = (config) => {
            notification.warning({
                ...config,
                class: [config?.class, 'sk-notification', props.className].filter(Boolean).join(' ')
            })
        }

        return {
            open,
            success,
            error,
            info,
            warning
        }
    }
})
</script>

<style lang="less" scoped>
:deep(.sk-notification) {

    // 自定义样式
    &.ant-notification-notice {
        padding: 16px 24px;
        border-radius: 4px;
    }
}
</style>