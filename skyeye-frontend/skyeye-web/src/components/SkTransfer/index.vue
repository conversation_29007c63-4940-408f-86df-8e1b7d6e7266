<template>
    <div class="sk-transfer-wrapper">
        <a-transfer v-if="isEdit === $config.formEditType.isEdit" v-bind="$attrs" :data-source="dataSource"
            :target-keys="modelValue" :disabled="disabled" :show-search="showSearch" :filter-option="handleFilter"
            :render="renderItem" :show-select-all="showSelectAll" :titles="titles" @change="handleChange"
            @search="handleSearch" @selectChange="handleSelectChange">
            <template #children="{ direction, selectedKeys, onItemSelect }">
                <slot :direction="direction" :selected-keys="selectedKeys" :on-item-select="onItemSelect"></slot>
            </template>
            <template v-if="$slots.footer" #footer="{ direction }">
                <slot name="footer" :direction="direction"></slot>
            </template>
        </a-transfer>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue && modelValue.length">
                {{ getSelectedTitles }}
            </template>
        </div>
        <div v-if="help" class="sk-transfer-help">
            <info-circle-outlined />
            {{ help }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

defineOptions({
    name: 'SkTransfer'
})

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    dataSource: {
        type: Array,
        default: () => []
    },
    disabled: {
        type: Boolean,
        default: false
    },
    showSearch: {
        type: Boolean,
        default: false
    },
    showSelectAll: {
        type: Boolean,
        default: true
    },
    titles: {
        type: Array,
        default: () => ['源列表', '目标列表']
    },
    help: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    placeholder: {
        type: String,
        default: '暂无数据'
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'search', 'selectChange'])

// 获取选中项的标题
const getSelectedTitles = computed(() => {
    if (!props.modelValue || !props.modelValue.length) return ''
    return props.dataSource
        .filter(item => props.modelValue.includes(item.key))
        .map(item => item.title)
        .join('、')
})

// 处理穿梭框数据变化
const handleChange = (targetKeys, direction, moveKeys) => {
    emit('update:modelValue', targetKeys)
    emit('change', { targetKeys, direction, moveKeys })
}

// 处理搜索
const handleSearch = (direction, value) => {
    emit('search', { direction, value })
}

// 处理选择变化
const handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
    emit('selectChange', { sourceSelectedKeys, targetSelectedKeys })
}

// 处理过滤
const handleFilter = (inputValue, item) => {
    return item.title.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1
}

// 渲染列表项
const renderItem = (item) => {
    return item.title
}
</script>

<style lang="less" scoped>
.sk-transfer-wrapper {
    display: inline-flex;
    flex-direction: column;
    gap: 8px;

    .sk-transfer-help {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
    }

    :deep(.ant-transfer) {
        .ant-transfer-list {
            min-height: 300px;
        }
    }
}
</style>