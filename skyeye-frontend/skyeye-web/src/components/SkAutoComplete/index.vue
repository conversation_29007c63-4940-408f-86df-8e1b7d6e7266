<template>
    <div class="sk-auto-complete-wrapper">
        <a-auto-complete v-if="isEdit === $config.formEditType.isEdit" :value="modelValue" :options="options"
            :placeholder="!$slots.default ? placeholder : undefined"
            :allowClear="!$slots.default ? allowClear : undefined" :autoFocus="autoFocus" :backfill="backfill"
            :defaultActiveFirstOption="defaultActiveFirstOption" :dropdownMatchSelectWidth="dropdownMatchSelectWidth"
            :filterOption="filterOption" :status="status" @change="handleChange" @select="handleSelect"
            @focus="handleFocus" @blur="handleBlur" @dropdownVisibleChange="handleDropdownVisibleChange"
            @search="handleSearch">
            <template v-if="$slots.default" #default>
                <slot></slot>
            </template>
            <template v-if="$slots.option" #option="item">
                <slot name="option" v-bind="item"></slot>
            </template>
        </a-auto-complete>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                {{ modelValue }}
            </template>
            <template v-else>
                <span class="sk-detail-placeholder">{{ placeholder }}</span>
            </template>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    // 绑定值
    modelValue: {
        type: String,
        default: ''
    },
    // 数据源
    options: {
        type: Array,
        default: () => []
    },
    // 占位符
    placeholder: {
        type: String,
        default: '请输入'
    },
    // 是否可以清除
    allowClear: {
        type: Boolean,
        default: false
    },
    // 自动获取焦点
    autoFocus: {
        type: Boolean,
        default: false
    },
    // 使用键盘选择选项的时候把选中项回填到输入框中
    backfill: {
        type: Boolean,
        default: false
    },
    // 是否默认高亮第一个选项
    defaultActiveFirstOption: {
        type: Boolean,
        default: true
    },
    // 下拉菜单和选择器同宽
    dropdownMatchSelectWidth: {
        type: [Boolean, Number],
        default: true
    },
    // 是否根据输入项进行筛选
    filterOption: {
        type: [Boolean, Function],
        default: true
    },
    // 验证状态
    status: {
        type: String,
        default: undefined
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: 1 // 默认可编辑
    }
})

const emit = defineEmits([
    'update:modelValue',
    'change',
    'select',
    'focus',
    'blur',
    'dropdownVisibleChange',
    'search'
])

// 处理输入变化
const handleChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
}

// 处理选择
const handleSelect = (value, option) => {
    emit('select', value, option)
}

// 处理获得焦点
const handleFocus = (e) => {
    emit('focus', e)
}

// 处理失去焦点
const handleBlur = (e) => {
    emit('blur', e)
}

// 处理下拉菜单显示状态变化
const handleDropdownVisibleChange = (open) => {
    emit('dropdownVisibleChange', open)
}

// 处理搜索
const handleSearch = (value) => {
    emit('search', value)
}
</script>

<style lang="less" scoped>
.sk-auto-complete-wrapper {
    :deep(.ant-select) {
        width: 100%;
    }
}
</style>
