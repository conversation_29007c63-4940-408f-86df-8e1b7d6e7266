<template>
    <div class="sk-map-location">
        <a-row>
            <!-- 经度输入框 -->
            <a-col :span="12">
                <a-form-item label="经度">
                    {{ longitude || '请在地图上点击选择' }}
                </a-form-item>
            </a-col>
            <!-- 纬度输入框 -->
            <a-col :span="12">
                <a-form-item label="纬度">
                    {{ latitude || '请在地图上点击选择' }}
                </a-form-item>
            </a-col>
            <!-- 详细地址输入框 -->
            <a-col :span="24">
                <a-form-item label="详细地址">
                    <SkInput v-model="absoluteAddress" placeholder="请输入详细地址" :isEdit="isEdit">
                        <template #suffix>
                            <SearchOutlined @click="handleSearch" />
                        </template>
                    </SkInput>
                </a-form-item>
            </a-col>
        </a-row>

        <!-- 地图容器 -->
        <div class="map-container sk-map-location" ref="mapContainer"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, getCurrentInstance } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import SkMessage from '@/components/SkMessage/index.vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import SkInput from '@/components/SkInput/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Object, String],
        default: () => ({
            longitude: '',
            latitude: '',
            absoluteAddress: '',
            provinceId: '',
            cityId: '',
            areaId: '',
            townshipId: ''
        })
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue'])

// 地图相关变量
const mapContainer = ref(null)
const map = ref(null)
const marker = ref(null)
const geocoder = ref(null)

// 经纬度和地址
const longitude = ref('')
const latitude = ref('')
const absoluteAddress = ref('')
const provinceId = ref('')
const cityId = ref('')
const areaId = ref('')
const townshipId = ref('')

// 监听本地值变化并更新父组件
watch([longitude, latitude, absoluteAddress, provinceId, cityId, areaId, townshipId], ([newLng, newLat, newAddr, newProvinceId, newCityId, newAreaId, newTownshipId]) => {
    emit('update:modelValue', {
        longitude: newLng,
        latitude: newLat,
        absoluteAddress: newAddr,
        provinceId: newProvinceId,
        cityId: newCityId,
        areaId: newAreaId,
        townshipId: newTownshipId
    })
}, { deep: true })

// 初始化地图
const initMap = async () => {
    try {
        // 设置安全密钥 - 必须在加载地图之前设置
        window._AMapSecurityConfig = {
            securityJsCode: proxy.$config.getConfig().securityJsCode
        }

        // 加载高德地图
        const AMap = await AMapLoader.load({
            key: proxy.$config.getConfig().skyeyeMapKey,
            version: '2.0',
            cookieEnabled: false, // 关闭 Cookie
            policy: 1, // 使用 HTTPS
            plugins: [
                'AMap.Geocoder',
                'AMap.PlaceSearch',
                'AMap.AutoComplete',
                'AMap.Scale',
                'AMap.ToolBar'
            ]
        })

        // 创建地图实例
        map.value = new AMap.Map(mapContainer.value, {
            zoom: 13,
            center: [longitude.value || 109.21508, latitude.value || 23.757652],
            viewMode: '2D',
            mapStyle: 'amap://styles/normal',
            pitch: 0,
            rotateEnable: false,
            protocol: 'https', // 使用 HTTPS 协议
            showBuildingBlock: false, // 不显示楼块，减少第三方请求
            features: ['bg', 'road', 'point'] // 只加载基本图层
        })

        // 创建标记点
        marker.value = new AMap.Marker({
            draggable: props.isEdit === proxy.$config.formEditType.isEdit,
            cursor: 'move',
            position: [longitude.value || 109.21508, latitude.value || 23.757652]
        })

        // 将标记点添加到地图
        map.value.add(marker.value)

        // 创建地理编码实例
        geocoder.value = new AMap.Geocoder({
            city: "全国",
            radius: 500,
            extensions: "all",
            batch: false
        })

        // 如果有初始坐标，设置标记点
        if (longitude.value && latitude.value) {
            setMarkerPosition([longitude.value, latitude.value])
        }

        // 绑定地图点击事件
        if (props.isEdit === proxy.$config.formEditType.isEdit) {
            map.value.on('click', handleMapClick)
            marker.value.on('dragend', handleMarkerDragEnd)
        }

        // 添加地图控件
        map.value.addControl(new AMap.Scale({
            position: 'LB'
        }))
        map.value.addControl(new AMap.ToolBar({
            position: 'RB'
        }))

    } catch (error) {
        SkMessage.error('地图加载失败')
    }
}

// 设置标记点位置
const setMarkerPosition = (position) => {
    marker.value.setPosition(position)
    map.value.setCenter(position)
}

// 处理地图点击
const handleMapClick = (e) => {
    const lngLat = e.lnglat
    const lng = lngLat.getLng()
    const lat = lngLat.getLat()
    setMarkerPosition([lng, lat])
    updateLocationInfo([lng, lat])
}

// 处理标记点拖拽结束
const handleMarkerDragEnd = (e) => {
    const position = marker.value.getPosition()
    const lng = position.getLng()
    const lat = position.getLat()
    updateLocationInfo([lng, lat])
}

// 更新位置信息
const updateLocationInfo = (position) => {
    // 保留更多小数位以提高精度
    longitude.value = position[0]
    latitude.value = position[1]

    // 使用逆地理编码获取地址
    geocoder.value.getAddress(position, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
            const formattedAddress = result.regeocode.formattedAddress
            absoluteAddress.value = formattedAddress
            provinceId.value = result.regeocode.addressComponent.province
            cityId.value = result.regeocode.addressComponent.city
            areaId.value = result.regeocode.addressComponent.district
            townshipId.value = result.regeocode.addressComponent.street
        } else {
            SkMessage.error('地址解析失败')
        }
    })
}

// 搜索地址
const handleSearch = () => {
    if (!absoluteAddress.value) {
        SkMessage.warning('请输入地址')
        return
    }

    geocoder.value.getLocation(absoluteAddress.value, function (status, result) {
        if (status === 'complete' && result.info === 'OK') {
            if (result.geocodes.length > 0) {
                const location = result.geocodes[0].location
                const lng = location.lng
                const lat = location.lat
                setMarkerPosition([lng, lat])
                updateLocationInfo([lng, lat])
            } else {
                SkMessage.warning('未找到匹配的地址')
            }
        } else {
            SkMessage.error('地址查询失败')
        }
    })
}

// 清理地图实例
const destroyMap = () => {
    if (map.value) {
        // 移除事件监听
        if (props.isEdit === proxy.$config.formEditType.isEdit) {
            map.value.off('click', handleMapClick)
            if (marker.value) {
                marker.value.off('dragend', handleMarkerDragEnd)
            }
        }

        // 移除标记点
        if (marker.value) {
            map.value.remove(marker.value)
            marker.value = null
        }

        // 销毁地图实例
        map.value.destroy()
        map.value = null
    }

    // 清理地理编码实例
    if (geocoder.value) {
        geocoder.value = null
    }
}

// 组件挂载时初始化地图
onMounted(() => {
    longitude.value = proxy.$util.isNull(props.formData?.longitude) ? '' : props.formData?.longitude
    latitude.value = proxy.$util.isNull(props.formData?.latitude) ? '' : props.formData?.latitude
    absoluteAddress.value = proxy.$util.isNull(props.formData?.absoluteAddress) ? '' : props.formData?.absoluteAddress
    provinceId.value = proxy.$util.isNull(props.formData?.provinceId) ? '' : props.formData?.provinceId
    cityId.value = proxy.$util.isNull(props.formData?.cityId) ? '' : props.formData?.cityId
    areaId.value = proxy.$util.isNull(props.formData?.areaId) ? '' : props.formData?.areaId
    townshipId.value = proxy.$util.isNull(props.formData?.townshipId) ? '' : props.formData?.townshipId
    initMap()
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
    destroyMap()
})
</script>

<style scoped>
/* 确保地图控件正确显示 */
:deep(.amap-control) {
    z-index: 2;
}
</style>