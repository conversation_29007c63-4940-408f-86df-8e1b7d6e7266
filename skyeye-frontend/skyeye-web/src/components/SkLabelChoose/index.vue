<template>
    <div class="sk-label-choose">
        <div v-if="isEdit == $config.formEditType.isEdit">
            <template v-for="(tag, index) in dynamicTags" :key="tag">
                <!-- 编辑状态 -->
                <a-input v-if="editInputIndex === index" :ref="el => editInputRef = el" v-model:value="editInputValue"
                    size="small" style="width: 78px; margin-right: 8px" @blur="handleEditInputConfirm"
                    @keyup.enter.prevent="handleEditInputConfirm" />
                <!-- 显示状态 -->
                <sk-tag v-else :closable="true" v-model="dynamicTagsVisible[index]" @close="handleDynamicClose(tag)"
                    color="blue">
                    <template #icon>
                        <edit-outlined class="edit-icon" @click.stop="handleEdit(tag, index)" />
                    </template>
                    {{ tag }}
                </sk-tag>
            </template>
            <!-- 新增输入框 -->
            <a-input v-if="inputVisible" :ref="el => inputRef = el" v-model:value="inputValue" type="text" size="small"
                style="width: 150px" :maxlength="20" :show-count="true" @blur="handleInputConfirm"
                @keyup.enter.prevent="handleInputConfirm" />
            <!-- 新增按钮 -->
            <sk-tag v-else style="background: #fff;" @click="showInput">
                <template #icon>
                    <plus-outlined />
                </template>
                新增标签
            </sk-tag>
        </div>
        <div v-else>
            <sk-tag v-for="tag in dynamicTags" :key="tag" color="blue">{{ tag }}</sk-tag>
        </div>
    </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import SkTag from '@/components/SkTag/index.vue'
import { PlusOutlined, EditOutlined } from '@ant-design/icons-vue'

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const inputRef = ref(null)
const inputVisible = ref(false)
const inputValue = ref('')
const dynamicTags = ref([])
const dynamicTagsVisible = ref([])
const showValue = ref('')

// 编辑相关
const editInputRef = ref(null)
const editInputIndex = ref(-1)
const editInputValue = ref('')

const showInput = () => {
    inputVisible.value = true
    nextTick(() => {
        if (inputRef.value) {
            inputRef.value.focus()
        }
    })
}

const handleInputConfirm = (event) => {
    if (event) {
        // 阻止事件冒泡和默认行为
        event.stopPropagation()
        event.preventDefault()
    }

    if (inputValue.value && !dynamicTags.value.includes(inputValue.value)) {
        dynamicTags.value.push(inputValue.value)
        dynamicTagsVisible.value.push(true)
        updateModelValue()
    }
    inputVisible.value = false
    inputValue.value = ''
}

const handleDynamicClose = (tag) => {
    const index = dynamicTags.value.indexOf(tag)
    if (index > -1) {
        dynamicTagsVisible.value[index] = false
        updateModelValue()
    }
}

const handleEdit = (tag, index) => {
    editInputIndex.value = index
    editInputValue.value = tag
    nextTick(() => {
        if (editInputRef.value) {
            editInputRef.value.focus()
        }
    })
}

const handleEditInputConfirm = (event) => {
    if (event) {
        // 阻止事件冒泡和默认行为
        event.stopPropagation()
        event.preventDefault()
    }

    if (editInputValue.value && !dynamicTags.value.includes(editInputValue.value)) {
        dynamicTags.value[editInputIndex.value] = editInputValue.value
        updateModelValue()
    }
    editInputIndex.value = -1
    editInputValue.value = ''
}

// 更新绑定值
const updateModelValue = () => {
    const visibleTags = dynamicTags.value.filter((_, index) => dynamicTagsVisible.value[index])
    const newValue = visibleTags.join(',')
    showValue.value = newValue
    emit('update:modelValue', newValue)
    emit('change', newValue)
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        const tags = newVal.split(',')
        dynamicTags.value = tags
        dynamicTagsVisible.value = new Array(tags.length).fill(true)
        showValue.value = newVal
    } else {
        dynamicTags.value = []
        dynamicTagsVisible.value = []
        showValue.value = ''
    }
}, { immediate: true })
</script>

<style scoped>
.edit-icon {
    cursor: pointer;
    color: #1890ff;
}

.sk-label-choose :deep(.ant-tag) {
    margin: 4px;
}
</style>