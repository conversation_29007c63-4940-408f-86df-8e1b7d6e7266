<template>
    <div class="sk-email">
        <!-- 整个输入区域，点击时显示输入框 -->
        <div class="input-with-icons" @click="showInput">
            <!-- 标签展示区域 -->
            <div class="tags-wrapper" :class="{ 'is-editing': isEdit === $config.formEditType.isEdit }">
                <!-- 只在有数据且不为空时才渲染标签 -->
                <template v-if="selectedUsers.length && selectedUsers[0]?.email">
                    <template v-for="(user, index) in selectedUsers" :key="user.id">
                        <!-- 编辑状态的输入框 -->
                        <a-input v-if="editInputIndex === index" ref="editInputRef" v-model:value="editInputValue"
                            size="small" class="tag-input" @blur="handleEditInputConfirm"
                            @keydown.enter.prevent="handleEditInputConfirm" @click.stop />
                        <!-- 标签显示状态 -->
                        <sk-tag v-else :closable="isEdit === $config.formEditType.isEdit" @close="handleRemove(user)"
                            color="blue">
                            <!-- 编辑图标 -->
                            <template #icon v-if="isEdit === $config.formEditType.isEdit">
                                <edit-outlined class="edit-icon" @click.stop="handleEdit(user, index)" />
                            </template>
                            {{ user.email }}
                        </sk-tag>
                    </template>
                </template>

                <!-- 新增邮箱的输入框 -->
                <a-input v-if="inputVisible && isEdit === $config.formEditType.isEdit" ref="inputRef"
                    v-model:value="inputValue" size="small" class="tag-input" :placeholder="placeholder"
                    @blur="handleInputConfirm" @keydown.enter.prevent="handleInputConfirm" @click.stop />

                <!-- 无数据时的占位文本 -->
                <span v-if="(!selectedUsers.length || !selectedUsers[0]?.email) && !inputVisible" class="placeholder">
                    {{ placeholder }}
                </span>
            </div>

            <!-- 右侧选择按钮组 -->
            <div v-if="isEdit === $config.formEditType.isEdit" class="icon-group">
                <UserAddOutlined class="icon" @click.stop="handleSelectPeople" />
                <ContactsOutlined class="icon" @click.stop="handleSelectMail" />
            </div>
        </div>

        <!-- 弹窗 -->
        <SkModal v-model="modalVisible" :title="modalTitle" width="60%" @ok="handleOk" @cancel="handleCancel"
            :showOk="true" :showCancel="true">
            <!-- 人员选择组件 -->
            <SkUserSelectModel v-model="tempSelectedUsers" v-if="modalType === 'people'" :multiple="multiple"
                :chooseOrNotMy="chooseOrNotMy" :chooseOrNotEmail="chooseOrNotEmail" />
            <!-- 通讯录选择组件 -->
            <ShowIndex ref="showIndexRef" v-if="modalType === 'mail'" :pageId="operatorParams.pageId"
                :params="operatorParams.params" :whetherCustomerData="whetherCustomerData" :customerData="customerData"
                @rowHandleSelect="handleRowSelect">
            </ShowIndex>
        </SkModal>
    </div>
</template>

<script setup>
// 导入所需的组件和工具
import { ref, watch, getCurrentInstance, nextTick } from 'vue'
import { UserAddOutlined, ContactsOutlined, EditOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkUserSelectModel from '@/components/SkUserSelectModel/index.vue'
import ShowIndex from "@/views/dsForm/show/index.vue"
import SkTag from '@/components/SkTag/index.vue'
import {SkMessage} from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 组件属性定义
const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    mationValue: {  // 用于存储用户详细信息的数组
        type: Array,
        default: () => []
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    multiple: {  // 是否允许多选
        type: Boolean,
        default: true
    },
    chooseOrNotMy: {  // 是否可选择自己
        type: [Number, String],
        default: 1
    },
    chooseOrNotEmail: {  // 是否必须有邮箱
        type: [Number, String],
        default: 1
    },
    isEdit: {  // 是否可编辑
        type: Number,
        default: 1
    },
    objectId: {  // 对象ID，用于回调
        type: String,
        default: ''
    }
})

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'update:mationValue', 'change'])

// 组件内部状态
const selectedUsers = ref([])  // 已选用户列表
const inputRef = ref(null)     // 新增输入框引用
const inputVisible = ref(false) // 是否显示新增输入框
const inputValue = ref('')     // 新增输入框的值
const editInputRef = ref(null)  // 编辑输入框引用
const editInputIndex = ref(-1)  // 当前编辑的标签索引
const editInputValue = ref('')  // 编辑输入框的值
const tempSelectedUsers = ref([]) // 弹窗临时选择的用户

// 弹窗相关状态
const modalVisible = ref(false)
const modalTitle = ref("人员选择")
const modalType = ref("people")
const whetherCustomerData = ref(false)
const operatorParams = ref({
    pageId: "FP2024022300008",
    params: {}
})
const customerData = ref({
    type: 'simpleTable',
    tableConfig: {
        showSelect: true,
        multiple: true,
        whetherChoose: 'checkbox',
    }
})

// 显示输入框
const showInput = () => {
    if (props.isEdit === proxy.$config.formEditType.isEdit && !inputVisible.value) {
        inputVisible.value = true
        nextTick(() => {
            inputRef.value?.focus()
        })
    }
}

// 处理输入确认
const handleInputConfirm = () => {
    if (inputValue.value) {
        // 验证邮箱格式
        if (!validateEmail(inputValue.value)) {
            SkMessage.error('请输入正确的邮箱格式')
            return
        }

        // 创建新用户对象
        const newUser = {
            id: Date.now().toString(),
            email: inputValue.value,
            name: inputValue.value.split('@')[0]
        }
        selectedUsers.value.push(newUser)
        updateModelValue()
    }
    inputVisible.value = false
    inputValue.value = ''
}

// 邮箱格式验证
const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
}

// 处理标签编辑
const handleEdit = (user, index) => {
    editInputIndex.value = index
    editInputValue.value = user.email
    nextTick(() => {
        editInputRef.value?.focus()//聚焦
    })
}

// 处理编辑确认
const handleEditInputConfirm = () => {
    if (editInputValue.value) {
        const user = selectedUsers.value[editInputIndex.value]
        user.email = editInputValue.value
        user.name = editInputValue.value.split('@')[0]
        updateModelValue()
    }
    editInputIndex.value = -1
    editInputValue.value = ''
}

// 处理移除标签
const handleRemove = (user) => {
    selectedUsers.value = selectedUsers.value.filter(u => u.id !== user.id)
    updateModelValue()
}

// 更新组件值并触发事件
const updateModelValue = () => {
    const emails = selectedUsers.value.map(u => u.email).join(',')
    const ids = selectedUsers.value.map(u => u.id)
    emit('update:modelValue', emails)
    emit('update:mationValue', selectedUsers.value)
    emit('change', selectedUsers.value, ids, props.objectId)
}

// 显示人员选择弹窗
const handleSelectPeople = () => {
    modalTitle.value = "人员选择"
    modalType.value = "people"
    tempSelectedUsers.value = props.mationValue
    modalVisible.value = true
}

// 显示通讯录弹窗
const handleSelectMail = () => {
    modalTitle.value = "通讯录选择"
    modalType.value = "mail"
    whetherCustomerData.value = true
    modalVisible.value = true
    tempSelectedUsers.value = props.mationValue
}

// 处理弹窗确定
const handleOk = () => {
    if (tempSelectedUsers.value?.length) {
        selectedUsers.value = [...tempSelectedUsers.value]
        updateModelValue()
    }
    modalVisible.value = false
}

// 处理弹窗取消
const handleCancel = () => {
    modalVisible.value = false
    tempSelectedUsers.value = [...props.mationValue]
}

// 处理通讯录行选择
const handleRowSelect = (selection) => {
    // 将选中的行转换为用户对象
    const newUsers = selection
        .filter(item => item.email) // 过滤掉没有邮箱的用户
        .map(item => ({
            id: item.id,
            name: item.name,
            email: item.email
        }));

    // 过滤掉已存在的邮箱
    const existingEmails = tempSelectedUsers.value.map(u => u.email)
    const uniqueNewUsers = newUsers.filter(u => !existingEmails.includes(u.email))
    
    tempSelectedUsers.value = [...tempSelectedUsers.value, ...uniqueNewUsers]
}

// 监听外部值变化
watch(() => props.mationValue, (newVal) => {
    if (newVal?.length) {
        selectedUsers.value = [...newVal]
    } else {
        selectedUsers.value = []
    }
}, { immediate: true })
</script>

<style lang="less" scoped>
.sk-email {
    width: 100%;

    .input-with-icons {
        position: relative;
        display: flex;
        align-items: center;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
        padding: 4px 40px 4px 4px;
        min-height: 32px;
        cursor: text; // 添加文本光标样式

        &:hover {
            border-color: #40a9ff;
        }

        .tags-wrapper {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 24px;
        }

        .placeholder {
            color: #bfbfbf;
            font-size: 14px;
        }

        .icon-group {
            position: absolute;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 8px;

            .icon {
                cursor: pointer;
                font-size: 16px;
                color: #666;

                &:hover {
                    color: #1890ff;
                }
            }
        }
    }

    .tag-input {
        width: 150px;
        margin-right: 8px;
    }

    .edit-icon {
        cursor: pointer;
        color: #1890ff;
    }

    :deep(.ant-tag) {
        margin: 0;
        display: inline-flex;
        align-items: center;
    }
}
</style>
