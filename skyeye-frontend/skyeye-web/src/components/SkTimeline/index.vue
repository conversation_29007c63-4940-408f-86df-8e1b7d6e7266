<template>
    <div class="sk-timeline-wrapper">
        <a-timeline :mode="mode" :pending="pending">
            <template v-for="(item, index) in items" :key="index">
                <a-timeline-item :color="item.color" :dot="item.dot" :label="item.label" :position="item.position">
                    <!-- 自定义图标 -->
                    <template #dot v-if="$slots.dot">
                        <slot name="dot" :item="item" :index="index"></slot>
                    </template>

                    <!-- 自定义内容 -->
                    <template v-if="$slots.default">
                        <slot :item="item" :index="index"></slot>
                    </template>
                    <template v-else>
                        {{ item.content }}
                    </template>
                </a-timeline-item>
            </template>

            <!-- 待完成节点 -->
            <template #pending v-if="$slots.pending">
                <slot name="pending"></slot>
            </template>
        </a-timeline>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 时间轴数据
    items: {
        type: Array,
        default: () => []
    },
    // 时间轴展示模式：left | alternate | right
    mode: {
        type: String,
        default: 'left'
    },
    // 是否展示幽灵节点
    pending: {
        type: Boolean,
        default: false
    }
})
</script>

<style scoped>
.sk-timeline-wrapper {
    width: 100%;
}

:deep(.ant-timeline) {
    width: 100%;
}

:deep(.ant-timeline-item) {
    padding-bottom: 20px;
}

:deep(.ant-timeline-item-content) {
    margin-inline-start: 28px;
}

:deep(.ant-timeline-item-head) {
    width: 12px;
    height: 12px;
}

:deep(.ant-timeline-item-head-custom) {
    width: auto;
    height: auto;
    margin-top: 0;
    padding: 0;
    line-height: 1;
}
</style>