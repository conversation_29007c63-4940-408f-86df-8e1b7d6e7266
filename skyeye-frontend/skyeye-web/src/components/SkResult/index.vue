<template>
    <a-result v-bind="$attrs" :class="['sk-result', className]" :status="status" :title="title" :sub-title="subTitle">
        <template v-if="$slots.extra" #extra>
            <slot name="extra"></slot>
        </template>
        <template v-if="$slots.icon" #icon>
            <slot name="icon"></slot>
        </template>
        <template v-if="$slots.default">
            <slot></slot>
        </template>
    </a-result>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'SkResult',
    props: {
        className: {
            type: String,
            default: ''
        },
        status: {
            type: String,
            default: 'info'
        },
        title: {
            type: String,
            default: ''
        },
        subTitle: {
            type: String,
            default: ''
        }
    }
})
</script>

<style lang="less" scoped>
.sk-result {

    // 自定义样式
    :deep(.ant-result-title) {
        color: rgba(0, 0, 0, 0.85);
        font-size: 24px;
        line-height: 1.8;
    }

    :deep(.ant-result-subtitle) {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        line-height: 1.6;
    }

    :deep(.ant-result-extra) {
        margin-top: 24px;
    }
}
</style>