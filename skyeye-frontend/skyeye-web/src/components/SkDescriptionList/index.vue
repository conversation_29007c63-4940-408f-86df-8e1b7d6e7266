<template>
    <div class="sk-description-list">
        <a-descriptions :title="title" :bordered="bordered" :column="column" :size="size" :layout="layout"
            :colon="colon" :labelStyle="labelStyle" :contentStyle="contentStyle">
            <template v-for="item in items" :key="item.key">
                <a-descriptions-item :label="item.label" :span="item.span">
                    <!-- 使用作用域插槽允许自定义内容 -->
                    <slot :name="item.key" :item="item">
                        {{ item.value }}
                    </slot>
                </a-descriptions-item>
            </template>

            <!-- 额外的自定义项 -->
            <slot></slot>

            <!-- 额外的操作区域 -->
            <template v-if="$slots.extra" #extra>
                <slot name="extra"></slot>
            </template>
        </a-descriptions>
    </div>
</template>

<script setup>
defineProps({
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 是否展示边框
    bordered: {
        type: Boolean,
        default: false
    },
    // 一行的列数
    column: {
        type: [Number, Object],
        default: 3
    },
    // 设置列表大小
    size: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'middle', 'small'].includes(value)
    },
    // 描述布局
    layout: {
        type: String,
        default: 'horizontal',
        validator: (value) => ['horizontal', 'vertical'].includes(value)
    },
    // 配置 label 和 content 的冒号
    colon: {
        type: Boolean,
        default: true
    },
    // label 样式
    labelStyle: {
        type: Object,
        default: () => ({})
    },
    // content 样式
    contentStyle: {
        type: Object,
        default: () => ({})
    },
    // 数据项
    items: {
        type: Array,
        default: () => [],
        validator: (items) => {
            return items.every(item =>
                item.key &&
                (item.label !== undefined) &&
                (item.value !== undefined || item.render)
            )
        }
    }
})
</script>

<style scoped>
.sk-description-list {
    width: 100%;
}

:deep(.ant-descriptions-item-label) {
    font-weight: 500;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
    background-color: #fafafa;
}
</style>