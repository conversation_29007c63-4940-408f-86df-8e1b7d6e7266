<template>
    <a-popconfirm v-bind="$attrs" :class="['sk-popconfirm', className]" @confirm="handleConfirm" @cancel="handleCancel"
        @openChange="handleOpenChange">
        <template #default>
            <slot></slot>
        </template>
        <template v-if="$slots.icon" #icon>
            <slot name="icon"></slot>
        </template>
        <template v-if="$slots.title" #title>
            <slot name="title"></slot>
        </template>
        <template v-if="$slots.description" #description>
            <slot name="description"></slot>
        </template>
    </a-popconfirm>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'SkPopconfirm',
    props: {
        className: {
            type: String,
            default: ''
        }
    },
    emits: ['confirm', 'cancel', 'openChange'],
    setup(props, { emit }) {
        const handleConfirm = (e) => {
            emit('confirm', e)
        }

        const handleCancel = (e) => {
            emit('cancel', e)
        }

        const handleOpenChange = (open) => {
            emit('openChange', open)
        }

        return {
            handleConfirm,
            handleCancel,
            handleOpenChange
        }
    }
})
</script>

<style lang="less" scoped>
.sk-popconfirm {

    // 自定义样式
    :deep(.ant-popover-message) {
        padding: 8px 0;
    }

    :deep(.ant-popover-buttons) {
        margin-top: 8px;
    }
}
</style>