<template>
    <div class="sk-message-wrapper">
        <a-message :class="messageClass" :style="messageStyle" :duration="duration" :maxCount="maxCount" :top="top"
            :getContainer="getContainer">
            <template v-if="$slots.content" #content="slotProps">
                <slot name="content" v-bind="slotProps"></slot>
            </template>
            <template v-if="$slots.icon" #icon="slotProps">
                <slot name="icon" v-bind="slotProps"></slot>
            </template>
        </a-message>
    </div>
</template>

<script>
import { message } from 'ant-design-vue'

// 创建一个包含静态方法的对象
const SkMessage = {
    success(content, duration, onClose) {
        return message.success(content, duration, onClose)
    },
    warning(content, duration, onClose) {
        return message.warning(content, duration, onClose)
    },
    info(content, duration, onClose) {
        return message.info(content, duration, onClose)
    },
    error(content, duration, onClose) {
        return message.error(content, duration, onClose)
    },
    loading(content, duration, onClose) {
        return message.loading(content, duration, onClose)
    },
    // 全局配置
    config(options) {
        // 确保配置生效
        const config = {
            maxCount: options.maxCount,
            top: options.top ? `${options.top}px` : '24px',
            duration: options.duration || 3,
            rtl: options.rtl || false,
            getContainer: options.getContainer || (() => document.body)
        }
        return message.config(config)
    },
    // 销毁所有消息
    destroy() {
        message.destroy()
    }
}

export default {
    name: 'SkMessage',
    props: {
        // 消息类名
        messageClass: {
            type: String,
            default: ''
        },
        // 消息样式
        messageStyle: {
            type: Object,
            default: () => ({})
        },
        // 自动关闭的延时，单位秒。设为 0 时不自动关闭
        duration: {
            type: Number,
            default: 3
        },
        // 最大显示数, 超过限制时，最早的消息会被自动关闭
        maxCount: {
            type: Number,
            default: 3
        },
        // 消息距离顶部的位置
        top: {
            type: [Number, String],
            default: 24
        },
        // 配置渲染节点的输出位置
        getContainer: {
            type: Function,
            default: () => document.body
        }
    }
}

// 导出静态方法
export { SkMessage }
</script>

<style scoped></style>