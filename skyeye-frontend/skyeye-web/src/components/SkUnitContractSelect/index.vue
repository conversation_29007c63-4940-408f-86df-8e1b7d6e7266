<template>
    <div class="contract-select">
        <SkSelect v-if="isEdit == $config.formEditType.isEdit" v-model="selectedValue" :loading="loading"
            :options="contractOptions" :placeholder="placeholder" @change="handleChange" />
        <div v-else>
            {{ showValue?.name }}
        </div>
    </div>
</template>

<script setup>
// 供应商/客户的合同选择
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import SkSelect from '@/components/SkSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: undefined
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    // 占位符
    placeholder: {
        type: String,
        default: '请选择合同'
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 单位ID
    businessId: {
        type: String,
        default: undefined
    },
    // 单位类型 (供应商/客户)
    businessType: {
        type: String,
        default: undefined
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 选中值
const selectedValue = ref(props.modelValue)
// 显示值
const showValue = ref({})
// 合同选项
const contractOptions = ref([])
// 加载状态
const loading = ref(false)

// 监听选中值变化
watch(() => props.modelValue, (newVal) => {
    selectedValue.value = newVal
})

// 监听内部值变化
watch(selectedValue, (value) => {
    emit('update:modelValue', value)
})

// 选择变化处理
const handleChange = (value, option) => {
    selectedValue.value = value
    emit('change', value, option)
}

// 获取合同列表
const getContractList = async () => {
    if (!props.businessId) return

    loading.value = true
    try {
        let url = ''
        const params = {
            objectId: props.businessId
        }

        // 根据类型选择不同的接口
        if (props.businessType === proxy.$config.sysServiceMation.supplierServiceImpl.key) {
            url = proxy.$config.getConfig().erpBasePath + 'mysuppliercontract008'
        } else if (props.businessType === proxy.$config.sysServiceMation.crmCustomer.key) {
            url = proxy.$config.getConfig().crmBasePath + 'mycrmcontract008'
        }

        const res = await proxy.$http.get(url, params)

        contractOptions.value = (res.rows || []).map(item => ({
            label: item.name,
            value: item.id,
            data: item
        }))
    } catch (error) {
        console.error('获取合同列表失败:', error)
        contractOptions.value = []
    } finally {
        loading.value = false
    }
}

// 监听单位ID和类型变化
watch([() => props.businessId, () => props.businessType], () => {
    if (props.isEdit == $config.formEditType.isEdit) {
        getContractList()
    }
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
    if (props.isEdit != $config.formEditType.isEdit) {
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation
    }
})
</script>

<style lang="less" scoped>
.contract-select {
    width: 100%;
}
</style>