<template>
    <div class="sk-dropdown">
        <a-dropdown v-bind="dropdownProps">
            <!-- 触发元素 -->
            <slot></slot>

            <!-- 下拉菜单 -->
            <template #overlay>
                <slot v-if="$slots.overlay" name="overlay"></slot>
                <a-menu v-else @click="handleMenuClick">
                    <template v-for="item in normalizedItems" :key="item.key">
                        <!-- 分割线 -->
                        <a-menu-divider v-if="item.type === 'divider'" />
                        <!-- 子菜单 -->
                        <a-sub-menu v-else-if="item.children" :key="item.key" :title="item.label">
                            <template v-if="item.icon" #icon>
                                <component :is="item.icon" />
                            </template>
                            <a-menu-item v-for="child in item.children" :key="child.key" :disabled="child.disabled">
                                <template v-if="child.icon" #icon>
                                    <component :is="child.icon" />
                                </template>
                                {{ child.label }}
                            </a-menu-item>
                        </a-sub-menu>
                        <!-- 普通菜单项 -->
                        <a-menu-item v-else :key="item.key" :disabled="item.disabled">
                            <template v-if="item.icon" #icon>
                                <component :is="item.icon" />
                            </template>
                            {{ item.label }}
                        </a-menu-item>
                    </template>
                </a-menu>
            </template>
        </a-dropdown>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 菜单项配置
    items: {
        type: Array,
        default: () => []
    },
    // 触发方式
    trigger: {
        type: [String, Array],
        default: 'hover'
    },
    // 菜单弹出位置
    placement: {
        type: String,
        default: 'bottomLeft',
        validator: (value) => [
            'topLeft', 'topCenter', 'topRight',
            'bottomLeft', 'bottomCenter', 'bottomRight'
        ].includes(value)
    },
    // 是否显示箭头
    arrow: {
        type: Boolean,
        default: false
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 菜单显示状态改变的回调
    onOpenChange: {
        type: Function,
        default: undefined
    },
    // 点击菜单项的回调
    onClick: {
        type: Function,
        default: undefined
    }
})

const emit = defineEmits(['openChange', 'select'])

// 处理菜单项，确保每个项都有必要的属性
const normalizedItems = computed(() => {
    const normalizeItem = (item) => {
        if (item.type === 'divider') {
            return { type: 'divider', key: `divider-${Math.random()}` }
        }

        const normalized = {
            key: item.key || `item-${Math.random()}`,
            label: item.label || '',
            icon: item.icon,
            disabled: item.disabled || false,
            onClick: item.onClick
        }

        if (item.children) {
            normalized.children = item.children.map(child => normalizeItem(child))
        }

        return normalized
    }

    return props.items.map(normalizeItem)
})

// 下拉菜单属性
const dropdownProps = computed(() => ({
    trigger: props.trigger,
    placement: props.placement,
    arrow: props.arrow,
    disabled: props.disabled,
    onOpenChange: (open) => {
        emit('openChange', open)
        props.onOpenChange?.(open)
    }
}))

// 处理菜单点击
const handleMenuClick = ({ key, keyPath, item, domEvent }) => {
    // 查找对应的菜单项
    const findItem = (items, targetKey) => {
        for (const item of items) {
            if (item.key === targetKey) return item
            if (item.children) {
                const found = findItem(item.children, targetKey)
                if (found) return found
            }
        }
        return null
    }

    const menuItem = findItem(normalizedItems.value, key)
    if (menuItem) {
        // 执行菜单项自身的点击回调
        menuItem.onClick?.(menuItem)
        // 执行组件的点击回调
        props.onClick?.(menuItem)
        // 触发选择事件
        emit('select', { key, keyPath, item: menuItem, domEvent })
    }
}
</script>

<style scoped>
.sk-dropdown {
    display: inline-block;
    position: relative;
}

:deep(.ant-dropdown-menu) {
    padding: 4px 0;
    min-width: 120px;
}

:deep(.ant-dropdown-menu-item),
:deep(.ant-dropdown-menu-submenu-title) {
    padding: 5px 12px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
}

:deep(.ant-dropdown-menu-item:hover),
:deep(.ant-dropdown-menu-submenu-title:hover) {
    background-color: rgba(0, 0, 0, 0.04);
}

:deep(.ant-dropdown-menu-item-disabled),
:deep(.ant-dropdown-menu-submenu-title-disabled) {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
}

:deep(.ant-dropdown-menu-item-disabled:hover),
:deep(.ant-dropdown-menu-submenu-title-disabled:hover) {
    background-color: transparent;
}

:deep(.ant-dropdown-menu-item .anticon),
:deep(.ant-dropdown-menu-submenu-title .anticon) {
    margin-right: 8px;
}

:deep(.ant-dropdown-menu-item-divider) {
    margin: 4px 0;
}

:deep(.ant-dropdown-menu-submenu-popup) {
    position: absolute;
    z-index: 1050;
}

:deep(.ant-dropdown-menu-submenu > .ant-dropdown-menu) {
    border-radius: 4px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 9px 28px 8px rgba(0, 0, 0, 0.05);
}
</style>