<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 提示信息 -->
            <SkAlert message="账套选择规则：点击选中账套后，点击确定按钮进行选择。" type="info" show-icon />

            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入名称" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :tipInfoHeight="56" :data-source="tableData" :loading="loading"
                :pagination="pagination" :ready="tableReady" @change="handleTableChange" :row-selection="{
                    type: 'radio',
                    selectedRowKeys: [selectedRowKey],
                    onChange: handleSelectionChange,
                    getCheckboxProps: (record) => ({
                        disabled: !record.haveAccess
                    })
                }" :row-key="record => record.id">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'haveAccess'">
                        <span :class="record.haveAccess ? 'text-success' : 'text-danger'">
                            {{ record.haveAccess ? '可用' : '不可用' }}
                        </span>
                    </template>
                </template>
            </SkTable>

            <!-- 底部按钮 -->
            <SkSpace>
                <SkButton @click.prevent="handleCancel">{{ $t('common.cancel') }}</SkButton>
                <SkButton type="primary" @click.prevent="handleConfirm" :disabled="!selectedRowKey">
                    {{ $t('common.confirm') }}
                </SkButton>
            </SkSpace>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

const emit = defineEmits(['confirm', 'cancel'])

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据相关
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())
const selectedRowKey = ref('')

// 表格列配置
const columns = [
    {
        title: '序号',
        type: 'index',
        width: 80,
        align: 'center'
    },
    {
        title: '名称',
        dataIndex: 'name',
        width: 200,
    },
    {
        title: '开始日期',
        dataIndex: 'startTime',
        width: 100,
        align: 'center'
    },
    {
        title: '截至日期',
        dataIndex: 'endTime',
        width: 100,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'haveAccess',
        width: 80,
        align: 'center'
    },
    {
        title: '备注',
        dataIndex: 'remark',
        width: 200,
    }
]

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'ifssetofbooks001',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 重置处理
const handleReset = () => {
    searchForm.keyword = ''
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 处理单选框变化
const handleSelectionChange = (selectedKeys) => {
    selectedRowKey.value = selectedKeys[0] || ''
}

// 处理确认
const handleConfirm = () => {
    const selectedRecord = tableData.value.find(item => item.id === selectedRowKey.value)
    
    // 验证选中的记录是否有效
    if (!selectedRecord) {
        SkMessage.warning('请选择一个账套')
        return
    }
    
    // 验证选中的记录是否包含 id
    if (!selectedRecord.id) {
        SkMessage.error('所选账套数据不完整，请重新选择')
        return
    }
    
    // 验证账套是否可用
    if (selectedRecord.haveAccess === false) {
        SkMessage.warning('所选账套不可用，请选择其他账套')
        return
    }
    
    console.log('确认选择账套:', selectedRecord)
    emit('confirm', selectedRecord)
}

// 处理取消
const handleCancel = () => {
    emit('cancel')
}

// 初始化
onMounted(async () => {
    nextTick(async () => {
        tableReady.value = true
        await fetchData()
    })

})
</script>

<style scoped></style>
