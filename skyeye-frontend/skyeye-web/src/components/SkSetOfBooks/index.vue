<template>
    <div>
        <!-- 只读模式 -->
        <div v-if="isEdit === $config.formEditType.notEdit && !$util.isNull(modelValue)">
            <div>{{ showValue?.name }}</div>
        </div>
        <!-- 编辑模式 -->
        <div v-else-if="isEdit === $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>

        <!-- 选择弹窗 -->
        <SkModal v-model="modalVisible" title="账套选择" @cancel="handleModalCancel">
            <IfsSetOfBooksListChoose v-if="modalVisible" @confirm="handleSetOfBooksConfirm" @cancel="handleModalCancel" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watch, nextTick } from 'vue'
import SkInput from '@/components/SkInput/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import IfsSetOfBooksListChoose from './ifsSetOfBooksListChoose.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { PlusOutlined } from '@ant-design/icons-vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 占位符
    placeholder: {
        type: String,
        default: '请选择账套'
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})

// 弹窗显示控制
const modalVisible = ref(false)

// 打开选择弹窗
const showModal = () => {
    modalVisible.value = true
}

// 处理账套选择确认
const handleSetOfBooksConfirm = (selectedRecord) => {
    if (!selectedRecord || !selectedRecord.id) {
        SkMessage.warning('所选账套数据不完整')
        return
    }
    
    // 更新显示值
    showValue.value = selectedRecord
    
    // 更新表单数据
    const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
    props.formData[mationKey] = selectedRecord
    
    // 直接设置 setOfBooksId
    const setOfBooksId = selectedRecord.id
    props.formData.setOfBooksId = setOfBooksId
    console.log('设置 setOfBooksId:', setOfBooksId)
    
    // 创建一个对象，键名为 setOfBooksId
    const result = {}
    result.setOfBooksId = setOfBooksId
    
    // 更新 modelValue 并触发 change 事件
    emit('update:modelValue', setOfBooksId)
    emit('change', result)
    
    modalVisible.value = false
}

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    if (props.pageType == proxy.$config.pageType.EDIT) {
        // 详情页面和编辑页面都会走这里
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation || {}
        console.log('onMounted showValue:', showValue.value)
        
        if (showValue.value.id) {
            // 确保 setOfBooksId 被设置
            const setOfBooksId = showValue.value.id
            props.formData.setOfBooksId = setOfBooksId
            console.log('onMounted 设置 setOfBooksId:', setOfBooksId)
            
            // 创建一个对象，键名为 setOfBooksId
            const result = {}
            result.setOfBooksId = setOfBooksId
            
            // 更新 modelValue
            emit('update:modelValue', setOfBooksId)
            emit('change', result)
        }
    } else if (!proxy.$util.isNull(props.modelValue)) {
        // 如果有初始值但不是编辑模式
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        showValue.value = props.formData[mationKey] || {}
        
        if (showValue.value.id && !props.formData.setOfBooksId) {
            const setOfBooksId = showValue.value.id
            props.formData.setOfBooksId = setOfBooksId
            console.log('onMounted (非编辑模式) 设置 setOfBooksId:', setOfBooksId)
            
            // 创建一个对象，键名为 setOfBooksId
            const result = {}
            result.setOfBooksId = setOfBooksId
            
            emit('change', result)
        }
    }
})

// 监听modelValue变化
watch(() => props.modelValue, async(newVal) => {
    if (!proxy.$util.isNull(newVal)) {
        await nextTick()
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        showValue.value = props.formData[mationKey] || {}
        
        // 确保 setOfBooksId 被设置
        if (showValue.value.id && !props.formData.setOfBooksId) {
            const setOfBooksId = showValue.value.id
            props.formData.setOfBooksId = setOfBooksId
            console.log('watch modelValue 设置 setOfBooksId:', setOfBooksId)
            
            // 创建一个对象，键名为 setOfBooksId
            const result = {}
            result.setOfBooksId = setOfBooksId
            
            emit('change', result)
        }
    } else {
        showValue.value = {
            id: '',
            name: ''
        }
    }
}, { immediate: true })

// 获取账套值对象
const getSetOfBooksValue = () => {
    const result = {}
    if (showValue.value && showValue.value.id) {
        result.setOfBooksId = showValue.value.id
    }
    return result
}

// 暴露方法给父组件
defineExpose({
    getSetOfBooksValue
})
</script>

<style scoped>
.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}
</style>
