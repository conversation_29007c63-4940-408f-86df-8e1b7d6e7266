export default {
    type: 'number',
    icon: 'NumberOutlined',
    props: {
        label: '',
        min: 0,
        max: 100,
        step: 1,
        required: false,
        attrKey: ''
    },
    propsConfig: {
        label: {
            type: 'string',
            label: '标签文本'
        },
        min: {
            type: 'number',
            label: '最小值'
        },
        max: {
            type: 'number',
            label: '最大值'
        },
        step: {
            type: 'number',
            label: '步长',
            min: 0.01
        },
        required: {
            type: 'boolean',
            label: '是否必填'
        },
        attrKey: {
            type: 'select',
            label: '绑定属性',
            options: [],
            config: {
                labelField: 'name',
                valueField: 'attrKey'
            }
        }
    }
} 