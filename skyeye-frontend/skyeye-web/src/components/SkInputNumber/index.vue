<template>
    <div class="sk-input-number">
        <a-input-number v-if="isEdit === $config.formEditType.isEdit" v-model:value="inputValue"
            :placeholder="placeholder" :disabled="disabled" :min="min" :max="max" :step="step" :precision="precision"
            :size="size" :controls="controls" :keyboard="keyboard" :stringMode="stringMode" :addonBefore="addonBefore"
            :addonAfter="addonAfter" :prefix="prefix" :formatter="formatter" :parser="parser" @change="handleChange"
            @focus="handleFocus" @blur="handleBlur" />
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="inputValue !== null && inputValue !== undefined">
                {{ formatReadOnlyValue }}
            </template>
        </div>
    </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';

export default defineComponent({
    name: 'SkInputNumber',
    props: {
        modelValue: {
            type: [Number, String],
            default: null
        },
        placeholder: {
            type: String,
            default: '请输入'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        min: {
            type: Number,
            default: undefined
        },
        max: {
            type: Number,
            default: undefined
        },
        step: {
            type: [Number, String],
            default: 1
        },
        precision: {
            type: Number,
            default: undefined
        },
        size: {
            type: String,
            default: 'middle',
            validator: (value) => ['large', 'middle', 'small'].includes(value)
        },
        controls: {
            type: Boolean,
            default: true
        },
        keyboard: {
            type: Boolean,
            default: true
        },
        stringMode: {
            type: Boolean,
            default: false
        },
        addonBefore: {
            type: String,
            default: ''
        },
        addonAfter: {
            type: String,
            default: ''
        },
        prefix: {
            type: String,
            default: ''
        },
        formatter: {
            type: Function,
            default: undefined
        },
        parser: {
            type: Function,
            default: undefined
        },
        isEdit: {
            type: Number,
            default: $config.formEditType.isEdit
        }
    },
    emits: ['update:modelValue', 'change', 'focus', 'blur'],
    setup(props, { emit }) {
        const inputValue = ref(props.modelValue);

        // 监听外部值变化
        watch(() => props.modelValue, (newVal) => {
            inputValue.value = newVal;
        });

        // 格式化只读模式下的显示值
        const formatReadOnlyValue = computed(() => {
            if (props.formatter) {
                return props.formatter(inputValue.value);
            }
            return inputValue.value;
        });

        // 处理值变化
        const handleChange = (value) => {
            inputValue.value = value;
            emit('update:modelValue', value);
            emit('change', value);
        };

        // 处理获得焦点
        const handleFocus = (e) => {
            emit('focus', e);
        };

        // 处理失去焦点
        const handleBlur = (e) => {
            emit('blur', e);
        };

        return {
            inputValue,
            formatReadOnlyValue,
            handleChange,
            handleFocus,
            handleBlur
        };
    }
});
</script>

<style lang="less" scoped>
.sk-input-number {
    width: 100%;

    :deep(.ant-input-number-group-wrapper) {
        width: 100%;
    }

}

.sk-detail-readonly {
    min-height: 32px;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.88);
}

:deep(.ant-input-number) {
    width: 100%;
}
</style>