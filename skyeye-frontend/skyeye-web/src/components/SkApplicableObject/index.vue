<template>
    <div class="sk-applicable-object">
        <!-- 只读模式 -->
        <div v-if="isEdit === $config.formEditType.notEdit">
            <div class="sk-detail-readonly">
                <template v-if="selectedObjects.length">
                    <a-row>
                        <a-col :span="24" class="input-item mb-8"
                            v-if="getFilteredObjects(OBJECT_TYPES.ENTERPRISE).length">
                            <div class="input-label mb-8">适用企业</div>
                            <div class="tags-container readonly-tags mb-16">
                                <SkTag v-for="item in getFilteredObjects(OBJECT_TYPES.ENTERPRISE)" :key="item.objectId"
                                    >
                                    {{ item.name }}
                                </SkTag>
                            </div>
                        </a-col>
                        <a-col :span="24" class="input-item mb-8"
                            v-if="getFilteredObjects(OBJECT_TYPES.DEPARTMENT).length">
                            <div class="input-label mb-8">适用部门</div>
                            <div class="tags-container readonly-tags mb-16">
                                <SkTag v-for="item in getFilteredObjects(OBJECT_TYPES.DEPARTMENT)" :key="item.objectId"
                                    >
                                    {{ item.name }}
                                </SkTag>
                            </div>
                        </a-col>
                        <a-col :span="24" class="input-item mb-8"
                            v-if="getFilteredObjects(OBJECT_TYPES.EMPLOYEE).length">
                            <div class="input-label mb-8">适用员工</div>
                            <div class="tags-container readonly-tags mb-16">
                                <SkTag v-for="item in getFilteredObjects(OBJECT_TYPES.EMPLOYEE)" :key="item.objectId"
                                    >
                                    {{ item.name }}
                                </SkTag>
                            </div>
                        </a-col>
                    </a-row>
                </template>
                <template v-else>
                    无
                </template>
            </div>
        </div>
        <!-- 编辑模式 -->
        <div v-else>
            <!-- 选择输入框 -->
            <div class="selection-inputs mt-8">
                <a-row>
                    <a-col :span="24" class="input-item mb-8">
                        <div class="input-label mb-8">适用企业</div>
                        <SkInput readonly @click="handleSelectEnterprise" class="tag-input"
                            :placeholder="getFilteredObjects(OBJECT_TYPES.ENTERPRISE).length ? '' : '选择企业'">
                            <template v-if="getFilteredObjects(OBJECT_TYPES.ENTERPRISE).length" #prefix>
                                <div class="tags-container">
                                    <SkTag v-for="item in getFilteredObjects(OBJECT_TYPES.ENTERPRISE)"
                                        :key="item.objectId" :closable="true" @close="handleRemove(item)">
                                        {{ item.name }}
                                    </SkTag>
                                </div>
                            </template>
                            <template #suffix>
                                <PlusOutlined class="add-icon" />
                            </template>
                        </SkInput>
                    </a-col>
                    <a-col :span="24" class="input-item mb-8">
                        <div class="input-label mb-8">适用部门</div>
                        <SkInput readonly @click="handleSelectDepartment" class="tag-input"
                            :placeholder="getFilteredObjects(OBJECT_TYPES.DEPARTMENT).length ? '' : '选择部门'">
                            <template v-if="getFilteredObjects(OBJECT_TYPES.DEPARTMENT).length" #prefix>
                                <div class="tags-container">
                                    <SkTag v-for="item in getFilteredObjects(OBJECT_TYPES.DEPARTMENT)"
                                        :key="item.objectId" :closable="true" @close="handleRemove(item)">
                                        {{ item.name }}
                                    </SkTag>
                                </div>
                            </template>
                            <template #suffix>
                                <PlusOutlined class="add-icon" />
                            </template>
                        </SkInput>
                    </a-col>
                    <a-col :span="24" class="input-item mb-8">
                        <div class="input-label mb-8">适用员工</div>
                        <SkInput readonly @click="handleSelectEmployee" class="tag-input"
                            :placeholder="getFilteredObjects(OBJECT_TYPES.EMPLOYEE).length ? '' : '选择员工'">
                            <template v-if="getFilteredObjects(OBJECT_TYPES.EMPLOYEE).length" #prefix>
                                <div class="tags-container">
                                    <SkTag v-for="item in getFilteredObjects(OBJECT_TYPES.EMPLOYEE)"
                                        :key="item.objectId" :closable="true" @close="handleRemove(item)">
                                        {{ item.name }}
                                    </SkTag>
                                </div>
                            </template>
                            <template #suffix>
                                <PlusOutlined class="add-icon" />
                            </template>
                        </SkInput>
                    </a-col>
                </a-row>
            </div>
        </div>

        <!-- 企业选择弹窗 -->
        <SkModal v-model="enterpriseModalVisible" title="选择企业" @cancel="handleModalCancel">
            <SkEnterpriseSelect v-if="enterpriseModalVisible" :multiple="true" @change="handleEnterpriseSelect"
                @cancel="handleModalCancel" />
        </SkModal>

        <!-- 部门选择弹窗 -->
        <SkModal v-model="departmentModalVisible" title="选择部门" @cancel="handleModalCancel">
            <SkDepartmentTableSelect v-if="departmentModalVisible" :multiple="true" @change="handleDepartmentSelect"
                @cancel="handleModalCancel" />
        </SkModal>

        <!-- 员工选择弹窗 -->
        <SkModal v-model="employeeModalVisible" title="选择员工" @cancel="handleModalCancel">
            <SkUserStaffSelect v-if="employeeModalVisible" :multiple="true" @change="handleEmployeeSelect"
                @cancel="handleModalCancel" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch, getCurrentInstance, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkEnterpriseSelect from '@/components/SkEnterpriseSelect/index.vue'
import SkUserStaffSelect from '@/components/SkUserStaffSelect/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkDepartmentTableSelect from '@/components/SkDepartmentTableSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Array, String],
        default: () => []
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 对象类型常量
const OBJECT_TYPES = {
    ENTERPRISE: 'enterprise',
    DEPARTMENT: 'department',
    EMPLOYEE: 'employee'
}

// 已选择的对象
const selectedObjects = ref([])

// 弹窗控制
const enterpriseModalVisible = ref(false)
const departmentModalVisible = ref(false)
const employeeModalVisible = ref(false)

// 处理选择企业
const handleSelectEnterprise = () => {
    enterpriseModalVisible.value = true
}

// 处理选择部门
const handleSelectDepartment = () => {
    departmentModalVisible.value = true
}

// 处理选择员工
const handleSelectEmployee = () => {
    employeeModalVisible.value = true
}

// 处理企业选择结果
const handleEnterpriseSelect = (enterprises) => {
    if (enterprises && enterprises.length) {
        try {
            // 转换为统一格式并添加到已选择列表
            const newObjects = enterprises.map(enterprise => {
                // 确保企业对象有效
                if (!enterprise) return null;

                return {
                    id: enterprise.id || '',
                    objectId: enterprise.id || '',
                    objectKey: enterprise.serviceClassName || 'companyMation', // 提供默认值
                    objectType: OBJECT_TYPES.ENTERPRISE, // 在内部使用字符串，在提交时转换为数字
                    name: enterprise.name || '未命名企业',
                    originalData: enterprise
                };
            }).filter(Boolean); // 过滤掉无效值

            // 过滤掉已存在的企业
            const filteredObjects = newObjects.filter(newObj =>
                !selectedObjects.value.some(existingObj =>
                    existingObj.objectId === newObj.objectId &&
                    existingObj.objectType === newObj.objectType
                )
            );

            if (filteredObjects.length) {
                selectedObjects.value = [...selectedObjects.value, ...filteredObjects];
                updateModelValue();
            }
        } catch (error) {
            SkMessage.error('处理企业选择时出错');
        }
    }
    enterpriseModalVisible.value = false;
}

// 处理部门选择结果
const handleDepartmentSelect = (departments) => {
    if (departments && departments.length) {
        try {
            // 转换为统一格式并添加到已选择列表
            const newObjects = departments.map(department => {
                // 确保部门对象有效
                if (!department) return null;

                return {
                    id: department.id || '',
                    objectId: department.id || '',
                    objectKey: department.serviceClassName || 'companyDepartment', // 提供默认值
                    objectType: OBJECT_TYPES.DEPARTMENT, // 在内部使用字符串，在提交时转换为数字
                    name: department.name || '未命名部门',
                    originalData: department
                };
            }).filter(Boolean); // 过滤掉无效值

            // 过滤掉已存在的部门
            const filteredObjects = newObjects.filter(newObj =>
                !selectedObjects.value.some(existingObj =>
                    existingObj.objectId === newObj.objectId &&
                    existingObj.objectType === newObj.objectType
                )
            );

            if (filteredObjects.length) {
                selectedObjects.value = [...selectedObjects.value, ...filteredObjects];
                updateModelValue();
            }
        } catch (error) {
            SkMessage.error('处理部门选择时出错');
        }
    }
    departmentModalVisible.value = false;
}

// 处理员工选择结果
const handleEmployeeSelect = (employees) => {
    if (employees && employees.length) {
        try {
            // 转换为统一格式并添加到已选择列表
            const newObjects = employees.map(employee => {
                // 确保员工对象有效
                if (!employee) return null;

                return {
                    id: employee.id || '',
                    objectId: employee.id || '',
                    objectKey: employee.serviceClassName || 'com.skyeye.personnel.service.impl.SysEveUserStaffServiceImpl', // 提供默认值
                    objectType: OBJECT_TYPES.EMPLOYEE, // 在内部使用字符串，在提交时转换为数字
                    name: employee.userName || employee.name || '未命名员工',
                    originalData: employee
                };
            }).filter(Boolean); // 过滤掉无效值

            // 过滤掉已存在的员工
            const filteredObjects = newObjects.filter(newObj =>
                !selectedObjects.value.some(existingObj =>
                    existingObj.objectId === newObj.objectId &&
                    existingObj.objectType === newObj.objectType
                )
            );

            if (filteredObjects.length) {
                selectedObjects.value = [...selectedObjects.value, ...filteredObjects];
                updateModelValue();
            }
        } catch (error) {
            SkMessage.error('处理员工选择时出错');
        }
    }
    employeeModalVisible.value = false;
}

// 处理移除对象
const handleRemove = (record) => {
    selectedObjects.value = selectedObjects.value.filter(item =>
        !(item.objectId === record.objectId && item.objectType === record.objectType)
    )
    updateModelValue()
}

// 更新modelValue
const updateModelValue = () => {
    // 在发送前转换 objectType 从字符串到数字
    const convertedObjects = selectedObjects.value.map(obj => {
        // 创建对象的副本，避免修改原始对象
        const convertedObj = { ...obj };

        // 将 objectType 从字符串转换为数字，与 layui 版本保持一致
        switch (convertedObj.objectType) {
            case OBJECT_TYPES.ENTERPRISE:
                convertedObj.objectType = 3; // 企业对应 3
                break;
            case OBJECT_TYPES.DEPARTMENT:
                convertedObj.objectType = 2; // 部门对应 2
                break;
            case OBJECT_TYPES.EMPLOYEE:
                convertedObj.objectType = 1; // 员工对应 1
                break;
            default:
                // 保持原样
                break;
        }

        return convertedObj;
    });

    emit('update:modelValue', JSON.stringify(convertedObjects))
    emit('change', convertedObjects)
}

// 处理弹窗取消
const handleModalCancel = () => {
    enterpriseModalVisible.value = false
    departmentModalVisible.value = false
    employeeModalVisible.value = false
}

// 处理API返回的数据
const processApiData = (data) => {
    if (!data || !Array.isArray(data)) {
        return [];
    }

    return data.map(obj => {
        const convertedObj = { ...obj };

        // 处理 objectMation 字段，与 layui 版本保持一致
        if (convertedObj.objectMation) {
            // 如果有 objectMation 字段，使用其中的 name
            if (convertedObj.objectMation.name) {
                convertedObj.name = convertedObj.objectMation.name;
            }
        }
        // 确保有 name 字段
        else if (!convertedObj.name && convertedObj.objectName) {
            convertedObj.name = convertedObj.objectName;
        }

        // 将 objectType 从数字转换为字符串，与 layui 版本保持一致
        if (typeof convertedObj.objectType === 'number') {
            switch (convertedObj.objectType) {
                case 3: // 企业对应 3
                    convertedObj.objectType = OBJECT_TYPES.ENTERPRISE;
                    break;
                case 2: // 部门对应 2
                    convertedObj.objectType = OBJECT_TYPES.DEPARTMENT;
                    break;
                case 1: // 员工对应 1
                    convertedObj.objectType = OBJECT_TYPES.EMPLOYEE;
                    break;
                default:
                    // 保持原样
                    break;
            }
        } else if (convertedObj.objectType === undefined) {
            // 如果没有 objectType 字段，尝试根据其他字段推断
            if (convertedObj.objectKey === 'companyMation') {
                convertedObj.objectType = OBJECT_TYPES.ENTERPRISE;
            } else if (convertedObj.objectKey === 'companyDepartment') {
                convertedObj.objectType = OBJECT_TYPES.DEPARTMENT;
            } else if (convertedObj.objectKey === 'com.skyeye.personnel.service.impl.SysEveUserStaffServiceImpl') {
                convertedObj.objectType = OBJECT_TYPES.EMPLOYEE;
            }
        }

        return convertedObj;
    });
}

// 初始化
onMounted(() => {
    // 如果有初始值，加载初始值
    if (props.attrKey && props.formData) {
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)

        // 尝试从 formData 中获取数据
        let initialData = props.formData[mationKey];

        // 如果没有找到数据，尝试从 applicableObjectsList 字段获取
        if (!initialData && props.formData.applicableObjectsList) {
            initialData = props.formData.applicableObjectsList;
        }

        if (initialData && Array.isArray(initialData)) {
            selectedObjects.value = processApiData(initialData);
        }
    }
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        let parsedData;
        if (Array.isArray(newVal)) {
            parsedData = newVal;
        } else {
            try {
                parsedData = JSON.parse(newVal);
            } catch (error) {
                parsedData = [];
            }
        }

        selectedObjects.value = processApiData(parsedData);
    } else if (props.formData && props.formData.applicableObjectsList) {
        // 如果 modelValue 为空但 formData 中有 applicableObjectsList，使用它
        selectedObjects.value = processApiData(props.formData.applicableObjectsList);
    } else {
        selectedObjects.value = [];
    }
}, { deep: true, immediate: true })

// 监听 formData 变化
watch(() => props.formData, (newVal) => {
    if (newVal && newVal.applicableObjectsList && Array.isArray(newVal.applicableObjectsList)) {
        selectedObjects.value = processApiData(newVal.applicableObjectsList);
    }
}, { deep: true })

// 获取过滤后的对象
const getFilteredObjects = (type) => {
    return selectedObjects.value.filter(item => item.objectType === type)
}
</script>

<style scoped>
.sk-applicable-object {
    width: 100%;
}

.input-item:last-child {
    margin-bottom: 0;
}

.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}

.tag-input {
    width: 100%;
    height: auto;
    min-height: 32px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    max-width: calc(100% - 30px);
    padding: 2px 0;
    overflow: visible;
}

:deep(.ant-tag) {
    margin: 2px 4px 2px 0;
    flex-shrink: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 150px;
}

:deep(.ant-input-affix-wrapper) {
    padding: 4px 8px;
    height: auto;
    min-height: 32px;
    overflow: visible;
}

:deep(.ant-input-suffix) {
    position: sticky;
    right: 8px;
    background-color: #fff;
    margin-left: 4px;
}

:deep(.ant-input) {
    padding-left: 8px !important;
}

:deep(.ant-input-affix-wrapper .ant-input-prefix) {
    margin-right: 0;
    max-width: calc(100% - 30px);
    overflow: visible;
    height: auto;
}

:deep(.ant-input-disabled) {
    background-color: #f5f5f5;
}

:deep(.ant-input-disabled) .ant-input-suffix {
    background-color: #f5f5f5;
}

:deep(.ant-input-disabled + .ant-input-suffix .add-icon) {
    color: #d9d9d9;
    cursor: not-allowed;
}

.sk-detail-readonly {
    width: 100%;
}

.readonly-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-width: 100%;
    overflow: visible;
}

.readonly-tags :deep(.ant-tag) {
    margin-right: 0;
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
    white-space: normal;
    height: auto;
    line-height: 1.5;
    padding: 4px 8px;
}
</style>
