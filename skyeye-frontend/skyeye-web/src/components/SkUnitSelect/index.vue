<template>
    <div class="unit-select-container">
        <div class="unit-type-switch">
            <SkRadio v-model="unitType" :options="dataList" :isEdit="isEdit"></SkRadio>
        </div>

        <div class="unit-select">
            <SkCustomerSelect v-if="unitType === 'customer'" v-model="selectedValue" :isEdit="isEdit"
                :pageType="pageType" :attrKey="attrKey" :formData="formData" @change="handleCustomerChange" />
            <SkSupplierSelect v-else v-model="selectedValue" :isEdit="isEdit" :pageType="pageType" :attrKey="attrKey"
                :formData="formData" @change="handleSupplierChange" />
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import SkCustomerSelect from '@/components/SkCustomerSelect/index.vue'
import SkSupplierSelect from '@/components/SkSupplierSelect/index.vue'
import SkRadio from '@/components/SkRadio/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Number, Object],
        default: undefined
    },
    pageType: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    attrKey: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const dataList = ref([{
    value: 'customer',
    label: '客户'
}, {
    value: 'supplier',
    label: '供应商'
}])

// 当前选择的单位类型
const unitType = ref('customer')

// 选中的值
const selectedValue = ref(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    selectedValue.value = newVal
})

// 监听单位类型变化
watch(unitType, () => {
    // 切换类型时清空选择的值
    selectedValue.value = undefined
})

// 处理客户选择变化
const handleCustomerChange = (value) => {
    handleChange(value.id, value?.serviceClassName || proxy.$config.sysServiceMation.crmCustomer.key)
}

// 处理供应商选择变化
const handleSupplierChange = (value) => {
    handleChange(value.id, value?.serviceClassName || proxy.$config.sysServiceMation.supplierServiceImpl.key)
}

const handleChange = (holderId, holderKey) => {
    emit('update:modelValue', {
        holderId: holderId,
        holderKey: holderKey
    });
    emit('change', {
        holderId: holderId,
        holderKey: holderKey
    }, props.attrKey);
}

onMounted(() => {
    if (props.pageType == proxy.$config.pageType.EDIT) {
        // 详情页面和编辑页面都会走这里
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        if (mation.id) {
            let serviceClassName = mation.serviceClassName
            const idKey = proxy.$util.getKeyIdToKey(props.attrKey)
            if (proxy.$util.isNull(serviceClassName)) {
                serviceClassName = props.formData[idKey]
            }
            emit('update:modelValue', {
                holderId: mation.id,
                holderKey: serviceClassName
            });
            if (serviceClassName == proxy.$config.sysServiceMation.crmCustomer.key) {
                unitType.value = 'customer'
            } else if (serviceClassName == proxy.$config.sysServiceMation.supplierServiceImpl.key) {
                unitType.value = 'supplier'
            }
        }
    }
})
</script>

<style lang="less" scoped>
.unit-select-container {
    .unit-type-switch {
        margin-bottom: 8px;
    }

    .unit-select {
        width: 100%;
    }
}
</style>