<template>
    <div class="sk-rule-config">
        <a-row>
            <!-- 左侧规则列表 -->
            <a-col :span="6" class="rule-list">
                <div class="rule-item" v-for="item in ruleTypes.filter(t => t.code !== 'code_manage_pipeline_code')"
                    :key="item.code" draggable="true" @dragstart="handleDragStart($event, item)"
                    @dragend="handleDragEnd">
                    <div class="rule-block" :style="{ backgroundColor: item.color }"></div>
                    <span>{{ item.label }}</span>
                </div>
            </a-col>

            <!-- 右侧配置区域 -->
            <a-col :span="18" class="rule-config">
                <div class="config-header">
                    <span>配置</span>
                    <div class="config-actions">
                        <a-button type="primary" @click="showRuleModal = true">规则</a-button>
                        <a-button @click="handleVariableConfig">变量配置</a-button>
                    </div>
                </div>

                <!-- 配置内容区域 -->
                <div class="config-items" @dragover.prevent @drop="handleDrop" @dragenter.prevent>
                    <div v-if="ruleItems.length === 0" class="drop-placeholder">
                        拖拽左侧规则到此处
                    </div>
                    <div v-for="(item, index) in ruleItems" :key="index" class="config-item"
                        :style="{ backgroundColor: ruleTypes.find(t => t.code === item.code)?.color }"
                        :class="{ 'selected': selectedIndex === index }" draggable="true"
                        @dragstart="handleConfigDragStart($event, index)" @dragend="handleConfigDragEnd"
                        @dragover.prevent @drop.stop="handleConfigDrop($event, index)" @dragenter.prevent
                        :data-index="index" @click="handleSelectItem(index)">
                        <div class="config-item-header">
                            <div class="drag-handle">
                                <MenuOutlined />
                            </div>
                            <span>{{ruleTypes.find(t => t.code === item.code)?.label}}</span>
                            <DeleteOutlined v-if="item.code !== 'code_manage_pipeline_code'" class="delete-icon"
                                @click.stop="handleDeleteItem(index)" />
                        </div>
                        <!-- 纯文本配置 -->
                        <template v-if="item.code === 'code_manage_pure_text'">
                            <SkInput v-model="item.value" placeholder="请输入文本内容" :maxLength="10" :showCount="true" />
                        </template>

                        <!-- 日期配置 -->
                        <template v-if="item.code === 'code_manage_date'">
                            <SkSelect v-model="item.value" style="width: 100%" :options="dateFormatOptions">
                            </SkSelect>
                        </template>

                        <!-- 变量配置 -->
                        <template v-if="item.code === 'code_manage_variable'">
                            <div class="variable-config">
                                <SkInput v-model="item.value" placeholder="请输入变量名称"
                                    @change="(e) => validateVariable(e, index)" :maxLength="50" :showCount="true" />
                                <div class="variable-tip" v-if="variableError">
                                    {{ variableError }}
                                </div>
                            </div>
                        </template>

                        <!-- 流水码配置 -->
                        <template v-if="item.code === 'code_manage_pipeline_code'">
                            <div class="sequence-config">
                                <SkInput v-model="item.value" style="width: 100%" placeholder="请输入n，例如：nnn"
                                    @input="(e) => handleSequenceInput(e, index)" :maxLength="10" :showCount="true" />
                                <div class="sequence-preview">
                                    预览格式：{{ item.value || 'nnn' }}
                                </div>
                                <div class="sequence-tip" v-if="sequenceError">
                                    {{ sequenceError }}
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 预览区域 -->
                <div class="preview-area">
                    <div class="preview-header">
                        <span>预览</span>
                    </div>
                    <div class="preview-content">
                        {{ generatePreview }}
                    </div>
                </div>
            </a-col>
        </a-row>

        <!-- 规则弹框 -->
        <SkModal v-model="showRuleModal" title="规则说明" width="800px">
            <a-tabs v-model:activeKey="activeTabKey">
                <a-tab-pane key="code_manage_pure_text" tab="纯文本">
                    <div class="rule-content">
                        <div class="rule-title">规则说明：</div>
                        <div class="rule-desc">任意字符，不包括双引号(")");并且不能有汉字</div>
                        <div class="rule-title">规则示例：</div>
                        <div class="rule-example">
                            <div>有效：abc123, test_123, hello_world</div>
                            <div>无效：你好world, test"123, 测试abc</div>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="code_manage_date" tab="日期">
                    <div class="rule-content">
                        <div class="rule-title">规则说明：</div>
                        <div class="rule-desc">
                            <div>年：yyyy</div>
                            <div>年月：yyyyMM</div>
                            <div>年月日：yyyyMMdd</div>
                            <div>年月日时：yyyyMMddHH</div>
                        </div>
                        <div class="rule-title">规则示例：</div>
                        <div class="rule-example">
                            <div>yyyy → 2024</div>
                            <div>yyyyMM → 202403</div>
                            <div>yyyyMMdd → 20240315</div>
                            <div>yyyyMMddHH → 2024031514</div>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="code_manage_variable" tab="变量">
                    <div class="rule-content">
                        <div class="rule-title">规则说明：</div>
                        <div class="rule-desc">只能是字母、数字或下划线的任意组合，第一个字符不能是数字，不能包含汉字</div>
                        <div class="rule-title">规则示例：</div>
                        <div class="rule-example">
                            <div>有效：abc, test_123, hello_world</div>
                            <div>无效：123abc, test-123, 变量名</div>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="code_manage_pipeline_code" tab="流水码">
                    <div class="rule-content">
                        <div class="rule-title">规则说明：</div>
                        <div class="rule-desc">n: 表示 0-9 的数字</div>
                        <div class="rule-title">规则示例：</div>
                        <div class="rule-example">
                            <div>nnn: 生成 001-999 的流水号</div>
                            <div>nnnn: 生成 0001-9999 的流水号</div>
                        </div>
                    </div>
                </a-tab-pane>
            </a-tabs>
        </SkModal>

        <!-- 变量配置弹框 -->
        <SkModal v-model="showVariableModal" title="变量配置" width="600px" :showOk="true" okText="保存" :showCancel="true"
            cancelText="关闭" @ok="handleVariableConfigSave" @cancel="showVariableModal = false">
            <div class="variable-config-content">
                <div class="variable-select">
                    <span class="label">可选变量：</span>
                    <SkSelect v-model="selectedVariable" style="width: 200px" :options="availableVariables" />
                    <QuestionCircleOutlined class="help-icon" @click="showVariableHelp" />
                </div>
                <div class="mapping-type">
                    <span class="label">映射管理：</span>
                    <a-radio-group v-model:value="mappingType">
                        <a-radio value="map">简单映射</a-radio>
                        <a-radio value="javascript">脚本(javascript)</a-radio>
                    </a-radio-group>
                </div>
                <div class="mapping-content">
                    <template v-if="mappingType === 'map'">
                        <SkTextArea v-model="simpleMapping" placeholder="请输入映射关系，格式：值|映射值" :rows="6" :maxLength="200"
                            :showCount="true" />
                    </template>
                    <template v-if="mappingType === 'javascript'">
                        <SkTextArea v-model="scriptMapping"
                            placeholder="// value不可变 &#10;if(value == '1'){&#10;    return '苹果';&#10;}else if(value == '2'){&#10;    return '梨';&#10;}"
                            :rows="6" :maxLength="200" :showCount="true" />
                    </template>
                </div>
            </div>
        </SkModal>

        <!-- 变量帮助弹框 -->
        <SkModal v-model="showHelpModal" title="帮助说明" width="600px">
            <div class="help-content">
                <div class="help-section">
                    <div class="help-title">场景</div>
                    <div class="help-text">如果调用方的数据为编码，生成编码的时候，想转为对应的中文或者任何对应的值</div>
                </div>
                <div class="help-section">
                    <div class="help-title">简单映射</div>
                    <div class="help-text">
                        1|苹果<br>
                        2|梨
                    </div>
                </div>
                <div class="help-section">
                    <div class="help-title">脚本(javascript)</div>
                    <div class="help-text">
                        // value不可变<br>
                        if(value == '1'){<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;return '苹果';<br>
                        }else if(value == '2'){<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;return '梨';<br>
                        }
                    </div>
                </div>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { MenuOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkTextArea from '@/components/SkTextArea/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    modelValue: {
        type: [Object, String],
        default: () => ({})
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'save'])

// 规则类型定义
const ruleTypes = [
    { code: 'code_manage_pure_text', label: '纯文本', color: '#ffa39e' },
    { code: 'code_manage_date', label: '日期', color: '#ffd591' },
    { code: 'code_manage_variable', label: '变量', color: '#b7eb8f' },
    { code: 'code_manage_pipeline_code', label: '流水码', color: '#87e8de' }
]

// 右侧规则项列表
const ruleItems = ref([
    {
        code: 'code_manage_pipeline_code',
        value: 'nnn',
        order: 1
    }
])

// 当前选中的项索引
const selectedIndex = ref(-1)

// 当前配置
const currentConfig = ref({})

// 规则弹框相关
const showRuleModal = ref(false)
const activeTabKey = ref('code_manage_pure_text')

// 拖拽排序相关变量
const dragIndex = ref(-1)
const dragOverIndex = ref(-1)

// 变量验证错误信息
const variableError = ref('')

// 配置项拖拽相关
const dragConfigIndex = ref(-1)

// 添加流水码错误提示
const sequenceError = ref('')

// 日期格式选项
const dateFormatOptions = [
    { value: 'yyyy', label: '年' },
    { value: 'yyyyMM', label: '年月' },
    { value: 'yyyyMMdd', label: '年月日' },
    { value: 'yyyyMMddHH', label: '年月日时' }
]

// 变量配置相关
const showVariableModal = ref(false)
const showHelpModal = ref(false)
const selectedVariable = ref('')
const mappingType = ref('map')
const simpleMapping = ref('')
const scriptMapping = ref('')

// 特征脚本存储
const featureScript = ref({})
// 模式存储
const pattern = ref('')

// 获取可用变量列表
const availableVariables = computed(() => {
    const variables = ruleItems.value
        .filter(item => item.code === 'code_manage_variable' && item.value)
        .map(item => ({
            value: item.value,
            label: item.value
        }))
    return variables
})

// 监听选中变量的变化
watch(selectedVariable, (newValue) => {
    if (newValue) {
        // 加载选中变量的配置
        const config = featureScript.value[newValue]
        if (config) {
            mappingType.value = config.type
            if (config.type === 'map') {
                simpleMapping.value = Object.entries(config.content)
                    .map(([key, value]) => `${key}|${value}`)
                    .join('\n')
                scriptMapping.value = ''
            } else {
                scriptMapping.value = config.content
                simpleMapping.value = ''
            }
        } else {
            // 新变量，清空配置
            mappingType.value = 'map'
            simpleMapping.value = ''
            scriptMapping.value = ''
        }
    } else {
        // 未选择变量，清空配置
        mappingType.value = 'map'
        simpleMapping.value = ''
        scriptMapping.value = ''
    }
})

// 监听映射类型的变化
watch(mappingType, () => {
    // 切换类型时清空另一个输入框
    if (mappingType.value === 'map') {
        scriptMapping.value = ''
    } else {
        simpleMapping.value = ''
    }
})

// 处理变量配置按钮点击
const handleVariableConfig = () => {
    if (availableVariables.value.length === 0) {
        SkMessage.warning('请先添加并配置变量')
        return
    }

    showVariableModal.value = true
}

// 显示帮助说明
const showVariableHelp = () => {
    showHelpModal.value = true
}

// 保存变量配置
const handleVariableConfigSave = () => {
    if (!selectedVariable.value) {
        SkMessage.warning('请选择变量')
        return
    }

    // 更新特征脚本
    featureScript.value[selectedVariable.value] = {
        type: mappingType.value === 'map' ? 'map' : 'javascript',
        content: mappingType.value === 'map'
            ? parseSimpleMapping(simpleMapping.value)
            : scriptMapping.value
    }

    // 更新流水码的 pattern
    updatePattern()

    // 保存配置
    handleSave()
}

// 解析简单映射内容
const parseSimpleMapping = (mapping) => {
    const result = {}
    if (!mapping) return result

    mapping.split('\n').forEach(line => {
        const [key, value] = line.split('|')
        if (key && value) {
            result[key.trim()] = value.trim()
        }
    })
    return result
}

// 更新 pattern
const updatePattern = () => {
    pattern.value = ruleItems.value
        .map(item => {
            if (!item.value) return ''

            switch (item.code) {
                case 'code_manage_variable':
                    return `{${item.value}}`
                case 'code_manage_pure_text':
                    return `"${item.value}"`
                case 'code_manage_date':
                    return item.value
                case 'code_manage_pipeline_code':
                    return item.value
                default:
                    return ''
            }
        })
        .filter(Boolean)  // 过滤掉空字符串
        .join('')
}

// 验证变量名
const validateVariable = (e) => {
    const value = e
    if (!value) {
        variableError.value = ''
        return
    }

    // 验证规则：字母、数字或下划线的任意组合，第一个字符不能是数字，不能包含汉字
    const regex = /^[a-zA-Z_][a-zA-Z0-9_]*$/
    const hasChineseChar = /[\u4e00-\u9fa5]/.test(value)

    if (hasChineseChar) {
        variableError.value = '变量名不能包含汉字'
    } else if (!regex.test(value)) {
        variableError.value = '变量名只能包含字母、数字、下划线，且首字符不能为数字'
    } else {
        variableError.value = ''
    }
}

// 拖拽相关方法
const handleDragStart = (e, item) => {
    e.dataTransfer.setData('text/plain', JSON.stringify(item))
    e.dataTransfer.setData('sourceType', 'ruleList') // 标记来源
    e.dataTransfer.effectAllowed = 'move'
}

const handleDragEnd = () => {
    dragIndex.value = -1
    dragOverIndex.value = -1
}

const handleDrop = (e) => {
    try {
        // 检查是否是从左侧规则列表拖入的
        const sourceType = e.dataTransfer.getData('sourceType')
        if (sourceType !== 'ruleList') {
            return
        }

        // 获取拖拽数据
        const data = JSON.parse(e.dataTransfer.getData('text/plain'))
        // 不允许添加流水码
        if (data.code === 'code_manage_pipeline_code') {
            return
        }

        // 创建新项
        const newItem = {
            ...data,
            value: '',
            order: ruleItems.value.length + 1
        }

        // 添加到末尾
        ruleItems.value.push(newItem)
        selectedIndex.value = ruleItems.value.length - 1
    } catch (error) {
        console.error('拖拽处理失败:', error)
    }
}

const handleSelectItem = (index) => {
    selectedIndex.value = index
    // 初始化当前配置
    const item = ruleItems.value[index]
    currentConfig.value = JSON.parse(JSON.stringify(item))
}

const handleDeleteItem = (index) => {
    // 不允许删除流水码
    if (ruleItems.value[index].code === 'code_manage_pipeline_code') {
        return
    }

    // 如果删除的是变量，需要清理相关配置
    const deletedItem = ruleItems.value[index]
    if (deletedItem.code === 'code_manage_variable' && deletedItem.value) {
        // 删除对应的特征脚本配置
        delete featureScript.value[deletedItem.value]
        // 如果被删除的变量是当前选中的变量，清空选中状态
        if (selectedVariable.value === deletedItem.value) {
            selectedVariable.value = ''
            simpleMapping.value = ''
            scriptMapping.value = ''
        }
    }

    ruleItems.value.splice(index, 1)
    // 更新剩余项的 order
    ruleItems.value.forEach((item, idx) => {
        item.order = idx + 1
    })

    if (selectedIndex.value === index) {
        selectedIndex.value = -1
    }

    // 更新 pattern
    updatePattern()

    // 保存更新后的配置
    handleSave()
}

// 生成预览内容
const generatePreview = computed(() => {
    return ruleItems.value.map(item => {
        switch (item.code) {
            case 'code_manage_pure_text':
                return item.value || ''
            case 'code_manage_date':
                return formatDate(item.value || 'yyyy')
            case 'code_manage_variable':
                return item.value || ''
            case 'code_manage_pipeline_code':
                return item.value || 'nnn'
            default:
                return ''
        }
    }).join(' ')
})

// 监听配置变化
watch(currentConfig, (newConfig) => {
    if (selectedIndex.value !== -1) {
        ruleItems.value[selectedIndex.value] = JSON.parse(JSON.stringify(newConfig))
    }
}, { deep: true })

// 格式化日期
const formatDate = (format) => {
    if (!format) return ''
    const date = new Date()
    // 简单的日期格式化实现
    return format
        .replace('yyyy', date.getFullYear())
        .replace('MM', String(date.getMonth() + 1).padStart(2, '0'))
        .replace('dd', String(date.getDate()).padStart(2, '0'))
        .replace('HH', String(date.getHours()).padStart(2, '0'))
}

// 修改流水码处理方法
const handleSequenceInput = (value, index) => {
    // 获取输入值
    const inputValue = typeof value === 'string' ? value : value.target.value

    // 验证输入是否只包含 n
    if (inputValue && !/^n+$/.test(inputValue)) {
        sequenceError.value = '只能输入n字符'
    }
    // 清除非n字符并更新值
    ruleItems.value[index].value = inputValue.replace(/[^n]/g, '')
    sequenceError.value = ''
}

// 保存配置
const handleSave = () => {
    const saveData = {
        patternList: JSON.stringify(ruleItems.value),
        featureScript: JSON.stringify(featureScript.value),
        pattern: pattern.value
    }
    emit('update:modelValue', saveData)
    emit('save')
}

// 配置项拖拽相关
const handleConfigDragStart = (e, index) => {
    dragConfigIndex.value = index
    e.dataTransfer.setData('sourceType', 'configItem') // 标记来源
    e.dataTransfer.effectAllowed = 'move'
}

const handleConfigDragEnd = () => {
    dragConfigIndex.value = -1
}

const handleConfigDrop = (e, index) => {
    e.preventDefault()

    // 检查拖拽来源
    const sourceType = e.dataTransfer.getData('sourceType')

    // 如果是从左侧拖入的新项
    if (sourceType === 'ruleList') {
        handleDrop(e)
        return
    }

    // 如果不是配置项之间的拖拽，直接返回
    if (sourceType !== 'configItem') {
        return
    }

    const dragIndex = dragConfigIndex.value

    // 不允许移动流水码
    if (ruleItems.value[dragIndex].code === 'code_manage_pipeline_code') {
        return
    }

    // 如果是配置项之间的拖拽
    if (dragIndex !== -1 && dragIndex !== index) {
        const items = [...ruleItems.value]
        const [removed] = items.splice(dragIndex, 1)
        items.splice(index, 0, removed)
        // 更新所有项的 order
        items.forEach((item, idx) => {
            item.order = idx + 1
        })
        ruleItems.value = items

        // 更新选中项索引
        if (selectedIndex.value === dragIndex) {
            selectedIndex.value = index
        }
    }
}

// 监听规则项变化，更新 pattern
watch(ruleItems, () => {
    updatePattern()
    handleSave()  // 确保每次 pattern 更新后都保存配置
}, { deep: true })

onMounted(() => {
    if (props.formData) {
        // 先做非空判断，确保数据的转换没问题
        if (props.formData.patternList) {
            ruleItems.value = JSON.parse(props.formData.patternList)
        }
        if (props.formData.featureScript) {
            featureScript.value = JSON.parse(props.formData.featureScript)
        }
        if (props.formData.pattern) {
            pattern.value = props.formData.pattern
        }
        updatePattern()
        // 更新一下modelValue
        handleSave()
    }
})
</script>

<style scoped>
.sk-rule-config {
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    min-height: 400px;
}

.sk-rule-config .rule-list {
    border-right: 1px solid #f0f0f0;
    padding: 16px;
}

.sk-rule-config .rule-list .rule-item {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    margin-bottom: 8px;
    border-radius: 4px;
    transition: all 0.3s;
}

.sk-rule-config .rule-list .rule-item:hover {
    background-color: #f5f5f5;
}

.sk-rule-config .rule-list .rule-item.active {
    background-color: #e6f7ff;
}

.sk-rule-config .rule-list .rule-item .rule-block {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    margin-right: 8px;
}

.sk-rule-config .rule-config {
    padding: 16px;
}

.sk-rule-config .rule-config .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.sk-rule-config .rule-config .config-header .config-actions {
    margin-left: auto;
}

.sk-rule-config .rule-config .config-header .config-actions .ant-btn {
    margin-left: 8px;
}

.sk-rule-config .rule-config .preview-area {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
}

.sk-rule-config .rule-config .preview-area .preview-header {
    margin-bottom: 16px;
}

.sk-rule-config .rule-config .preview-area .preview-content {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 4px;
    min-height: 100px;
}

/* 规则弹框样式 */
.rule-content {
    padding: 16px;
}

.rule-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.rule-desc {
    margin-bottom: 16px;
    color: #666;
    line-height: 1.8;
}

.rule-example {
    background-color: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    color: #666;
    line-height: 1.8;
}

.rule-example div {
    margin-bottom: 4px;
}

:deep(.ant-modal-body) {
    padding: 0;
}

:deep(.ant-tabs-content) {
    padding: 16px;
}

.drop-placeholder {
    text-align: center;
    color: #999;
    padding: 32px 0;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
}

.config-items {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    min-height: 100px;
    overflow-x: auto;
    padding-bottom: 8px;
    /* 为滚动条预留空间 */
}

.config-item {
    flex: 0 0 240px;
    /* 固定宽度，不伸缩 */
    width: 240px;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s;
    cursor: pointer;
}

.config-item.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.config-item :deep(.ant-input),
.config-item :deep(.ant-select),
.config-item :deep(.ant-input-number) {
    margin-bottom: 8px;
}

.config-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.config-item-header .drag-handle {
    cursor: move;
    margin-right: 8px;
    color: #999;
}

.config-item-header .drag-handle:hover {
    color: #666;
}

.delete-icon {
    color: #999;
    transition: all 0.3s;
}

.delete-icon:hover {
    color: #ff4d4f;
}

.config-item.dragging {
    opacity: 0.5;
    border-style: dashed;
}

/* 美化滚动条样式 */
.config-items::-webkit-scrollbar {
    height: 6px;
}

.config-items::-webkit-scrollbar-thumb {
    background-color: #e8e8e8;
    border-radius: 3px;
}

.config-items::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 3px;
}

.sequence-config,
.variable-config {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sequence-preview {
    color: #666;
    font-size: 12px;
}

.variable-tip {
    color: #ff4d4f;
    font-size: 12px;
}

.sequence-tip {
    color: #ff4d4f;
    font-size: 12px;
}

.variable-config-content {
    padding: 16px;
}

.variable-config-content .label {
    display: inline-block;
    width: 80px;
    margin-right: 8px;
}

.variable-select {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.mapping-type {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.help-icon {
    margin-left: 8px;
    color: #1890ff;
    cursor: pointer;
}

.help-content {
    padding: 16px;
}

.help-section {
    margin-bottom: 16px;
}

.help-title {
    font-weight: bold;
    margin-bottom: 8px;
}

.help-text {
    color: #666;
    line-height: 1.8;
    white-space: pre-wrap;
}
</style>