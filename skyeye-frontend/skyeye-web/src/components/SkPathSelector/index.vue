<template>
    <div class="sk-path-selector">
        <SkInput v-model="inputValue" :placeholder="placeholder" class="path-input">
            <template #suffix>
                <a-dropdown :trigger="['click']" v-if="sampleData">
                    <EyeOutlined />
                    <template #overlay>
                        <div class="data-preview">
                            <div class="preview-header">
                                <span>数据结构预览</span>
                                <a-button type="link" size="small" @click="copyPreviewData">
                                    复制
                                </a-button>
                            </div>
                            <div class="preview-content">
                                <pre class="json-view">{{ prettyJson }}</pre>
                            </div>
                            <div class="preview-footer">
                                <SkButton size="small" @click.prevent="handleTestPath">测试路径</SkButton>
                            </div>
                        </div>
                    </template>
                </a-dropdown>
            </template>
        </SkInput>
        <div v-if="testResult" class="test-result" :class="{ error: testError }">
            {{ testResult }}
        </div>
    </div>
</template>

<script>
import { defineComponent, ref, watch, computed } from 'vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import SkInput from '../SkInput/index.vue'
import SkButton from '../SkButton/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

export default defineComponent({
    name: 'sk-path-selector',

    components: {
        EyeOutlined,
        SkInput,
        SkButton
    },

    props: {
        modelValue: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入数据路径，如: data.list'
        }
    },

    emits: ['update:modelValue'],

    setup(props, { emit }) {
        const inputValue = ref(props.modelValue)
        const testResult = ref('')
        const testError = ref(false)

        // 示例数据
        const sampleData = ref({
            code: 200,
            data: {
                list: [
                    { id: 1, name: '示例1' },
                    { id: 2, name: '示例2' }
                ],
                total: 2
            },
            message: 'success'
        })

        // 格式化的 JSON 字符串
        const prettyJson = computed(() => {
            return JSON.stringify(sampleData.value, null, 2)
        })

        watch(() => props.modelValue, (val) => {
            inputValue.value = val
        })

        watch(inputValue, (val) => {
            emit('update:modelValue', val)
        })

        const handleTestPath = () => {
            try {
                if (!inputValue.value) {
                    testResult.value = '请输入数据路径'
                    testError.value = true
                    return
                }

                const result = inputValue.value.split('.').reduce((obj, key) => {
                    if (obj === undefined) throw new Error('无效路径')
                    return obj[key]
                }, sampleData.value)

                if (result === undefined) {
                    throw new Error('路径未找到对应数据')
                }

                testResult.value = `测试成功，数据类型: ${Array.isArray(result) ? 'Array' : typeof result}`
                testError.value = false

                // 在控制台显示测试结果
                console.log('路径测试结果:', result)
            } catch (error) {
                testResult.value = `测试失败: ${error.message}`
                testError.value = true
            }

            // 3秒后清除测试结果
            setTimeout(() => {
                testResult.value = ''
            }, 3000)
        }

        const copyPreviewData = () => {
            const text = prettyJson.value    
            if (!text) {
                SkMessage.warning('没有可复制的内容')
                return
            }
            proxy.$util.copyToClipboard(text,{
                onSuccess: () => SkMessage.success('复制成功'),
                onError: () => SkMessage.error('复制失败')
            }) 
        }

        return {
            inputValue,
            sampleData,
            prettyJson,
            testResult,
            testError,
            handleTestPath,
            copyPreviewData
        }
    }
})
</script>

<style scoped>
.data-preview {
    padding: 16px;
    min-width: 300px;
    max-width: 500px;
    background: #1f1f1f;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.data-preview .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #303030;
    color: #fff;
}

.data-preview .preview-header :deep(.ant-btn-link) {
    color: #1890ff;
}

.data-preview .preview-header :deep(.ant-btn-link:hover) {
    color: #40a9ff;
}

.data-preview .preview-content {
    max-height: 300px;
    overflow: auto;
    margin-bottom: 8px;
}

.data-preview .preview-content .json-view {
    margin: 0;
    padding: 12px;
    background: #141414;
    border-radius: 4px;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-all;
    color: #fff;
    user-select: text;
    cursor: text;
}

.data-preview .preview-footer {
    text-align: right;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #303030;
}

.test-result {
    margin-top: 4px;
    font-size: 12px;
    color: #52c41a;
}

.test-result.error {
    color: #ff4d4f;
}
</style>