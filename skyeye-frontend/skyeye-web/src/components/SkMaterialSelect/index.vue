<template>
    <div class="sk-customer-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else class="sk-detail-readonly">
            <span :title="showValue.name">{{ showValue.name }}</span>
        </div>

        <!-- 商品选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择商品" width="80%" :bodyStyle="{ height: '80vh' }">
            <div class="container-manage">
                <div class="left-tree">
                    <SkCard title="商品分类" :bordered="false">
                        <div class="list-tree">
                            <SkDict v-model="treeSelectData" objectType="ERP_MATERIAL_CATEGORY" showType="selectTree"
                                @change="handleSearch" />
                        </div>
                    </SkCard>
                </div>

                <div class="right-content">
                    <SkCard ref="cardRef" :bordered="false">
                        <!-- 搜索区域 -->
                        <div class="table-search">
                            <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                                :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                                <template #submitIcon>
                                    <search-outlined />
                                </template>
                                <a-form-item name="keyword">
                                    <SkInput v-model="searchForm.keyword" placeholder="请输入商品名称、型号" allowClear />
                                </a-form-item>
                            </SkForm>
                        </div>

                        <!-- 表格区域 -->
                        <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                            :ready="tableReady" :pagination="pagination" @change="handleTableChange">
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.dataIndex === 'choose'">
                                    <a-button type="link" size="small" @click.stop="handleSelect(record)">
                                        选择
                                    </a-button>
                                </template>
                                <template v-if="column.dataIndex === 'categoryId'">
                                    {{ initDictData['ERP_MATERIAL_CATEGORY'][record.categoryId] }}
                                </template>
                                <template v-if="column.dataIndex === 'fromType'">
                                    <div
                                        v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialFromType'], 'id', record.fromType, 'name')">
                                    </div>
                                </template>
                                <template v-if="column.dataIndex === 'type'">
                                    <div
                                        v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialType'], 'id', record.type, 'name')">
                                    </div>
                                </template>
                                <template v-if="column.dataIndex === 'unit'">
                                    <div
                                        v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialUnit'], 'id', record.unit, 'name')">
                                    </div>
                                </template>
                            </template>
                        </SkTable>
                    </SkCard>
                </div>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkDict from '@/components/SkDict/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择商品'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})

const tableReady = ref(false)

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '商品名称',
        dataIndex: 'name',
        width: 150
    },
    {
        title: '型号',
        dataIndex: 'model',
        width: 120
    },
    {
        title: '所属分类',
        dataIndex: 'categoryId',
        width: 120
    },
    {
        title: '产品来源',
        dataIndex: 'fromType',
        width: 120
    },
    {
        title: '产品类型',
        dataIndex: 'type',
        width: 120
    },
    {
        title: '产品规格类型',
        dataIndex: 'unit',
        width: 120
    }
]

// 分页配置
const pagination = reactive(proxy.$config.pagination())

const treeSelectData = ref('')

// 初始化数据字典数据
const initDictData = ref({})
// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['materialFromType', 'materialType', 'materialUnit']);
    initEnumData.value = enumResult

    let dictResult = await proxy.$util.getDictListMapByCode(['ERP_MATERIAL_CATEGORY']);
    initDictData.value = dictResult
}

// 获取商品列表
const getMaterialList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            ...searchForm,
            categoryId: treeSelectData.value
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'material010',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取商品列表失败')
    } finally {
        loading.value = false
    }
}

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getMaterialList()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getMaterialList()
}

// 处理选择
const handleSelect = (record) => {
    showValue.value = record
    emit('update:modelValue', record.id)
    emit('change', record)
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    // 详情页面和编辑页面都会走这里
    const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
    const mation = props.formData[mationKey]
    showValue.value = mation || {}
    if (showValue.value.id) {
        emit('update:modelValue', showValue.value.id)
    }
    if (props.isEdit == proxy.$config.formEditType.isEdit) {
        getInitData()
    }
})
</script>

<style scoped></style>