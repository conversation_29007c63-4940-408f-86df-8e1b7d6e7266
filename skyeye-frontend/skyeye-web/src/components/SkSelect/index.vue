<template>
    <div class="sk-select-wrapper">
        <a-select v-if="isEdit === $config.formEditType.isEdit" v-model:value="value" :options="dataList" :mode="mode"
            :placeholder="getPlaceholder" :allowClear="allowClear" :showSearch="showSearch"
            :filterOption="handleFilterOption" :maxTagCount="maxTagCount" :maxTagTextLength="maxTagTextLength"
            :loading="loading" :status="status" :disabled="disabled" :field-names="fieldNames" @change="handleChange"
            @focus="handleFocus" @blur="handleBlur" @onSearch="showSearch ? handleSearch : null" @select="handleSelect"
            @deselect="handleDeselect" @dropdownVisibleChange="handleDropdownVisibleChange">
            <template v-if="$slots.suffixIcon" #suffixIcon>
                <slot name="suffixIcon"></slot>
            </template>
            <template v-if="$slots.option" #option="item">
                <slot name="option" v-bind="item"></slot>
            </template>
        </a-select>
        <!-- 只读模式 -->
        <div v-else class="sk-detail-readonly">
            <template v-if="modelValue">
                <template v-if="mode === 'multiple' || mode === 'tags'">
                    <a-tag v-for="item in getSelectedLabels" :key="item.id">
                        {{ item.name }}
                    </a-tag>
                </template>
                <template v-else>
                    {{ getSelectedLabel }}
                </template>
            </template>
        </div>
        <!-- 帮助信息 -->
        <div v-if="help && helpShow" class="sk-select-help">
            <info-circle-outlined />
            {{ help }}
        </div>
    </div>
</template>

<script setup>
import { computed, getCurrentInstance, watch, ref, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { proxy } = getCurrentInstance()


const props = defineProps({
    // 绑定值
    modelValue: {
        type: [String, Number, Array],
        default: undefined
    },
    // 数据源
    options: {
        type: Array,
        default: () => []
    },
    // 选择模式
    mode: {
        type: String,
        default: undefined
    },
    // 占位符
    placeholder: {
        type: String,
        default: () => ''
    },
    // 是否可以清除
    allowClear: {
        type: Boolean,
        default: true
    },
    // 是否支持搜索
    showSearch: {
        type: Boolean,
        default: true
    },
    // 是否根据输入项进行筛选
    filterOption: {
        type: [Boolean, Function],
        default: undefined
    },
    // 最多显示多少个 tag，响应式模式会对性能产生损耗
    maxTagCount: {
        type: [Number, String],
        default: undefined
    },
    // 最大显示的 tag 文本长度
    maxTagTextLength: {
        type: Number,
        default: undefined
    },
    // 加载中状态
    loading: {
        type: Boolean,
        default: false
    },
    // 验证状态
    status: {
        type: String,
        default: undefined
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },
    // 属性键
    attrKey: {
        type: String,
        default: undefined
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 页面类型
    pageType: {
        type: String,
        default: ''
    },
    // 添加 fieldNames 属性
    fieldNames: {
        type: Object,
        default: () => ({
            label: 'label',
            value: 'value',
            options: 'options'
        })
    },
    // 自定义属性
    attrDefinitionCustom: {
        type: Object,
        default: () => ({})
    },
    // 前置属性
    preAttribute: {
        type: String,
        default: ''
    },
    // 对象ID
    objectId: {
        type: String,
        default: ''
    },
    // 对象键
    objectKey: {
        type: String,
        default: ''
    },
    // 帮助信息
    help: {
        type: String,
        default: ''
    },
    // 帮助信息是否显示
    helpShow: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits([
    'update:modelValue',
    'change',
    'focus',
    'blur',
    'search',
    'select',
    'deselect',
    'dropdownVisibleChange'
])

// 获取选中项的标签（单选）
const getSelectedLabel = computed(() => {
    if (!proxy.$util.isNull(props.attrKey)) {
        const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
        return props.formData[keyMation]?.name || props.formData[keyMation]?.title || props.formData[keyMation]?.dictName || ''
    } else {
        const selected = props.options.find(item =>
            item[props.fieldNames.value] === props.modelValue
        )
        return selected ? selected[props.fieldNames.label] : ''
    }
})

// 获取选中项的标签（多选）
const getSelectedLabels = computed(() => {
    if (!Array.isArray(props.modelValue)) return []
    if (!proxy.$util.isNull(props.attrKey)) {
        const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
        return props.formData[keyMation] || []
    } else {
        const selected = props.options.filter(item =>
            props.modelValue.includes(item[props.fieldNames.value])
        )
        selected.forEach(item => {
            item.name = item[props.fieldNames.label]
        })
        return selected
    }
})

// 处理值变化
const handleChange = (value, option) => {
    emit('change', value, option, props.attrKey)
}

// 处理获得焦点
const handleFocus = (e) => {
    emit('focus', e)
}

// 处理失去焦点
const handleBlur = (e) => {
    emit('blur', e)
}

// 处理搜索
const handleSearch = (value) => {
    if (props.showSearch) {
        emit('search', value)
    }
}

// 处理选择
const handleSelect = (value, option) => {
    emit('select', value, option)
}

// 处理取消选择
const handleDeselect = (value, option) => {
    emit('deselect', value, option)
}

// 处理下拉菜单显示状态变化
const handleDropdownVisibleChange = (open) => {
    emit('dropdownVisibleChange', open)
}

// 使用计算属性处理 v-model
const value = computed({
    get: () => {
        if (props.mode === 'multiple') {
            if (typeof props.modelValue === 'string') {
                return JSON.parse(props.modelValue || '[]')
            }
            emit('update:modelValue', JSON.stringify(props.modelValue))
            return props.modelValue || []
        } else {
            if (proxy.$util.isNull(props.modelValue)) {
                return undefined
            }
            // 初始化加载的时候，判断modelValue是否在dataList中，如果不在，则设置为undefined
            if (!proxy.$util.isNull(props.modelValue) && props.isEdit == proxy.$config.formEditType.isEdit) {
                const num1 = dataList.value.findIndex(v => v[props.fieldNames.value] == props.modelValue);
                if (num1 < 0) {
                    return undefined
                }
            }
            return props.modelValue.toString()
        }
    },
    set: (val) => {
        if (props.mode === 'multiple') {
            emit('update:modelValue', JSON.stringify(val))
        } else {
            emit('update:modelValue', val)
        }
    }
})

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newVal) => {
        if (proxy.$util.isNull(newVal)) {
            value.value = undefined
        } else {
            value.value = newVal.toString()
        }
    },
    { immediate: true }
)

// 处理过滤选项
const handleFilterOption = (input, option) => {
    if (typeof props.filterOption === 'function') {
        return props.filterOption(input, option)
    }
    if (props.filterOption === false) {
        return false
    }
    return option && option[props.fieldNames.label] ?
        option[props.fieldNames.label].toLowerCase().indexOf(input.toLowerCase()) >= 0 : false
}

// 递归获取嵌套值
const getNestedValue = (obj, parts) => {
    // 如果是 undefined，直接返回 undefined
    if (obj === undefined) {
        return undefined;
    }
    // 如果是 null 或不是对象，且还有更多层级，返回 null
    if ((!obj || typeof obj !== 'object') && parts.length > 0) {
        return null;
    }
    // 如果没有更多的parts，返回当前值
    if (parts.length === 0) {
        return obj;
    }
    // 获取当前层级的值
    const currentValue = obj[parts[0]];
    // 如果还有更多层级但当前值是 null 或不是对象，返回 null
    if (parts.length > 1 && (!currentValue || typeof currentValue !== 'object')) {
        return null;
    }
    // 递归处理剩余部分
    return getNestedValue(currentValue, parts.slice(1));
}

const dataList = ref([])
const showView = async () => {
    const customAttr = props.attrDefinitionCustom
    const dataType = customAttr.dataType
    const _dataList = []

    if (dataType == 1) {
        // 自定义
        var obj = proxy.$util.isNull(customAttr.defaultData) ? [] : customAttr.defaultData;
        if (typeof obj == 'string') {
            obj = JSON.parse(obj);
        }
        obj.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id
            newItem["item"] = item
            _dataList.push(newItem)
        })
        dataList.value = _dataList
    } else if (dataType == 2) {
        // 枚举
        if (proxy.$util.isNull(customAttr.objectId)) {
            return
        }
        const rows = await proxy.$util.getEnumListByCode(customAttr.objectId)
        rows.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id.toString()
            newItem["item"] = item
            newItem["isDefault"] = item.isDefault
            _dataList.push(newItem)
        })
        if (props.pageType == proxy.$config.pageType.ADD) {
            if (proxy.$util.isNull(props.modelValue)) {
                const num1 = _dataList.findIndex(v => v.isDefault);
                if (num1 >= 0) {
                    value.value = _dataList[num1].value
                    handleChange(_dataList[num1].value)
                }
            }
        }
        dataList.value = _dataList
    } else if (dataType == 3) {
        // 数据字典
        const rows = await proxy.$util.getDictListByCode(customAttr.objectId)
        rows.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id
            newItem["item"] = item
            newItem["isDefault"] = item.isDefault
            _dataList.push(newItem)
        })
        if (props.pageType == proxy.$config.pageType.ADD) {
            if (proxy.$util.isNull(props.modelValue)) {
                const num1 = _dataList.findIndex(v => v.isDefault);
                if (num1 >= 0) {
                    value.value = _dataList[num1].value
                    handleChange(_dataList[num1].value)
                }
            }
        }
        dataList.value = _dataList
    } else if (dataType == 4) {
        // 自定义接口
        const businessApi = customAttr.businessApi;
        const service = proxy.$util.getServiceKey(businessApi.serviceStr);
        const params = {};
        for (const key in businessApi.params) {
            const value = businessApi.params[key]
            if (!proxy.$util.isNull(value)) {
                // 判断value是否是${}包裹的
                if (value.startsWith('${') && value.endsWith('}')) {
                    const field = value.slice(2, -1);
                    const fieldParts = field.split('.');
                    const realValue = getNestedValue(props.formData, fieldParts);
                    params[key] = realValue;
                } else {
                    if (key == 'objectId') {
                        params[key] = props.objectId
                    } else {
                        params[key] = value;
                    }
                }
            } else {
                if (!proxy.$util.isNull(props.preAttribute) && !proxy.$util.isNull(props.formData)) {
                    const preAttributeVal = props.formData[props.preAttribute];
                    params[key] = preAttributeVal;
                }
            }
        }

        const url = proxy.$config.getConfig()[service] + businessApi.api;
        const res = await proxy.$http.request({
            url: url, method: businessApi.method,
            [businessApi.method.toLowerCase() === 'get' ? 'params' : 'data']: params
        })
        res.rows.forEach((item, index) => {
            let newItem = {}
            newItem["name"] = item.name
            newItem[props.fieldNames.label] = item.name
            newItem[props.fieldNames.value] = item.id
            newItem["item"] = item
            _dataList.push(newItem)
        })
        dataList.value = _dataList
    } else {
        dataList.value = props.options
    }

}

// 监听 attrDefinitionCustom 变化
watch(
    () => props.attrDefinitionCustom,
    async (newVal) => {
        await showView()
    },
    {
        immediate: true,
        deep: true
    }
)

// 是否是首次加载数据
const isFirstLoad = ref(false)

// 监听动态字段变化
watch(
    () => JSON.parse(JSON.stringify(props.formData)),
    async (newVal, oldVal) => {
        if (props.attrDefinitionCustom?.dataType === 4) {
            const params = props.attrDefinitionCustom?.businessApi?.params || {}
            let isChange = false
            for (const [key, value] of Object.entries(params)) {
                if (value && value.startsWith('${') && value.endsWith('}')) {
                    const field = value.slice(2, -1);
                    const fieldParts = field.split('.');
                    const oldValue = getNestedValue(oldVal, fieldParts);
                    const newValue = getNestedValue(newVal, fieldParts);
                    // 使用严格相等比较，null 和 undefined 都会被认为是变化
                    if (!Object.is(newValue, oldValue)) {
                        isChange = true
                        break;
                    }
                }
            }
            if (isChange) {
                if (isFirstLoad.value) {
                    // 不是首次加载时，重置选中值
                    value.value = undefined
                } else {
                    isFirstLoad.value = true
                }
                await showView()
            }
        }
    },
    { deep: true, flush: 'post' }
)

// 监听 options 变化
watch(
    () => props.options,
    (newVal) => {
        if (newVal && newVal.length > 0) {
            // 循环处理每个选项，将value转为字符串
            newVal.forEach(item => {
                if (item[props.fieldNames.value] !== undefined) {
                    item[props.fieldNames.value] = item[props.fieldNames.value].toString()
                }
            })
            dataList.value = newVal
        } else {
            dataList.value = []
        }
    },
    {
        immediate: true,
        deep: true
    }
)

// 使用计算属性处理 placeholder
const getPlaceholder = computed(() => {
    return props.placeholder || t('common.placeholder.select')
})

// 在组件卸载时重置标记
onUnmounted(() => {
    isFirstLoad.value = false
})
</script>

<style lang="less" scoped>
.sk-select-wrapper {
    :deep(.ant-select) {
        width: 100%;
    }

    .sk-select-help {
        display: flex;
        align-items: center;
        gap: 4px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
    }
}
</style>