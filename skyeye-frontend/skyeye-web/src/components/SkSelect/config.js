export default {
    type: 'select',
    icon: 'FormOutlined',
    props: {
        label: '',
        options: [],
        required: false,
        attrKey: ''
    },
    propsConfig: {
        label: {
            type: 'string',
            label: '标签文本'
        },
        options: {
            type: 'array',
            label: '选项列表',
            config: {
                item: {
                    props: {
                        label: {
                            type: 'string',
                            label: '选项文本'
                        },
                        value: {
                            type: 'string',
                            label: '选项值'
                        }
                    }
                }
            }
        },
        required: {
            type: 'boolean',
            label: '是否必填'
        },
        attrKey: {
            type: 'select',
            label: '绑定属性',
            options: [],
            config: {
                labelField: 'name',
                valueField: 'attrKey'
            }
        }
    }
} 