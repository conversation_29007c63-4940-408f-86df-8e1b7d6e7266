<template>
    <div class="sk-input-wrapper">
        <div class="sk-detail-readonly">
            <template v-if="modelValue">
                {{ modelValue }}
            </template>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    }
})
</script>

<style scoped>
.sk-input-wrapper {
    width: 100%;
}
</style>