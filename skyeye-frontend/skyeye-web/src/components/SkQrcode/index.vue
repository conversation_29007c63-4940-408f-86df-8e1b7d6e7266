<template>
    <div class="sk-qrcode">
        <a-qrcode :value="value" :size="size" :color="color" :bgColor="bgColor" :bordered="bordered"
            :errorLevel="errorLevel" :icon="icon" :iconSize="iconSize" :status="status" @refresh="handleRefresh" />
    </div>
</template>

<script setup>
defineProps({
    // 二维码内容
    value: {
        type: String,
        required: true
    },
    // 二维码大小
    size: {
        type: Number,
        default: 160
    },
    // 二维码颜色
    color: {
        type: String,
        default: '#000000'
    },
    // 二维码背景色
    bgColor: {
        type: String,
        default: '#ffffff'
    },
    // 是否显示边框
    bordered: {
        type: Boolean,
        default: true
    },
    // 二维码纠错等级 L M Q H
    errorLevel: {
        type: String,
        default: 'M',
        validator: (value) => ['L', 'M', 'Q', 'H'].includes(value)
    },
    // 二维码中图标地址
    icon: {
        type: String,
        default: undefined
    },
    // 二维码中图标大小
    iconSize: {
        type: Number,
        default: 40
    },
    // 二维码状态：active、expired、loading
    status: {
        type: String,
        default: 'active'
    }
})

const emit = defineEmits(['refresh'])

// 刷新事件
const handleRefresh = () => {
    emit('refresh')
}
</script>

<style lang="less" scoped>
.sk-qrcode {
    display: inline-block;
}
</style>