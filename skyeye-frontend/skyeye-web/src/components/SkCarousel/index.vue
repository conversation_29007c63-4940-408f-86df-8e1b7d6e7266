<template>
    <div class="sk-carousel" :class="{ 'sk-carousel-vertical': isVertical }">
        <a-carousel :autoplay="autoplay" :dots="dots" :dotPosition="dotPosition" :effect="effect"
            :autoplaySpeed="autoplaySpeed" :easing="easing" @beforeChange="handleBeforeChange"
            @afterChange="handleAfterChange">
            <template v-if="$slots.default">
                <slot></slot>
            </template>
            <template v-else>
                <div v-for="(item, index) in items" :key="index">
                    <slot name="item" :item="item" :index="index">
                        <div class="sk-carousel-item" :style="getItemStyle(item)">
                            <template v-if="item.type === 'image'">
                                <img :src="item.url" :alt="item.title || ''" />
                            </template>
                            <template v-else>
                                <div class="sk-carousel-content">
                                    <h3>{{ item.title }}</h3>
                                    <p>{{ item.description }}</p>
                                </div>
                            </template>
                        </div>
                    </slot>
                </div>
            </template>

            <!-- 自定义箭头 -->
            <template v-if="arrows && $slots.prevArrow" #prevArrow>
                <slot name="prevArrow"></slot>
            </template>
            <template v-if="arrows && $slots.nextArrow" #nextArrow>
                <slot name="nextArrow"></slot>
            </template>
        </a-carousel>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 轮播内容项
    items: {
        type: Array,
        default: () => []
    },
    // 是否自动切换
    autoplay: {
        type: Boolean,
        default: false
    },
    // 是否显示面板指示点
    dots: {
        type: Boolean,
        default: true
    },
    // 面板指示点位置
    dotPosition: {
        type: String,
        default: 'bottom',
        validator: (value) => ['top', 'bottom', 'left', 'right'].includes(value)
    },
    // 动画效果
    effect: {
        type: String,
        default: 'scrollx',
        validator: (value) => ['scrollx', 'fade'].includes(value)
    },
    // 是否垂直显示（已废弃，使用 dotPosition 代替）
    vertical: {
        type: Boolean,
        default: false
    },
    // 自动切换的时间间隔（毫秒）
    autoplaySpeed: {
        type: Number,
        default: 3000
    },
    // 动画效果
    easing: {
        type: String,
        default: 'linear'
    },
    // 是否显示箭头
    arrows: {
        type: Boolean,
        default: false
    },
    // 自定义高度
    height: {
        type: [String, Number],
        default: '300px'
    }
})

const emit = defineEmits(['beforeChange', 'afterChange'])

// 计算是否为垂直模式
const isVertical = computed(() => {
    return props.dotPosition === 'left' || props.dotPosition === 'right'
})

// 处理切换前事件
const handleBeforeChange = (from, to) => {
    emit('beforeChange', { from, to })
}

// 处理切换后事件
const handleAfterChange = (current) => {
    emit('afterChange', current)
}

// 获取项目样式
const getItemStyle = (item) => {
    const style = {
        height: typeof props.height === 'number' ? `${props.height}px` : props.height
    }

    if (item.type === 'image') {
        style.backgroundImage = `url(${item.url})`
        style.backgroundSize = 'cover'
        style.backgroundPosition = 'center'
    }

    if (item.backgroundColor) {
        style.backgroundColor = item.backgroundColor
    }

    return style
}
</script>

<style scoped>
.sk-carousel {
    width: 100%;
}

.sk-carousel-vertical {
    height: 100%;
}

.sk-carousel-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    overflow: hidden;
}

.sk-carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sk-carousel-content {
    text-align: center;
    padding: 20px;
}

.sk-carousel-content h3 {
    font-size: 24px;
    margin-bottom: 10px;
}

.sk-carousel-content p {
    font-size: 16px;
    margin: 0;
}

:deep(.ant-carousel) {
    width: 100%;
}

:deep(.ant-carousel .slick-slide) {
    text-align: center;
    overflow: hidden;
}

:deep(.ant-carousel .slick-dots) {
    margin: 0;
    padding: 0;
}

:deep(.ant-carousel .slick-dots li button) {
    background: #fff;
    opacity: 0.3;
}

:deep(.ant-carousel .slick-dots li.slick-active button) {
    opacity: 1;
}

/* 垂直模式样式 */
.sk-carousel-vertical :deep(.ant-carousel .slick-dots) {
    right: 12px;
}

/* 自定义箭头样式 */
:deep(.ant-carousel .slick-prev),
:deep(.ant-carousel .slick-next) {
    color: #fff;
    font-size: 20px;
    opacity: 0.3;
    transition: all 0.3s;
}

:deep(.ant-carousel .slick-prev:hover),
:deep(.ant-carousel .slick-next:hover) {
    opacity: 1;
}
</style>