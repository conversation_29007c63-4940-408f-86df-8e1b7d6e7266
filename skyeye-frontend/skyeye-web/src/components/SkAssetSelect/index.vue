<template>
    <div class="sk-customer-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else>
            {{ showValue.name }}
        </div>

        <!-- 资产选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择资产">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <!-- 搜索区域 -->
                    <div class="table-search">
                        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                            :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                            <template #submitIcon>
                                <search-outlined />
                            </template>
                            <a-form-item name="keyword">
                                <SkInput v-model="searchForm.keyword" placeholder="请输入资产名称" allowClear />
                            </a-form-item>
                        </SkForm>
                    </div>

                    <!-- 表格区域 -->
                    <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                        :ready="tableReady" :pagination="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'choose'">
                                <a-button type="link" size="small" @click.stop="handleSelect(record)">
                                    选择
                                </a-button>
                            </template>
                            <template v-if="column.dataIndex === 'assetImg'">
                                <SkTableImg :imgPath="record.assetImg" />
                            </template>
                            <template v-if="column.dataIndex === 'typeId'">
                                {{ initDictData['ADM_ASSET_TYPE'][record.typeId] }}
                            </template>
                        </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'
import SkTableImg from '@/components/SkTableImg/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择资产'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
const searchForm = reactive({
    keyword: '',
})

const tableReady = ref(false)

// 表格列配置
const columns = [
    {
        title: '选择',
        dataIndex: 'choose',
        width: 80,
        align: 'center',
        fixed: 'left'
    },
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '资产名称',
        dataIndex: 'name',
        width: 100
    },
    {
        title: '图片',
        dataIndex: 'assetImg',
        width: 80,
        align: 'center'
    },
    {
        title: '资产类型',
        dataIndex: 'typeId',
        width: 120
    },
    {
        title: '资产编号前缀',
        dataIndex: 'numberPrefix',
        width: 120
    },
    {
        title: '参考价',
        dataIndex: 'readPrice',
        width: 120
    },
]

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化数据字典数据
const initDictData = ref({})
const getInitData = async () => {
    let dictResult = await proxy.$util.getDictListMapByCode(['ADM_ASSET_TYPE']);
    initDictData.value = dictResult
}

// 获取商品列表
const getAssetList = async () => {
    try {
        loading.value = true
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            ...searchForm,

        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + 'asset001',
            params
        )
        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取商品列表失败')
    } finally {
        loading.value = false
    }
}

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
    nextTick(() => {
        setTimeout(() => {
            tableReady.value = true
            handleSearch()
        }, 100)
    })
}

// 处理查询
const handleSearch = () => {
    pagination.current = 1
    getAssetList()
}

// 处理表格变化
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    getAssetList()
}

// 处理选择
const handleSelect = (record) => {
    showValue.value = record
    emit('update:modelValue', record.id)
    emit('change', record, props.attrKey)
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    // 详情页面和编辑页面都会走这里
    const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
    const mation = props.formData[mationKey]
    showValue.value = mation || {}
    if (showValue.value.id) {
        emit('update:modelValue', showValue.value.id)
    }
    if (props.isEdit == proxy.$config.formEditType.isEdit) {
        getInitData()
    }
})
</script>

<style scoped></style>