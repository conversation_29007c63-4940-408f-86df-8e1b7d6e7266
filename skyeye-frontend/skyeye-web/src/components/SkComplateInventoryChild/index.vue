<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" :rules="rules" @submit="handleSubmit" @reset="handleCancel">
            <!-- 基本信息 -->
            <SkHrTitle>基本信息</SkHrTitle>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="产品">
                        <div class="sk-detail-readonly">{{ detailData.materialMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="规格">
                        <div class="sk-detail-readonly">{{ detailData.normsMation?.name }}</div>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="仓库">
                        <div class="sk-detail-readonly">{{ detailData.depotMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="计划开始时间">
                        <div class="sk-detail-readonly">{{ detailData.planStartTime }}</div>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="计划结束时间">
                        <div class="sk-detail-readonly">{{ detailData.planEndTime }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="类型">
                        <template v-if="detailData.type">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['materialNormsCodeType'], 'id', detailData.type, 'name')">
                            </div>
                        </template>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="单价">
                        <div class="sk-detail-readonly">{{ detailData.unitPrice }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="计划数量">
                        <div class="sk-detail-readonly">{{ detailData.planNumber }}</div>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 盘点信息 -->
            <SkHrTitle>盘点信息</SkHrTitle>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="条形码">
                        <SkInput v-model="formData.code" placeholder="点击查看条形码" readonly @click="showCodeModal" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="实际盘点数量" required>
                        <SkInputNumber v-model="formData.realNumber" placeholder="请输入实际盘点数量" :min="0"
                            style="width: 100%" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item label="盘盈数量">
                        <SkInputNumber v-model="formData.profitNum" placeholder="请输入盘盈数量" :min="0"
                            style="width: 100%" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="盘亏数量">
                        <SkInputNumber v-model="formData.lossNum" placeholder="请输入盘亏数量" :min="0" style="width: 100%" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-form-item label="盘盈条形码">
                <a-textarea v-model:value="formData.profitNormsCode" placeholder="请输入盘盈条形码" :rows="4" />
            </a-form-item>

            <a-form-item label="盘亏条形码">
                <a-textarea v-model:value="formData.lossNormsCode" placeholder="请输入盘亏条形码" :rows="4" />
            </a-form-item>
        </SkForm>

        <!-- 条形码选择弹窗 -->
        <SkModal v-model="codeModalVisible" title="选择条形码" @cancel="handleCodeModalCancel">
            <div class="container-manage">
                <SkCard ref="cardRef" :bordered="false">
                    <SkTable :columns="codeColumns" :data-source="codeTableData" :loading="codeLoading"
                        :pagination="codePagination" @change="handleCodeTableChange">
                    <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'codeNum'">
                        <a @click="handleCodeSelect(record)">{{ record.codeNum }}</a>
                    </template>
                    </template>
                    </SkTable>
                </SkCard>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkInputNumber from '@/components/SkInputNumber/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'

const props = defineProps({
    id: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['success', 'cancel'])

const { proxy } = getCurrentInstance()

// 表单数据
const formRef = ref(null)
const formData = reactive({
    code: '',
    realNumber: undefined,
    profitNum: 0,
    lossNum: 0,
    profitNormsCode: '',
    lossNormsCode: ''
})

// 详情数据
const detailData = ref({})

// 表单验证规则
const rules = {
    realNumber: [
        { required: true, message: '请输入实际盘点数量' },
        { type: 'number', min: 0, message: '数量必须大于等于0' }
    ]
}

// 条形码选择相关
const codeModalVisible = ref(false)
const codeLoading = ref(false)
const codeTableData = ref([])
const codePagination = reactive(proxy.$config.pagination({
    pageSize: 8,
    pageSizeOptions: [8, 16, 24, 32, 40, 48, 56]
}))

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['materialNormsCodeType'])
    initEnumData.value = result
}

// 条形码表格列配置
const codeColumns = [
    {
        title: '序号',
        type: 'index',
        width: 80,
        align: 'center',
        customRender: ({
            index
        }) =>
            `${(codePagination.current - 1) * codePagination.pageSize + index + 1}`,
    },
    {
        title: '条形码',
        dataIndex: 'codeNum',
        width: 250
    }
]

// 获取详情数据
const getDetailData = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryInventoryChildById',
            { id: props.id }
        )
        detailData.value = res.bean || {}
    } catch (error) {
        SkMessage.error('获取详情失败')
    }
}

// 显示条形码选择弹窗
const showCodeModal = () => {
    codeModalVisible.value = true
    loadCodeTable()
}

// 加载条形码表格数据
const loadCodeTable = async () => {
    codeLoading.value = true
    try {
        const params = {
            page: codePagination.current,
            limit: codePagination.pageSize,
            objectId: props.id
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryInventoryChildCodeList',
            params
        )

        codeTableData.value = res.rows
        codePagination.total = res.total
    } catch (error) {
        SkMessage.error('获取条形码列表失败')
    } finally {
        codeLoading.value = false
    }
}

// 处理条形码表格变化
const handleCodeTableChange = (pag) => {
    if (pag) {
        codePagination.current = Number(pag.current || 1)
        codePagination.pageSize = Number(pag.pageSize || 8)
    }
    loadCodeTable()
}

// 选择条形码
const handleCodeSelect = (record) => {
    formData.code = record.codeNum
    codeModalVisible.value = false
}

// 关闭条形码弹窗
const handleCodeModalCancel = () => {
    codeModalVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
    try {
        const params = {
            id: props.id,
            realNumber: formData.realNumber,
            profitNum: formData.profitNum || 0,
            lossNum: formData.lossNum || 0,
            profitNormsCode: formData.profitNormsCode,
            lossNormsCode: formData.lossNormsCode
        }

        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'complateInventoryChild',
            params
        )

        SkMessage.success('保存成功')
        emit('success')
    } catch (error) {
        SkMessage.error('保存失败')
    }
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 初始化
onMounted(async () => {
    await getInitData()
    getDetailData()
})
</script>

<style scoped></style>
