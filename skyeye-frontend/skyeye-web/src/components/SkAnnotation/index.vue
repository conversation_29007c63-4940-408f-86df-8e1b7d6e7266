<template>
    <div class="sk-annotation" ref="annotationRef">
        <!-- 主体内容区域 -->
        <div class="sk-annotation-content" @mouseup="handleMouseUp" @contextmenu.prevent="handleContextMenu">
            <div class="content-wrapper" ref="contentRef">
                <!-- 原始内容 -->
                <div class="content-original">
                    <slot></slot>
                </div>
                <!-- 高亮层 -->
                <div class="highlight-layer">
                    <div v-for="(annotation, index) in annotations" :key="index" class="highlight-text"
                        :class="{ 'active': activeMarker === index }" :style="{
                            transform: `translate(${annotation.position.left}px, ${annotation.position.top}px)`,
                            width: `${annotation.position.width}px`,
                            height: `${annotation.position.height}px`,
                            backgroundColor: activeMarker === index ? 'rgba(24, 144, 255, 0.7)' : highlightColor
                        }" @mouseenter="activeMarker = index" @mouseleave="activeMarker = -1"></div>
                </div>
            </div>
        </div>

        <!-- 批注列表 -->
        <div class="sk-annotation-list" :class="{ 'collapsed': isCollapsed }">
            <!-- 展开/收缩按钮 -->
            <div class="collapse-button" @click="toggleCollapse">
                <right-outlined :class="{ 'rotated': !isCollapsed }" />
            </div>

            <!-- 批注列表内容 -->
            <div class="annotation-list-content">
                <div v-for="(annotation, index) in annotations" :key="index" class="annotation-item"
                    :class="{ 'active': activeMarker === index }" @mouseenter="activeMarker = index"
                    @mouseleave="activeMarker = -1">
                    <div class="annotation-header">
                        <span class="annotation-number">{{ index + 1 }}</span>
                        <span class="annotation-author">{{ annotation.author }}</span>
                        <span class="annotation-time">{{ formatTime(annotation.time) }}</span>
                        <a-button type="link" size="small" @click="deleteAnnotation(index)" v-if="allowDelete">
                            <template #icon><delete-outlined /></template>
                        </a-button>
                    </div>
                    <div class="annotation-text">{{ annotation.text }}</div>
                    <div class="annotation-selection">
                        <span class="text">{{ annotation.selection }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右键菜单 -->
        <a-dropdown :open="contextMenuVisible" :trigger="['contextmenu']" @openChange="handleContextMenuVisibleChange">
            <template #overlay>
                <a-menu>
                    <a-menu-item key="1" @click="showAddAnnotationModal">
                        <template #icon><form-outlined /></template>
                        添加批注
                    </a-menu-item>
                </a-menu>
            </template>
            <div class="context-menu-trigger" :style="contextMenuStyle"></div>
        </a-dropdown>

        <!-- 添加批注的弹出框 -->
        <a-modal v-model:open="modalVisible" title="添加批注" @ok="handleAddAnnotation" @cancel="handleCancelAdd"
            :okText="'确认'" :cancelText="'取消'">
            <a-form :model="formState" layout="vertical">
                <a-form-item label="批注内容">
                    <a-textarea v-model:value="formState.text" :rows="4" placeholder="请输入批注内容" />
                </a-form-item>
                <a-form-item label="选中内容" v-if="selectedText">
                    <div class="selected-text">{{ selectedText }}</div>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, watch, onUnmounted, nextTick } from 'vue';
import { DeleteOutlined, FormOutlined, RightOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

export default defineComponent({
    name: 'SkAnnotation',
    components: {
        DeleteOutlined,
        FormOutlined,
        RightOutlined
    },
    props: {
        author: {
            type: String,
            default: '匿名用户'
        },
        allowDelete: {
            type: Boolean,
            default: true
        },
        // 添加高亮颜色属性
        highlightColor: {
            type: String,
            default: 'rgba(24, 144, 255, 0.15)' // 增加默认状态的透明度
        },
        // 添加批注数据的 prop
        modelValue: {
            type: Array,
            default: () => []
        }
    },
    emits: [
        'add',
        'delete',
        'update:modelValue'  // 添加更新数据的事件
    ],
    setup(props, { emit }) {
        // 修改为本地数据管理
        const localAnnotations = ref([]);  // 添加本地数据存储

        // 监听外部数据变化
        watch(() => props.modelValue, (newVal) => {
            if (newVal) {
                localAnnotations.value = newVal;
            }
        }, { immediate: true });

        const markers = ref([]);
        const modalVisible = ref(false);
        const selectedText = ref('');
        const selectedRange = ref(null);
        const activeMarker = ref(-1);
        const annotationRef = ref(null);
        const contextMenuVisible = ref(false);
        const contextMenuPosition = ref({ x: 0, y: 0 });
        const contentRef = ref(null);

        const formState = reactive({
            text: ''
        });

        // 添加收缩状态
        const isCollapsed = ref(false);

        // 切换收缩状态
        const toggleCollapse = () => {
            isCollapsed.value = !isCollapsed.value;
        };

        // 处理鼠标抬起事件，保存选中内容
        const handleMouseUp = () => {
            const selection = window.getSelection();
            const text = selection.toString().trim();

            if (text) {
                const range = selection.getRangeAt(0);
                const contentRect = contentRef.value.getBoundingClientRect();

                // 获取选区的位置信息
                const rects = range.getClientRects();
                // 获取第一个矩形区域
                const rect = rects[0];

                // 计算相对于内容区域的位置
                const position = {
                    top: rect.top - contentRect.top,
                    left: rect.left - contentRect.left,
                    width: rect.width,
                    height: rect.height
                };

                selectedText.value = text;
                selectedRange.value = {
                    rect: rect,
                    text: text,
                    position: position,
                    start: range.startOffset,
                    end: range.endOffset
                };
            }
        };

        // 处理右键菜单
        const handleContextMenu = (event) => {
            if (selectedText.value) {
                contextMenuPosition.value = {
                    x: event.clientX,
                    y: event.clientY
                };
                contextMenuVisible.value = true;
            }
        };

        // 右键菜单样式
        const contextMenuStyle = computed(() => ({
            position: 'fixed',
            left: `${contextMenuPosition.value.x}px`,
            top: `${contextMenuPosition.value.y}px`,
            zIndex: 1000
        }));

        // 处理右键菜单显示状态变化
        const handleContextMenuVisibleChange = (visible) => {
            contextMenuVisible.value = visible;
            if (!visible) {
                // 如果不是点击"添加批注"导致的关闭，则清除选中内容
                if (!modalVisible.value) {
                    selectedText.value = '';
                    selectedRange.value = null;
                }
            }
        };

        // 显示添加批注弹窗
        const showAddAnnotationModal = () => {
            contextMenuVisible.value = false;
            modalVisible.value = true;
        };

        // 获取标记样式
        const getMarkerStyle = (marker) => {
            return {
                top: `${marker.top}px`,
                left: `${marker.left}px`
            };
        };

        // 添加批注
        const handleAddAnnotation = () => {
            if (!formState.text || !selectedRange.value || !contentRef.value) return;

            const annotation = {
                id: Date.now(),
                author: props.author,
                text: formState.text,
                time: new Date().toISOString(),
                selection: selectedText.value,
                position: selectedRange.value.position,
                selectionStart: selectedRange.value.start,
                selectionEnd: selectedRange.value.end
            };

            // 更新本地数据
            localAnnotations.value = [...localAnnotations.value, annotation];
            // 触发更新事件
            emit('update:modelValue', localAnnotations.value);
            emit('add', annotation);

            // 重置表单
            formState.text = '';
            selectedText.value = '';
            selectedRange.value = null;
            modalVisible.value = false;
        };

        // 取消添加
        const handleCancelAdd = () => {
            formState.text = '';
            selectedText.value = '';
            selectedRange.value = null;
            modalVisible.value = false;
        };

        // 删除批注
        const deleteAnnotation = (index) => {
            const annotation = localAnnotations.value[index];
            // 更新本地数据
            localAnnotations.value = localAnnotations.value.filter((_, i) => i !== index);
            // 触发更新事件
            emit('update:modelValue', localAnnotations.value);
            emit('delete', annotation, index);
        };

        // 格式化时间
        const formatTime = (time) => {
            return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
        };

        // 处理批注悬停
        const handleAnnotationHover = (index) => {
            activeMarker.value = index;
        };

        // 添加重新计算位置的函数
        const recalculatePositions = () => {
            if (!contentRef.value || !localAnnotations.value.length) return;

            const contentRect = contentRef.value.getBoundingClientRect();
            const contentElement = contentRef.value.querySelector('.content-original');
            const range = document.createRange();

            // 更新每个批注的位置
            localAnnotations.value = localAnnotations.value.map(annotation => {
                // 找到对应的文本节点
                const textNodes = [];
                const walkNode = (node) => {
                    if (node.nodeType === Node.TEXT_NODE) {
                        textNodes.push(node);
                    } else {
                        node.childNodes.forEach(walkNode);
                    }
                };
                walkNode(contentElement);

                // 在文本节点中找到匹配的文本
                let foundNode = null;
                let foundStart = -1;
                for (const textNode of textNodes) {
                    const text = textNode.textContent;
                    const index = text.indexOf(annotation.selection);
                    if (index !== -1) {
                        foundNode = textNode;
                        foundStart = index;
                        break;
                    }
                }

                if (foundNode) {
                    range.setStart(foundNode, foundStart);
                    range.setEnd(foundNode, foundStart + annotation.selection.length);
                    const rect = range.getBoundingClientRect();

                    // 更新位置信息
                    return {
                        ...annotation,
                        position: {
                            top: rect.top - contentRect.top,
                            left: rect.left - contentRect.left,
                            width: rect.width,
                            height: rect.height
                        }
                    };
                }

                return annotation;
            });
        };

        // 监听窗口大小变化
        onMounted(() => {
            const handleResize = () => {
                recalculatePositions();
            };

            // 使用 ResizeObserver 监听容器大小变化
            const resizeObserver = new ResizeObserver(handleResize);
            if (contentRef.value) {
                resizeObserver.observe(contentRef.value);
            }

            // 监听窗口大小变化
            window.addEventListener('resize', handleResize);

            // 监听滚动事件
            window.addEventListener('scroll', handleResize);

            // 组件卸载时清理
            onUnmounted(() => {
                resizeObserver.disconnect();
                window.removeEventListener('resize', handleResize);
                window.removeEventListener('scroll', handleResize);
            });

            // 初始计算位置
            nextTick(() => {
                recalculatePositions();
            });
        });

        return {
            annotations: localAnnotations,  // 使用本地数据
            markers,
            modalVisible,
            selectedText,
            activeMarker,
            annotationRef,
            formState,
            contextMenuVisible,
            contextMenuStyle,
            handleMouseUp,
            handleContextMenu,
            handleContextMenuVisibleChange,
            showAddAnnotationModal,
            handleAddAnnotation,
            handleCancelAdd,
            deleteAnnotation,
            formatTime,
            getMarkerStyle,
            contentRef,
            handleAnnotationHover,
            isCollapsed,
            toggleCollapse,
            recalculatePositions
        };
    }
});
</script>

<style scoped>
.sk-annotation {
    display: flex;
    gap: 20px;
}

.sk-annotation-content {
    flex: 1;
    padding: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
}

.content-wrapper {
    position: relative;
}

.content-original {
    position: relative;
    z-index: 3;
    background: transparent;
    line-height: 1.8;
}

.highlight-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.highlight-text {
    position: absolute;
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: normal;
    user-select: none;
    z-index: 1;
    line-height: inherit;
}

.highlight-text.active {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
    z-index: 2;
}

.sk-annotation-list {
    width: 300px;
    padding: 16px;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    max-height: 500px;
    display: flex;
    flex-direction: column;
}

.sk-annotation-list.collapsed {
    width: 40px;
    padding: 16px 0;
    overflow: hidden;
}

.collapse-button {
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 11;
    transition: all 0.3s ease;
}

.collapse-button:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.collapse-button .anticon {
    transition: transform 0.3s ease;
}

.collapse-button .rotated {
    transform: rotate(180deg);
}

.annotation-list-content {
    opacity: 1;
    transition: all 0.3s ease;
    min-height: 100px;
    overflow-y: auto;
    max-height: calc(100% - 32px);
}

.annotation-list-content::-webkit-scrollbar {
    width: 6px;
}

.annotation-list-content::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
}

.annotation-list-content::-webkit-scrollbar-track {
    background-color: #f0f0f0;
    border-radius: 3px;
}

.collapsed .annotation-list-content {
    opacity: 0;
    pointer-events: none;
    transform: translateX(100%);
}

.sk-annotation-list.collapsed .annotation-list-content {
    display: none;
}

.annotation-item {
    position: relative;
    cursor: pointer;
    margin-bottom: 12px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background: #fafafa;
}

.annotation-item.active {
    border-color: #1890ff;
    background: #e6f7ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.annotation-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.annotation-number {
    width: 20px;
    height: 20px;
    background: #1890ff;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.annotation-author {
    font-weight: 500;
    margin-right: 8px;
}

.annotation-time {
    color: #999;
    font-size: 12px;
    flex: 1;
}

.annotation-text {
    margin-bottom: 8px;
    line-height: 1.5;
}

.annotation-selection {
    font-size: 12px;
    color: #666;
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
}

.selected-text {
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    color: #666;
}

.context-menu-trigger {
    position: fixed;
    width: 1px;
    height: 1px;
}
</style>