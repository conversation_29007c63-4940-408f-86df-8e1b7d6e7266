<template>
    <div class="sk-steps">
        <a-steps v-bind="stepsProps" @change="handleChange" @finish="handleFinish">
            <!-- 自定义步骤图标 -->
            <template v-if="$slots.icon" #icon>
                <slot name="icon"></slot>
            </template>

            <!-- 自定义步骤标题 -->
            <template v-if="$slots.title" #title>
                <slot name="title"></slot>
            </template>

            <!-- 自定义步骤描述 -->
            <template v-if="$slots.description" #description>
                <slot name="description"></slot>
            </template>

            <!-- 自定义步骤内容 -->
            <template v-if="$slots.progressDot" #progressDot>
                <slot name="progressDot"></slot>
            </template>

            <!-- 步骤项 -->
            <template v-for="(step, index) in normalizedSteps" :key="index">
                <a-step>
                    <!-- 自定义图标 -->
                    <template v-if="step.icon" #icon>
                        <component :is="step.icon" />
                    </template>
                    <!-- 标题 -->
                    <template #title>
                        {{ step.title }}
                    </template>
                    <!-- 描述 -->
                    <template v-if="step.description" #description>
                        {{ step.description }}
                    </template>
                    <!-- 子标题 -->
                    <template v-if="step.subTitle" #subTitle>
                        {{ step.subTitle }}
                    </template>
                </a-step>
            </template>
        </a-steps>

        <!-- 步骤内容区域 -->
        <div v-if="showContent" class="sk-steps-content">
            <slot name="content" :current="current">
                <div v-if="normalizedSteps[current]?.content">
                    {{ normalizedSteps[current].content }}
                </div>
            </slot>
        </div>

        <!-- 步骤操作区域 -->
        <div v-if="showActions" class="sk-steps-action">
            <slot name="actions" :current="current" :handlePrev="handlePrev" :handleNext="handleNext">
                <a-space>
                    <a-button v-if="current > 0" @click="handlePrev">
                        上一步
                    </a-button>
                    <a-button v-if="current < normalizedSteps.length - 1" type="primary" @click="handleNext">
                        下一步
                    </a-button>
                    <a-button v-else type="primary" @click="handleFinish">
                        完成
                    </a-button>
                </a-space>
            </slot>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    // 步骤配置
    steps: {
        type: Array,
        default: () => []
    },
    // 当前步骤
    current: {
        type: Number,
        default: 0
    },
    // 步骤条类型
    type: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'navigation', 'inline'].includes(value)
    },
    // 步骤条方向
    direction: {
        type: String,
        default: 'horizontal',
        validator: (value) => ['horizontal', 'vertical'].includes(value)
    },
    // 点状步骤条
    progressDot: {
        type: [Boolean, Function],
        default: false
    },
    // 标签放置位置
    labelPlacement: {
        type: String,
        default: 'horizontal',
        validator: (value) => ['horizontal', 'vertical'].includes(value)
    },
    // 大小
    size: {
        type: String,
        default: 'default',
        validator: (value) => ['small', 'default'].includes(value)
    },
    // 起始序号
    initial: {
        type: Number,
        default: 0
    },
    // 状态
    status: {
        type: String,
        default: 'process',
        validator: (value) => ['wait', 'process', 'finish', 'error'].includes(value)
    },
    // 是否显示内容区域
    showContent: {
        type: Boolean,
        default: true
    },
    // 是否显示操作区域
    showActions: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['update:current', 'change', 'finish', 'prev', 'next'])

// 处理步骤项，确保每个步骤都有必要的属性
const normalizedSteps = computed(() => {
    return props.steps.map(step => ({
        title: step.title || '',
        description: step.description || '',
        subTitle: step.subTitle || '',
        icon: step.icon,
        status: step.status,
        disabled: step.disabled || false,
        content: step.content || ''
    }))
})

// 步骤条属性
const stepsProps = computed(() => ({
    current: props.current,
    type: props.type,
    direction: props.direction,
    progressDot: props.progressDot,
    labelPlacement: props.labelPlacement,
    size: props.size,
    initial: props.initial,
    status: props.status
}))

// 处理步骤改变
const handleChange = (current) => {
    emit('update:current', current)
    emit('change', current)
}

// 处理完成
const handleFinish = () => {
    emit('finish')
}

// 处理上一步
const handlePrev = () => {
    if (props.current > 0) {
        emit('update:current', props.current - 1)
        emit('prev', props.current - 1)
    }
}

// 处理下一步
const handleNext = () => {
    if (props.current < normalizedSteps.value.length - 1) {
        emit('update:current', props.current + 1)
        emit('next', props.current + 1)
    }
}
</script>

<style scoped></style>