<template>
    <div class="sk-tour-wrapper">
        <a-tour v-model:current="current" :open="open" :steps="steps" :mask="mask" :type="type" :placement="placement"
            :arrow="arrow" :scrollIntoViewOptions="scrollIntoViewOptions" @close="handleClose" @finish="handleFinish"
            @change="handleChange">
            <template v-if="$slots.indicator" #indicatorNode="{ current, total }">
                <slot name="indicator" :current="current" :total="total"></slot>
            </template>
            <template v-if="$slots.cover" #cover="{ step }">
                <slot name="cover" :step="step"></slot>
            </template>
            <template v-if="$slots.description" #description="{ step }">
                <slot name="description" :step="step"></slot>
            </template>
        </a-tour>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    // 是否显示
    modelValue: {
        type: Boolean,
        default: false
    },
    // 引导步骤配置
    steps: {
        type: Array,
        default: () => []
    },
    // 是否显示遮罩
    mask: {
        type: Boolean,
        default: true
    },
    // 类型，primary或default
    type: {
        type: String,
        default: 'default'
    },
    // 引导气泡位置
    placement: {
        type: String,
        default: 'bottom'
    },
    // 当前步骤
    current: {
        type: Number,
        default: 0
    },
    // 是否显示箭头
    arrow: {
        type: Boolean,
        default: true
    },
    // 滚动配置
    scrollIntoViewOptions: {
        type: [Boolean, Object],
        default: true
    }
})

const emit = defineEmits(['update:modelValue', 'update:current', 'close', 'finish', 'change'])

const open = ref(props.modelValue)
const current = ref(props.current)

// 监听值变化
watch(
    () => props.modelValue,
    (val) => {
        open.value = val
    }
)

watch(
    () => props.current,
    (val) => {
        current.value = val
    }
)

watch(open, (val) => {
    emit('update:modelValue', val)
})

watch(current, (val) => {
    emit('update:current', val)
})

// 关闭事件
const handleClose = () => {
    open.value = false
    emit('close')
}

// 完成事件
const handleFinish = () => {
    open.value = false
    emit('finish')
}

// 步骤改变事件
const handleChange = (newCurrent) => {
    current.value = newCurrent
    emit('change', newCurrent)
}
</script>

<style scoped>
.sk-tour-wrapper {
    :deep(.ant-tour) {
        width: auto;
        max-width: 400px;
    }

    :deep(.ant-tour-content) {
        padding: 16px;
    }

    :deep(.ant-tour-title) {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    :deep(.ant-tour-description) {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        line-height: 1.5;
    }

    :deep(.ant-tour-buttons) {
        margin-top: 16px;
    }

    :deep(.ant-tour-next-btn) {
        margin-left: 8px;
    }

    :deep(.ant-tour-indicator) {
        margin-top: 12px;
    }
}
</style>