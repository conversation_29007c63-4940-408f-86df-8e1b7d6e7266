<template>
    <div class="sk-user-select">
        <!-- 已选人员标签展示区域 -->
        <div class="selected-tags" v-if="isEdit == $config.formEditType.isEdit"
            :style="{ border: showChooseUser ? '1px solid #d9d9d9' : 'none' }">
            <a-tag v-if="showChooseUser" v-for="user in selectedUsers" :key="user.id" closable
                @close="removeUser(user)">
                {{ user.name }}
            </a-tag>
            <a-button type="link" @click="showModal" v-if="showChooseUser && isEdit == $config.formEditType.isEdit">
                <plus-outlined /> 添加人员
            </a-button>
            <a-button v-if="!showChooseUser" type="primary" size="small" @click="showModal">选择人员</a-button>
        </div>

        <div v-else>
            <a-tag v-for="user in selectedUsers" :key="user.id">
                {{ user.name }}
            </a-tag>
        </div>

        <!-- 人员选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择人员" width="60%" @ok="handleOk" @cancel="handleCancel" :showOk="true"
            :showCancel="true">
            <SkUserSelectModel v-model="tempSelectedUsers" :multiple="multiple" :chooseOrNotMy="chooseOrNotMy"
                :chooseOrNotEmail="chooseOrNotEmail" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkUserSelectModel from '@/components/SkUserSelectModel/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [Array, String],
        default: () => ''
    },
    // 是否允许多选：true为多选，false为单选
    multiple: {
        type: Boolean,
        default: true
    },
    // 人员列表中是否包含自己--1.包含；其他参数不包含
    chooseOrNotMy: {
        type: [Number, String],
        default: 1
    },
    // 人员列表中是否必须绑定邮箱--1.必须；其他参数为不必要
    chooseOrNotEmail: {
        type: [Number, String],
        default: 1
    },
    attrKey: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    // 是否可编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    showChooseUser: {
        type: Boolean,
        default: true
    },
    objectId: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const tempSelectedUsers = ref([])
const selectedUsers = ref([])
const modalVisible = ref(false)

// 显示弹窗
const showModal = () => {
    // 初始化临时选择的用户为当前已选用户
    tempSelectedUsers.value = [...selectedUsers.value]
    modalVisible.value = true
}

// 处理弹窗确定
const handleOk = () => {
    // 将临时选择的用户更新到实际选中的用户
    selectedUsers.value = [...tempSelectedUsers.value]
    const userIds = selectedUsers.value.map(u => u.id)
    if (props.attrKey) {
        const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
        if (props.multiple) {
            // 多选，获取已选用户的id
            emit('update:modelValue', JSON.stringify(userIds))
            props.formData[keyMation] = selectedUsers.value
        } else {
            // 单选，获取已选用户的id
            emit('update:modelValue', userIds.length > 0 ? userIds[0] : null)
            props.formData[keyMation] = selectedUsers.value ? selectedUsers.value[0] : null
        }
    }
    emit('change', selectedUsers.value, userIds, props.objectId)
    modalVisible.value = false
}

// 处理弹窗取消
const handleCancel = () => {
    modalVisible.value = false
    // 重置临时选择的用户
    tempSelectedUsers.value = [...selectedUsers.value]
}

// 移除已选用户（标签区域）
const removeUser = (user) => {
    if (props.isEdit === $config.formEditType.isEdit) {
        selectedUsers.value = selectedUsers.value.filter(u => u.id !== user.id)
        if (props.attrKey) {
            const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
            if (props.multiple) {
                // 多选，获取已选用户的id
                const userIds = selectedUsers.value.map(u => u.id)
                emit('update:modelValue', JSON.stringify(userIds))
                props.formData[keyMation] = selectedUsers.value
            } else {
                // 单选，获取已选用户的id
                const userIds = selectedUsers.value.map(u => u.id)
                emit('update:modelValue', userIds.length > 0 ? userIds[0] : null)
                props.formData[keyMation] = selectedUsers.value ? selectedUsers.value[0] : null
            }
        }
        emit('change', selectedUsers.value)
    }
}

const setSelectedUsers = () => {
    if (props.attrKey) {
        if (proxy.$util.isNull(props.modelValue)) {
            return
        }
        const keyMation = proxy.$util.getKeyIdToMation(props.attrKey)
        if (props.multiple) {
            // 判断modelValue是否为数组对象
            if (Array.isArray(props.modelValue)) {
                emit('update:modelValue', JSON.stringify(props.modelValue))
            }
            selectedUsers.value = props.formData[keyMation] || []
        } else {
            // 判断props.formData[keyMation]是否为对象
            if (Array.isArray(props.formData[keyMation])) {
                if (Array.isArray(props.modelValue)) {
                    emit('update:modelValue', JSON.stringify(props.modelValue))
                }
                selectedUsers.value = props.formData[keyMation] || []
            } else {
                let temp = []
                temp.push(props.formData[keyMation])
                selectedUsers.value = temp
            }
        }
    }
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    setSelectedUsers()
}, { deep: true })

// 初始化
onMounted(() => {
    setSelectedUsers()
})
</script>

<style scoped>
.sk-user-select {
    width: 100%;
}

.sk-user-select .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    min-height: 32px;
    padding: 4px 11px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
}

.sk-user-select .selected-tags:hover {
    border-color: #40a9ff;
}

.sk-user-select :deep(.ant-tree) {
    background: transparent;
}
</style>