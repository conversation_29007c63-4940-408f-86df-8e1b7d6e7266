<template>
    <div class="sk-table" ref="tableWrapperRef">
        <a-table :columns="processedColumns" :data-source="dataSource" :loading="loading" :pagination="false"
            :scroll="scrollConfig" :row-selection="rowSelection" :bordered="bordered" :customRow="customRow"
            :row-key="rowKey" :expandable="expandable" :default-expand-all-rows="defaultExpandAllRows"
            :children-column-name="childrenColumnName" :indent-size="indentSize" @resizeColumn="handleResize"
            @change="handleTableChange" :locale="{
                triggerDesc: '点击降序',
                triggerAsc: '点击升序',
                cancelSort: '取消排序'
            }">
            <template #[item]="data" v-for="item in Object.keys($slots)" :key="item">
                <slot :name="item" v-bind="data"></slot>
            </template>
        </a-table>

        <SkPagination v-if="pagination" v-model:current="paginationConfig.current"
            v-model:pageSize="paginationConfig.pageSize" :total="paginationConfig.total"
            :show-size-changer="paginationConfig.showSizeChanger" :show-quick-jumper="paginationConfig.showQuickJumper"
            :show-total="paginationConfig.showTotal" @change="handlePaginationChange"
            :pageSizeOptions="['10', '20', '50', '100']" :optionsConfig="{
                showTitle: false,
                width: 100,
                placement: 'bottomLeft',
                dropdownStyle: {
                    padding: '4px 0'
                }
            }">
            <template #buildOptionText="{ value }">
                <span>{{ value }} {{ $t('common.pageSize') }}</span>
            </template>
        </SkPagination>
    </div>
</template>

<script setup>
import { computed, ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import SkPagination from '../SkPagination/index.vue'

const props = defineProps({
    columns: {
        type: Array,
        required: true,
        default: () => []
    },
    dataSource: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    pagination: {
        type: [Object, Boolean],
        default: () => $config.pagination
    },
    rowSelection: {
        type: [Object, null],
        default: null
    },
    bordered: {
        type: Boolean,
        default: false
    },
    customRow: {
        type: Function,
        default: undefined
    },
    rowKey: {
        type: [String, Function],
        default: 'id'
    },
    expandable: {
        type: Object,
        default: undefined
    },
    defaultExpandAllRows: {
        type: Boolean,
        default: false
    },
    childrenColumnName: {
        type: String,
        default: 'children'
    },
    indentSize: {
        type: Number,
        default: 15
    },
    ready: {
        type: Boolean,
        default: true
    },
    // 是否考虑弹窗内外边距
    modalPadding: {
        type: Boolean,
        default: true
    },
    // 提示信息的高度
    tipInfoHeight: {
        type: Number,
        default: 0
    }
})

const tableWrapperRef = ref(null)
// 定义 resizeObserver
let resizeObserver = null

// 计算滚动配置
const scrollConfig = computed(() => {
    // 如果还没准备好或没有 ref，返回基础配置
    if (!props.ready || !tableWrapperRef.value) {
        return { x: '100%' }
    }

    // 获取父容器高度
    const parentElement = tableWrapperRef.value.parentElement
    const parentHeight = parentElement ? parentElement.offsetHeight : 0

    // 检查是否在弹窗中，如果是则需要考虑弹窗的内外边距
    const modalPadding = props.modalPadding ? (parentElement?.closest('.ant-modal-content') ? 55 : 0) : 0 // 弹窗标题24px + 上下内边距16px*2 - 1px边框

    // 计算表格内容的总宽度
    const totalWidth = processedColumns.value.reduce((total, col) => {
        return total + (col.width || 120) // 如果没有设置宽度，默认120
    }, 0)

    // 计算表头层级数
    const getHeaderLevels = (columns) => {
        let maxLevel = 1
        const traverse = (cols, level = 1) => {
            cols.forEach(col => {
                if (col.children?.length) {
                    maxLevel = Math.max(maxLevel, level + 1)
                    traverse(col.children, level + 1)
                }
            })
        }
        traverse(processedColumns.value)
        return maxLevel
    }

    // 单个表头行高
    const headerRowHeight = 55
    // 计算总表头高度 (每一层都是headerRowHeight)
    const headerHeight = headerRowHeight * getHeaderLevels(processedColumns.value)

    // 计算表格内容区域的高度
    const searchFormHeight = parentElement.querySelector('.table-search')?.offsetHeight || 0
    const btnGroupHeight = parentElement.querySelector('.table-btn-group')?.offsetHeight || 0
    const operationsHeight = parentElement.querySelector('.table-operations')?.offsetHeight || 0
    const paginationHeight = props.pagination ? 48 : 0  // 分页器高度
    // 根据搜索框和按钮组是否存在来计算margin
    const marginHeight = (searchFormHeight == 0 ? 0 : 16) + (operationsHeight == 0 ? 0 : 16)
    const parentPaddingContentHeight = 16  // 上下padding总和

    // 计算可用的内容区域高度
    const contentHeight = parentHeight - searchFormHeight - btnGroupHeight - operationsHeight - headerHeight - paginationHeight
        - marginHeight - parentPaddingContentHeight - modalPadding - props.tipInfoHeight

    return {
        x: Math.max(totalWidth, tableWrapperRef.value?.offsetWidth || 0),
        y: contentHeight > 100 ? contentHeight : undefined
    }
})

// 监听 ready 属性变化
watch(() => props.ready, (newVal) => {
    if (newVal) {
        // 等待父组件渲染完成后再计算
        nextTick(() => {
            if (tableWrapperRef.value) {
                const parentElement = tableWrapperRef.value.parentElement
                if (parentElement) {
                    // 强制更新一次
                    nextTick()
                }
            }
        })
    }
})

// 监听父容器尺寸变化
onMounted(() => {
    nextTick(() => {
        if (tableWrapperRef.value) {
            const parentElement = tableWrapperRef.value.parentElement
            if (parentElement) {
                resizeObserver = new ResizeObserver(() => {
                    nextTick()
                })
                resizeObserver.observe(parentElement)
            }
        }
    })
})

// 组件卸载时清理
onUnmounted(() => {
    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }
})

// 初始化列的默认属性
const initColumnDefaults = (column) => {
    const defaults = {
        align: 'left',           // 默认左对齐
        ellipsis: true,          // 默认开启省略
        minWidth: 60,            // 最小宽度
        width: column.width || 120,  // 设置默认宽度
        resizable: true,         // 默认可调整宽度
        showSorterTooltip: true, // 显示排序提示
        filterMultiple: true,    // 默认多选过滤
    }

    // 处理表头分组
    if (column.children && column.children.length > 0) {
        // 如果有子列，说明是分组表头
        const childrenWidth = column.children.reduce((total, child) => {
            const childDefaults = initColumnDefaults(child)
            return total + (childDefaults.width || 120)
        }, 0)

        return {
            ...defaults,
            ...column,
            width: childrenWidth,  // 父列宽度为所有子列宽度之和
            children: column.children.map(child => initColumnDefaults(child))
        }
    }

    // 如果是操作列，设置特殊属性
    if (column.key === 'action') {
        return {
            ...defaults,
            resizable: false,    // 操作列不可调整宽度
            ellipsis: false,     // 操作列不省略
            align: 'center',     // 操作列居中对齐
            width: column.width || 150,  // 操作列默认宽度
            ...column
        }
    }

    // 如果是序号列，设置特殊属性
    if (column.key === 'index') {
        return {
            ...defaults,
            resizable: false,    // 序号列不可调整宽度
            align: 'center',     // 序号列居中对齐
            width: 60,           // 序号列固定宽度
            ...column
        }
    }

    column.key = column.dataIndex
    // 合并默认属性和列定义
    const finalColumn = {
        ...defaults,
        ...column,
        width: column.width || defaults.width  // 确保有宽度值
    }

    // 确保width是数字类型
    if (finalColumn.width) {
        finalColumn.width = parseInt(finalColumn.width, 10)
    }

    return finalColumn
}

// 使用 ref 来存储列配置
const columnsRef = ref(props.columns.map(col => initColumnDefaults(col)))

// 处理列配置
const processedColumns = computed(() => {
    const processColumn = (column) => {
        const newColumn = { ...column }

        // 如果列可调整宽度，添加 resizable 属性
        if (newColumn.resizable) {
            newColumn.resizable = {
                minWidth: newColumn.minWidth || 60,
                maxWidth: newColumn.maxWidth || 1000,
                onResize: (width) => {
                    // 找到对应的列并更新宽度
                    const targetColumn = columnsRef.value.find(col => col.key === column.key)
                    if (targetColumn) {
                        targetColumn.width = width
                    }
                }
            }
        }

        // 处理子列
        if (column.children) {
            newColumn.children = column.children.map(processColumn)
        }

        // 处理单元格配置
        if (column.onCell) {
            const originalOnCell = column.onCell
            newColumn.onCell = (record, index) => {
                const cellConfig = originalOnCell(record, index) || {}
                return {
                    ...cellConfig,
                    style: {
                        ...cellConfig.style,
                        padding: '16px',
                        textAlign: column.align || 'left',
                        backgroundColor: cellConfig.rowSpan > 1 ? '#fafafa' : undefined,
                        verticalAlign: 'middle'
                    }
                }
            }
        }

        // 添加默认的单元格配置
        if (!newColumn.onCell) {
            newColumn.onCell = () => ({
                style: {
                    padding: '16px',
                    textAlign: column.align || 'left',
                    verticalAlign: 'middle'
                }
            })
        }

        return newColumn
    }

    return columnsRef.value.map(processColumn)
})

const emit = defineEmits(['change', 'resizeColumn'])

// 分页配置
const paginationConfig = computed(() => {
    if (!props.pagination) return false

    return {
        current: props.pagination.current || 1,
        pageSize: props.pagination.pageSize || 10,
        total: props.pagination.total || 0,
        showSizeChanger: props.pagination.showSizeChanger ?? true,
        showQuickJumper: props.pagination.showQuickJumper ?? true,
        showTotal: props.pagination.showTotal || (total => `共 ${total} 条`)
    }
})

// 处理分页变化
const handlePaginationChange = (page, pageSize) => {
    emit('change', { current: page, pageSize }, null, null)
}

// 修改表格变化处理函数
const handleTableChange = (_, filters, sorter) => {
    emit('change', {
        current: paginationConfig.value.current,
        pageSize: paginationConfig.value.pageSize
    }, filters, sorter)
}

// 处理列宽调整
const handleResize = (width, column) => {
    // 找到对应的列并更新宽度
    const targetColumn = columnsRef.value.find(col => col.key === column.key)
    if (targetColumn) {
        targetColumn.width = width
        // 触发事件通知父组件
        emit('resizeColumn', width, targetColumn)
    }
}

// 监听 props.columns 的变化
watch(() => props.columns, (newColumns) => {
    columnsRef.value = newColumns.map(col => initColumnDefaults(col))
}, { deep: true })
</script>

<style lang="less" scoped>
.sk-table {
    // 确保表格容器有相对定位，作为fixed元素的参考点
    position: relative;

    // 调整固定列的层级
    :deep(.ant-table) {

        // 表格主体的z-index
        .ant-table-content {
            position: relative;
            z-index: 1;
        }

        .ant-table-cell {
            font-size: 13px;
        }

        // 固定列的z-index
        .ant-table-cell.ant-table-cell-fix-right {
            z-index: 2;
            font-size: 13px;
        }
    }

    // 分页器下拉框的z-index需要更高
    :deep(.ant-pagination) {
        position: relative;
        z-index: 11;

        .ant-pagination-options {
            position: relative;
            z-index: inherit;

            .ant-select {
                position: relative;
                z-index: inherit;

                &-dropdown {
                    position: absolute;
                    z-index: 1100 !important;
                }
            }
        }
    }

    // 如果有表格滚动条，确保不会被固定列遮挡
    :deep(.ant-table-body) {
        overflow-x: auto !important;
        overflow-y: auto !important;
    }

    // 添加分页器样式
    :deep(.ant-pagination) {
        margin-top: 16px;
        text-align: right;
    }
}
</style>