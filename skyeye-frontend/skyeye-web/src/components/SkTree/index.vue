<template>
    <div class="sk-tree-wrapper">
        <!-- 编辑模式 -->
        <template v-if="isEdit === $config.formEditType.isEdit">
            <div v-if="showSearch" class="tree-search-wrapper">
                <a-input-search v-model:value="searchValue" :placeholder="searchPlaceholder" @change="handleSearch"
                    allow-clear />
            </div>
            <a-tree v-model:expandedKeys="expandedKeys" v-model:selectedKeys="selectedKeys"
                v-model:checkedKeys="checkedKeys" :tree-data="filteredTreeData" :fieldNames="fieldNames"
                :checkable="checkable" :selectable="selectable" :show-line="showLine" :blockNode="blockNode"
                :draggable="draggable" :multiple="multiple" :defaultExpandAll="defaultExpandAll"
                :defaultExpandParent="defaultExpandParent" :autoExpandParent="autoExpandParent" :show-icon="showIcon"
                :disabled="disabled" @select="handleSelect" @check="handleCheck" @expand="handleExpand"
                @drop="handleDrop" @rightClick="handleRightClick" @dragenter="handleDragEnter"
                @dragover="handleDragOver" @dragleave="handleDragLeave" @dragstart="handleDragStart"
                @dragend="handleDragEnd">
                <template #title="nodeData">
                    <slot name="title" :title="getNodeTitle(nodeData)" :level="nodeData.level" :node="nodeData">
                        <span v-if="searchValue && showHighlight" v-html="highlightText(getNodeTitle(nodeData))"></span>
                        <span v-else>{{ getNodeTitle(nodeData) }}</span>
                    </slot>
                </template>

                <template #icon="nodeData">
                    <slot name="icon"
                        :hasChildren="!!nodeData.node?.children?.length || !!nodeData.node?.dataRef?.children?.length" />
                </template>
            </a-tree>
        </template>

        <!-- 只读模式 -->
        <template v-else>
            <div class="sk-detail-readonly">
                <template v-for="(node, index) in getSelectedNodes" :key="index">
                    {{ getNodeTitle(node) }}<template v-if="index < getSelectedNodes.length - 1">,</template>
                </template>
            </div>
        </template>
    </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
    // 树形数据
    data: {
        type: Array,
        default: () => []
    },
    // 是否显示复选框
    checkable: {
        type: Boolean,
        default: false
    },
    // 是否可选择
    selectable: {
        type: Boolean,
        default: true
    },
    // 是否显示连接线
    showLine: {
        type: Boolean,
        default: false
    },
    // 是否节点占据一行
    blockNode: {
        type: Boolean,
        default: true
    },
    // 字段名称配置
    fieldNames: {
        type: Object,
        default: () => ({
            title: 'title',
            key: 'key',
            children: 'children'
        })
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: () => $config.formEditType.isEdit
    },
    // 是否可拖拽
    draggable: {
        type: Boolean,
        default: false
    },
    // 是否支持多选
    multiple: {
        type: Boolean,
        default: false
    },
    // 是否默认展开所有树节点
    defaultExpandAll: {
        type: Boolean,
        default: false
    },
    // 是否默认展开父节点
    defaultExpandParent: {
        type: Boolean,
        default: false
    },
    // 是否自动展开父节点
    autoExpandParent: {
        type: Boolean,
        default: true
    },
    // 是否显示搜索框
    showSearch: {
        type: Boolean,
        default: false
    },
    // 搜索框占位符
    searchPlaceholder: {
        type: String,
        default: '请输入关键字搜索'
    },
    // 搜索时是否高亮匹配项
    showHighlight: {
        type: Boolean,
        default: true
    },
    // 添加默认选中的节点
    defaultCheckedKeys: {
        type: Array,
        default: () => []
    },
    // 是否显示图标
    showIcon: {
        type: Boolean,
        default: false
    },
    // 是否禁用整个树
    disabled: {
        type: Boolean,
        default: false
    },
    // 默认展开的节点 key 列表
    defaultExpandedKeys: {
        type: Array,
        default: () => []
    },
    // 是否显示根节点
    showRoot: {
        type: Boolean,
        default: false
    },
    // 根节点配置
    rootNode: {
        type: Object,
        default: () => ({})
    },
    // 双向绑定值
    modelValue: {
        type: [Array, String],
        default: () => []
    }
})

const emit = defineEmits([
    'select',
    'check',
    'expand',
    'drop',
    'rightClick',
    'dragenter',
    'dragover',
    'dragleave',
    'dragstart',
    'dragend',
    'search',
    'update:modelValue'
])

const expandedKeys = ref(props.defaultExpandedKeys)
const selectedKeys = ref([])
const checkedKeys = ref([])
const treeData = ref([])
const searchValue = ref('')

// 获取所有节点的key
const getAllKeys = (data) => {
    const keys = []
    const traverse = (nodes) => {
        nodes.forEach(node => {
            keys.push(node[props.fieldNames.key])
            if (node[props.fieldNames.children]) {
                traverse(node[props.fieldNames.children])
            }
        })
    }
    traverse(data)
    return keys
}

// 添加一个计算属性来处理根节点数据
const defaultRootNode = computed(() => {
    return {
        [props.fieldNames.title]: props.rootNode[props.fieldNames.title] || '全部',
        [props.fieldNames.key]: props.rootNode[props.fieldNames.key] || '0',
        [props.fieldNames.children || 'children']: []
    }
})

// 搜索过滤后的树数据
const filteredTreeData = computed(() => {
    let data = treeData.value

    // 如果需要显示根节点，将数据包装在根节点下
    if (props.showRoot) {
        const root = {
            [props.fieldNames.title]: defaultRootNode.value[props.fieldNames.title],
            [props.fieldNames.key]: defaultRootNode.value[props.fieldNames.key],
            [props.fieldNames.children || 'children']: data
        }
        data = [root]
    }

    // 搜索过滤
    if (!searchValue.value) return data
    return filterTreeData(data, searchValue.value)
})

// 过滤树数据
const filterTreeData = (data, keyword) => {
    const filtered = []
    const traverse = (nodes, parent) => {
        return nodes.filter(node => {
            const title = getNodeTitle({ data: node })
            const matched = title.toLowerCase().includes(keyword.toLowerCase())
            const hasChildren = node[props.fieldNames.children]

            if (hasChildren) {
                const matchedChildren = traverse(node[props.fieldNames.children], node)
                if (matchedChildren.length) {
                    node[props.fieldNames.children] = matchedChildren
                    return true
                }
            }
            return matched
        })
    }
    return traverse(data)
}

// 高亮搜索文本
const highlightText = (text) => {
    if (!searchValue.value) return text
    const index = text.toLowerCase().indexOf(searchValue.value.toLowerCase())
    if (index < 0) return text

    const before = text.substring(0, index)
    const middle = text.substring(index, index + searchValue.value.length)
    const after = text.substring(index + searchValue.value.length)
    return before + `<span class="highlight">${middle}</span>` + after
}

// 获取节点标题
const getNodeTitle = (nodeData) => {
    const titleField = props.fieldNames.title || 'title'
    return nodeData.data?.[titleField] || nodeData[titleField] || nodeData.title
}

// 事件处理函数
const handleSelect = (selectedKeys, info) => {
    emit('select', selectedKeys, info)
}

const handleCheck = (checkedKeys, info) => {
    emit('check', checkedKeys, info)
}

const handleExpand = (expandedKeys, info) => {
    emit('expand', expandedKeys, info)
}

const handleDrop = (info) => {
    const dropKey = info.node.key
    const dragKey = info.dragNode.key
    const dropPos = info.node.pos.split('-')
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])

    // 在这里处理拽逻辑
    emit('drop', { info, dropKey, dragKey, dropPosition })
}

const handleRightClick = ({ event, node }) => {
    emit('rightClick', { event, node })
}

const handleDragEnter = (info) => {
    emit('dragenter', info)
}

const handleDragOver = (info) => {
    emit('dragover', info)
}

const handleDragLeave = (info) => {
    emit('dragleave', info)
}

const handleDragStart = (info) => {
    emit('dragstart', info)
}

const handleDragEnd = (info) => {
    emit('dragend', info)
}

const handleSearch = () => {
    emit('search', searchValue.value)
}

// 获取选中的节点信息（用于只读模式显示）
const getSelectedNodes = computed(() => {
    const keys = props.checkable ? checkedKeys.value : selectedKeys.value
    if (!keys || keys.length === 0) return []
    return getNodesByKeys(keys)
})

// 根据 keys 获取节点信息
const getNodesByKeys = (keys) => {
    const nodes = []
    const findNode = (data, key) => {
        for (const node of data) {
            if (node[props.fieldNames.key] === key) {
                nodes.push(node)
                return true
            }
            if (node[props.fieldNames.children]) {
                if (findNode(node[props.fieldNames.children], key)) {
                    return true
                }
            }
        }
        return false
    }

    keys.forEach(key => {
        findNode(props.data, key)
    })
    return nodes
}

// 监听数据变化
watch(
    () => props.data,
    (newVal) => {
        treeData.value = newVal
        if (props.defaultCheckedKeys?.length) {
            checkedKeys.value = props.defaultCheckedKeys
        }
        if (props.defaultExpandAll) {
            const keys = getAllKeys(newVal)
            if (props.showRoot) {
                keys.unshift(defaultRootNode.value[props.fieldNames.key])
            }
            expandedKeys.value = keys
        }
    },
    { immediate: true, deep: true }
)

watch(
    () => props.defaultExpandedKeys,
    (newVal) => {
        expandedKeys.value = newVal
    },
    { immediate: true, deep: true }
)

// 监听数据变化,确保选中状态同步
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            // 处理字符串形式的key
            if (typeof newVal === 'string') {
                checkedKeys.value = newVal.split(',').filter(Boolean)
            } else {
                // 处理数组形式的key
                checkedKeys.value = Array.isArray(newVal) ? newVal : []
            }
        }
    },
    { deep: true }
)

// 监听选中状态变化
watch(
    () => checkedKeys.value,
    (newVal) => {
        // 始终发送数组形式的值
        emit('update:modelValue', Array.isArray(newVal) ? newVal : [])
    },
    { deep: true }
)
</script>

<style scoped>
.sk-tree-wrapper {
    width: 100%;
}

.tree-search-wrapper {
    margin-bottom: 8px;
}

:deep(.highlight) {
    color: #f50;
    background-color: transparent;
}

:deep(.ant-tree-node-content-wrapper) {
    display: flex;
    align-items: center;
    flex: 1;
    min-height: 24px;
    line-height: 24px;
}

:deep(.ant-tree-iconEle) {
    display: flex;
    align-items: center;
    margin-right: 4px;
    line-height: 24px;
}

:deep(.ant-tree-title) {
    display: inline-flex;
    align-items: center;
    width: 100%;
}

:deep(.anticon) {
    font-size: 16px;
    line-height: 1;
}

:deep(.ant-tree-treenode-disabled .ant-tree-node-content-wrapper) {
    /* 禁用节点的文字颜色 */
    color: gray !important;
}
</style>