<template>
    <div class="sk-code-editor">
        <div class="editor-toolbar" v-if="mounted && isEdit === $config.formEditType.isEdit">
            <a-button type="text" size="small" @click="formatCode" title="格式化代码">
                <template #icon><format-painter-outlined /></template>
            </a-button>
        </div>
        <div ref="editorContainer" class="editor-container" v-if="mounted && isEdit === $config.formEditType.isEdit">
        </div>
        <div v-if="isEdit === $config.formEditType.notEdit" class="sk-code-editor-readonly">
            <pre><code>{{ modelValue }}</code></pre>
        </div>
    </div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { FormatPainterOutlined } from '@ant-design/icons-vue'
import { EditorView } from '@codemirror/view'
import { EditorState } from '@codemirror/state'
import {
    lineNumbers,
    highlightActiveLineGutter,
    highlightSpecialChars,
    drawSelection,
    dropCursor,
    rectangularSelection,
    crosshairCursor,
    highlightActiveLine,
    keymap
} from "@codemirror/view"
import {
    defaultHighlightStyle,
    syntaxHighlighting,
    indentOnInput,
    bracketMatching,
    foldGutter,
    foldKeymap
} from "@codemirror/language"
import { defaultKeymap, history, historyKeymap } from "@codemirror/commands"
import { searchKeymap, highlightSelectionMatches } from "@codemirror/search"
import { autocompletion, completionKeymap } from "@codemirror/autocomplete"
import { lintKeymap } from "@codemirror/lint"
import { javascript } from '@codemirror/lang-javascript'
import { html } from '@codemirror/lang-html'
import { css } from '@codemirror/lang-css'
import { json } from '@codemirror/lang-json'
import { sql } from '@codemirror/lang-sql'
import { html_beautify, css_beautify, js_beautify } from 'js-beautify'
import { SkMessage } from '@/components/SkMessage/index.vue'

export default defineComponent({
    name: 'sk-code-editor',

    components: {
        FormatPainterOutlined
    },

    props: {
        modelValue: {
            type: String,
            default: ''
        },
        language: {
            type: String,
            default: 'html',
            validator: (value) => {
                return ['javascript', 'html', 'css', 'json', 'sql'].includes(value)
            }
        },
        placeholder: {
            type: String,
            default: ''
        },
        isEdit: {
            type: Number,
            default: () => $config.formEditType.isEdit
        }
    },

    emits: ['update:modelValue', 'change'],

    setup(props, { emit }) {
        const editorContainer = ref(null)
        let view = null
        const mounted = ref(false)

        const basicExtensions = [
            lineNumbers(),
            highlightActiveLineGutter(),
            highlightSpecialChars(),
            history(),
            drawSelection(),
            dropCursor(),
            EditorState.allowMultipleSelections.of(true),
            indentOnInput(),
            syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
            bracketMatching(),
            autocompletion(),
            rectangularSelection(),
            crosshairCursor(),
            highlightActiveLine(),
            highlightSelectionMatches(),
            keymap.of([
                ...defaultKeymap,
                ...searchKeymap,
                ...historyKeymap,
                ...foldKeymap,
                ...completionKeymap,
                ...lintKeymap
            ])
        ]

        const getLanguageSupport = (lang) => {
            switch (lang) {
                case 'javascript':
                    return javascript()
                case 'html':
                    return html()
                case 'css':
                    return css()
                case 'json':
                    return json()
                case 'sql':
                    return sql()
                default:
                    return html()
            }
        }

        const createEditor = () => {
            if (!editorContainer.value) return

            const startState = EditorState.create({
                doc: props.modelValue,
                extensions: [
                    ...basicExtensions,
                    getLanguageSupport(props.language),
                    EditorView.updateListener.of(update => {
                        if (update.docChanged) {
                            const value = update.state.doc.toString()
                            emit('update:modelValue', value)
                            emit('change', value)
                        }
                    }),
                    EditorView.theme({
                        '&': { height: '100%' },
                        '.cm-scroller': {
                            fontFamily: 'monospace',
                            fontSize: '14px'
                        }
                    }),
                    EditorView.updateListener.of(update => {
                        if (update.docChanged) {
                            const value = update.state.doc.toString()
                            emit('update:modelValue', value)
                            emit('change', value)
                        }
                    })
                ]
            })

            view = new EditorView({
                state: startState,
                parent: editorContainer.value
            })
        }

        watch(() => props.language, (newLang) => {
            if (view) {
                view.setState(EditorState.create({
                    doc: view.state.doc,
                    extensions: [
                        ...basicExtensions,
                        getLanguageSupport(newLang),
                        EditorView.updateListener.of(update => {
                            if (update.docChanged) {
                                const value = update.state.doc.toString()
                                emit('update:modelValue', value)
                                emit('change', value)
                            }
                        }),
                        EditorView.theme({
                            '&': { height: '100%' },
                            '.cm-scroller': {
                                fontFamily: 'monospace',
                                fontSize: '14px'
                            }
                        })
                    ]
                }))
            }
        })

        watch(() => props.modelValue, (newValue) => {
            if (view && props.isEdit === $config.formEditType.isEdit && newValue !== view.state.doc.toString()) {
                view.dispatch({
                    changes: {
                        from: 0,
                        to: view.state.doc.length,
                        insert: newValue
                    }
                })
            }
        })

        const formatCode = async () => {
            try {
                if (!view) return

                const currentCode = view.state.doc.toString()
                let formattedCode = currentCode

                const options = {
                    indent_size: 4,
                    indent_char: ' ',
                    max_preserve_newlines: 2,
                    preserve_newlines: true,
                    keep_array_indentation: false,
                    break_chained_methods: false,
                    indent_scripts: 'keep',
                    space_before_conditional: true,
                    unescape_strings: false,
                    jslint_happy: false,
                    end_with_newline: false,
                    wrap_line_length: 100,
                    indent_inner_html: true,
                    comma_first: false,
                    e4x: true
                }

                switch (props.language) {
                    case 'javascript':
                    case 'json':
                        formattedCode = js_beautify(currentCode, options)
                        break
                    case 'html':
                        formattedCode = html_beautify(currentCode, options)
                        break
                    case 'css':
                        formattedCode = css_beautify(currentCode, options)
                        break
                    default:
                        formattedCode = html_beautify(currentCode, options)
                }

                view.dispatch({
                    changes: {
                        from: 0,
                        to: view.state.doc.length,
                        insert: formattedCode
                    }
                })

                SkMessage.success('代码格式化成功')
            } catch (error) {
                console.error('格式化失败:', error)
                SkMessage.error('代码格式化失败')
            }
        }

        onMounted(() => {
            mounted.value = true
            nextTick(() => {
                if (props.isEdit === $config.formEditType.notEdit) return
                createEditor()
            })
        })

        onBeforeUnmount(() => {
            if (view) {
                view.destroy()
            }
            mounted.value = false
        })

        return {
            editorContainer,
            mounted,
            formatCode,
            isEdit: props.isEdit
        }
    }
})
</script>

<style lang="less" scoped>
.sk-code-editor {
    height: 400px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;

    .editor-toolbar {
        padding: 4px 8px;
        border-bottom: 1px solid #d9d9d9;
        background-color: #fafafa;

        .ant-btn {
            padding: 2px 4px;

            &:hover {
                color: #1890ff;
                background-color: rgba(24, 144, 255, 0.1);
            }
        }
    }

    .editor-container {
        height: calc(100% - 33px);
        overflow: auto;
    }
}

.sk-code-editor-readonly {
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-radius: 2px;
    height: 100%;
    overflow: auto;

    pre {
        margin: 0;
        padding: 0;

        code {
            font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    }
}
</style>