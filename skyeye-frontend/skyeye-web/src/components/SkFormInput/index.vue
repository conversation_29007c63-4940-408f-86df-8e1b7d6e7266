<template>
    <div class="sk-form-input">
        <template v-if="isDesign">
            <!-- 设计模式下的预览 -->
            <div class="design-preview">
                <div class="form-item">
                    <div class="form-label" v-if="label">{{ label }}</div>
                    <a-input :placeholder="placeholder" disabled />
                </div>
            </div>
        </template>
        <template v-else>
            <!-- 实际运行时的组件 -->
            <a-form-item :label="label" :required="required">
                <a-input v-model:value="modelValue" :placeholder="placeholder" />
            </a-form-item>
        </template>
    </div>
</template>

<script setup>
defineProps({
    isDesign: {
        type: Boolean,
        default: false
    },
    label: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: ''
    },
    required: {
        type: Boolean,
        default: false
    },
    modelValue: {
        type: [String, Number],
        default: ''
    }
})

defineEmits(['update:modelValue'])
</script>

<style lang="scss" scoped>
.sk-form-input {
    .design-preview {
        .form-item {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .form-label {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
            }
        }
    }
}
</style> 