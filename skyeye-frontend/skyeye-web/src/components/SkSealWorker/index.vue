<template>
    <div class="sk-customer-select">
        <div class="select-input" v-if="isEdit == $config.formEditType.isEdit">
            <SkInput v-model="showValue.name" :placeholder="placeholder" readonly>
                <template #suffix>
                    <plus-outlined class="add-icon" @click="showModal" />
                </template>
            </SkInput>
        </div>
        <div v-else>
            {{ showValue?.name }}
        </div>

        <!-- 工人选择弹窗 -->
        <SkModal v-model="modalVisible" title="选择工人" width="80%" :bodyStyle="{ height: '80vh' }">
            <ShowIndex pageId="FP2023082900008" @rowHandleSelect="handleSelect" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import SkModal from '@/components/SkModal/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Object],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择工人'
    },
    pageType: {
        type: String,
        default: ''
    },
    attrKey: {
        type: String,
        default: ''
    },
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit
    },
    formData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 显示值
const showValue = ref({})
// 弹窗显示控制
const modalVisible = ref(false)

// 显示弹窗
const showModal = () => {
    modalVisible.value = true
}

// 处理选择
const handleSelect = (record) => {
    showValue.value = record.userMation
    let modelValue = {}
    modelValue[props.attrKey] = showValue.value.id
    emit('update:modelValue', modelValue)
    emit('change', record)
    modalVisible.value = false
}

// 组件挂载时获取数据
onMounted(async () => {
    if (props.pageType == proxy.$config.pageType.EDIT) {
        // 详情页面和编辑页面都会走这里
        const mationKey = proxy.$util.getKeyIdToMation(props.attrKey)
        const mation = props.formData[mationKey]
        showValue.value = mation || {}
        if (showValue.value.userId) {
            let modelValue = {}
            modelValue[props.attrKey] = showValue.value.userId
            emit('update:modelValue', modelValue)
        }
    }
})
</script>

<style scoped>
.add-icon {
    cursor: pointer;
    color: #1890ff;
}

.add-icon:hover {
    color: #40a9ff;
}
</style>