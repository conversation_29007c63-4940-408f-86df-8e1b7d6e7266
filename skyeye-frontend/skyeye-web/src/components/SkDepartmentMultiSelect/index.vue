<template>
    <div class="sk-department-multi-select">
        <SkSelect v-model="selectedValue" :options="departmentOptions" :loading="loading" mode="multiple"
            :placeholder="placeholder" :isEdit="isEdit" :allowClear="allowClear" @change="handleChange"
            :formData="formData" :attrKey="attrKey" />
    </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import SkSelect from '@/components/SkSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Number, Array],
        default: () => []
    },
    placeholder: {
        type: String,
        default: '请选择部门'
    },
    allowClear: {
        type: Boolean,
        default: true
    },
    // 编辑状态
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },
    // 表单数据
    formData: {
        type: Object,
        default: () => ({})
    },
    // 属性key
    attrKey: {
        type: String,
        default: ''
    },
})

const emit = defineEmits(['update:modelValue', 'change'])

const loading = ref(false)
const departmentOptions = ref([])
const selectedValue = ref(props.modelValue)

// 监听选中值变化
watch(() => props.modelValue, (newVal) => {
    selectedValue.value = newVal
})

// 监听内部值变化
watch(selectedValue, (value) => {
    emit('update:modelValue', value)
})

// 选择变化处理
const handleChange = (value, options) => {
    selectedValue.value = value
    emit('change', value, options)
}

// 获取部门数据
const getDepartmentData = async () => {
    loading.value = true
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().reqBasePath + 'queryDepartmentListByCurrentUserBelong'
        )
        let data = res.rows || []
        departmentOptions.value = data.map(item => ({
            value: item.id,
            label: item.name
        }))
    } catch (error) {
        proxy.$message.error('获取部门数据失败')
        departmentOptions.value = []
    } finally {
        loading.value = false
    }
}

// 初始化
onMounted(() => {
    getDepartmentData()
})
</script>

<style scoped>
.sk-department-multi-select {
    width: 100%;
}
</style>