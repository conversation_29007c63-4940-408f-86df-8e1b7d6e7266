<template>
    <div class="sk-dict-wrapper">
        <div v-if="isEdit == $config.formEditType.isEdit">
            <SkRadio v-if="showType === 'radio' && isShow" v-model="value" :options="dataList" @change="handleChange" />
            <SkSelect v-if="showType === 'select' && isShow" v-model="value" :options="dataList"
                @change="handleChange" />
            <SkSelect v-if="showType === 'verificationSelect' && isShow" mode="multiple" v-model="value"
                :options="dataList" @change="handleChange" />
        </div>
        <div v-else-if="isEdit == $config.formEditType.notEdit">
            {{ showValue }}
        </div>
    </div>
</template>

<script setup>
import { getCurrentInstance, onMounted, ref, watch, computed } from 'vue'
import SkRadio from '@/components/SkRadio/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: [String, Number, Boolean, Array],
        default: undefined
    },

    // 属性key
    attrKey: {
        type: String,
        default: ''
    },

    // 显示类型
    showType: {
        type: String,
        default: ''
    },

    // 数据字典类型
    objectType: {
        type: String,
        default: ''
    },

    // 页面类型
    pageType: {
        type: String,
        default: ''
    },

    // 是否编辑
    isEdit: {
        type: Number,
        default: $config.formEditType.isEdit // 默认可编辑
    },

    formData: {},

    attrDefinitionCustom: {
        type: Object,
        default: function () {
            return {}
        }
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const value = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})
// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newVal) => {
        value.value = typeof newVal === 'number' ? String(newVal) : newVal
    },
    { immediate: true }
)

const isShow = ref(false)
const dataList = ref([])
const showValue = ref('')

const handleChange = (value, option) => {
    emit('change', value, option)
}

// 获取数据
const fetchData = async () => {
    if (props.isEdit == $config.formEditType.isEdit) {
        const rows = await proxy.$util.getEnumListByCode(props.objectType)
        const _dataList = []
        rows.forEach((item, index) => {
            _dataList.push({
                label: item.name,
                value: item.id.toString(),
                item: item,
                isDefault: item.isDefault
            })
        })

        if (props.pageType == proxy.$config.pageType.ADD) {
            if (proxy.$util.isNull(props.modelValue)) {
                const num1 = _dataList.findIndex(v => v.isDefault);
                if (num1 >= 0) {
                    value.value = _dataList[num1].value.toString()
                    handleChange(_dataList[num1].value.toString())
                }
            }
        }

        dataList.value = _dataList
        isShow.value = true
    } else {
        if (proxy.$util.isNull(props.modelValue)) {
            return
        }
        const rows = await proxy.$util.getEnumListByCode(props.objectType)
        showValue.value = rows.find(item => item.id == props.modelValue)?.name
    }
}

onMounted(() => {
    fetchData()
})

</script>

<style scoped></style>