export default {
    common: {
        hello: '你好，{name}',
        welcome: '欢迎使用',
        login: '登录',
        logout: '退出登录',
        username: '用户名',
        password: '密码',
        searchPlaceholder: '搜索菜单...',
        theme: '主题设置',
        profile: '个人信息',
        settings: '系统设置',
        currentLanguage: '当前语言',
        edit: '编辑',
        delete: '删除',
        deleteConfirm: '确定要删除这条数据吗？',
        add: '添加',
        batchAdd: '批量添加',
        addChild: '新增子节点',
        batchAddChild: '批量新增子节点',
        save: '保存',
        cancel: '取消',
        refresh: '刷新数据',
        purchaseRequisition: '采购申请',
        vehicleApplication: '用车申请',
        applicationSupplies: '用品领用申请',
        sealManageBorrow: '印章借用申请',
        sealManageRevert: '印章归还申请',
        licenceManageBorrow: '证照借用申请',
        licenceManageRevert: '证照归还申请',
        regularization: '录入申请',
        checkWorkLeave: '请假申请',
        onlineclockInformation: '线上打卡信息',
        resignation: '离职申请',
        businessTravel: '出差申请',
        workOvertime: '加班申请',
        saleOfLeave: '销假申请',
        assetRequisition: '资产领用申请',
        assetReturn: '资产归还申请',
        search: '查询',
        reset: '重置',
        action: '操作',
        design: '布局设计',
        restore: '还原',
        restoreConfirm: '确定要还原吗？',
        preview: '预览',
        pageSize: '条/页',
        serialNum: '序号',
        submitApproval: '提交审批',
        approve: '审批',
        revoke: '撤销',
        withdraw: '撤回',
        revokeConfirm: '确定要撤销吗？',
        submit: '提交',
        submitConfirm: '确认要提交吗？',
        outbound: '出库',
        inbound: '入库',
        confirm: '确定',
        gain: '获取',
        confirmMessage: '确定要执行此操作吗？',
        abandoned: '作废',
        abandonedConfirm: '确定要作废吗？',
        induction: '入职',
        enclosureUpload: '附件上传',
        pay: '支付',
        payConfirm: '确定要支付吗？',
        maintain: '保养',
        chargeoff: '核销',
        chargeoffConfirm: '确定要核销吗？',
        accept: '接收',
        acceptConfirm: '确认要接收该车间任务吗？',
        unaccept: '反接收',
        unacceptConfirm: '确认要反接收该车间任务吗？',
        processAcceptance: '工序验收',
        warehousing: '加工入库',
        removeAll: '移除所有',
        synchronousWorkflow: '同步工作流',
        placeholder: {
            select: '请选择'
        },
        datePicker: {
            placeholder: '请选择日期',
            startDate: '开始日期',
            endDate: '结束日期',
            today: '今天',
            now: '此刻',
            selectTime: '选择时间',
            selectDate: '选择日期',
            selectMonth: '选择月份',
            selectYear: '选择年份',
            selectDecade: '选择年代',
            weeks: {
                sun: '日',
                mon: '一',
                tue: '二',
                wed: '三',
                thu: '四',
                fri: '五',
                sat: '六'
            },
            months: {
                jan: '1月',
                feb: '2月',
                mar: '3月',
                apr: '4月',
                may: '5月',
                jun: '6月',
                jul: '7月',
                aug: '8月',
                sep: '9月',
                oct: '10月',
                nov: '11月',
                dec: '12月'
            },
            fullWeeks: {
                sun: '星期日',
                mon: '星期一',
                tue: '星期二',
                wed: '星期三',
                thu: '星期四',
                fri: '星期五',
                sat: '星期六'
            }
        },
        approvalHistory: '审批历史',
        flowchart: '流程图',
        basicInfo: '基本信息',
        canclePublish: '取消发布',
        canclePublishConfirm: '确定要取消发布吗？',
        publish: '发布',
        publishConfirm: '确定要发布吗？',
        flowchartDesign: '流程设计',
        versionUpgrade: '版本升级',
        versionUpgradeConfirm: '确定要版本升级吗？',
        exportXml: '导出XML',
        published: '已发布',
        unpublished: '未发布',
        perform: '执行',
        performConfirm: '确定要执行吗？',
        close: '关闭',
        closeConfirm: '确定要关闭吗？',
        layAside: '搁置',
        layAsideConfirm: '确定要搁置吗？',
        recovery: '恢复',
        recoveryConfirm: '确定要恢复吗？',
        details: '详情',
        contract: '合同',
        contacts: '联系人',
        opportunity: '商机',
        followUp: '跟单',
        documents: '文档',
        discussion: '讨论贴',
        revisit: '回访',
        payment: '回款',
        invoiceTitle: '发票抬头',
        invoice: '发票',
        soldProducts: '已销售产品',
        team: '团队',
        stateChange: '状态变更',
        stateChangeConfirm: '确定要变更状态吗？',
        selectProvince: '请选择省份',
        selectCity: '请选择城市',
        selectDistrict: '请选择区县',
        selectTown: '请选择乡镇',
        inputDetailAddress: '请输入详细地址',
        download: '下载',
        draftDocument: '起草公文',
        transferPurchaseOrder: '转采购订单',
        batchCopy: '批量复制',
        resultAndSummary: '成果和总结',
        service: '服务',
        milestone: '里程碑',
        task: '项目任务',
        completed: '完成',
        completedConfirm: '确定要完成吗？',
        gantt: '甘特图',
        addSubTask: '新增子任务',
        sign: '签到',
        signRecord: '签到记录',
        signConfirm: '确定要签到吗？',
        faultInfo: '故障信息',
        serviceEvaluation: '服务评价',
        evaluate: '评价',
        feedBack: '信息反馈',
        createName: '创建人',
        createTime: '创建时间',
        lastUpdateName: '修改人',
        lastUpdateTime: '修改时间',
        operation: '操作',
        cancelRemind: '取消提醒',
        cancelRemindConfirm: '确定要取消提醒吗？',
        setRemind: '设置提醒',
        clearYear: '清空本年度节假日',
        clearYearConfirm: '确定要清空本年度节假日吗？',
        downloadTemplate: '下载模板',
        importTemplate: '导入模板',
        deleteOperationSuccessMsg: '删除成功',
        deleteOperationFailedMsg: '删除失败',
        clearYearSuccessMsg: '清空成功',
        clearYearFailedMsg: '清空失败',
        importSuccessMsg: '导入成功',
        importFailedMsg: '导入失败',
        upload: '上传',
        enable: '启用',
        enableConfirm: '确定要启用吗？',
        disable: '禁用',
        disableConfirm: '确定要禁用吗？',
        view: '详情',
        board: '看板',
        cost: '成本',
        backup: '数据备份'
    },
    menu: {
        fileManagement: '文件管理',
        calendar: '日程',
        note: '笔记'
    },
    theme: {
        default: '默认主题',
        dark: '暗黑主题',
        light: '明亮主题',
        blue: '深蓝主题',
        green: '清新绿',
        purple: '优雅紫'
    },
    message: {
        loginSuccess: '登录成功',
        loginFailed: '登录失败',
        logoutSuccess: '退出成功',
        noPath: '该菜单没有配置路径'
    },
    account: {
        title: '账户选择器',
        placeholder: '请选择账户',
        admin: '管理员账户',
        test: '测试账户',
        normal: '普通用户账户'
    },
    route: {
        AccountExample: '账户选择器',
        ColorPickerExample: '颜色选择器',
        SliderExample: '滑块',
        RateExample: '评分',
        CollapseExample: '折叠面板',
        BadgeExample: '徽标数',
        FormExample: '表单组件'
    },
    formDesign: {
        basicComponents: '基础组件',
        layoutComponents: '布局组件',
        dragTip: '拖动组件到画布中',
        componentProps: '组件属性',
        input: '输入框',
        switch: '开关',
        select: '下拉选择',
        inputNumber: '数字输入框',
        datePicker: '日期选择器',
        timePicker: '时间选择器',
        dateTimePicker: '日期时间选择器',
        upload: '上传组件',
        uploadTip: '拖动文件到画布中',
        number: '数字组件',
        checkbox: '复选框',
        date: '日期组件',
        grid: '栅格布局',
        tabs: '标签页',
        selectComponentTip: '请选择一个组件来编辑其属性'
    },
    erp: {
        purchaseOrder: {
            transfer: '转采购入库',
            return: '转采购退货',
            delivery: '转到货单'
        },
        purchaseRequest: {
            inquiry: '询价',
            pricing: '定价',
            contract: '转采购合同'
        },
        depotOut: {
            outbound: '出库'
        },
        purchaseDeliveryNote: {
            qualityInspection: '转质检单'
        },
        purchaseQualityInspection: {
            salesReturn: '转退货单'
        }
    },
    erpProduce: {
        productionPlan: {
            transferProduct: '转生产计划',
            transferPurchase: '转采购订单'
        },
        erpReturnPick: {
            transferReturn: '转退料入库'
        },
        erpPatchPick: {
            transferPatch: '转补料出库'
        },
        erpRequisitionPick: {
            transferRequisition: '转领料出库'
        },
        materialsAwaitingConfirmation: {
            materialReceipt: '物料接收',
            materialReturn: '物料退货'
        },
        erpProductionList: {
            turnConversionProcessingOrder: '转加工单',
            turnEntireOrderToOutsourcing: '转整单委外'
        },
        wholeOutList: {
            wholeOutToPut: '转采购入库',
            wholeOutToReturn: '转采购退货',
            wholeOutToArrival: '转到货单'
        },
        salesOrder: {
            turnSales: '转销售出库',
            turnReturns: '转销售退货',
            turnToPreProductionPlan: '转出货计划'
        }
    },

    school: {
        examDesign: {
            copy: '复制',
            sign: '设计',
            publish: '发布',
            questionBankSelection: '选择题库',
            addPaper: '新增试卷',
            savePaper: '保存试卷',
            endExam: '结束考试',
            endExamConfirm: '确定要结束考试吗？',
            examPublishConfirm: '确定要发布考试吗？',
            removePageTagsConfirm: '确定要删除分页标签吗？',
            viewTestDetail: '查看试卷详情',
            readOver: '批阅',
            examinationInformation: '试卷信息'
        },
        schoolQuestionBank: {
            addRadio: '单选题',
            addCheckBox: '多选题',
            addFillblank: '填空题',
            addScore: '评分题',
            addOrderby: '排序题',
            addMultiFillblank: '多项填空题',
            addChenradio: '矩阵单选题',
            addChencheckbox: '矩阵多选题',
            addChenscore: '矩阵评分题',
            addChenfbk: '矩阵填空题',
            eduCationSelect: '知识点选择',
            addOption: '添加选项',
            addColumnOption: '添加列选项',
            addRowOption: '添加行选项',
            batchAddColumnOption: '批量添加列',
            batchAddRowOption: '批量添加行',
            batchAddOption: '批量添加'
        },
        subjectManage: {
            list: '班级',
            manage: '管理',
            attendance: '考勤',
            attendanceDetails: '考勤情况',
            manage: '管理',
            chapter: '章节',
            knowledge: '知识点',
            courseware: '互动课件',
            homework: '作业',
            material: '资料',
            commentdetails: '评论详情',
            confirmSituation: '确认情况',
            announcement: '公告',
            topic: '话题',
            noConfirm: '未确认',
            hasConfirm: '已确认',
            hasSubmit: '已提交',
            noSubmit: '未提交'
        }
    },
    shop: {
        orderComment: {
            reply: '回复'
        }
    },
    allactiviti: {
        alltodopossess: {
            activation: '激活',
            pending: '挂起',
            activationConfirm: '确认激活该流程吗？',
            pendingConfirm: '确认挂起该流程吗？'
        },
        processedProcess: {
            refresh: '刷新流程图',
            refreshConfirm: '确认重新生成流程图吗？'
        }
    },
    boss: {
        bossPersonRequire: {
            setperson: '设置负责人'
        },
        bossInterviewArrangement: {
            arrangeInterviewer: "安排面试官"
        },
        bossInterviewer: {
            interview: '面试'
        }
    },
    userauth: {
        sysEveUser: {
            passwordEdit: '重置密码',
            bindRole: '绑定角色',
            resetDate: '重置有效期'
        },
        sysEveUserStaff: {
            leave: '离职',
            turnTeacherConfirm: '转教职工',
            archives: '员工档案',
            family: '家庭情况',
            education: '教育信息',
            jobResume: '工作履历',
            language: '语种能力',
            certificate: '证书信息',
            rewardPunish: '奖惩信息',
            contract: '合同信息'
        }
    },
    system: {
        menu: {
            permission: '权限点'
        }
    },
    wages: {
        wagesStaffMation: {
            wagesDesign: '薪资变更',
            assetsDesign: '薪资设定'
        }
    },
    oa: {
        checkWork: {
            title: '考勤打卡',
            shift: '班次',
            workdays: '工作日',
            clockIn: '上班打卡',
            clockOut: '下班打卡',
            am: '上午',
            pm: '下午',
            oddWeek: '单周',
            evenWeek: '双周',
            workEveryDay: '每周的当天都工作',
            restEveryDay: '每周的当天都休假',
            singleWeekWork: '单周上班，双周休假',
            clockTip: '（打卡时间以服务器端时间为准。由于通过网络传递，实际打卡时间可能与上面显示的时间有几秒钟的差异）',
            notClockIn: '您还未进行上班打卡',
            notClockOut: '您还未进行下班打卡',
            absentToday: '已下班(无法打卡)，您今日矿工一天哦',
            clockComplete: '您已完成今日的打卡任务',
            holidayNoNeed: '今天是节假日，无需打卡',
            normalTip1: '1.（法定上班时间为{clockIn}，晚于该时间即为迟到。法定下班时间为{clockOut}，早于该时间即为早退）',
            normalTip2: '2.下班后还未进行上班打卡的员工，则视为旷工，当天则不允许打卡，如果当天忘记打卡，需第二天向领导进行申诉申请，并说明原因。',
            overtimeTip1: '1.（加班日上班时间为{clockIn}，晚于该时间即为迟到。法定下班时间为{clockOut}，早于该时间即为早退）',
            overtimeTip2: '2.下班后还未进行上班打卡的员工，则视为旷工，当天则不允许打卡，如果当天忘记打卡，系统不会结算该天的工作。',
            earlyClockOutConfirm: '当前时间为<span class="state-down">{time}</span>，确定现在打下班卡吗？',
            clockOutTitle: '下班打卡',
            clockInSuccess: '上班打卡成功',
            clockOutSuccess: '下班打卡成功',
            fetchEventsFailed: '获取日历数据失败',
            fetchShiftsFailed: '获取班次数据失败',
            fetchStatusFailed: '获取打卡状态失败',
            monday: '一',
            tuesday: '二',
            wednesday: '三',
            thursday: '四',
            friday: '五',
            saturday: '六',
            sunday: '日'
        }
    }
}