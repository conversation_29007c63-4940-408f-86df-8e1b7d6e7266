export default {
    common: {
        hello: 'Hello, {name}',
        welcome: 'Welcome',
        login: 'Login',
        logout: 'Logout',
        username: '<PERSON><PERSON><PERSON>',
        password: 'Password',
        searchPlaceholder: 'Search menu...',
        theme: 'Theme Settings',
        profile: 'Profile',
        settings: 'Settings',
        currentLanguage: 'Current Language',
        edit: 'Edit',
        delete: 'Delete',
        deleteConfirm: 'Are you sure you want to delete this data?',
        add: 'Add',
        batchAdd: 'Batch Add',
        addChild: 'Add Child',
        batchAddChild: 'Batch Add Child',
        save: 'Save',
        cancel: 'Cancel',
        refresh: 'Refresh Data',
        purchaseRequisition: 'Purchase Requisition',
        vehicleApplication: 'vehicle Application',
        applicationSupplies: 'Application for Supplies Requisition',
        sealManageBorrow: 'Seal Borrowing Application',
        sealManageRevert: 'Seal Return Application',
        licenceManageBorrow: 'License Borrowing Application',
        licenceManageRevert: 'Licenses Return Application',
        regularization: 'Enter application',
        checkWorkLeave: 'Leave application',
        resignation: 'Resignation application',
        businessTravel: 'Business trip application',
        workOvertime: 'Overtime application',
        saleOfLeave: 'Application for vacation cancellation',
        assetRequisition: 'Asset Requisition Application',
        assetReturn: 'Asset Return Application',
        search: 'Search',
        reset: 'Reset',
        action: 'Action',
        design: 'Design',
        restore: 'Restore',
        restoreConfirm: 'Are you sure you want to restore?',
        preview: 'Preview',
        pageSize: 'page',
        serialNum: 'Serial Number',
        submitApproval: 'Submittal Approval',
        approve: 'Approve',
        revoke: 'Revoke',
        withdraw: 'Withdraw',
        revokeConfirm: 'Are you sure you want to revoke?',
        submit: 'Submit',
        submitConfirm: 'Are you sure to submit?',
        outbound: 'Outbound',
        inbound: 'Inbound',
        confirm: 'Confirm',
        gain: 'Gain',
        confirmMessage: 'Are you sure you want to execute this operation?',
        abandoned: 'Abandoned',
        abandonedConfirm: 'Are you sure you want to abandoned?',
        induction: 'Induction',
        enclosureUpload: 'Attachment Upload',
        pay: 'Pay',
        payConfirm: 'Are you sure to pay?',
        maintain: 'Maintain',
        chargeoff: 'Chargeoff',
        chargeoffConfirm: 'Are you sure you want to write it off?',
        accept: 'accept',
        acceptConfirm: 'Are you sure you want to receive the workshop task?',
        unaccept: 'Anti acceptance',
        unacceptConfirm: 'Are you sure you want to reject the workshop task?',
        processAcceptance: 'Process acceptance',
        warehousing: 'Processing and warehousing',
        removeAll: 'Remove All',
        synchronousWorkflow: 'Synchronous Workflow',
        placeholder: {
            select: 'Please select'
        },
        datePicker: {
            placeholder: 'Please select date',
            startDate: 'Start Date',
            endDate: 'End Date',
            today: 'Today',
            now: 'Now',
            selectTime: 'Select Time',
            selectDate: 'Select Date',
            selectMonth: 'Select Month',
            selectYear: 'Select Year',
            selectDecade: 'Select Decade',
            weeks: {
                sun: 'Sun',
                mon: 'Mon',
                tue: 'Tue',
                wed: 'Wed',
                thu: 'Thu',
                fri: 'Fri',
                sat: 'Sat'
            },
            months: {
                jan: 'Jan',
                feb: 'Feb',
                mar: 'Mar',
                apr: 'Apr',
                may: 'May',
                jun: 'Jun',
                jul: 'Jul',
                aug: 'Aug',
                sep: 'Sep',
                oct: 'Oct',
                nov: 'Nov',
                dec: 'Dec'
            },
            fullWeeks: {
                sun: 'Sunday',
                mon: 'Monday',
                tue: 'Tuesday',
                wed: 'Wednesday',
                thu: 'Thursday',
                fri: 'Friday',
                sat: 'Saturday'
            }
        },
        approvalHistory: 'Approval History',
        flowchart: 'Flowchart',
        basicInfo: 'Basic Information',
        canclePublish: 'Cancel Publish',
        canclePublishConfirm: 'Are you sure you want to cancel publish?',
        publish: 'Publish',
        publishConfirm: 'Are you sure you want to publish?',
        flowchartDesign: 'Flowchart Design',
        versionUpgrade: 'Version Upgrade',
        versionUpgradeConfirm: 'Are you sure you want to version upgrade?',
        exportXml: 'Export XML',
        published: 'Published',
        unpublished: 'Unpublished',
        perform: 'Perform',
        performConfirm: 'Are you sure you want to perform?',
        close: 'Close',
        closeConfirm: 'Are you sure you want to close?',
        layAside: 'Lay Aside',
        layAsideConfirm: 'Are you sure you want to lay aside?',
        recovery: 'Recovery',
        recoveryConfirm: 'Are you sure you want to recovery?',
        details: 'Details',
        contract: 'Contract',
        contacts: 'Contacts',
        opportunity: 'Opportunity',
        followUp: 'Follow Up',
        documents: 'Documents',
        discussion: 'Discussion',
        revisit: 'Revisit',
        payment: 'Payment',
        invoiceTitle: 'Invoice Title',
        invoice: 'Invoice',
        soldProducts: 'Sold Products',
        team: 'Team',
        stateChange: 'State Change',
        stateChangeConfirm: 'Are you sure you want to change the state?',
        selectProvince: 'Please select province',
        selectCity: 'Please select city',
        selectDistrict: 'Please select district',
        selectTown: 'Please select town',
        inputDetailAddress: 'Please input detail address',
        download: 'Download',
        draftDocument: 'Draft Document',
        transferPurchaseOrder: 'Transfer Purchase Order',
        batchCopy: 'Batch Copy',
        resultAndSummary: 'Result And Summary',
        service: 'Service',
        milestone: 'Milestone',
        task: 'Task',
        completed: 'Completed',
        completedConfirm: 'Are you sure to complete?',
        gantt: 'Gantt',
        addSubTask: 'Add Sub Task',
        sign: 'Sign',
        signRecord: 'Sign Record',
        signConfirm: 'Are you sure to sign?',
        faultInfo: 'Fault Information',
        serviceEvaluation: 'Service Evaluation',
        evaluate: 'Evaluate',
        feedBack: 'Feed Back',
        createName: 'Create Name',
        createTime: 'Create Time',
        lastUpdateName: 'Last Update Name',
        lastUpdateTime: 'Last Update Time',
        operation: 'Operation',
        cancelRemind: 'Cancel Remind',
        cancelRemindConfirm: 'Are you sure you want to cancel remind?',
        setRemind: 'Set Remind',
        clearYear: 'Clear Year',
        clearYearConfirm: 'Are you sure you want to clear year?',
        downloadTemplate: 'Download Template',
        importTemplate: 'Import Template',
        deleteOperationSuccessMsg: 'Delete Success',
        deleteOperationFailedMsg: 'Delete Failed',
        clearYearSuccessMsg: 'Clear Success',
        clearYearFailedMsg: 'Clear Failed',
        importSuccessMsg: 'Import Success',
        importFailedMsg: 'Import Failed',
        upload: 'Upload',
        enable: 'Enable',
        enableConfirm: 'Are you sure you want to enable?',
        disable: 'Disable',
        disableConfirm: 'Are you sure you want to disable?',
        view: 'Details',
        board: 'Board',
        cost: 'Cost',
        backup: 'Database Backup'
    },
    menu: {
        fileManagement: 'File Management',
        calendar: 'Calendar',
        note: 'Notes'
    },
    theme: {
        default: 'Default Theme',
        dark: 'Dark Theme',
        light: 'Light Theme',
        blue: 'Deep Blue Theme',
        green: 'Fresh Green Theme',
        purple: 'Elegant Purple Theme'
    },
    message: {
        loginSuccess: 'Login successful',
        loginFailed: 'Login failed',
        logoutSuccess: 'Logout successful',
        noPath: 'This menu has no configured path'
    },
    account: {
        title: 'Account Selector',
        placeholder: 'Please select account',
        admin: 'Admin Account',
        test: 'Test Account',
        normal: 'Normal User Account'
    },
    route: {
        AccountExample: 'Account Selector',
        ColorPickerExample: 'Color Picker',
        SliderExample: 'Slider',
        RateExample: 'Rate',
        CollapseExample: 'Collapse',
        BadgeExample: 'Badge',
        FormExample: 'Form Components'
    },
    formDesign: {
        basicComponents: 'Basic Components',
        layoutComponents: 'Layout Components',
        dragTip: 'Drag components to the canvas',
        componentProps: 'Component Properties',
        input: 'Input',
        switch: 'Switch',
        select: 'Select',
        inputNumber: 'Input Number',
        datePicker: 'Date Picker',
        timePicker: 'Time Picker',
        dateTimePicker: 'Date Time Picker',
        upload: 'Upload',
        uploadTip: 'Drag files to the canvas',
        number: 'Number',
        checkbox: 'Checkbox',
        date: 'Date',
        grid: 'Grid',
        tabs: 'Tabs',
        selectComponentTip: 'Please select a component to edit its properties'
    },
    erp: {
        purchaseOrder: {
            transfer: 'To Inbound',
            return: 'To Return',
            delivery: 'To Delivery'
        },
        purchaseRequest: {
            inquiry: 'Inquiry',
            pricing: 'Pricing',
            contract: 'To Purchase Contract'
        },
        depotOut: {
            outbound: 'Outbound'
        },
        purchaseDeliveryNote: {
            qualityInspection: 'Transfer Inspection Form'
        },
        purchaseQualityInspection: {
            salesReturn: 'Repurchase Note'
        }
    },
    erpProduce: {
        productionPlan: {
            transferProduct: 'Transfer production plan',
            transferPurchase: 'Transfer purchase order'
        },
        erpReturnPick: {
            transferReturn: 'Transfer return warehouse'
        },
        erpPatchPick: {
            transferPatch: 'Transfer patch out warehouse'
        },
        erpRequisitionPick: {
            transferRequisition: 'Transfer requisition out warehouse'
        },
        materialsAwaitingConfirmation: {
            materialReceipt: 'Material receipt',
            materialReturn: 'Material return'
        },
        erpProductionList: {
            turnConversionProcessingOrder: 'Turn conversion processing order',
            turnEntireOrderToOutsourcing: 'Turn entire order to outsourcing'
        },
        wholeOutList: {
            wholeOutToPut: 'Turn purchase warehouse',
            wholeOutToReturn: 'Turn purchase return',
            wholeOutToArrival: 'Turn delivery'
        },
        salesOrder: {
            turnSales: 'Turn to sales outbound',
            turnReturns: 'Turn to sales return',
            turnToPreProductionPlan: 'Turn to pre-production plan'
        }
    },

    school: {
        examDesign: {
            copy: 'Copy',
            sign: 'Sign',
            publish: 'Publish',
            questionBankSelection: 'Question Bank Selection',
            addPaper: 'Add Paper',
            savePaper: 'Save Paper',
            endExam: 'End Exam',
            endExamConfirm: 'Are you sure you want to end the exam?',
            examPublish: 'Publish Exam',
            examPublishConfirm: 'Are you sure you want to publish the exam?',
            viewTestDetail: 'ViewTestDetail',
            readOver: 'ReadOver',
            examinationInformation: 'ExaminationInformation'
        },
        schoolQuestionBank: {
            addRadio: 'AddRadio',
            addCheckBox: 'AddCheckBox',
            addFillblank: 'AddFillblank',
            addScore: 'AddScore',
            addOrderby: 'AddOrderby',
            addMultiFillblank: 'AddMultiFillblank',
            addChenradio: 'AddChenradio',
            addChencheckbox: 'AddChencheckbox',
            addChenscore: 'AddChenscore',
            addChenfbk: 'AddChenfbk',
            eduCationSelect: 'EducationSelect',
            addOption: 'addOption',
            addColumnOption: 'AddColumnOption',
            addRowOption: 'AddRowOption',
            batchAddColumnOption: 'BatchAddColumnOption',
            batchAddRowOption: 'BatchAddRowOption',
            batchAddOption: 'BatchAddOption',
            removePageTagsConfirm: 'Are you sure you want to remove page tags?'
        },
        subjectManage: {
            list: 'List',
            manage: 'Manage',
            attendance: 'Attendance',
            attendanceDetails: 'Attendance Details',
            manage: 'Manage',
            chapter: 'Chapter',
            knowledge: 'Knowledge',
            courseware: 'Courseware',
            commentdetails: 'Commentdetails',
            homework: 'Homework',
            material: 'Material',
            announcement: 'Announcement',
            confirmSituation: 'ConfirmSituation',
            topic: 'Topic',
            commentdetails: 'Commentdetails',
            noConfirm: 'NoConfirm',
            hasConfirm: 'HasConfirm',
            hasSubmit: 'HasSubmit',
            noSubmit: 'NoSubmit'
        }
    },
    shop: {
        orderComment: {
            reply: 'Reply'
        }
    },
    allactiviti: {
        alltodopossess: {
            activation: 'Activation',
            pending: 'Pending',
            activationConfirm: 'Are you sure to activate this process?',
            pendingConfirm: 'Are you sure to suspend this process?'
        },
        processedProcess: {
            refresh: 'Refresh the flowchart',
            refreshConfirm: 'Are you sure to regenerate the flowchart?'
        }
    },
    boss: {
        bossPersonRequire: {
            setperson: 'Setperson'
        },
        bossInterviewArrangement: {
            arrangeInterviewer: "ArrangeInterviewer"
        },
        bossInterviewer: {
            interview: "Interview"
        }
    },
    userauth: {
        sysEveUser: {
            passwordEdit: 'PasswordEdit',
            bindRole: 'BindRole',
            resetDate: 'ResetDate'
        },
        sysEveUserStaff: {
            leave: 'Leave',
            turnTeacherConfirm: 'TurnTeacherConfirm',
            archives: 'Archives',
            family: 'Family',
            education: 'Education',
            jobResume: 'JobResume',
            language: 'Language',
            certificate: 'Certificate',
            rewardPunish: 'RewardPunish',
            contract: ' Contract'
        }
    },
    system: {
        menu: {
            permission: 'Permission'
        }
    },
    wages: {
        wagesStaffMation: {
            wagesDesign: 'WagesDesign',
            assetsDesign: 'AssetsDesign'
        }
    },
    oa: {
        checkWork: {
            title: 'Attendance Clock',
            shift: 'Shift',
            workdays: 'Workdays',
            clockIn: 'Clock In',
            clockOut: 'Clock Out',
            am: 'AM',
            pm: 'PM',
            oddWeek: 'Odd Week',
            evenWeek: 'Even Week',
            workEveryDay: 'Work every day of the week',
            restEveryDay: 'Rest every day of the week',
            singleWeekWork: 'Work on odd weeks, rest on even weeks',
            clockTip: '(Clock time is based on server time. Due to network transmission, the actual clock time may differ from the time shown above by a few seconds)',
            notClockIn: 'You have not clocked in yet',
            notClockOut: 'You have not clocked out yet',
            absentToday: 'Off duty (cannot clock in), you are absent today',
            clockComplete: 'You have completed today\'s clock task',
            holidayNoNeed: 'Today is a holiday, no need to clock in',
            normalTip1: '1. (Official work time is {clockIn}, later than this time is considered late. Official off time is {clockOut}, earlier than this time is considered early leave)',
            normalTip2: '2. Employees who have not clocked in after work hours are considered absent, and are not allowed to clock in that day. If you forget to clock in that day, you need to appeal to your leader the next day and explain the reason.',
            overtimeTip1: '1. (Overtime work time is {clockIn}, later than this time is considered late. Official off time is {clockOut}, earlier than this time is considered early leave)',
            overtimeTip2: '2. Employees who have not clocked in after work hours are considered absent, and are not allowed to clock in that day. If you forget to clock in that day, the system will not calculate your work for that day.',
            earlyClockOutConfirm: 'The current time is <span class="state-down">{time}</span>, are you sure you want to clock out now?',
            clockOutTitle: 'Clock Out',
            clockInSuccess: 'Clock in successful',
            clockOutSuccess: 'Clock out successful',
            fetchEventsFailed: 'Failed to fetch calendar data',
            fetchShiftsFailed: 'Failed to fetch shift data',
            fetchStatusFailed: 'Failed to fetch clock status',
            monday: 'Mon',
            tuesday: 'Tue',
            wednesday: 'Wed',
            thursday: 'Thu',
            friday: 'Fri',
            saturday: 'Sat',
            sunday: 'Sun'
        }
    }
}