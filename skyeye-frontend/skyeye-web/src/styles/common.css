/* 布局相关 */
.layout {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  overflow: hidden;
}

/* 内容区域 */
.content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
  overflow: hidden;
}
/* 评论输入框 */
.comment-input {
    width: 100%;
    margin-top: 8px;
}

.page-content {
  flex: 1;
  padding: 7px 12px;
  overflow-y: auto;
  max-width: 100%;
  height: 100%;
  position: absolute;
}

.content-tag-name {
  min-width: 70px;
  /* 确保有足够的宽度显示标签名 */
  padding: 7px 15px;
  white-space: nowrap;
  /* 防止文字换行 */
  display: flex;
  align-items: center;
  margin-right: 5px;
  /* 与输入框保持间距 */
}

/* 版权信息 */
.layout-copyright {
  text-align: center;
  padding: 4px;
  color: #666;
  font-size: 12px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  line-height: 1;
}

.login-copyright {
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1;
}

/* 表格相关 */
.table-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 操作栏 */
.action-bar {
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

/* 常用工具类 */
.ml-10 {
  margin-left: 10px;
}

.danger {
  color: #ff4d4f;
}

.danger:hover {
  color: #ff7875;
}

/* Ant Design Vue 组件样式覆盖 */
.ant-table-wrapper {
  height: 100%;
}

.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ant-table {
  flex: 1;
  overflow: hidden;
}

.ant-table-container {
  height: 100%;
}

.ant-table-header {
  background: #fafafa;
}

.ant-table-thead>tr>th {
  background: #fafafa !important;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
}

.ant-table-body {
  overflow-y: auto !important;
  height: calc(100% - 55px) !important;
}

.ant-table-pagination {
  position: sticky;
  bottom: 0;
  background: #fff;
  margin: 8px 0 !important;
  padding: 8px 24px;
  border-top: 1px solid #f0f0f0;
  z-index: 1;
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .header-right {
    padding-right: 16px;
  }

  .trigger {
    padding: 0 16px;
  }

  .tabs-container {
    margin: 0 8px;
  }

  .search-container {
    width: 200px;
  }
}

/* 添加 Ant Design Vue 组件的通用样式覆盖 */
:deep(.ant-input-search .ant-input-wrapper) {
  height: 32px;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 4px;
}

:deep(.ant-input-search .ant-input) {
  height: 30px;
}

:deep(.ant-btn) {
  border-radius: 8px;
}

:deep(.ant-modal-content) {
  border-radius: 8px;
}

/* 添加更多布局相关的通用样式 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 添加更多间距相关的通用样式 */
.mt-8 {
  margin-top: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-8 {
  margin-left: 8px;
}

.mr-8 {
  margin-right: 8px;
}

/* 添加更多文本相关的通用样式 */
.text-primary {
  color: #1890ff;
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #ff4d4f;
}

.text-center {
  text-align: center;
}

/* ... 其他通用样式 ... */
.sk-detail-readonly {
  display: inline-flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  /* 允许长单词换行 */
  overflow-wrap: break-word;
  /* 现代浏览器使用的属性 */
  word-break: break-word;
  /* 在合适的断点处换行 */
  white-space: normal;
  /* 允许空白处换行 */
  max-width: 100%;
  /* 确保不会超出容器 */
}

.sk-detail-readonly .sk-detail-placeholder {
  color: rgba(0, 0, 0, 0.25);
}

.sk-detail-readonly span {
  display: inline-block;
  width: 100%;
}

.sk-detail-readonly img {
  max-width: 100% !important;
}

.ant-form-item-label>label {
  display: inline-block !important;
  height: 32px;
  line-height: 32px;
  text-align: right;
  vertical-align: middle;
}

/* 一行显示内容，超出部分显示... */
.line-1,
.ant-form-item-label>label,
.sk-detail-readonly span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


/* 管理页面样式开始 */
.container-manage {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #ffffff;
  height: 100%;
}

.left-tree {
  width: 260px;
  flex-shrink: 0;
}

.left-tree .list-search {
  padding: 16px 0px;
  border-bottom: 1px solid #f0f0f0;
}

.left-tree .list-tree {
  padding: 16px 0px;
  height: calc(100vh - 310px);
  overflow-y: auto;
  overflow-x: hidden;
}

.right-content {
  flex: 1;
  width: calc(100% - 270px);
}

.right-content .tab-content {
  padding: 16px 24px;
  background: #fff;
}

.right-content .content-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.right-content .content-header .header-info h2 {
  margin: 0 0 8px;
  font-size: 20px;
}

.right-content .content-header .header-info .desc {
  margin: 0;
  color: #666;
}

.right-content .ant-tabs-content {
  height: calc(100vh - 310px);
  overflow-y: auto;
}

.empty-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-btn-group {
  margin-bottom: 8px;
}

.table-search {
  margin-bottom: 16px;
  border-radius: 2px;
}

.table-operations {
  margin-bottom: 16px;
  max-width: 100%;
  text-align: right;
}

.table-other {
  margin-bottom: 16px;
  max-width: 100%;
}

.danger-link {
  color: #ff4d4f;
}

.danger-link:hover {
  color: #ff7875;
}

/* 管理页面样式结束 */

.ant-modal-centered {
  overflow-x: hidden;
}

.photo-img {
  width: 21px;
  height: 21px;
  cursor: pointer;
}

.tag-color {
  display: inline-block;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 2px;
  border: 1px solid var(--tag-color);
  color: var(--tag-color);
  background-color: color-mix(in srgb, var(--tag-color) 10%, white);
}

.layui-badge {
  display: inline-block;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 2px;
}

.layui-bg-blue {
  border: 1px solid #1890ff;
  color: #1890ff;
  background-color: color-mix(in srgb, #1890ff 10%, white);
}

.help-icon {
  margin-left: 4px;
  color: #999;
  cursor: help;
}

.help-icon:hover {
  color: #666;
}

.add-icon {
  cursor: pointer;
  color: #1890ff;
}

.add-icon:hover {
  color: #40a9ff;
}

.context-menu-wrapper {
  position: fixed;
  z-index: 1000;
  background: white;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 状态颜色 */
.state-new {
  color: blue !important;
}

.state-up {
  color: green !important;
}

.state-down {
  color: red !important;
}

.state-error {
  color: orange !important;
}

.state-success {
  color: #0e90d2 !important;
}

.stock-item {
  margin-bottom: 4px;
}

.stock-item .stock-badge {
  display: inline-block;
  padding: 4px 8px;
  color: #fff;
  background: #1890ff;
  border-radius: 2px;
}

/* 工作时间相关样式（个人中心的考勤班次） */
/* 顶部提示条 */
.top-tip-bar {
  background: #e6f4ff;
  border: 1px solid #91caff;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.tip-list {
  display: flex;
  align-items: center;
  gap: 24px;
}

.tip-item {
  display: flex;
  align-items: center;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.color-dot.work {
  background-color: #1890ff;
}

.color-dot.rest {
  background-color: #bfbfbf;
}

.color-dot.alternate {
  background-color: #fa8c16;
}

.tip-text {
  font-size: 14px;
  color: #666;
}

/* 班次卡片样式 */
.schedule-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 8px;
}

.schedule-item {
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.schedule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.schedule-title {
  font-size: 15px;
  color: #333;
}

.schedule-type-tag {
  padding: 2px 8px;
  background-color: #1890ff;
  color: #fff;
  border-radius: 2px;
  font-size: 13px;
}

.schedule-body {
  padding: 16px;
}

.time-row {
  margin-bottom: 16px;
}

.time-text {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.time-text:last-child {
  margin-bottom: 0;
}

/* 工作日组件样式 */
.workday-section {
  display: flex;
  align-items: center;
}

.workday-label {
  color: #666;
  font-size: 14px;
  margin-right: 12px;
  white-space: nowrap;
}

.workday-row {
  display: flex;
  align-items: center;
  flex: 1;
}

.workday-tags {
  display: flex;
  gap: 4px;
}

.workday-tag {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 2px;
  font-size: 13px;
  color: #fff;
  cursor: default;
}

.workday-tag.work {
  background-color: #1890ff;
}

.workday-tag.rest {
  background-color: #bfbfbf;
}

.workday-tag.alternate {
  background-color: #ffa940;
}

/* 学校 我的题库 专用样式开始 */
/* 删除的样式 隐藏边框 */
.school-delete-button-cell {
  border: none !important;
  padding: 4px !important;
  background-color: #ffffff !important;
}

/* 表格单元格通用样式 */
.school-matrix-table th,
.school-matrix-table td {
  padding: 10px;
  text-align: center;
  border: 1px solid #e8e8e8;
}

/* 矩阵容器样式 */
.school-matrix-container {
  max-height: 400px;
  overflow: auto;
  margin-bottom: 10px;
}

/* 确保表头固定 */
.school-matrix-table thead th {
  background-color: #fcfcfc;
  top: 0;
  z-index: 3;
}

/* 第一列相对固定 */
.school-first-col {
  min-width: 150px;
  width: 150px;
  position: sticky;
  background-color: #fcfcfc;
  left: 0;
  z-index: 2;
}

/* 增加表格宽度控制，防止挤压 */
.school-matrix-table {
  width: auto;
  table-layout: auto;
  border-collapse: separate;
  border-spacing: 0;
}

/* 选项单元格样式 */
.school-option-header {
  min-width: 120px;
  width: 120px;
  white-space: nowrap;
}

/* 批量添加按钮 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 我的题库专用样式结束 */

/* 地图专用样式开始 */

/* 地图容器 */
.sk-map-location {
  width: 100%;
}

.map-container {
  width: 100%;
  height: 600px;
  border: 1px solid #ccc;
  margin-top: 16px;
  position: relative;
}
.route-points-panel {
  margin-top: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 16px;
}

.route-points-list {
  max-height: 300px;
  overflow-y: auto;
}

.route-point-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.empty-text {
  color: #999;
  text-align: center;
  padding: 16px;
}

/* 地图专用样式结束 */