<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <SkAlert message="销假申请只能销已经申请的请假时间段，如果销为申请的日期，则会默认审核不通过（如果销年假，则会退还对应的年假）。" show-icon />
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号、标题" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1616239712898')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t("common.saleOfLeave") }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange" :tipInfoHeight="56">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId"
                            @afterDataLoaded="handleAfterDataLoaded" />
                    </template>

                    <template v-if="column.dataIndex === 'state'">
                        <div v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(
                            initEnumData['flowableStateEnum'], 'id', record.state, 'name')"></div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <a v-if="$config.auth('1618112063022')" @click="handleConfirmOk(record)">
                                    {{ $t("common.submitApproval") }}
                                </a>
                                <SkDivider v-if="$config.auth('1618112063022')" type="vertical" />

                                <!-- 编辑 -->
                                <a v-if="$config.auth('1618112042591')" @click="handleEdit(record, 'edit')">
                                    {{ $t("common.edit") }}
                                </a>
                                <SkDivider v-if="$config.auth('1618112042591')" type="vertical" />
                                <!-- 作废 -->
                                <SkPopconfirm v-if="$config.auth('1618112071386')"
                                    :title="$t('common.abandonedConfirm')" @confirm="handleAbandoned(record)"
                                    :okText="$t('common.abandoned')" :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.abandoned") }}</a>
                                </SkPopconfirm>
                            </template>

                            <template v-if="record.editRow == 2">
                                <!-- 撤销 -->
                                <SkPopconfirm v-if="$config.auth('1618112082353')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.revoke") }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @cell-change="handleCellChange" @afterDataLoaded="handleAfterDataLoaded">
                    <!-- 请假日期 -->
                    <template #cell-input-cancelDay="slotProps">
                        <SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
                            :formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                            :attrKey="slotProps.column.dataIndex" />
                    </template>
                    <!-- 开始时间 -->
                    <template #cell-input-cancelStartTime="slotProps">
                        <SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
                            :formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                            :attrKey="slotProps.column.dataIndex" :showNow="true" picker="timeminute" :minuteStep="30"
                            @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                    </template>
                    <!-- 结束时间 -->
                    <template #cell-input-cancelEndTime="slotProps">
                        <SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
                            :formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                            :attrKey="slotProps.column.dataIndex" :showNow="true" picker="timeminute"
                            @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)"
                            :minuteStep="30" />
                    </template>
                    <!-- 显示班次信息 -->
                    <template #cell-detail-placeholder1="slotProps">
                        <a @click.stop="handleShowWorkTime(slotProps.record)">{{ slotProps.record.placeholder1 }}</a>
                    </template>
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
            <!-- 班次信息弹窗 -->
            <SkModal v-model="workTimeModalVisible" title="班次信息">
                <LoginUserCheckWorkTime ref="workTimeRef" v-if="workTimeModalVisible" :timeId="currentTimeId" />
            </SkModal>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from "vue"
import { useI18n } from "vue-i18n"
import SkForm from "@/components/SkForm/index.vue"
import SkInput from "@/components/SkInput/index.vue"
import SkCard from "@/components/SkCard/index.vue"
import SkButton from "@/components/SkButton/index.vue"
import SkSpace from "@/components/SkSpace/index.vue"
import SkTable from "@/components/SkTable/index.vue"
import SkDivider from "@/components/SkDivider/index.vue"
import SkPopconfirm from "@/components/SkPopconfirm/index.vue"
import { SkMessage } from "@/components/SkMessage/index.vue"
import SkModal from "@/components/SkModal/index.vue"
import ShowIndex from "@/views/dsForm/show/index.vue"
import ApprovalPersonSelect from "@/views/system/submitApproval/approvalPersonSelect.vue"
import ProcessDetail from "@/views/dsForm/process/detail.vue"
import SkDatePicker from '@/components/SkDatePicker/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import LoginUserCheckWorkTime from '@/views/oa/checkWorkTime/loginUserCheckWorkTime.vue'

const { proxy } = getCurrentInstance();
const { t } = useI18n();

// 搜索表单数据
const searchForm = reactive({
    keyword: "",
});

// 表格列配置
const columns = ref([
    {
        title: t("common.serialNum"),
        dataIndex: "index",
        width: 80,
        align: "center",
        fixed: "left",
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1;
        },
    },
    {
        title: "单号",
        dataIndex: "oddNumber",
        width: 200,
        align: "center",
    },
    {
        title: "标题",
        dataIndex: "name",
        width: 240,
    },
    {
        title: "流程ID",
        dataIndex: "processInstanceId",
        width: 240,
        align: "center",
    },
    {
        title: "状态",
        dataIndex: "state",
        width: 120,
    },
    {
        title: "创建人",
        dataIndex: "createName",
        width: 140,
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
        width: 150,
        align: "center",
    },
    {
        title: "最后修改人",
        dataIndex: "lastUpdateName",
        width: 140,
    },
    {
        title: "最后修改时间",
        dataIndex: "lastUpdateTime",
        width: 150,
        align: "center",
    },
    {
        title: "操作",
        key: "action",
        width: 220,
        align: "center",
        fixed: "right",
    },
]);

const tableData = ref([]); // 表格数据
const loading = ref(false); // 加载状态
const tableReady = ref(false);

// 分页配置
const pagination = reactive(proxy.$config.pagination());

// 初始化枚举数据
const initEnumData = ref({});
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(["flowableStateEnum"]);
    initEnumData.value = result;
};

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().checkworkBasePath + "checkworkcancelleave001",
            params
        );

        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error("获取数据失败");
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ""; // 清空搜索关键字
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1);
        pagination.pageSize = Number(pag.pageSize || 10);
    }
    fetchData();
};

const modalVisible = ref(false);
const modalTitle = ref("审批人选择");
const modalType = ref("approval");
const operatorParams = ref({});

// 添加
const handleAdd = () => {
    modalTitle.value = "新增";
    modalType.value = "add";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2023071800005",
        params: {},
    };
};

// 编辑
const handleEdit = (record) => {
    modalTitle.value = "编辑";
    modalType.value = "edit";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2023071800006",
        params: {
            id: record.id,
        },
    };
};

// 添加数据加载后的处理函数
const handleAfterDataLoaded = (formData) => {
    if (!proxy.$util.isNull(formData.cancelLeaveTimeSlotList)) {
        formData.cancelLeaveTimeSlotList.forEach(item => {
            item["placeholder1"] = item.timeMation.startTime + ' ~ ' + item.timeMation.endTime;
            item["placeholder2"] = item.stateName;
        })
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = "详情";
    modalType.value = "details";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2023071800007",
        params: {
            id: record.id,
        },
    };
};

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false;
    if (isSubmit) {
        fetchData(); // 刷新表格数据
    }
};

// 作废
const handleAbandoned = async (record) => {
    try {
        const params = {
            id: record.id,
        };
        await proxy.$http.post(
            proxy.$config.getConfig().checkworkBasePath + "checkworkcancelleave007",
            params
        );
        SkMessage.success("作废成功");
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("作废失败");
    }
};

// 当前行记录
const currentRecord = ref(null);

// 提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick();
        modalTitle.value = "审批人选择";
        modalType.value = "approval";
        // 打开审批人选择弹窗
        currentRecord.value = record;
        modalVisible.value = true;
    } catch (error) {
        SkMessage.error("操作失败");
    }
};

// 处理弹窗取消
const handleModalCancel = async () => {
    await nextTick();
    modalVisible.value = false;
};

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id,
        };

        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().checkworkBasePath + "checkworkcancelleave006",
            params
        );

        // 提交成功
        SkMessage.success("提交成功");
        // 关闭弹窗
        modalVisible.value = false;
        // 刷新数据
        await fetchData();
    } catch (error) {
        SkMessage.error("提交失败");
    }
};

// 撤销处理
const handleRevoke = async (record) => {
    try {
        const params = {
            processInstanceId: record.processInstanceId,
        };

        await proxy.$http.put(
            proxy.$config.getConfig().checkworkBasePath + "checkworkcancelleave009",
            params
        );
        SkMessage.success("撤销成功");
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("撤销失败");
    }
};

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)
    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        // 初始化
        record["cancelHour"] = material.cancelHour || 0

        // 等待组件挂载完成
        await nextTick()

        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
    }
}

// 处理单行单元格变化
const handleCellChange = ({ record, dataIndex }) => {
    // 获取班次信息
    const timeMation = record.timeMation?.item || record.timeMation;
    // 计算工时
    if (dataIndex === 'timeId') {
        if (timeMation) {
            // 更新时间范围显示
            const result = {
                placeholder1: timeMation.startTime + ' ~ ' + timeMation.endTime
            };
            Object.assign(record, result);

            // 清空时间相关字段
            record.cancelDay = '';
            record.cancelStartTime = '';  // 清空开始时间
            record.cancelEndTime = '';    // 清空结束时间
            record.cancelHour = 0;        // 清空工时
        }
    }

    // 计算销假工时
    if (!proxy.$util.isNull(record.cancelStartTime) && !proxy.$util.isNull(record.cancelEndTime) && timeMation) {
        // 判断开始时间是否晚于结束时间
        if (record.cancelStartTime > record.cancelEndTime) {
            SkMessage.error('开始时间不能晚于结束时间');
            record.cancelStartTime = '';
            record.cancelEndTime = '';
            record.cancelHour = 0;
            return;
        }

        // 获取上下班打卡时间
        const clockInTime = timeMation.startTime;    // 上班打卡时间
        const clockOutTime = timeMation.endTime;     // 下班打卡时间

        // 判断销假时间是否在工作时间范围内
        if ((record.cancelStartTime < clockInTime && record.cancelEndTime < clockInTime) ||
            (record.cancelStartTime > clockOutTime && record.cancelEndTime > clockOutTime)) {
            // 如果销假时间完全在上班打卡前或下班打卡后，工时为0
            record.cancelHour = 0;
            SkMessage.warning('销假时间不在工作时间范围内，工时计为0');
            return;
        }

        // 1. 如果销假开始时间早于上班时间，以上班时间为准
        const actualStartTime = record.cancelStartTime < timeMation.startTime ? timeMation.startTime : record.cancelStartTime;
        // 2. 如果销假结束时间晚于下班时间，以下班时间为准
        const actualEndTime = record.cancelEndTime > timeMation.endTime ? timeMation.endTime : record.cancelEndTime;

        let totalMinutes = 0;
        if (!timeMation.restStartTime || !timeMation.restEndTime) {
            // 没有休息时间
            totalMinutes = Math.abs(proxy.$util.timeUtil.timeDifference(actualEndTime, actualStartTime));
        } else {
            // 有休息时间
            const restStart = timeMation.restStartTime;
            const restEnd = timeMation.restEndTime;

            // 判断是否跨越休息时间
            if (actualStartTime < restStart && actualEndTime > restEnd) {
                // 跨越休息时间，计算上午和下午的工时
                const morningMinutes = Math.abs(proxy.$util.timeUtil.timeDifference(restStart, actualStartTime));
                const afternoonMinutes = Math.abs(proxy.$util.timeUtil.timeDifference(actualEndTime, restEnd));
                totalMinutes = morningMinutes + afternoonMinutes;
            } else if (actualStartTime >= restEnd || actualEndTime <= restStart) {
                // 在休息时间之前或之后
                totalMinutes = Math.abs(proxy.$util.timeUtil.timeDifference(actualEndTime, actualStartTime));
            } else if (actualStartTime >= restStart && actualEndTime <= restEnd) {
                // 销假时间完全在休息时间内
                totalMinutes = 0;
                SkMessage.warning('销假时间在休息时间内，工时计为0');
            } else if (actualStartTime < restStart && actualEndTime > restStart) {
                // 开始时间在休息前，结束时间在休息中
                totalMinutes = Math.abs(proxy.$util.timeUtil.timeDifference(restStart, actualStartTime));
            } else if (actualStartTime < restEnd && actualEndTime > restEnd) {
                // 开始时间在休息中，结束时间在休息后
                totalMinutes = Math.abs(proxy.$util.timeUtil.timeDifference(actualEndTime, restEnd));
            }
        }

        // 转换为小时
        const hour = proxy.$util.calculationUtil.division(totalMinutes, 60);

        // 更新工时
        record.cancelHour = hour;
    }
};

// 班次信息弹窗相关
const workTimeModalVisible = ref(false)
const workTimeRef = ref(null)
const currentTimeId = ref('')

// 处理显示班次信息
const handleShowWorkTime = (record) => {
    currentTimeId.value = record.timeId
    workTimeModalVisible.value = true
}

// 初始化
onMounted(async () => {
    await nextTick();
    requestAnimationFrame(() => {
        tableReady.value = true;
    });
    await getInitData();
    fetchData();
});
</script>
<style scoped></style>