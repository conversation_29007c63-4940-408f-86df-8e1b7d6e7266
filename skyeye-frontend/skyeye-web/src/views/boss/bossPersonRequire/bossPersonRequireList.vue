<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1651308552871')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon><reload-outlined /></template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['bossPersonRequireState'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <SkPopconfirm v-if="$config.auth('1651308562351')" :title="$t('common.submitConfirm')"
                                    @confirm="handleConfirmOk(record)" @cancel="handleConfirmCancel"
                                    :okText="$t('common.submit')" :cancelText="$t('common.cancel')">
                                    <a>{{ $t('common.submitApproval') }}</a>
                                </SkPopconfirm>
                                <SkDivider v-if="$config.auth('1651308562351')" type="vertical" />
                                <a v-if="$config.auth('1651308552871')" @click="handleEdit(record, 'edit')">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1651308552871')" type="vertical" />
                                <SkPopconfirm v-if="$config.auth('1651308571857')"
                                    :title="$t('common.abandonedConfirm')" @confirm="handleInvalid(record)"
                                    :okText="$t('common.confirm')" :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.abandoned') }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.editRow == 2">
                                <SkPopconfirm v-if="$config.auth('1651308591882')" :title="$t('common.revoke')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @handleChange="handleChange" @loadPageItem="loadPageItem"></ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    nextTick,
    getCurrentInstance
} from 'vue'
import {
    useI18n
} from 'vue-i18n'
import {
    SearchOutlined,
    PlusOutlined,
    ReloadOutlined
} from '@ant-design/icons-vue'
import SkButton from '@/components/SkButton/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import {
    SkMessage
} from '@/components/SkMessage/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'

const {
    proxy
} = getCurrentInstance()
const {
    t
} = useI18n()

// 表格列配置
const columns = [{
    title: t('common.serialNum'),
    dataIndex: 'index',
    width: 80,
    customRender: ({
        index
    }) => {
        // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
        return (pagination.current - 1) * pagination.pageSize + index + 1
    }
},
{
    title: '单号',
    dataIndex: 'oddNumber',
    width: 150,
    align: 'center'
},
{
    title: '流程ID',
    dataIndex: 'processInstanceId',
    width: 200,
    align: 'center'
},
{
    title: '需求部门',
    width: 100,
    customRender: ({
        record
    }) => record.recruitDepartmentMation?.name
},
{
    title: '需求岗位',
    width: 120,
    customRender: ({
        record
    }) => record.recruitJobMation?.name
},

{
    title: '状态',
    dataIndex: 'state',
    width: 100
},
{
    title: '薪资范围',
    dataIndex: 'wages',
    width: 80,
    align: 'center'
},
{
    title: '需求人数',
    dataIndex: 'recruitNum',
    width: 80,
    align: 'center'
},
{
    title: '已招聘人数',
    dataIndex: 'recruitedNum',
    width: 80,
    align: 'center'
},
{ title: proxy.$t('common.createName'), dataIndex: 'createName', width: 140 },
{ title: proxy.$t('common.createTime'), dataIndex: 'createTime', width: 150, align: 'center' },
{ title: proxy.$t('common.lastUpdateName'), dataIndex: 'lastUpdateName', width: 140 },
{ title: proxy.$t('common.lastUpdateTime'), dataIndex: 'lastUpdateTime', width: 150, align: 'center' },
{
    title: t('common.action'),
    key: 'action',
    width: 220,
    align: 'center',
    fixed: 'right'
}
]

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const tableReady = ref(false)
const modalTitle = ref('审批人选择')
const modalType = ref('approval')
const operatorParams = ref({})
const modalVisible = ref(false)
const currentRecord = ref(null)


// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['bossPersonRequireState']);
    initEnumData.value = enumResult
}
// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }
        // 获取列表数据
        const res = await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'queryBossPersonRequireList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}


// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023052100001',
        params: {}
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023052100002',
        params: {
            id: record.id
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023052100003',
        params: {
            id: record.id
        }
    }
}
const showIndexRef = ref(null)
const handleChange = async ({ attrKey, value }) => {
    if (attrKey === 'recruitDepartmentId') {
        //获取岗位
        const materialRes = await proxy.$http.get(
            proxy.$config.getConfig().reqBasePath + 'companyjob007', {
            departmentId: value,
        })

        showIndexRef.value?.writeComponentRef?.updatePageItem('recruitJobId', {
            attrDefinitionCustom: {
                dataType: 1,
                defaultData: materialRes.rows || []
            }
        });
    }
}

const loadPageItem = async (bean, formData) => {
    if (modalType.value == 'edit') {
        if (bean.attrKey == 'recruitJobId') {
            const jobRes = await proxy.$http.get(
                proxy.$config.getConfig().reqBasePath + 'companyjob007', {
                departmentId: formData.recruitDepartmentId
            })
            bean.attrDefinitionCustom = {
                dataType: 1,
                defaultData: jobRes.rows || []
            }
        }
    }
}
// 作废
const handleInvalid = async (record) => {
    try {
        await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'invalidPersonRequire', {
            id: record.id
        })
        SkMessage.success('作废成功')
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '作废失败')
    }
}
// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}
// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 撤销
const handleRevoke = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().bossBasePath + 'revokePersonRequire', {
            processInstanceId: record.processInstanceId
        }
        )
        SkMessage.success('撤销成功')
        fetchData()
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id
        }
        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'submitPersonRequire',
            params
        )
        SkMessage.success('提交成功')
        modalVisible.value = false
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '提交失败')
    }
}

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    await nextTick()
    modalTitle.value = '提交审批'
    modalType.value = 'approval'
    currentRecord.value = record
    modalVisible.value = true
}
// 处理确认取消
const handleConfirmCancel = () => {
    currentRecord.value = null
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    fetchData()
})
</script>

<style scoped></style>