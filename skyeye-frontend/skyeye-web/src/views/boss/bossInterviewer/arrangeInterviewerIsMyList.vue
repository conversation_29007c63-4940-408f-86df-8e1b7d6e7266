<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <reload-outlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['bossInterviewArrangementState'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <a v-if="$config.auth('1650011649292') && record.state == '3'"
                                @click="handleInterview(record)">{{
                                    $t('boss.bossInterviewer.interview') }}
                            </a>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef" v-if="modalType === 'details'" :pageId="operatorParams.pageId"
                    :params="operatorParams.params">
                </ShowIndex>
                <InterviewerResult v-if="modalType === 'interview'" :id="interviewId" @submit="handleModalOk"
                    @reset="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import InterviewerResult from './interviewerResult.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()
const tableReady = ref(false)

// 搜索表单数据
const searchForm = reactive({
    keyword: undefined
})

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        width: 200,
        align: 'left'
    },
    {
        title: '面试者',
        width: 100,
        customRender: ({ record }) => record.interviewMation?.name
    },
    {
        title: '面试部门',
        width: 140,
        customRender: ({ record }) => record.personRequireMation?.recruitDepartmentMation?.name
    },
    {
        title: '面试岗位',
        width: 150,
        customRender: ({ record }) => record.personRequireMation?.recruitJobMation?.name
    },
    {
        title: '面试时间',
        dataIndex: 'interviewTime',
        width: 120,
        align: 'center'
    },
    {
        title: '面试官',
        width: 120,
        customRender: ({ record }) => record.interviewerMation?.name
    },
    {
        title: '面试状态',
        dataIndex: 'state',
        width: 160
    },
    {
        title: t('common.action'),
        key: 'action',
        width: 100,
        align: 'center',
        fixed: 'right'
    }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})
const modalVisible = ref(false)
const interviewId = ref('')

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['bossInterviewArrangementState'])
    initEnumData.value = result
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }
        // 获取列表数据
        const res = await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'queryArrangementInterviewerIsMyList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 处理表格变化
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

// 开始面试
const handleInterview = (record) => {
    modalTitle.value = '面试'
    modalType.value = 'interview'
    interviewId.value = record.id
    modalVisible.value = true
}

// 查看详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023060400004',
        params: {
            id: record.id
        }
    }
}

// 处理弹窗确认
const handleModalOk = () => {
    modalVisible.value = false
    fetchData()
    SkMessage.success('操作成功')
}

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    fetchData()
})
</script>

<style scoped></style>