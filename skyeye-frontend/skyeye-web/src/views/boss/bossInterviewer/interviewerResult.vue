<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" :rules="rules" @submit="handleSubmit" @reset="handleReset">
            <SkHrTitle>面试者信息</SkHrTitle>
            <a-row>
                <a-col :span="12">
                    <a-form-item label="面试者">
                        <div>{{ interviewData.interviewMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="面试时间">
                        <div>{{ interviewData.interviewTime }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="联系方式">
                        <div>{{ interviewData.interviewMation?.phone }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="工作年限">
                        <div>{{ interviewData.interviewMation?.workYears }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="心仪岗位">
                        <div>{{ interviewData.interviewMation?.favoriteJob }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="基本简历">
                        <div v-html="formatTextArea(interviewData.interviewMation?.basicResume)"></div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="附件资料">
                        <div v-if="interviewData.interviewMation?.enclosureResume?.enclosureInfoList?.length"
                            class="file-list">
                            <a v-for="(file, index) in interviewData.interviewMation.enclosureResume.enclosureInfoList"
                                :key="index" class="file-link" @click="downloadFile(file)">
                                {{ file.name }}
                            </a>
                        </div>
                    </a-form-item>
                </a-col>
            </a-row>

            <SkHrTitle>人员需求信息</SkHrTitle>
            <a-row>
                <a-col :span="12">
                    <a-form-item label="需求部门">
                        <div>{{ interviewData.personRequireMation?.recruitDepartmentMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="需求岗位">
                        <div>{{ interviewData.personRequireMation?.recruitJobMation?.name }}</div>
                    </a-form-item>
                </a-col>
            </a-row>

            <SkHrTitle>面试信息</SkHrTitle>
            <a-row>
                <a-col :span="12">
                    <a-form-item label="面试结果" name="state" required>
                        <SkRadio v-model="formData.state" :options="stateOptions" />
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="面试评价" name="evaluation" required>
                        <SkTextarea v-model="formData.evaluation" placeholder="请输入面试评价" :maxLength="400" :rows="4" />
                    </a-form-item>
                </a-col>
            </a-row>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive } from 'vue'
import SkForm from '@/components/SkForm/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkTextarea from '@/components/SkTextarea/index.vue'
import SkRadio from '@/components/SkRadio/index.vue'
import SkUpload from '@/components/SkUpload/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    id: {
        type: [String, Number],
        required: true,
        validator(value) {
            return ['string', 'number'].includes(typeof value) && value.toString().length > 0
        }
    }
})

const emit = defineEmits(['submit', 'reset'])
const { proxy } = getCurrentInstance()

// 表单数据
const formData = reactive({
    state: '4',
    evaluation: ''
})

// 面试数据
const interviewData = ref({})

// 表单校验规则
const rules = {
    state: [{
        required: true,
        message: '请选择面试结果',
        trigger: 'change'
    }],
    evaluation: [{
        required: true,
        message: '请输入面试评价',
        trigger: 'blur'
    }]
}

// 面试结果选项
const stateOptions = [
    { label: '面试通过', value: '4' },
    { label: '面试不通过', value: '5' }
]

// 格式化文本域内容
const formatTextArea = (text) => {
    if (!text) return ''
    return text.replace(/\\n/g, '<br>')
}

// 获取面试安排详情
const getDetail = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().bossBasePath + 'queryArrangementById',
            { id: props.id }
        )
        if (res.bean) {
            interviewData.value = res.bean
            if (res.bean.interviewMation?.basicResume) {
                interviewData.value.interviewMation.basicResume = formatTextArea(res.bean.interviewMation.basicResume)
            }
        }
    } catch (error) {
        SkMessage.error('获取面试详情失败')
    }
}

// 提交表单
const handleSubmit = async () => {
    try {
        const params = {
            id: props?.id || '',
            state: formData.state,
            evaluation: formData.evaluation?.trim()
        }
        if (!params.id) {
            throw new Error('缺少面试安排ID参数')
        }
        await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'setBossInterviewResult',
            params
        )
        emit('submit')
    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 重置表单
const handleReset = () => {
    formData.state = '4'
    formData.evaluation = ''
    emit('reset')
}

// 初始化
onMounted(() => {
    if (props.id) {
        getDetail()
    }
})

// Add the downloadFile function in the script section before handleSubmit
const downloadFile = async (file) => {
    try {
        if (!file || !file.fileAddress) {
            SkMessage.error('文件信息不完整')
            return
        }

        // 如果是相对路径，添加基础路径
        let fileUrl = file.fileAddress
        if (!fileUrl.startsWith('http')) {
            fileUrl = proxy.$config.getConfig().fileBasePath + fileUrl
        }

        // 发起下载请求
        const response = await fetch(fileUrl)
        if (!response.ok) {
            throw new Error('文件下载失败')
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.setAttribute('download', file.name || `file_${Date.now()}${getFileExtension(fileUrl)}`)
        document.body.appendChild(link)
        link.click()

        // 清理
        window.URL.revokeObjectURL(downloadUrl)
        document.body.removeChild(link)
        SkMessage.success('下载成功')
    } catch (error) {
        console.error('Download Error:', error)
        SkMessage.error('下载失败')
    }
}

// 获取文件扩展名
const getFileExtension = (filename) => {
    const ext = filename.split('.').pop()
    return ext ? `.${ext}` : ''
}
</script>

<style scoped>
.file-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-link {
    color: #1677ff;
    cursor: pointer;
    text-decoration: none;
}

.file-link:hover {
    color: #69b1ff;
    text-decoration: underline;
}
</style>