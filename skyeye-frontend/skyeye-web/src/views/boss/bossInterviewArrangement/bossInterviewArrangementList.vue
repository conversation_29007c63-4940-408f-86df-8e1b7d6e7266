<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1649929352279')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon><reload-outlined /></template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['bossInterviewArrangementState'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.state == 2 || record.state == 3">
                                <a v-if="$config.auth('1649929352279')" @click="handleEdit(record)">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1649929352279')" type="vertical" />
                                <SkPopconfirm v-if="$config.auth('1649929381788')"
                                    :title="$t('common.abandonedConfirm')" @confirm="handleInvalid(record)"
                                    :okText="$t('common.confirm')" :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.abandoned') }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.state == 4">
                                <a v-if="$config.auth('1650189947768')" @click="handleInduction(record, 'edit')">
                                    {{ $t('common.induction') }}
                                </a>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick">
                </ShowIndex>
                <InductionWrite v-if="modalType === 'induction'" :id="inductionID" @submit="handleModalOk"
                    @reset="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ReloadOutlined, SearchOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import InductionWrite from './inductionResult.vue'

// 定义组件的 emits
const emit = defineEmits(['update:modelValue', 'change', 'submit', 'reset'])

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const tableReady = ref(false)
// 搜索表单数据
const searchForm = reactive({
    keyword: undefined
})

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        width: 150,
        align: 'center'
    },
    {
        title: '面试者',
        width: 100,
        align: 'center',
        customRender: ({ record }) => record.interviewMation?.name
    },
    {
        title: '面试部门',
        width: 120,
        align: 'center',
        customRender: ({ record }) => record.personRequireMation?.recruitDepartmentMation?.name
    },
    {
        title: '面试岗位',
        width: 120,
        align: 'center',
        customRender: ({ record }) => record.personRequireMation?.recruitJobMation?.name
    },
    {
        title: '面试时间',
        dataIndex: 'interviewTime',
        width: 160,
        align: 'center'
    },
    {
        title: '面试官',
        width: 100,
        align: 'center',
        customRender: ({ record }) => record.interviewerMation?.name
    },
    {
        title: '面试状态',
        dataIndex: 'state',
        width: 100,
        align: 'center'
    },
    {
        title: '录入时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'center',
        sorter: true
    },
    {
        title: t('common.action'),
        key: 'action',
        width: 150,
        align: 'center',
        fixed: 'right'
    }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const modalType = ref('')
const operatorParams = ref({
    pageId: '',
    params: {}
})
const modalVisible = ref(false)
const showIndexRef = ref(null)
const modalTitle = ref('')
const inductionID = ref('')

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['bossInterviewArrangementState']);
    initEnumData.value = result
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }
        // 获取列表数据
        const res = await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'queryMyEntryBossInterviewArrangementList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 处理表格变化
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}
// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023060400002',
        params: {}
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023060400004',
        params: {
            id: record.id
        }
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023060400003',
        params: {
            id: record.id,
            pageType: proxy.$config.pageType.EDIT
        }
    }
}

// 作废
const handleInvalid = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().bossBasePath + 'nullifyArrangement',
            { id: record.id }
        )
        SkMessage.success('作废成功')
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '作废失败')
    }
}

//入职
const handleInduction = (record) => {
    modalTitle.value = '入职'
    modalType.value = 'induction'
    inductionID.value = record.id
    modalVisible.value = true
}
// 处理弹窗确认
const handleModalOk = () => {
    modalVisible.value = false
    fetchData()
}

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 弹窗确认方法
const handleModalClick = () => {
    modalVisible.value = false
    fetchData()
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    getInitData()
    fetchData()
})
</script>


<style scoped></style>