<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" :rules="rules" @submit="handleSubmit" @reset="handleReset">
            <SkHrTitle>面试者信息</SkHrTitle>
            <a-row>
                <a-col :span="12">
                    <a-form-item label="面试者">
                        <div>{{ interviewData.interviewMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="面试时间">
                        <div>{{ interviewData.interviewTime }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="联系方式">
                        <div>{{ interviewData.interviewMation?.phone }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="工作年限">
                        <div>{{ interviewData.interviewMation?.workYears }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="心仪岗位">
                        <div>{{ interviewData.interviewMation?.favoriteJob }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="基本简历">
                        <div v-html="formatTextArea(interviewData.interviewMation?.basicResume)"></div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="附件资料">
                        <div v-if="interviewData.interviewMation?.enclosureResume?.enclosureInfoList?.length"
                            class="file-list">
                            <a v-for="(file, index) in interviewData.interviewMation.enclosureResume.enclosureInfoList"
                                :key="index" class="file-link" @click="downloadFile(file)">
                                {{ file.name }}
                            </a>
                        </div>
                    </a-form-item>
                </a-col>
            </a-row>

            <SkHrTitle>人员需求信息</SkHrTitle>
            <a-row>
                <a-col :span="12">
                    <a-form-item label="需求部门">
                        <div>{{ interviewData.personRequireMation?.recruitDepartmentMation?.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="需求岗位">
                        <div>{{ interviewData.personRequireMation?.recruitJobMation?.name }}</div>
                    </a-form-item>
                </a-col>
            </a-row>

            <SkHrTitle>入职信息</SkHrTitle>
            <a-row>
                <a-col :span="12">
                    <a-form-item label="入职结果" name="state" required>
                        <SkRadio v-model="formData.state" :options="stateOptions" @change="handleStateChange" />
                    </a-form-item>
                </a-col>
                <template v-if="formData.state === '6'">
                    <a-col :span="12">
                        <a-form-item label="入职时间" name="entryTime" required>
                            <SkDatePicker v-model="formData.entryTime" placeholder="请选择入职时间" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="参加工作时间" name="workTime" required>
                            <SkDatePicker v-model="formData.workTime" placeholder="请选择参加工作时间" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="身份证" name="userIdCard" required>
                            <SkInput v-model="formData.userIdCard" placeholder="请输入身份证" maxlength="18" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="入职状态" name="inductionState" required>
                            <SkSelect v-model="formData.inductionState" :options="inductionStateOptions"
                                placeholder="请选择入职状态" :field-names="{ label: 'label', value: 'value' }"
                                @change="handleInductionStateChange" show-label />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-if="formData.inductionState === '4'">
                        <a-form-item label="预计试用结束日期" name="trialTime" required>
                            <SkDatePicker v-model="formData.trialTime" placeholder="请选择预计试用结束日期" />
                        </a-form-item>
                    </a-col>
                </template>
                <a-col :span="24" v-if="formData.state === '7'">
                    <a-form-item label="拒绝原因" name="reason" required>
                        <SkTextarea v-model="formData.reason" placeholder="请输入拒绝原因" :maxLength="400" :rows="4" />
                    </a-form-item>
                </a-col>
            </a-row>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive } from 'vue'
import SkForm from '@/components/SkForm/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkRadio from '@/components/SkRadio/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkDatePicker from '@/components/SkDatePicker/index.vue'
import SkTextarea from '@/components/SkTextarea/index.vue'
import SkUpload from '@/components/SkUpload/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    id: {
        type: [String, Number],
        default: ''
    }
})

const emit = defineEmits(['submit', 'reset'])
const { proxy } = getCurrentInstance()

// 表单数据
const formData = reactive({
    state: '6',
    entryTime: '',
    workTime: '',
    userIdCard: '',
    inductionState: '',
    trialTime: '',
    reason: ''
})

// 面试数据
const interviewData = ref({})

// 表单校验规则
const rules = {
    state: [{
        required: true,
        message: '请选择入职结果',
        trigger: 'change'
    }],
    entryTime: [{
        required: true,
        message: '请选择入职时间',
        trigger: 'change'
    }],
    workTime: [{
        required: true,
        message: '请选择参加工作时间',
        trigger: 'change'
    }],
    userIdCard: [{
        required: true,
        message: '请输入身份证',
        trigger: 'blur'
    }, {
        pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        message: '请输入正确的身份证号码',
        trigger: 'blur'
    }],
    inductionState: [{
        required: true,
        message: '请选择入职状态',
        trigger: 'change'
    }],
    trialTime: [{
        required: true,
        message: '请选择预计试用结束日期',
        trigger: 'change'
    }],
    reason: [{
        required: true,
        message: '请输入拒绝原因',
        trigger: 'blur'
    }]
}

// 入职结果选项
const stateOptions = [
    { label: '同意入职', value: '6' },
    { label: '拒绝入职', value: '7' }
]

// 入职状态选项
const inductionStateOptions = ref([])

// 获取入职状态选项
const getInductionStateOptions = async () => {
    try {
        const result = await proxy.$util.getEnumListByCode('userStaffState')
        inductionStateOptions.value = result.map(item => ({
            label: item.name,
            value: item.id.toString(),
            key: item.id
        }))
    } catch (error) {
        SkMessage.error('获取入职状态选项失败')
    }
}

// 格式化文本域内容
const formatTextArea = (text) => {
    if (!text) return ''
    return text.replace(/\n/g, '<br>')
}

// 获取面试安排详情
const getDetail = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().bossBasePath + 'queryArrangementById',
            { id: props.id }
        )
        if (res.bean) {
            interviewData.value = res.bean
            // 处理基本简历的换行
            if (res.bean.interviewMation?.basicResume) {
                res.bean.interviewMation.basicResume = formatTextArea(res.bean.interviewMation.basicResume)
            }
        }
    } catch (error) {
        SkMessage.error('获取面试详情失败')
    }
}

// 处理入职结果变化
const handleStateChange = (value) => {
    if (value === '7') {
        formData.entryTime = ''
        formData.workTime = ''
        formData.userIdCard = ''
        formData.inductionState = ''
        formData.trialTime = ''
    } else {
        formData.reason = ''
    }
}

// 处理入职状态变化
const handleInductionStateChange = (value) => {
    formData.inductionState = value
    if (value !== '4') {
        formData.trialTime = ''
    }
}

// 提交表单
const handleSubmit = async () => {
    try {
        const params = {
            id: props.id,
            state: formData.state,
            reason: formData.reason,
            entryTime: formData.entryTime,
            workTime: formData.workTime,
            userIdCard: formData.userIdCard,
            inductionState: formData.inductionState,
            trialTime: formData.trialTime
        }
        await proxy.$http.post(
            proxy.$config.getConfig().bossBasePath + 'setInductionResult',
            params
        )
        SkMessage.success('提交成功')
        emit('submit')
    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 重置表单
const handleReset = () => {
    formData.state = '6'
    formData.entryTime = ''
    formData.workTime = ''
    formData.userIdCard = ''
    formData.inductionState = ''
    formData.trialTime = ''
    formData.reason = ''
    emit('reset')
}

// 初始化
onMounted(async () => {
    if (props.id) {
        await Promise.all([
            getDetail(),
            getInductionStateOptions()
        ])
    }
})

// Add the downloadFile function in the script section
const downloadFile = async (file) => {
    try {
        if (!file || !file.fileAddress) {
            SkMessage.error('文件信息不完整')
            return
        }

        // 如果是相对路径，添加基础路径
        let fileUrl = file.fileAddress
        if (!fileUrl.startsWith('http')) {
            fileUrl = proxy.$config.getConfig().fileBasePath + fileUrl
        }

        // 发起下载请求
        const response = await fetch(fileUrl)
        if (!response.ok) {
            throw new Error('文件下载失败')
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.setAttribute('download', file.name || `file_${Date.now()}${getFileExtension(fileUrl)}`)
        document.body.appendChild(link)
        link.click()

        // 清理
        window.URL.revokeObjectURL(downloadUrl)
        document.body.removeChild(link)
        SkMessage.success('下载成功')
    } catch (error) {
        console.error('Download Error:', error)
        SkMessage.error('下载失败')
    }
}

// 获取文件扩展名
const getFileExtension = (filename) => {
    const ext = filename.split('.').pop()
    return ext ? `.${ext}` : ''
}
</script>

<style scoped>
.file-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-link {
    color: #1677ff;
    cursor: pointer;
    text-decoration: none;
}

.file-link:hover {
    color: #69b1ff;
    text-decoration: underline;
}
</style>