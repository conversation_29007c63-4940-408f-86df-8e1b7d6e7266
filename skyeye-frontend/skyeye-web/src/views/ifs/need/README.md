# 财务管理系统需求文档

## 1. 系统概述

### 1.1 系统目标
构建一个完整的企业财务管理系统，实现财务数据的全流程管理，包括日常收支、报销管理、借还款、凭证管理、账务处理等功能。

### 1.2 系统架构
- 前端：Vue 3 + Ant Design Vue
- 后端：根据实际情况选择
- 数据库：建议使用 MySQL

## 2. 功能模块

### 2.1 报销管理
#### 2.1.1 报销单管理
- 报销单创建、编辑、删除
- 报销单审批流程
- 报销类型配置
- 费用类型管理
- 预算控制
- 单据附件管理
- 报销单导出

#### 2.1.2 差旅报销
- 差旅费用标准配置
- 行程管理
- 住宿费用管理
- 交通费用管理
- 补贴管理

### 2.2 借还款管理
#### 2.2.1 借款管理
- 借款申请
- 借款审批
- 借款用途管理
- 借款额度控制
- 借款统计分析

#### 2.2.2 还款管理
- 还款计划
- 还款记录
- 逾期管理
- 还款提醒
- 利息计算

### 2.3 收支管理
#### 2.3.1 收入管理
- 收入录入
- 收入分类
- 收入确认
- 收入统计
- 应收管理

#### 2.3.2 支出管理
- 支出录入
- 支出分类
- 支出审批
- 支出统计
- 应付管理

### 2.4 财务账户管理
- 账户信息管理
- 账户余额管理
- 账户权限控制
- 账户流水记录
- 银企对账
- 资金调拨

### 2.5 账套管理
- 多账套支持
- 账套参数设置
- 账套权限管理
- 跨账套业务处理
- 账套报表

### 2.6 会计科目管理
- 科目树管理
- 科目编码规则
- 科目余额
- 科目对照表
- 辅助核算

### 2.7 凭证管理
- 凭证录入
- 凭证审核
- 凭证打印
- 凭证查询
- 凭证汇总
- 自动生成凭证

### 2.8 明细账管理
- 总账
- 明细账
- 日记账
- 多维度查询
- 账务核对

### 2.9 财务报表
- 资产负债表
- 利润表
- 现金流量表
- 自定义报表
- 报表导出

### 2.10 系统管理
- 用户权限管理
- 审批流程配置
- 单据编号规则
- 系统参数设置
- 数据字典管理

## 3. 数据库设计

### 3.1 主要数据表
```sql
-- 1. 报销单表
CREATE TABLE ifs_expense_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) COMMENT '报销单号',
    applicant_id BIGINT COMMENT '申请人ID',
    department_id BIGINT COMMENT '部门ID',
    expense_type VARCHAR(32) COMMENT '报销类型',
    total_amount DECIMAL(12,2) COMMENT '报销总额',
    status VARCHAR(32) COMMENT '状态',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间'
);

-- 2. 借款单表
CREATE TABLE ifs_loan_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) COMMENT '借款单号',
    applicant_id BIGINT COMMENT '申请人ID',
    loan_amount DECIMAL(12,2) COMMENT '借款金额',
    loan_purpose VARCHAR(255) COMMENT '借款用途',
    loan_date DATE COMMENT '借款日期',
    repayment_date DATE COMMENT '预计还款日期',
    status VARCHAR(32) COMMENT '状态',
    create_time DATETIME COMMENT '创建时间'
);

-- 3. 还款单表
CREATE TABLE ifs_repayment_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) COMMENT '还款单号',
    loan_order_id BIGINT COMMENT '关联借款单ID',
    repayment_amount DECIMAL(12,2) COMMENT '还款金额',
    repayment_date DATE COMMENT '还款日期',
    status VARCHAR(32) COMMENT '状态',
    create_time DATETIME COMMENT '创建时间'
);

-- 4. 收支记录表
CREATE TABLE ifs_income_expense (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type VARCHAR(32) COMMENT '类型(收入/支出)',
    amount DECIMAL(12,2) COMMENT '金额',
    category_id BIGINT COMMENT '类别ID',
    account_id BIGINT COMMENT '账户ID',
    transaction_date DATE COMMENT '交易日期',
    description VARCHAR(255) COMMENT '说明',
    create_time DATETIME COMMENT '创建时间'
);

-- 5. 财务账户表
CREATE TABLE ifs_financial_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    account_name VARCHAR(100) COMMENT '账户名称',
    account_no VARCHAR(32) COMMENT '账号',
    bank_name VARCHAR(100) COMMENT '开户行',
    account_type VARCHAR(32) COMMENT '账户类型',
    balance DECIMAL(12,2) COMMENT '余额',
    status VARCHAR(32) COMMENT '状态'
);

-- 6. 会计科目表
CREATE TABLE ifs_accounting_subject (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    subject_code VARCHAR(32) COMMENT '科目编码',
    subject_name VARCHAR(100) COMMENT '科目名称',
    parent_id BIGINT COMMENT '父级ID',
    level INT COMMENT '层级',
    balance_direction VARCHAR(32) COMMENT '余额方向',
    status VARCHAR(32) COMMENT '状态'
);

-- 7. 凭证表
CREATE TABLE ifs_voucher (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    voucher_no VARCHAR(32) COMMENT '凭证号',
    voucher_date DATE COMMENT '凭证日期',
    attachment_count INT COMMENT '附件数',
    total_debit DECIMAL(12,2) COMMENT '借方合计',
    total_credit DECIMAL(12,2) COMMENT '贷方合计',
    status VARCHAR(32) COMMENT '状态',
    create_time DATETIME COMMENT '创建时间'
);
```

## 4. 目录结构

```
src/views/ifs/
├── README.md # 本文档
├── components/ # 公共组件
│ ├── ExpenseForm/ # 报销表单组件
│ ├── LoanForm/ # 借款表单组件
│ ├── VoucherForm/ # 凭证表单组件
│ └── ...
├── expense/ # 报销管理
│ ├── index.vue # 报销单列表
│ ├── create.vue # 创建报销单
│ └── detail.vue # 报销单详情
├── loan/ # 借还款管理
│ ├── index.vue # 借还款列表
│ ├── create.vue # 创建借还款单
│ └── detail.vue # 借还款单详情
├── finance/ # 收支管理
│ ├── income.vue # 收入管理
│ ├── expense.vue # 支出管理
│ └── statistics.vue # 统计分析
├── account/ # 账户管理
│ ├── index.vue # 账户列表
│ └── detail.vue # 账户详情
├── ledger/ # 账套管理
│ ├── index.vue # 账套列表
│ └── setting.vue # 账套设置
├── subject/ # 会计科目
│ ├── index.vue # 科目列表
│ └── detail.vue # 科目详情
├── voucher/ # 凭证管理
│ ├── index.vue # 凭证列表
│ ├── create.vue # 创建凭证
│ └── detail.vue # 凭证详情
└── statement/ # 明细账
├── general.vue # 总账
├── detail.vue # 明细账
└── daily.vue # 日记账
```

## 5. 界面设计

我可以继续提供具体的界面代码实现，您想先看哪个模块的具体实现？

## 6. 注意事项

1. 数据安全性
- 实现完整的权限控制
- 关键数据加密存储
- 操作日志记录

2. 性能优化
- 大数据量处理优化
- 缓存策略
- 分页加载

3. 用户体验
- 响应式设计
- 操作流程优化
- 数据校验

4. 系统集成
- 与其他系统的对接
- 数据导入导出
- API接口规范

## 7. 后续规划

1. 第一阶段：基础功能实现
- 报销管理
- 借还款管理
- 收支管理

2. 第二阶段：核心功能完善
- 账套管理
- 科目管理
- 凭证管理

3. 第三阶段：高级功能开发
- 财务报表
- 数据分析
- 移动端适配

# 财务管理系统核心业务逻辑

## 1. 核心业务流程

### 1.1 费用报销业务
1. 业务规则
   - 报销额度控制：按部门、职级设置额度限制
   - 预算管理：与预算系统联动，控制报销金额
   - 审批流程：根据金额、类型自动匹配审批流程
   - 借款冲抵：自动检查并冲抵借款
   - 发票管理：发票查重、真伪验证

2. 核心计算逻辑
   - 可报销金额 = MIN(标准限额, 实际金额)
   - 预算检查 = 部门预算 - 已用预算 - 本次报销
   - 借款冲抵金额 = MIN(报销总额, 未还借款)
   - 实际付款金额 = 报销总额 - 借款冲抵金额

### 1.2 借还款业务
1. 业务规则
   - 借款额度：按职级设置最大借款额度
   - 借款期限：设置最长借款期限
   - 多次借款：未还清前限制新借款
   - 还款方式：现金还款、报销冲抵
   - 逾期管理：自动计算逾期天数

2. 核心计算逻辑
   - 可借额度 = 最大借款额度 - 在借金额
   - 逾期天数 = 当前日期 - 约定还款日期
   - 剩余未还 = 借款总额 - 已还金额 - 报销冲抵
   - 逾期利息 = 未还金额 × 日利率 × 逾期天数

### 1.3 凭证处理业务
1. 业务规则
   - 借贷平衡：借贷方金额必须相等
   - 科目余额：控制科目余额方向
   - 辅助核算：必填项检查
   - 序时控制：凭证号连续性
   - 期间控制：限制跨期录入

2. 核心计算逻辑
   - 借贷平衡 = SUM(借方金额) - SUM(贷方金额) = 0
   - 科目余额 = 期初余额 + 借方发生额 - 贷方发生额
   - 凭证号生成 = 年月 + 字号 + 序号
   - 分录汇总 = GROUP BY 科目编码

### 1.4 结账处理业务
1. 业务规则
   - 结账检查：凭证完整性、余额平衡
   - 结转规则：收入支出结转损益
   - 期间锁定：结账后禁止修改
   - 自动结转：系统自动生成结转凭证
   - 跨期调整：特殊情况允许红冲

2. 核心计算逻辑
   - 收入结转 = SUM(收入类科目余额)
   - 费用结转 = SUM(费用类科目余额)
   - 本期损益 = 收入结转 - 费用结转
   - 期末余额 = 期初余额 + 本期发生额

## 2. 数据处理逻辑

### 2.1 科目余额处理
1. 余额方向
   - 借方科目：期末余额 = 借方 - 贷方 > 0
   - 贷方科目：期末余额 = 贷方 - 借方 > 0
   - 双向科目：余额可以是借方或贷方

2. 余额计算
   - 期初余额：上期结转金额
   - 本期发生：本期借贷方发生额
   - 累计发生：年初至今发生额
   - 期末余额：期初 + 本期发生

### 2.2 报表生成逻辑
1. 资产负债表
   - 资产类：借方科目期末余额
   - 负债类：贷方科目期末余额
   - 权益类：所有者权益科目余额
   - 平衡关系：资产 = 负债 + 所有者权益

2. 利润表
   - 营业收入：主营业务收入 + 其他业务收入
   - 营业成本：主营业务成本 + 其他业务支出
   - 期间费用：管理费用 + 销售费用 + 财务费用
   - 净利润：收入总额 - 成本费用总额

## 3. 业务校验规则

### 3.1 单据校验
1. 报销单
   - 金额合法性：金额>0且在限额内
   - 预算控制：不超预算限额
   - 发票有效性：发票真伪和重复
   - 审批完整性：审批流程完整

2. 借款单
   - 额度检查：在可借额度内
   - 借款期限：不超最长期限
   - 还款计划：还款日期合理
   - 审批有效：审批流程完整

### 3.2 凭证校验
1. 数据完整性
   - 必填项检查
   - 科目有效性
   - 金额合法性
   - 摘要规范性

2. 业务合规性
   - 借贷平衡
   - 科目余额方向
   - 辅助核算完整
   - 期间有效性

## 4. 权限控制逻辑

### 4.1 功能权限
1. 角色划分
   - 制单人：录入凭证
   - 审核人：审核凭证
   - 出纳：办理收付款
   - 会计：账务处理
   - 财务主管：审批和结账

2. 数据权限
   - 部门数据权限
   - 金额限额权限
   - 科目使用权限
   - 期间操作权限

### 4.2 审批权限
1. 金额级别
   - 普通审批：5000以下
   - 部门经理：5000-50000
   - 财务总监：50000以上

2. 特殊权限
   - 反审核权限
   - 作废权限
   - 红冲权限
   - 结账权限

# 财务管理模块业务流程说明

## 1. 日常业务流程

### 1.1 单据录入流程
1. 费用报销流程
   ```
   业务发生 -> 填写报销单 -> 提交审批 -> 审批通过 -> 生成付款凭证 -> 出纳付款 -> 完成报销
   ```
   - 填写报销单：选择费用类型、上传单据、填写金额等
   - 审批流程：根据金额和类型进行多级审批
   - 生成凭证：自动生成费用类凭证
   - 付款处理：出纳根据审批结果进行付款

2. 借还款流程
   ```
   申请借款 -> 审批借款 -> 付款 -> 使用款项 -> 报销冲账/还款 -> 完成借款
   ```
   - 借款申请：说明用途、金额、预计还款日期
   - 审批处理：审核借款合理性
   - 还款方式：直接还款或报销冲抵

3. 收入处理流程
   ```
   收入发生 -> 开具发票 -> 确认收入 -> 收款核销 -> 生成凭证
   ```
   - 收入确认：根据业务类型确认收入
   - 收款核销：将收款与应收单据进行匹配
   - 凭证处理：生成收入类凭证

### 1.2 凭证处理流程

1. 凭证录入
   ```
   选择凭证类型 -> 填写基本信息 -> 录入分录 -> 校验平衡 -> 保存凭证
   ```
   - 基本信息：日期、字号、附件数等
   - 分录信息：
     * 借方科目、金额
     * 贷方科目、金额
     * 摘要说明
     * 辅助核算项
   - 自动校验：
     * 借贷必须平衡
     * 科目必须存在
     * 金额必须正确

2. 凭证审核
   ```
   提交审核 -> 审核人检查 -> 确认无误 -> 审核通过 -> 进入过账
   ```
   - 审核要点：
     * 科目使用是否正确
     * 金额计算是否准确
     * 原始单据是否完整
     * 摘要说明是否清晰
   - 审核结果：
     * 通过：进入过账环节
     * 退回：退回修改
     * 作废：整张凭证作废

3. 凭证过账
   ```
   审核通过 -> 系统过账 -> 更新科目余额 -> 生成账簿记录 -> 完成过账
   ```
   - 过账处理：
     * 更新科目余额
     * 生成明细账记录
     * 更新总账数据
   - 过账后：
     * 凭证锁定不可修改
     * 可以查看和打印
     * 可以复制生成新凭证

### 1.3 期末处理流程

1. 期末结账准备
   ```
   检查凭证 -> 核对余额 -> 结转损益 -> 生成报表 -> 期间锁定
   ```
   - 检查内容：
     * 凭证是否齐全
     * 序时账是否连续
     * 借贷是否平衡
     * 科目余额是否正确

2. 结转损益
   ```
   收入结转 -> 费用结转 -> 计算损益 -> 更新利润
   ```
   - 结转步骤：
     * 结转收入类科目
     * 结转费用类科目
     * 计算本期损益
     * 更新未分配利润

3. 报表生成
   ```
   采集数据 -> 生成报表 -> 校验数据 -> 打印报表
   ```
   - 报表类型：
     * 资产负债表
     * 利润表
     * 现金流量表
     * 科目余额表

## 2. 特殊业务处理

### 2.1 往来账务处理
1. 应收账款
   ```
   确认应收 -> 收款核销 -> 坏账处理
   ```
   - 应收确认：根据合同或订单确认
   - 收款核销：收款后与应收匹配
   - 坏账处理：无法收回时处理

2. 应付账款
   ```
   确认应付 -> 付款申请 -> 付款核销
   ```
   - 应付确认：根据发票或合同确认
   - 付款处理：按付款计划支付
   - 核销处理：付款后与应付匹配

### 2.2 固定资产处理
1. 资产购置
   ```
   购置申请 -> 验收入账 -> 计提折旧
   ```
   - 入账处理：确认资产原值
   - 折旧处理：按月计提折旧
   - 清理处理：资产处置时清理

2. 资产处置
   ```
   处置申请 -> 确认处置 -> 结转损益
   ```
   - 处置方式：报废、出售、转让
   - 账务处理：结转净值、确认损益

## 3. 数据维护

### 3.1 基础数据维护
1. 科目管理
   - 科目设置
   - 辅助核算设置
   - 余额方向设置

2. 期间管理
   - 会计期间设置
   - 结账管理
   - 期间锁定控制

### 3.2 系统数据维护
1. 用户权限
   - 角色设置
   - 权限分配
   - 数据权限控制

2. 单据管理
   - 单据编号规则
   - 审批流程设置
   - 打印模板管理

## 4. 注意事项

### 4.1 操作规范
1. 凭证处理
   - 及时录入业务凭证
   - 确保凭证序时完整
   - 及时进行凭证审核
   - 定期检查未过账凭证

2. 结账管理
   - 按时完成结账
   - 结账前全面检查
   - 及时进行数据备份
   - 妥善保管会计资料

### 4.2 系统使用
1. 日常维护
   - 定期检查系统运行
   - 及时处理异常数据
   - 定期清理临时数据
   - 保持系统性能最优

2. 数据安全
   - 严格权限管理
   - 定期数据备份
   - 敏感数据加密
   - 操作日志记录

