<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1714910816333')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <reload-outlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'payTypeId'">
                        {{ initDictData['IFS_PAY_TYPE'][record.payTypeId] }}
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <SkPopconfirm v-if="$config.auth('1714910833787')" :title="$t('common.submitConfirm')"
                                    @confirm="handleConfirmOk(record)" @cancel="handleConfirmCancel"
                                    :okText="$t('common.submit')" :cancelText="$t('common.cancel')">
                                    <a>{{ $t('common.submitApproval') }}</a>
                                </SkPopconfirm>
                                <SkDivider v-if="$config.auth('1714910833787')" type="vertical" />
                                <a v-if="$config.auth('1714910816333')" @click="handleEdit(record, 'edit')">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1714910816333')" type="vertical" />
                                <SkPopconfirm v-if="$config.auth('1714910825479')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.editRow == 2">
                                <SkPopconfirm v-if="$config.auth('1714910841416')" :title="$t('common.revoke')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    </ShowIndex>
                    <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                        :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { SearchOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import SkButton from '@/components/SkButton/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'



const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        width: 150,
        align: 'center'
    },
    {
        title: '付款方式',
        dataIndex: 'payTypeId',
        width: 100
    },
    {
        title: '还款金额',
        dataIndex: 'price',
        width: 120
    },
    {
        title: '收款人全称',
        dataIndex: 'collectionName',
        width: 120
    },
    {
        title: '收款账号',
        dataIndex: 'collectionCode',
        width: 150
    },
    {
        title: '开户行',
        dataIndex: 'openingBank',
        width: 150
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 200,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 100    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120,
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'center',
        sorter: true
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 160,
        align: 'center',
        sorter: true
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 160,
        align: 'center',
        sorter: true
    },
    {
        title: t('common.action'),
        key: 'action',
        width: 220,
        align: 'center',
        fixed: 'right'
    }
]

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const tableReady = ref(false)
const modalTitle = ref('审批人选择')
const modalType = ref('approval')
const operatorParams = ref({})
const modalVisible = ref(false)
const currentRecord = ref(null)

// 初始化数据字典数据
const initDictData = ref({})
// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['flowableStateEnum']);
    initEnumData.value = enumResult

    let dictResult = await proxy.$util.getDictListMapByCode(['IFS_PAY_TYPE']);
    initDictData.value = dictResult
}
// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }
        // 获取列表数据
        const res = await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'queryLoanRepayList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}
// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050500008',
        params: {}
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050500009',
        params: {
            id: record.id
        }
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().ifsBasePath + 'deleteLoanRepayById',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}
// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 撤销
const handleRevoke = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().ifsBasePath + 'revokeLoanRepay',
            { processInstanceId: record.processInstanceId }
        )
        SkMessage.success('撤销成功')
        fetchData()
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id
        }
        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'submitLoanRepayToApproval',
            params
        )
        SkMessage.success('提交成功')
        modalVisible.value = false
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '提交失败')
    }
}

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick()
        modalTitle.value = '提交审批'
        modalType.value = 'approval'
        currentRecord.value = record
        modalVisible.value = true
    } catch (error) {
        SkMessage.error('操作失败')
    }
}
// 处理确认取消
const handleConfirmCancel = () => {
    currentRecord.value = null
}


// 添加查看详情方法
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050500010',
        params: {
            id: record.id
        }
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    fetchData()
})
</script>

<style scoped></style>