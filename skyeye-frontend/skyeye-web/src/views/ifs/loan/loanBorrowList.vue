<template>
  <div class="container-manage">
    <SkCard ref="cardRef" :bordered="false">
      <!-- 搜索表单 -->
      <div class="table-search">
        <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
          :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
          <template #submitIcon>
            <search-outlined />
          </template>
          <a-form-item name="keyword">
            <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
          </a-form-item>
        </SkForm>
      </div>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <SkSpace>
          <SkButton v-if="$config.auth('1714910702062')" type="primary" @click.prevent="handleAdd">
            <template #icon><plus-outlined /></template>
            {{ $t('common.add') }}
          </SkButton>
          <SkButton type="primary" @click.prevent="fetchData">
            <template #icon><reload-outlined /></template>
            {{ $t('common.refresh') }}
          </SkButton>
        </SkSpace>
      </div>

      <!-- 表格 -->
      <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
        :ready="tableReady" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'oddNumber'">
            <a @click="handleDetail(record)">
              {{ record.oddNumber }}
            </a>
          </template>
          <template v-if="column.dataIndex === 'processInstanceId'">
            <ProcessDetail :processInstanceId="record.processInstanceId" />
          </template>
          <template v-if="column.dataIndex === 'payTypeId'">
            {{ initDictData['IFS_PAY_TYPE'][record.payTypeId] }}
          </template>
          <template v-if="column.dataIndex === 'state'">
            <div
              v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <SkSpace>
              <template v-if="record.editRow == 1">
                <!-- 提交审批 -->
                <a v-if="$config.auth('1714910725373')" @click="handleConfirmOk(record)">
                  {{ $t("common.submitApproval") }}
                </a>
                <SkDivider v-if="$config.auth('1714910725373')" type="vertical" />

                <!-- 编辑 -->
                <a v-if="$config.auth('1714910702062')" @click="handleEdit(record, 'edit')">
                  {{ $t('common.edit') }}
                </a>
                <SkDivider v-if="$config.auth('1714910702062')" type="vertical" />

                <!-- 删除 -->
                <SkPopconfirm v-if="$config.auth('1714910714820')" :title="$t('common.deleteConfirm')"
                  @confirm="handleDelete(record)" :okText="$t('common.delete')" :cancelText="$t('common.cancel')">
                  <a class="danger-link">{{ $t('common.delete') }}</a>
                </SkPopconfirm>
              </template>

              <template v-if="record.editRow == 2">
                <!-- 撤销 -->
                <SkPopconfirm v-if="$config.auth('1714910733052')" :title="$t('common.revokeConfirm')"
                  @confirm="handleRevoke(record)" :okText="$t('common.confirm')" :cancelText="$t('common.cancel')">
                  <a class="danger-link">{{ $t('common.revoke') }}</a>
                </SkPopconfirm>
              </template>
            </SkSpace>
          </template>
        </template>
      </SkTable>

      <!-- 新增弹窗 -->
      <SkModal v-model="modalVisible" :title="modalTitle">
        <ShowIndex ref="showIndexRef" v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
          :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick">
        </ShowIndex>
        <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
          :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
      </SkModal>
    </SkCard>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import SkCard from '@/components/SkCard/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 表格加载和分页
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const tableReady = ref(false)
const cardRef = ref(null)

// 弹窗相关
const modalVisible = ref(false)
const modalType = ref('approval')
const modalTitle = ref('审批人选择')
const operatorParams = ref({})
const showIndexRef = ref(null)
const currentRecord = ref(null)

// 表格列配置
const columns = [
  {
    title: t('common.serialNum'),
    dataIndex: 'index',
    width: 60,
    customRender: ({ index }) => {
      return (pagination.current - 1) * pagination.pageSize + index + 1
    }
  },
  {
    title: '单据编号',
    dataIndex: 'oddNumber',
    width: 200
  },
  {
    title: '付款方式',
    dataIndex: 'payTypeId',
    width: 120,

  },
  {
    title: '借款金额',
    dataIndex: 'price',
    width: 150,

  },
  {
    title: '收款人全称',
    dataIndex: 'collectionName',
    width: 150,

  },
  {
    title: '收款账号',
    dataIndex: 'collectionCode',
    width: 120,
  },
  {
    title: '开户行',
    dataIndex: 'openingBank',
    width: 120
  },
  {
    title: '流程ID',
    dataIndex: 'processInstanceId',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: 80,
  },
  {
    title: '创建者',
    dataIndex: 'createName',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '最后更新者',
    dataIndex: 'lastUpdateName',
    width: 120,
  },
  {
    title: '最后更新时间',
    dataIndex: 'lastUpdateTime',
    width: 150,
  },
  {
    title: t('common.action'),
    key: 'action',
    width: 220,
    fixed: 'right',
  }
]

// 搜索表单数据  、
const searchForm = reactive({
  keyword: ''
})

// 表格数据
const tableData = ref([])

// 状态枚举缓存
const statusEnum = ref(null)

// 初始化数据字典数据
const initDictData = ref({})
// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
  let enumResult = await proxy.$util.getEnumListMapByCode(['flowableStateEnum']);
  initEnumData.value = enumResult

  let dictResult = await proxy.$util.getDictListMapByCode(['IFS_PAY_TYPE']);
  initDictData.value = dictResult
}

// 获取表格数据
const fetchData = async () => {
  try {
    loading.value = true

    // 获取状态枚举
    if (!statusEnum.value) {
      statusEnum.value = await proxy.$http.post(
        proxy.$config.getConfig().reqBasePath + 'getEnumDataByClassName',
        {
          className: proxy.$util.classEnumMap['flowableStateEnum'].className
        }
      )
    }

    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      keyword: searchForm.keyword?.trim()
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    const res = await proxy.$http.post(
      proxy.$config.getConfig().ifsBasePath + 'queryLoanBorrowList',
      params
    )

    if (res && res.rows) {
      tableData.value = res.rows
      pagination.total = res.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
      SkMessage.warning('暂无数据')
    }

  } catch (error) {
    console.error('获取数据失败:', error)
    SkMessage.error('获取数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 新增按钮处理
const handleAdd = () => {
  modalTitle.value = '新增'
  modalType.value = 'add'
  modalVisible.value = true
  operatorParams.value = {
    pageId: 'FP2024050500005',
    params: {},
  }
}

// 编辑
const handleEdit = (record) => {
  modalTitle.value = '编辑'
  modalType.value = 'edit'
  modalVisible.value = true
  operatorParams.value = {
    pageId: 'FP2024050500006',
    params: {
      id: record.id
    }
  }
}

// 查看详情
const handleDetail = (record) => {
  modalTitle.value = '详情'
  modalType.value = 'details'
  modalVisible.value = true
  operatorParams.value = {
    pageId: 'FP2024050500007',
    params: {
      id: record.id
    }
  }
}

// 删除
const handleDelete = async (record) => {
  try {
    await proxy.$http.delete(
      proxy.$config.getConfig().ifsBasePath + 'deleteLoanBorrowById',
      { id: record.id }
    )
    SkMessage.success('删除成功')
    fetchData()
  } catch (error) {
    SkMessage.error('删除失败')
  }
}

// 撤销
const handleRevoke = async (record) => {
  try {
    await proxy.$http.put(
      proxy.$config.getConfig().ifsBasePath + 'revokeLoanBorrow',
      { processInstanceId: record.processInstanceId }
    )
    SkMessage.success('撤销成功')
    fetchData()
  } catch (error) {
    SkMessage.error('撤销失败')
  }
}

// 弹窗关闭处理
const handleModalClick = (isSubmit) => {
  modalVisible.value = false
  if (isSubmit) {
    fetchData()
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
  if (pag) {
    pagination.current = Number(pag.current || 1)
    pagination.pageSize = Number(pag.pageSize || 10)
  }
  fetchData()
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
  try {
    // 构建参数
    const params = {
      id: currentRecord.value.id,
      approvalId: person.id
    }

    // 发送提交请求
    await proxy.$http.post(
      proxy.$config.getConfig().ifsBasePath + 'submitLoanBorrowToApproval',
      params
    )

    // 提交成功
    SkMessage.success('提交成功')
    // 关闭弹窗
    modalVisible.value = false
    // 刷新数据
    fetchData()
  } catch (error) {
    SkMessage.error(error.message || '提交失败')
  }
}

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
  try {
    await nextTick()
    modalTitle.value = '审批人选择'
    modalType.value = 'approval'
    currentRecord.value = record
    modalVisible.value = true
  } catch (error) {
    SkMessage.error('操作失败')
  }
}

// 取消审批人选择
const handleModalCancel = () => {
  modalVisible.value = false
}

// 初始化
onMounted(async () => {
  await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
  await getInitData()
  fetchData()
})

</script>

<style scoped></style>