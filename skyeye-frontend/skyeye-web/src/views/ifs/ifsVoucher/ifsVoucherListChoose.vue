<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 提示信息 -->
            <SkAlert
                message="仅支持以下格式的凭证文件：【png, jpg, xbm, bmp, webp, jpeg, svgz, git, ico, tiff, svg, jiff, pjpeg, pjp, tif, gif, docx, doc, xls, xlsx, ppt, pptx, wps, et, dps, csv, pdf】"
                type="info" show-icon />

            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入凭证名称" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>


            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1641208147247')" type="primary" @click.prevent="handleUpload">
                        <template #icon><upload-outlined /></template>
                        {{ $t('common.upload') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>

            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'path'">
                        <SkTableImg :imgPath="record.path" />
                    </template>

                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['voucherState'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 删除 -->
                            <SkPopconfirm v-if="$config.auth('1641208155066')" :title="$t('common.deleteConfirm')"
                                @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t('common.delete') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>
        </SkCard>
    </div>
    <!-- 上传文件隐藏输入框 -->
    <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" />
</template>

<script setup>
import { useI18n } from "vue-i18n";
import SkTableImg from '@/components/SkTableImg/index.vue'
import SkTable from "@/components/SkTable/index.vue";
import SkAlert from '@/components/SkAlert/index.vue'
import SkForm from "@/components/SkForm/index.vue";
import SkSpace from "@/components/SkSpace/index.vue";
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import SkButton from "@/components/SkButton/index.vue";
import {
    SearchOutlined,
    ReloadOutlined,
} from "@ant-design/icons-vue";
import { ref, onMounted, getCurrentInstance, reactive } from "vue";
import SkInput from "@/components/SkInput/index.vue";
import SkCard from "@/components/SkCard/index.vue";
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkPopconfirm from "@/components/SkPopconfirm/index.vue";

const { proxy } = getCurrentInstance();
const { t } = useI18n();
const uploadVisible = ref(false)


// 表格列定义
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '名称',
        dataIndex: 'createId',
        key: 'name',
        width: 150,
        ellipsis: true
    },
    {
        title: '展示',
        dataIndex: 'path',
        width: 80,
        align: 'center',
    },
    {
        title: '状态',
        dataIndex: 'enabled',
        width: 90,
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 140,
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center'
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140,
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 100,
        fixed: 'right',
        align: 'center'
    }
]

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const tableReady = ref(false)
const initEnumData = ref({})
const cardRef = ref(null)

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + "ifsVoucher001",
            params
        );

        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};
// 上传成功后的处理
const handleSaveEnclosureChange = async (info) => {
    var params = {
        name: info.fileName,
        path: info.picUrl,
        type: info.type,
        size: info.size,
        sizeType: info.fileSizeType,
        catalog: '0'
    };
    await proxy.$http.post(proxy.$config.getConfig().ifsBasePath + 'ifsVoucher002', params);
};

// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().ifsBasePath + 'ifsVoucher003',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        await fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

const fileInputRef = ref(null)

// 打开上传弹窗
const handleUpload = (record) => {
    fileInputRef.value.value = ''
    fileInputRef.value.click()
}

const handleFileChange = async (e) => {
    const file = e.target.files[0]; 
    if (!file) return;

    const formData = new FormData(); 
    formData.append('file', file);
    formData.append('name', file.name); 
    formData.append('type', '30'); 

    try {
        SkMessage.loading('正在上传...'); 
        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'common003',
            formData
        );
        await handleSaveEnclosureChange(res.bean); 
        SkMessage.success('上传成功'); 
        fetchData()
    } catch (error) {
        SkMessage.error('上传失败'); 
    }
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ''
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['voucherState']);
    initEnumData.value = enumResult
}

// 初始化
onMounted(async () => {
    fetchData()
    handleSaveEnclosureChange()
    await getInitData()
    handleFileChange()
    tableReady.value = true
})
</script>

<style scoped></style>
