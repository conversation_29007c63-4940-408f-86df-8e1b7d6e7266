<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-btn-group">
                <SkAuthBtnGroup authPointCode="1714869343001" @change="handleChange" />
            </div>
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1714869454529')" type="primary" @click.prevent="handleAdd">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange" row-key="id">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'payTypeId'">
                        {{ initDictData['IFS_PAY_TYPE']?.[record.payTypeId] }}
                    </template>
                    <template v-if="column.dataIndex === 'price'">
                        {{ formatNumber(record.price) }}
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <SkPopconfirm v-if="$config.auth('1714869476939')" :title="$t('common.submitConfirm')"
                                    @confirm="handleConfirmOk(record)" @cancel="handleConfirmCancel"
                                    :okText="$t('common.submit')" :cancelText="$t('common.cancel')">
                                    <a>{{ $t('common.submitApproval') }}</a>
                                </SkPopconfirm>
                                <SkDivider v-if="$config.auth('1714869476939')" type="vertical" />
                                <a v-if="$config.auth('1714869454529')" @click="handleEdit(record, 'edit')">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1714869454529')" type="vertical" />
                                <SkPopconfirm v-if="$config.auth('1714869465652')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.editRow == 2">
                                <SkPopconfirm v-if="$config.auth('1714869485320')" :title="$t('common.revoke')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>
            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef" v-if="
                    modalType === 'add' ||
                    modalType === 'edit' ||
                    modalType === 'details'
                " :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick">
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import SkTable from '@/components/SkTable/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkAuthBtnGroup from '@/components/SkAuthBtnGroup/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import { SearchOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 格式化数字,保留2位小数
const formatNumber = (num) => {
    if (num === null || num === undefined) {
        return '0.00'
    }
    // 转换为数字类型
    const number = Number(num)
    if (isNaN(number)) {
        return '0.00'
    }
    return number.toFixed(2)
}

// 表格列定义
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        key: 'oddNumber',
        width: 180
    },
    {
        title: '付款方式',
        dataIndex: 'payTypeId',
        key: 'payTypeId',
        width: 120
    },
    {
        title: '报销金额',
        dataIndex: 'price',
        key: 'price',
        width: 120,
        align: 'right'
    },
    {
        title: '收款人全称',
        dataIndex: 'collectionName',
        key: 'collectionName',
        width: 150
    },
    {
        title: '收款账号',
        dataIndex: 'collectionCode',
        key: 'collectionCode',
        width: 180
    },
    {
        title: '开户行',
        dataIndex: 'openingBank',
        key: 'openingBank',
        width: 180
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        key: 'processInstanceId',
        width: 180
    },
    {
        title: '状态',
        dataIndex: 'state',
        key: 'state',
        width: 100
    },
    {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 180
    }
]

// 数据相关
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)

// 权限点处理
const authMation = ref({})
const handleChange = (key, value) => {
    authMation.value[key] = value
    pagination.current = 1
    fetchData()
}

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化数据字典数据
const initDictData = ref({})
// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['flowableStateEnum']);
    initEnumData.value = enumResult

    let dictResult = await proxy.$util.getDictListMapByCode(['IFS_PAY_TYPE']);
    initDictData.value = dictResult
}

const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            ...authMation.value,
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + "queryReimbursementList",
            params
        );

        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败');
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 获取序号
const getIndex = (index) => {
    return (pagination.current - 1) * pagination.pageSize + index + 1
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ''
    pagination.current = 1
    fetchData()
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1);
        pagination.pageSize = Number(pag.pageSize || 10);
    }
    fetchData();
};

const modalVisible = ref(false);
const modalTitle = ref("报销单");
const modalType = ref("edit");
const operatorParams = ref({});

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑报销单'
    modalType.value = "edit"
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050500002',
        params: {
            id: record.id
        }
    }
}

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = "add"
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050500001',
        params: {}
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = "详情";
    modalType.value = "details";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2024050500003",
        params: {
            id: record.id,
        },
    };
};

// 删除方法
const handleDelete = async (record) => {
    try {
        loading.value = true
        const params = {
            id: record.id,
            whetherDelete: 2
        }
        const res = await proxy.$http.delete(
            proxy.$config.getConfig().ifsBasePath + 'deleteReimbursementById',
            { data: params }
        )

        if (res.code === 200) {
            SkMessage.success('删除成功')
            await fetchData()
        } else {
            SkMessage.error(res.message || '删除失败')
        }
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

const handleConfirmCancel = () => {
    // 取消提交审批
}

// 当前行记录
const currentRecord = ref(null);

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick();
        modalTitle.value = "提交审批";
        modalType.value = "approval";
        // 打开审批人选择弹窗
        currentRecord.value = record;
        modalVisible.value = true;
    } catch (error) {
        SkMessage.error("操作失败");
    }
};

// 处理弹窗取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id
        }
        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'submitReimbursementToApproval',
            params
        )
        SkMessage.success('提交成功')
        modalVisible.value = false
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '提交失败')
    }
}

const handleRevoke = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().ifsBasePath + 'revokeReimbursement',
            { processInstanceId: record.processInstanceId }
        )
        SkMessage.success('撤销成功')
        fetchData()
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 初始化
onMounted(async () => {
    await getInitData()
    await fetchData()
    tableReady.value = true
})
</script>

<style lang="less" scoped></style>