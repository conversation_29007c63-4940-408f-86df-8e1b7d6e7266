<template>
    <div class="container-manage">
        <!-- 左侧类型列表 -->
        <div class="left-tree">
            <SkCard title="单据类型" :bordered="false">
                <div class="list-search">
                    <SkInput v-model="searchText" placeholder="请输入要搜索的节点" @change="onSearch" allowClear />
                </div>
                <div class="list-tree">
                    <SkTree :data="treeData" :defaultExpandedKeys="expandedKeys"
                        :field-names="{ title: 'name', key: 'id', children: 'children' }" @expand="onExpand" :showRoot="true"
                        :autoExpandParent="true" @select="handleTreeSelect" />
                </div>
            </SkCard>
        </div>

        <!-- 右侧内容区 -->
        <div class="right-content">
            <SkCard :bordered="false">
                <!-- 顶部切换组件 -->
                <div class="table-btn-group">
                    <SkAuthBtnGroup v-if="isShow" authPointCode="1571638010771" @change="handleChanges" />
                </div>

                <!-- 搜索表单 -->
                <div class="table-search">
                    <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                        :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                        <template #submitIcon>
                            <search-outlined />
                        </template>
                        <a-form-item name="keyword">
                            <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
                        </a-form-item>
                    </SkForm>
                </div>

                <!-- 操作按钮 -->
                <div class="table-operations">
                    <SkSpace>
                        <SkButton type="primary" @click.prevent="handleAdd" v-if="$config.auth('1571638020191')">
                            <template #icon><plus-outlined /></template>
                            {{ $t('common.add') }}
                        </SkButton>
                        <SkButton type="primary" @click.prevent="fetchData">
                            <template #icon>
                                <ReloadOutlined />
                            </template>
                            {{ $t('common.refresh') }}
                        </SkButton>
                    </SkSpace>
                </div>

                <!-- 表格 -->
                <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                    :ready="tableReady" @change="handleTableChange" :tipInfoHeight="56">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'oddNumber'">
                            <a @click="handleDetail(record)">{{ record.oddNumber }}</a>
                        </template>
                        <template v-if="column.dataIndex === 'processInstanceId'">
                            <ProcessDetail :processInstanceId="record.processInstanceId" />
                        </template>
                        <template v-if="column.dataIndex === 'state'">
                            <div
                                v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                            </div>
                        </template>
                        <template v-if="column.dataIndex === 'type'">
                            {{ initDictData['IFS_ORDER_TYPE']?.[record.type] }}
                        </template>
                        <template v-if="column.key === 'action'">
                            <SkSpace>
                                <template v-if="record.editRow == '1'">
                                    <a v-if="$config.auth('1642323133958')" @click="handleSubmitApproval(record)">
                                        {{ $t('common.submitApproval') }}
                                    </a>
                                    <SkDivider v-if="$config.auth('1642323133958')" type="vertical" />

                                    <a v-if="$config.auth('1571638020191')" @click="handleEdit(record)">
                                        {{ $t('common.edit') }}
                                    </a>
                                    <SkDivider v-if="$config.auth('1571638020191')" type="vertical" />

                                    <SkPopconfirm v-if="$config.auth('1571638039187')"
                                        :title="$t('common.deleteConfirm')" @confirm="handleDelete(record)"
                                        :okText="$t('common.delete')" :cancelText="$t('common.cancel')">
                                        <a class="danger-link">{{ $t('common.delete') }}</a>
                                    </SkPopconfirm>
                                </template>

                                <template v-if="record.editRow == '2'">
                                    <SkPopconfirm v-if="$config.auth('1642323144571')"
                                        :title="$t('common.revokeConfirm')" @confirm="handleRevoke(record)"
                                        :okText="$t('common.confirm')" :cancelText="$t('common.cancel')">
                                        <a class="danger-link">{{ $t('common.revoke') }}</a>
                                    </SkPopconfirm>
                                </template>
                            </SkSpace>
                        </template>
                    </template>
                </SkTable>
            </SkCard>
        </div>

        <!-- 添加弹窗组件 -->
        <SkModal v-model="modalVisible" :title="modalTitle">
            <ShowIndex ref="showIndexRef" v-if="modalType === 'details' || modalType === 'edit'"
                :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                @afterDataLoaded="handleAfterDataLoaded">
            </ShowIndex>
            <ApprovalPersonSelect v-if="modalType === 'approvalSelect'" :actKey="currentRecord?.serviceClassName"
                :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalClick" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'
import { PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTree from '@/components/SkTree/index.vue'
import SkAuthBtnGroup from '@/components/SkAuthBtnGroup/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'

const { proxy } = getCurrentInstance()

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格数据相关
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())

// 树相关
const originalData = ref([])
const treeData = ref([])
const expandedKeys = ref(['0'])
const searchText = ref('')
const selectedType = ref('')

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')
const currentRecord = ref(null)

// 初始化枚举数据
const initEnumData = ref({})
const initDictData = ref({})

// 在已有的变量定义后添加
const showIndexRef = ref(null)
const operatorParams = ref({})

const isShow = ref(false)

// 获取数据字典
const getInitData = async () => {
    // 获取字典数据
    let dictResult = await proxy.$util.getDictListByCode('IFS_ORDER_TYPE')
    // 获取枚举数据
    let enumResult = await proxy.$util.getEnumListMapByCode(['flowableStateEnum', 'correspondentEnterEnum'])
    initEnumData.value = enumResult

    // 转换字典数据为树形结构
    if (dictResult && dictResult.length > 0) {
        const treeNodes = dictResult.map(item => ({
            id: item.id,
            name: item.dictName,
            parentId: item.parentId,
            children: []
        }))

        // 使用 listToTree 方法转换为树形结构
        const tree = proxy.$util.listToTree(treeNodes, {
            id: 'id',
            parentId: 'parentId',
            children: 'children'
        })

        originalData.value = tree
        treeData.value = JSON.parse(JSON.stringify(tree))

        // 如果有根节点，则展开它
        if (tree.length > 0) {
            expandedKeys.value = [tree[0].id]
        }
    }
    isShow.value = true
}

// 树节点搜索
const onSearch = (value) => {
    if (proxy.$util.isNull(value.value)) {
        treeData.value = JSON.parse(JSON.stringify(originalData.value))
        // 如果有节点则展开第一个节点
        if (originalData.value.length > 0) {
            expandedKeys.value = [originalData.value[0].id]
        }
        return
    }
    const filteredData = proxy.$util.searchTree(originalData.value, value.value, {
        matchFields: ['name'],
        children: 'children'
    })
    treeData.value = filteredData
    const keys = []
    const collectExpandedKeys = (nodes) => {
        nodes.forEach(node => {
            if (node.expanded) {
                keys.push(node.id)
            }
            if (node.children?.length) {
                collectExpandedKeys(node.children)
            }
        })
    }
    collectExpandedKeys(filteredData)
    expandedKeys.value = keys
}

// 处理展开/收起
const onExpand = (keys, info) => {
    if (!info.expanded) {
        // 获取当前节点及其所有子节点的 keys
        const childKeys = getAllChildKeys(info.node)
        // 将当前节点的 key 也加入到需要移除的列表中
        childKeys.push(info.node.key)
        // 从所有展开的 keys 中移除当前节点及其子节点的 keys
        expandedKeys.value = expandedKeys.value.filter(key => !childKeys.includes(key))
    } else {
        expandedKeys.value = keys
    }
}

// 修改 getAllChildKeys 函数，使其能够正确处理多层级节点
const getAllChildKeys = (node) => {
    const keys = []
    const traverse = (currentNode) => {
        // 如果是树组件的节点对象，需要通过 dataRef 或 data 获取实际数据
        const nodeData = currentNode.dataRef || currentNode.data || currentNode
        
        // 获取子节点数组
        const children = nodeData.children || []
        
        children.forEach(child => {
            // 使用与 fieldNames 配置相匹配的 key 字段
            keys.push(child.id)  // 使用 id 作为 key
            traverse(child)
        })
    }
    
    traverse(node)
    return keys
}

// 树节点选择
const handleTreeSelect = (selectedKeys) => {
    selectedType.value = selectedKeys[0] || ''
    refreshTable()
}

// 表格列配置
const columns = ref([
    {
        title: '序号',
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单据编号',
        dataIndex: 'oddNumber',
        width: 200,
        align: 'center'
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 100,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 90,
        align: 'center'
    },
    {
        title: '单据类型',
        dataIndex: 'type',
        width: 120,
    },
    {
        title: '往来单位类型',
        dataIndex: 'holderKey',
        width: 100,
    },
    {
        title: '往来单位',
        dataIndex: 'holderId',
        width: 150,
        customRender: ({ record }) => record.holderMation?.name
    },
    {
        title: '经手人',
        dataIndex: 'handsPersonName',
        width: 120
    },
    {
        title: '单据日期',
        dataIndex: 'operTime',
        width: 120,
        align: 'center'
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center'
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 120
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 250,
        fixed: 'right',
        align: 'center'
    }
])

// 权限点处理
const authMation = ref({})
const handleChanges = async (key, value) => {
    authMation.value[key] = value
    pagination.current = 1
    fetchData()
}

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            typeId: selectedType.value,
            keyword: searchForm.keyword?.trim() || '',
            ...authMation.value
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'income001',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 重置处理
const handleReset = () => {
    searchForm.keyword = ''
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 刷新表格
const refreshTable = () => {
    pagination.current = 1
    fetchData()
}

// 修改新增处理方法
const handleAdd = () => {
    modalType.value = 'edit'
    modalTitle.value = '新增'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023031800001'
    }
}

// 修改编辑处理方法
const handleEdit = (record) => {
    modalType.value = 'edit'
    modalTitle.value = '编辑'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023031800002',
        params: {
            id: record.id
        }
    }
}

// 修改详情处理方法
const handleDetail = (record) => {
    modalType.value = 'details'
    modalTitle.value = '详情'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023032200001',
        params: {
            id: record.id
        }
    }
}

// 删除处理
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().ifsBasePath + 'income005',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        await fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 撤销处理
const handleRevoke = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().ifsBasePath + 'income009',
            { processInstanceId: record.processInstanceId }
        )
        SkMessage.success('撤销成功')
        await fetchData()
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 提交审批处理
const handleSubmitApproval = (record) => {
    currentRecord.value = record
    modalTitle.value = '审批人选择'
    modalType.value = 'approvalSelect'
    modalVisible.value = true
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id
        }

        await proxy.$http.post(
            proxy.$config.getConfig().ifsBasePath + 'income008',
            params
        )

        SkMessage.success('提交成功')
        modalVisible.value = false
        await fetchData()
    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 添加弹窗关闭处理方法
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 添加数据加载后的处理方法
const handleAfterDataLoaded = (formData) => {
    // 这里可以处理表单数据加载后的逻辑
    const notEdit = proxy.$config.formEditType.notEdit

    if (!proxy.$util.isNull(formData.erpOrderItemList)) {
        formData.erpOrderItemList.forEach(item => {
            // 下拉框的特殊配置
            item[`normsId_config`] = {
                dataType: 1,
                defaultData: item.materialMation?.materialNorms || []
            }

            // 当有单据来源时设置禁用
            if (!proxy.$util.isNull(formData.fromId)) {
                const disabledFields = [
                    'materialId',
                    'normsId',
                    'unitPrice',
                    'allPrice',
                    'taxRate',
                    'taxMoney',
                    'taxUnitPrice',
                    'taxLastMoney'
                ]

                disabledFields.forEach(field => {
                    item[`${field}_config`] = { isEdit: notEdit }
                })
            }
        })
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
})
</script>

<style scoped>
/* 使用common.css中的公共样式 */
</style>