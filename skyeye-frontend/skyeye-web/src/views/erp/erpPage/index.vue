<template>
    <div class="container-manage">
        <!-- 顶部卡片统计 -->
        <a-row :gutter="[16, 16]">
            <!-- 本月累计销售 -->
            <a-col :span="6">
                <SkCard bordered title="本月累计销售" :hoverable="true" :headStyle="{ padding: '15px' }"
                    :bodyStyle="{ padding: '15px' }">
                    <template #extra>
                        <SkTag color="blue">月</SkTag>
                    </template>
                    <p class="big-font">{{ salesMoney }}</p>
                    <p class="desc-text">当前月已审核通过的销售订单金额</p>
                </SkCard>
            </a-col>

            <!-- 本月累计零售 -->
            <a-col :span="6">
                <SkCard bordered title="本月累计零售" :hoverable="true" :headStyle="{ padding: '15px' }"
                    :bodyStyle="{ padding: '15px' }">
                    <template #extra>
                        <SkTag color="green">月</SkTag>
                    </template>
                    <p class="big-font">{{ retailMoney }}</p>
                    <p class="desc-text">当前月已审核通过的零售订单金额</p>
                </SkCard>
            </a-col>

            <!-- 本月累计采购 -->
            <a-col :span="6">
                <SkCard bordered title="本月累计采购" :hoverable="true" :headStyle="{ padding: '15px' }"
                    :bodyStyle="{ padding: '15px' }">
                    <template #extra>
                        <SkTag color="orange">月</SkTag>
                    </template>
                    <p class="big-font">{{ purchaseMoney }}</p>
                    <p class="desc-text">当前月已审核通过的采购订单金额</p>
                </SkCard>
            </a-col>

            <!-- 本月利润 -->
            <a-col :span="6">
                <SkCard bordered title="本月利润（已审核通过）" :hoverable="true" :headStyle="{ padding: '15px' }"
                    :bodyStyle="{ padding: '15px' }">
                    <template #extra>
                        <SkTag color="red">月</SkTag>
                    </template>
                    <p class="big-font">{{ profitMoney }}</p>
                    <p class="desc-text">零售订单金额 + 销售订单金额 - 采购订单金额</p>
                </SkCard>
            </a-col>

            <!-- 采购统计 -->
            <a-col :span="12">
                <SkCard bordered :bodyStyle="{ padding: '10px' }" title="采购统计" :headStyle="{ padding: '10px' }">
                    <div ref="purchaseChartRef" style="height: 400px;"></div>
                </SkCard>
            </a-col>

            <!-- 销售统计 -->
            <a-col :span="12">
                <SkCard bordered :bodyStyle="{ padding: '10px' }" title="销售统计" :headStyle="{ padding: '10px' }">
                    <div ref="salesChartRef" style="height: 400px;"></div>
                </SkCard>
            </a-col>

            <!-- 利润统计 -->
            <a-col :span="24">
                <SkCard bordered :bodyStyle="{ padding: '10px' }" title="利润统计" :headStyle="{ padding: '10px' }">
                    <div ref="profitChartRef" style="height: 400px;"></div>
                </SkCard>
            </a-col>
        </a-row>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import SkCard from '@/components/SkCard/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 数据统计
const salesMoney = ref('0.00')
const retailMoney = ref('0.00')
const purchaseMoney = ref('0.00')
const profitMoney = ref('0.00')

// 图表引用
const purchaseChartRef = ref(null)
const salesChartRef = ref(null)
const profitChartRef = ref(null)

// 图表实例
let purchaseChart = null
let salesChart = null
let profitChart = null

// 初始化数据
const initData = async () => {
    try {
        // 获取顶部四个统计数据
        const summaryRes = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'erppage001'
        )

        if (summaryRes && summaryRes.bean) {
            salesMoney.value = summaryRes.bean.salesMoney || '0.00'
            retailMoney.value = summaryRes.bean.retailMoney || '0.00'
            purchaseMoney.value = summaryRes.bean.purchaseMoney || '0.00'
            profitMoney.value = summaryRes.bean.profitMoney || '0.00'
        }

        // 获取近六个月采购数据
        await initSixMonthPurchase()

    } catch (error) {
        SkMessage.error('初始化数据失败:', error)
    }
}

// 近六个月审核通过的采购订单
const initSixMonthPurchase = async () => {
    const res = await proxy.$http.post(
        proxy.$config.getConfig().erpBasePath + 'erppage002'
    )

    if (res && res.rows) {
        renderPurchaseChart(res.rows)
    }

    // 获取近六个月销售数据
    await initSixMonthSales()
}

// 近六个月审核通过的销售订单
const initSixMonthSales = async () => {
    const res = await proxy.$http.post(
        proxy.$config.getConfig().erpBasePath + 'erppage003'
    )

    if (res && res.rows) {
        renderSalesChart(res.rows)
    }

    // 获取近12个月利润数据
    await initSixMonthProfit()
}

// 近12个月审核通过的利润订单
const initSixMonthProfit = async () => {
    const res = await proxy.$http.post(
        proxy.$config.getConfig().erpBasePath + 'erppage004'
    )

    if (res && res.rows) {
        renderProfitChart(res.rows)
    }
}

// 渲染采购图表
const renderPurchaseChart = (rows) => {
    if (!purchaseChartRef.value) return

    const nameStr = []
    const numStr = []

    rows.forEach(item => {
        nameStr.push(item.yearMonth)
        numStr.push(proxy.$util.isNull(item.money) ? 0 : item.money)
    })

    purchaseChart = echarts.init(purchaseChartRef.value)
    purchaseChart.setOption({
        title: {
            text: '近六个月审核通过的采购订单',
            x: 'center'
        },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '15%'  // 向左平移15%
        },
        xAxis: {
            type: 'category',
            data: nameStr
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: '金额',
            type: 'line',
            smooth: true,
            data: numStr,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(78, 123, 239, 0.5)'
                    }, {
                        offset: 1, color: 'rgba(78, 123, 239, 0.1)'
                    }]
                }
            },
            lineStyle: {
                width: 3,
                color: '#4e7bef'
            },
            itemStyle: {
                color: '#4e7bef'
            }
        }]
    })
}

// 渲染销售图表
const renderSalesChart = (rows) => {
    if (!salesChartRef.value) return

    const nameStr = []
    const numStr = []

    rows.forEach(item => {
        nameStr.push(item.yearMonth)
        numStr.push(proxy.$util.isNull(item.money) ? 0 : item.money)
    })

    salesChart = echarts.init(salesChartRef.value)
    salesChart.setOption({
        title: {
            text: '近六个月审核通过的销售订单',
            x: 'center'
        },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '15%'  // 向左平移15%
        },
        xAxis: {
            type: 'category',
            data: nameStr
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: '金额',
            type: 'line',
            smooth: true,
            data: numStr,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(78, 123, 239, 0.5)'
                    }, {
                        offset: 1, color: 'rgba(78, 123, 239, 0.1)'
                    }]
                }
            },
            lineStyle: {
                width: 3,
                color: '#4e7bef'
            },
            itemStyle: {
                color: '#4e7bef'
            }
        }]
    })
}

// 渲染利润图表
const renderProfitChart = (rows) => {
    if (!profitChartRef.value) return

    const nameStr = []
    const numStr = []

    rows.forEach(item => {
        nameStr.push(item.yearMonth)
        numStr.push(proxy.$util.isNull(item.profitMoney) ? 0 : item.profitMoney)
    })

    profitChart = echarts.init(profitChartRef.value)
    profitChart.setOption({
        title: {
            text: '近十二个月的利润（已审核通过）',
            x: 'center',
            subtext: '零售订单金额 + 销售订单金额 - 采购订单金额'
        },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '15%'  // 向左平移15%
        },
        xAxis: {
            type: 'category',
            data: nameStr
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: '利润',
            type: 'line',
            smooth: true,
            data: numStr,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(78, 123, 239, 0.5)'
                    }, {
                        offset: 1, color: 'rgba(78, 123, 239, 0.1)'
                    }]
                }
            },
            lineStyle: {
                width: 3,
                color: '#4e7bef'
            },
            itemStyle: {
                color: '#4e7bef'
            }
        }]
    })
}

// 窗口大小变化时重新调整图表大小
const handleResize = () => {
    if (purchaseChart) purchaseChart.resize()
    if (salesChart) salesChart.resize()
    if (profitChart) profitChart.resize()
}

// 组件挂载时初始化数据
onMounted(() => {
    initData()
    window.addEventListener('resize', handleResize)
})

// 组件卸载时清理资源
onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (purchaseChart) purchaseChart.dispose()
    if (salesChart) salesChart.dispose()
    if (profitChart) profitChart.dispose()
})
</script>

<style scoped>
.container-manage {
    display: block;
    height: auto;
}

.big-font {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
}

.desc-text {
    color: #999;
    font-size: 14px;
}

/* 添加深度选择器覆盖 ant-card-body 样式 */
:deep(.ant-card-body) {
    height: auto !important;
    overflow: auto;
}
</style>
