<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <!-- 搜索区域 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <SearchOutlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1718282987406')" type="primary" @click.prevent="handleAdd">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格区域 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <!-- 单号列 -->
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetails(record)">{{ record.oddNumber }}</a>
                        <template v-if="!$util.isNull(record.fromId)">
                            <SkTag :color="$util.getTagColor(column.dataIndex)">
                                转
                            </SkTag>
                        </template>
                    </template>
                    <!-- 流程ID列 -->
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <!-- 状态列 -->
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <!-- 退货状态列 -->
                    <template v-if="column.dataIndex === 'returnState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['qualityInspectionReturnState'], 'id', record.returnState, 'name')">
                        </div>
                    </template>
                    <!-- 入库状态列 -->
                    <template v-if="column.dataIndex === 'putState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['qualityInspectionPutState'], 'id', record.putState, 'name')">
                        </div>
                    </template>
                    <!-- 来源类型列 -->
                    <template v-if="column.dataIndex === 'fromTypeId'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['qualityInspectionFromType'], 'id', record.fromTypeId, 'name')">
                        </div>
                    </template>
                    <!-- 操作列 -->
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 编辑状态的操作按钮 -->
                            <template v-if="record.editRow === 1">
                                <a v-if="$config.auth('1718283008391')" @click="handleSubmitApproval(record)">
                                    {{ $t('common.submitApproval') }}
                                </a>
                                <SkDivider v-if="$config.auth('1718283008391')" type="vertical" />
                                <a v-if="$config.auth('1718282987406')" @click="handleEdit(record)">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1718282987406')" type="vertical" />
                                <SkPopconfirm v-if="$config.auth('1718283023135')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>
                            <!-- 审批中状态的操作按钮 -->
                            <template v-if="record.editRow === 2">
                                <SkPopconfirm v-if="$config.auth('1718283040132')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>
                            <!-- 审批通过状态的操作按钮 -->
                            <template v-if="record.state === 'pass'">
                                <template v-if="record.putState === 2 || record.putState === 3">
                                    <a v-if="$config.auth('1718283090913')" @click="handleTurnPurchase(record)">
                                        {{ $t('erp.purchaseOrder.transfer') }}
                                    </a>
                                    <SkDivider v-if="$config.auth('1718283090913')" type="vertical" />
                                </template>
                                <template v-if="record.returnState === 2 || record.returnState === 3">
                                    <a v-if="$config.auth('1718283139640')" @click="handleTurnReturns(record)">
                                        {{ $t('erp.purchaseQualityInspection.salesReturn') }}
                                    </a>
                                </template>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <!-- 弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle" @cancel="handleModalClick">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details' || modalType === 'turnPurchase' || modalType === 'turnReturns'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @loaded="handleShowIndexLoaded"
                    :whetherCustomerData="whetherCustomerData" :customerData="customerData" @loadPageItem="loadPageItem"
                    @customerDataSave="customerDataSave" @close="handleModalClick" @cell-change="handleCellChange"
                    @afterDataLoaded="handleAfterDataLoaded" @handleChange="handleChange">
                    <!-- 传递自定义单元格组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                    <!-- 添加用户选择的自定义渲染 -->
                    <template #cell-chooseInput-inspectorId="slotProps">
                        <a-form-item-rest>
                            <SkUserSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex" :multiple="false"
                                @change="(users) => handleUserChange(users, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
                <ApprovalPersonSelect v-else :actKey="currentRecord?.serviceClassName" :businessData="null"
                    @submit="handleApprovalPersonSubmit" @cancel="handleModalClick" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'
import { PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkUserSelect from '@/components/SkUserSelect/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n();

// 表格相关数据
const loading = ref(false)
const tableData = ref([])
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())
const searchForm = reactive({
    keyword: ''
})

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})
const showIndexRef = ref(null)
const whetherCustomerData = ref(false)
const customerData = ref({})
const currentRecord = ref(null)

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode([
        'flowableStateEnum',
        'qualityInspectionReturnState',
        'qualityInspectionPutState',
        'qualityInspectionFromType'
    ])
    initEnumData.value = result
}

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        type: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单号',
        dataIndex: 'oddNumber',
        width: 220,
    },
    {
        title: '单据日期',
        dataIndex: 'operTime',
        width: 140,
        align: 'center'
    },
    {
        title: '来源单据信息',
        align: 'center',
        children: [{
            title: '来源类型',
            dataIndex: 'fromTypeId',
            width: 150,
        }, {
            title: '单据编号',
            dataIndex: 'fromId',
            width: 200,
            customRender: ({ record }) => record.fromMation?.oddNumber
        }]
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 100
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 90
    },
    {
        title: '退货状态',
        dataIndex: 'returnState',
        width: 90
    },
    {
        title: '入库状态',
        dataIndex: 'putState',
        width: 150
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center'
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 120
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: t('common.action'),
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 250
    }
]

// 加载表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: pagination.current,
            limit: pagination.pageSize,
            ...searchForm
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryQualityInspectionList',
            params
        )

        tableData.value = res.rows
        pagination.total = res.total
    } catch (error) {
        SkMessage.error('获取列表失败')
    } finally {
        loading.value = false
    }
}

// 处理表格变化
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 处理搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 处理新增
const handleAdd = () => {
    modalType.value = 'add'
    modalTitle.value = '新增'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024061300001'
    }
}

// 处理编辑
const handleEdit = (record) => {
    modalType.value = 'edit'
    modalTitle.value = '编辑'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024061300002',
        params: { id: record.id }
    }
}

// 处理详情
const handleDetails = (record) => {
    modalType.value = 'details'
    modalTitle.value = '详情'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024061300003',
        params: { id: record.id }
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'deleteQualityInspection',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 处理提交审批
const handleSubmitApproval = (record) => {
    currentRecord.value = record
    modalTitle.value = '审批人选择'
    modalType.value = 'approval'
    modalVisible.value = true
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id
        }
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'submitQualityInspectionToApproval',
            params
        )
        SkMessage.success('提交成功')
        modalVisible.value = false
        fetchData()
    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 处理撤销
const handleRevoke = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().erpBasePath + 'revokeQualityInspection',
            { processInstanceId: record.processInstanceId }
        )
        SkMessage.success('撤销成功')
        fetchData()
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 处理转采购入库
const handleTurnPurchase = async (record) => {
    modalType.value = 'turnPurchase'
    modalTitle.value = '转采购入库'
    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryQualityInspectionTransById',
        { id: record.id }
    )
    const data = res.bean
    data.erpOrderItemList = data.qualityInspectionItemList
    whetherCustomerData.value = true
    customerData.value = res.bean
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023042300002',
        params: { id: record.id }
    }
}

// 处理转退货单
const handleTurnReturns = async (record) => {
    modalType.value = 'turnReturns'
    modalTitle.value = '转退货单'
    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryQualityInspectionTransReturnById',
        { id: record.id }
    )
    const data = res.bean
    data.erpOrderItemList = data.qualityInspectionItemList
    whetherCustomerData.value = true
    customerData.value = res.bean
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023042400002',
        params: { id: record.id }
    }
}

// 处理弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 处理数据保存
const customerDataSave = async (data) => {
    try {
        const pageId = operatorParams.value.pageId
        if (pageId === 'FP2023042300002') {
            // 转采购入库
            await proxy.$http.post(
                proxy.$config.getConfig().erpBasePath + 'qualityInspectionToPurchasePut',
                data
            )
        } else if (pageId === 'FP2023042400002') {
            // 转退货单
            await proxy.$http.post(
                proxy.$config.getConfig().erpBasePath + 'qualityInspectionToPurchaseReturn',
                data
            )
        }
        SkMessage.success('操作成功')
        handleModalClick(true)
    } catch (error) {
        SkMessage.error('操作失败')
    }
}

// 处理用户选择变化
const handleUserChange = async (users, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (users && users.length > 0) {
        record[mationKey] = users[0]
    } else {
        delete record[mationKey]
    }
}

// 处理组件加载完成
const handleShowIndexLoaded = (formData) => {
    const pageId = operatorParams.value.pageId

    // 在组件加载完成后执行需要的操作
    if (pageId == 'FP2024061300002' && !proxy.$util.isNull(formData.fromId)) {
        showIndexRef.value?.writeComponentRef?.updateShowAdd("qualityInspectionItemList", false)
    } else if (pageId == 'FP2023042300002' || pageId == 'FP2023042400002' && !proxy.$util.isNull(formData.fromId)) {
        showIndexRef.value?.writeComponentRef?.updateShowAdd("erpOrderItemList", false)
    }
}

const loadPageItem = (bean, formData) => {
    if (operatorParams.value.pageId == 'FP2023042300002' || operatorParams.value.pageId == 'FP2023042400002' && !proxy.$util.isNull(formData.fromId)) {
        if (bean.attrKey == 'holderId') {
            bean.isEdit = $config.formEditType.notEdit
        }
        // 转单据时隐藏来源单据
        if (operatorParams.value.pageId == 'FP2023042300002' || operatorParams.value.pageId == 'FP2023042400002') {
            if (bean.attrKey == 'fromId') {
                bean.isShow = false
            }
        }
    }
}

// 处理数据回显时，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
    const pageId = operatorParams.value.pageId
    const notEdit = proxy.$config.formEditType.notEdit
    if (!proxy.$util.isNull(formData.qualityInspectionItemList)) {
        formData.qualityInspectionItemList.forEach(item => {
            // 下拉框的特殊配置
            item[`normsId_config`] = {
                dataType: 1,
                defaultData: item.materialMation?.materialNorms || []
            }

            // 当有单据来源时设置禁用
            if (!proxy.$util.isNull(formData.fromId)) {
                const disabledFields = [
                    'materialId',
                    'normsId',
                    'unitPrice',
                    'allPrice',
                    'taxRate',
                    'taxMoney',
                    'taxUnitPrice',
                    'taxLastMoney',
                    'qualityInspection',
                    'qualityInspectionRatio'
                ]

                disabledFields.forEach(field => {
                    item[`${field}_config`] = { isEdit: notEdit }
                })
            } else {
                // 如果没用单据来源，但是要转采购入库或转采购质检时设置禁用
                if (pageId === 'FP2023042300002' || pageId === 'FP2023042400002') {
                    const disabledFields = [
                        'materialId',
                        'normsId',
                        'unitPrice',
                        'allPrice',
                        'taxRate',
                        'taxMoney',
                        'taxUnitPrice',
                        'taxLastMoney'
                    ]

                    disabledFields.forEach(field => {
                        item[`${field}_config`] = { isEdit: notEdit }
                    })
                }
            }
        })
    }
}

// 处理单元格变化
const handleCellChange = ({ record, dataIndex, value, column }, formData) => {
    // 处理物料选择变化
    if (dataIndex == 'materialId') {
        record.unitPrice = 0
    } else if (dataIndex == 'normsId') {
        const materialNorms = column.getConfig(record).defaultData
        const norms = materialNorms.find(item => item.id === value)
        // 获取物料的预估采购价
        record.unitPrice = norms.estimatePurchasePrice
    }
    // 处理金额计算
    const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
    // 将result合入到record中
    Object.assign(record, result)

    calcMoney(formData)
}

// 处理表单项变化
const handleChange = ({ attrKey, formData }) => {
    if (attrKey == 'discount') {
        calcMoney(formData)
    }
}

const calcMoney = (formData) => {
    // 处理金额计算
    const erpOrderItemList = formData?.erpOrderItemList || []
    let totalPrice = 0;
    if (!proxy.$util.isNull(erpOrderItemList)) {
        erpOrderItemList.forEach((item, i) => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.taxLastMoney)
        })
    }

    // 优惠信息
    const discountMoney = proxy.$util.erpUtil.calcDiscountMoney(formData, totalPrice)
    totalPrice = proxy.$util.calculationUtil.subtraction(totalPrice, discountMoney)
    formData.discountMoney = discountMoney
    formData.totalPrice = totalPrice
}

// 处理物料变化逻辑
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        record["normsId"] = undefined
        record["unitPrice"] = 0

        // 等待组件挂载完成
        await nextTick()

        // 修改当前行的表格列配置
        showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
            "normsId": {
                dataType: 1,
                defaultData: material.materialNorms
            }
        })

        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
    }
}

// 初始化
onMounted(async () => {
    await getInitData()
    nextTick(() => {
        tableReady.value = true
        fetchData()
    })
})
</script>

<style scoped></style>