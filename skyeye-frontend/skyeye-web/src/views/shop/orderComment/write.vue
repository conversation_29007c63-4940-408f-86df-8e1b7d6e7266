<template>
	<div>
		<SkForm ref="formRef" v-model="formData" :rules="rules" @submit="handleSubmit" @reset="handleCancel">
			<SkHrTitle>基础信息</SkHrTitle>
			<a-row>
				<a-col :span="24">
					<a-form-item label="回复内容" name="context">
						<SkTextarea v-model="formData.context" placeholder="请输入回复内容" allowClear />
					</a-form-item>
				</a-col>
			</a-row>
		</SkForm>
	</div>
</template>

<script setup>
import {
	ref,
	reactive,
	onMounted,
	getCurrentInstance
} from 'vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTextarea  from '@/components/SkTextarea/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const {
	proxy
} = getCurrentInstance()

// 定义 props 和 emits
const props = defineProps({
	id: {
		type: [String, Number],
		default: ''
	}
})
const emit = defineEmits(['submit', 'reset'])

// 表单数据和状态初始化
const formRef = ref(null)
const loading = ref(false)

// 使用单个 formData 对象
const formData = reactive({
	context: '',
	id: '',
	parentId: '',
	normsId: '',
	materialId: '',
	orderId: '',
	orderItemId: '',
	type: ''
})

// 表单校验规则
const rules = {
	context: [{
		required: true,
		message: '请输入回复内容',
		trigger: 'blur'
	}]
}

// 获取详情
const getDetail = async () => {
	try {
		const res = await proxy.$http.post(
			proxy.$config.getConfig().shopBasePath + 'selectOrderCommentById',
			{ id: props.id }
		)
		if (res.bean) {
			formData.parentId = res.bean.id,
			formData.normsId = res.bean.normsId,
			formData.materialId = res.bean.materialId,
			formData.orderId = res.bean.orderId,
			formData.orderItemId = res.bean.orderItemId,
			formData.type = 2
		}
	} catch (error) {
		SkMessage.error('获取详情失败')
	}
}

// 处理提交
const handleSubmit = async () => {
	try {
		await formRef.value.validate()
		loading.value = true
		const params = {
			id: props.id,
			context: formData.context,
			type: formData.type,
			isComment: 1, // 设置为已回复
			parentId: formData.parentId,
			normsId: formData.normsId,
			materialId: formData.materialId,
			orderId: formData.orderId,
			orderItemId: formData.orderItemId
		}
		await proxy.$http.post(
			proxy.$config.getConfig().shopBasePath + 'insertOrderComment',
			params
		)
		emit('submit')
	} catch (error) {
		SkMessage.error(error.message || '操作失败')
	} finally {
		loading.value = false
	}
}

const handleCancel = () => {
	emit('reset')
}

// 初始化数据
onMounted(async () => {
	if (props.id) {
		await getDetail()
	}
})
</script>

<style scoped></style>