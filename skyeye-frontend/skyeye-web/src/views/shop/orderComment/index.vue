<template>
	<div class="container-manage">
		<SkCard ref="cardRef" :bordered="false">
			<!-- 搜索表单 -->
			<div class="table-search">
				<SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
					:submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
					<template #submitIcon>
						<search-outlined />
					</template>
					<a-form-item name="keyword">
						<SkInput v-model="searchForm.keyword" placeholder="请输入" allowClear />
					</a-form-item>
				</SkForm>
			</div>
			<!-- 操作按钮 -->
			<div class="table-operations">
				<SkSpace>
					<SkButton type="primary" @click.prevent="fetchData">
						<template #icon>
							<ReloadOutlined />
						</template>
						{{ $t('common.refresh') }}
					</SkButton>
				</SkSpace>
			</div>
			<!-- 表格 -->
			<SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
				:ready="tableReady" @change="handleTableChange">
				<template #bodyCell="{ column, record }">
					<template v-if="column.dataIndex === 'type'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['orderCommentType'], 'id', record.type, 'name')">
						</div>
					</template>
					<!-- 是否回复列 -->
					<template v-else-if="column.dataIndex === 'isComment'">
						{{ record.isComment === 1 ? '是' : '否' }}
					</template>
					<!-- 操作列 -->
					<template v-else-if="column.key === 'action'">
						<SkSpace>
							<a v-if="$config.auth('1733054310275') && record.type !== 2"
								@click="handleReplay(record)">{{
									$t('shop.orderComment.reply') }}
							</a>
							<SkDivider type="vertical" />
						</SkSpace>
					</template>
				</template>
			</SkTable>
			<SkModal v-model="modalVisible" :title="modalTitle">
				<OrderWrite :id="replayId" @submit="handleModalOk" @reset="handleModalCancel" />
			</SkModal>
		</SkCard>
	</div>

</template>

<script setup>
import { ref, reactive, onMounted, nextTick, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import SkButton from '@/components/SkButton/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import OrderWrite from './write.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 表格列配置
const columns = [{
	title: t('common.serialNum'),
	dataIndex: 'index',
	width: 80,
	align: 'center',
	fixed: 'left',
	customRender: ({ index }) => {
		// 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
		return (pagination.current - 1) * pagination.pageSize + index + 1
	}
},
{
	title: '评价类型',
	dataIndex: 'type',
	width: 100,
	align: 'center'
},
{
	title: '评价内容',
	dataIndex: 'context',
	width: 80,
	align: 'left'
},
{
	title: '星级',
	dataIndex: 'start',
	width: 50,
	align: 'left'
},
{
	title: '商家是否回复',
	dataIndex: 'isComment',
	width: 50,
	align: 'left'
},
{
	title: '上传时间',
	dataIndex: 'createTime',
	width: 100,
	align: 'center',
	sorter: true
},
{
	title: t('common.action'),
	key: 'action',
	width: 80,
	align: 'center',
	fixed: 'right'
}
]
// 搜索表单数据
const searchForm = reactive({
	keyword: '',
})

// 编辑状态相关变量
const replayId = ref('') // 当前编辑的图标ID
const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
	let result = await proxy.$util.getEnumListMapByCode(['orderCommentType']);
	initEnumData.value = result
}
// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('回复评价')
// 获取数据
const fetchData = async () => {
	loading.value = true
	try {
		// 构建查询参数
		const params = {
			page: Number(pagination.current || 1),
			limit: Number(pagination.pageSize || 10),
			keyword: searchForm.keyword?.trim() || ''
		}
		// 发送查询请求
		const res = await proxy.$http.post(
			proxy.$config.getConfig().shopBasePath + 'queryOrderCommentPageListPC',
			params
		)
		tableData.value = res.rows || []
		pagination.total = res.total || 0
	} catch (error) {
		SkMessage.error('获取数据失败')
		tableData.value = []
		pagination.total = 0
	} finally {
		loading.value = false
	}
}
// 事件处理 搜索
const handleSearch = () => {
	pagination.current = 1
	fetchData()
}

const handleTableChange = (pag) => {
	if (pag) {
		pagination.current = Number(pag.current || 1)
		pagination.pageSize = Number(pag.pageSize || 10)
	}
	fetchData()
}

const handleReplay = (record) => {
	modalTitle.value = '回复'
	replayId.value = record.id
	modalVisible.value = true
}


const handleModalCancel = () => {
	modalVisible.value = false
}

const tableReady = ref(false)

const handleModalOk = () => {
	modalVisible.value = false
	fetchData()
}

// 初始化
onMounted(async () => {
	await nextTick()
	requestAnimationFrame(() => {
		tableReady.value = true
	})
	getInitData()
	fetchData() // 获取表格数据
})
</script>
