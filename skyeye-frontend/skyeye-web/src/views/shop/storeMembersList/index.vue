<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item>
                        <SkFlex>
                            <div class="content-tag-name">门店</div>
                            <SkSelect v-model="selectedStoreId" showSearch :options="options" placeholder="请选择门店"
                                :filterOption="filterOption" @change="handleStoreChange" />
                        </SkFlex>
                    </a-form-item>
                    <a-form-item>
                        <SkFlex>
                            <div class="content-tag-name">会员</div>
                            <SkInput v-model="searchForm.keyword" placeholder="请输入会员姓名/联系电话" allowClear
                                @pressEnter="handleSearch" />
                        </SkFlex>
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 数据表格 -->
            <SkTable v-if="tableReady" :columns="columns" :data-source="tableData" :loading="loading"
                :pagination="pagination" :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <a v-if="$config.auth('1722130800533')" @click="handleEdit(record)">
                            {{ $t('common.edit') }}
                        </a>
                        <SkDivider type="vertical" />
                        <a-popconfirm :title="$t('common.deleteConfirm')" @confirm="handleDelete(record)"
                            :okText="$t('common.delete')" :cancelText="$t('common.cancel')">
                            <a class="danger-link">{{ $t('common.delete') }}</a>
                        </a-popconfirm>
                    </template>
                </template>
            </SkTable>

            <!-- 编辑弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle" :width="modalWidth">
                <ShowIndex :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalCancel">
                </ShowIndex>
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ReloadOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkFlex from '@/components/SkFlex/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'


const { proxy } = getCurrentInstance()
const selectedStoreId = ref('')
const options = ref([])

// 表格
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const tableReady = ref(false)
const operatorParams = ref({})

// 弹窗控制
const modalVisible = ref(false)
const modalTitle = ref('添加商品类型')
const modalWidth = ref('70%')

// 表格列配置
const columns = [
    {
        title: '序号',
        width: 80,
        align: 'center',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '会员姓名',
        dataIndex: 'name',
        width: 180,
    },
    {
        title: '联系电话',
        dataIndex: 'phone',
        width: 200,
        align: 'center',

    },
    {
        title: '邮箱',
        dataIndex: 'email',
        width: 200,
    },
    {
        title: '备注',
        dataIndex: 'remark',
        width: 300,
    },
    {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 150,
        align: 'center',
    }
]

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 添加 filterOption 函数
const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 选择门店的处理函数
const handleStoreChange = (value) => {
    selectedStoreId.value = value
    fetchData()
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023042400006',
        params: {
            id: record.id
        }
    }
}

// 新增
const handleAdd = () => {
    modalTitle.value = '新增'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023042400005',
        params: {}
    }
}

// 弹窗取消
const handleModalCancel = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 获取门店数据
const fetchStores = async () => {
    try {
        const res = await proxy.$http.get(proxy.$config.getConfig().shopBasePath + 'storeStaff005');
        options.value = res.rows.map(store => ({
            value: store.id,
            label: store.name
        }));
        // 自动选中第一个门店
        selectedStoreId.value = options.value[0].value;
        await fetchData();
    } catch (error) {
        SkMessage.error('获取门店数据失败');
    }
};

// 获取会员数据
const fetchData = async () => {
    loading.value = true;
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            holderId: selectedStoreId.value || '', // 使用搜索表单的值
            keyword: searchForm.keyword || ''
        };

        const res = await proxy.$http.post(
            proxy.$config.getConfig().shopBasePath + 'member001',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        loading.value = true
        const params = {
            id: record.id
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().shopBasePath + 'member004',
            params
        )
        SkMessage.success('删除成功')
        await fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    } finally {
        loading.value = false
    }
}

// 修改初始化方法
onMounted(async () => {
    await fetchStores();
    tableReady.value = true
})

</script>

<style scoped></style>
