// 生成医疗保险理赔数据

import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';


// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 生成随机日期
function randomDate(start, end) {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// 随机生成姓名
const familyNames = '赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻水云苏潘葛范彭郎鲁韦昌马苗凤花方俞任袁柳鲍史唐费岑薛雷贺倪汤滕殷罗毕郝邬安常乐于时傅卞齐康伍余元卜顾孟平黄和穆萧尹姚邵湛汪祁毛禹狄米贝明臧计成戴宋茅庞熊纪舒屈项祝董粱杜阮席季麻强贾路娄危江童颜郭梅盛林刁钟徐邱骆高夏蔡田胡凌霍万柯卢莫房缪干解应宗丁宣邓郁单杭洪包诸左石崔吉龚程邢滑裴陆荣翁荀羊甄家封芮储靳邴松井富乌焦巴弓牧隗山谷车侯伊宁仇祖武符刘景詹束龙叶幸司韶黎乔苍双闻莘劳逄姬冉宰桂牛寿通边燕冀尚农温庄晏瞿茹习鱼容向古戈终居衡步都耿满弘国文东殴沃曾关红游盖益桓公晋楚闫';
const givenNames = '子豪宇轩浩然文轩浩宇子涵子轩梓豪梓涵梓轩梓睿梓瑞梓瑜梓瑶梓萱梓童梓潼梓琪梓琳梓馨梓鑫梓欣梓妍梓雨梓悦梓月梓玥梓怡梓莹梓颖梓瑗梓璇梓萌梓蕊梓菡梓芸梓烨梓晨梓辰梓晟梓晖梓晗梓景梓靖梓静梓淇梓琦梓琬梓琰梓瑷梓璐梓璋梓璇梓萍梓婷梓婧梓婕梓婉梓媛梓嫣梓嫒梓曼梓漫梓潇梓霄梓霆梓霖梓霏梓露梓馥梓馨梓鸿梓鹏梓龙梓虎梓凤梓凰梓楚梓秦梓赢梓蓉梓茗梓苑梓芷梓芯梓花梓荷梓莲梓菊梓薇梓蝶梓雪梓霜梓青梓翠梓玉梓珠梓瑚梓琼梓珊梓珍梓玲梓铃梓铭梓银梓鑫梓锦梓雯梓霞梓霖梓鸾梓凤';

// 医院名称
const hospitalNames = [
    { name: '北京协和医院', level: '1' },
    { name: '华西医院', level: '1' },
    { name: '中山大学附属第一医院', level: '1' },
    { name: '浙江大学医学院附属第一医院', level: '1' },
    { name: '复旦大学附属中山医院', level: '1' },
    { name: '南京鼓楼医院', level: '2' },
    { name: '上海市第一人民医院', level: '2' },
    { name: '广东省人民医院', level: '2' },
    { name: '四川省人民医院', level: '2' },
    { name: '武汉市中心医院', level: '3' },
    { name: '长沙市第一医院', level: '3' },
    { name: '南昌市第一医院', level: '3' },
    { name: '合肥市第一人民医院', level: '4' },
    { name: '郑州市人民医院', level: '4' },
    { name: '太原市中心医院', level: '4' },
    { name: '西安市第一医院', level: '5' },
    { name: '兰州市第一人民医院', level: '5' },
    { name: '贵阳市第一人民医院', level: '5' }
];

// 生成随机姓名
function generateName() {
    const familyName = familyNames[Math.floor(Math.random() * familyNames.length)];
    const givenNameLength = Math.random() > 0.5 ? 1 : 2;
    let givenName = '';
    for (let i = 0; i < givenNameLength; i++) {
        givenName += givenNames[Math.floor(Math.random() * givenNames.length)];
    }
    return familyName + givenName;
}

// 生成身份证号
function generateIdCard() {
    // 省份代码 (实际数据)
    const provinceCode = ['11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65'];
    // 生成随机省份代码
    const province = provinceCode[Math.floor(Math.random() * provinceCode.length)];
    // 生成随机地区代码 (简化处理)
    const city = String(Math.floor(Math.random() * 99)).padStart(2, '0');
    const district = String(Math.floor(Math.random() * 99)).padStart(2, '0');

    // 生成出生日期 (1960-2005年)
    const year = String(1960 + Math.floor(Math.random() * 45));
    const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');
    const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0'); // 简化处理，避免月份日期问题

    // 生成顺序码
    const sequence = String(Math.floor(Math.random() * 999)).padStart(3, '0');

    // 生成校验码
    const base = province + city + district + year + month + day + sequence;
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

    let sum = 0;
    for (let i = 0; i < base.length; i++) {
        sum += parseInt(base[i]) * weights[i];
    }

    const checkCode = codes[sum % 11];

    return base + checkCode;
}

// 保险类型
const insuranceTypes = [
    { code: '1', name: '城镇职工基本医疗保险' },
    { code: '2', name: '城乡居民基本医疗保险' },
    { code: '3', name: '补充医疗保险' },
    { code: '4', name: '商业医疗保险' }
];

// 参保人群类型
const personTypes = [
    { code: '1', name: '在职职工' },
    { code: '2', name: '退休人员' },
    { code: '3', name: '居民' },
    { code: '4', name: '学生' }
];

// 医疗机构等级
const hospitalLevels = [
    { code: '1', name: '三级甲等' },
    { code: '2', name: '三级乙等' },
    { code: '3', name: '二级甲等' },
    { code: '4', name: '二级乙等' },
    { code: '5', name: '一级' }
];

// 疾病类型
const diseaseTypes = [
    { code: '1', name: '呼吸系统疾病' },
    { code: '2', name: '循环系统疾病' },
    { code: '3', name: '消化系统疾病' },
    { code: '4', name: '内分泌系统疾病' },
    { code: '5', name: '骨骼肌肉系统疾病' },
    { code: '6', name: '泌尿系统疾病' },
    { code: '7', name: '神经系统疾病' },
    { code: '8', name: '其他疾病' }
];

// 修改生成保险数据的函数
function generateInsuranceData(count) {
    const data = [];
    const startDate = new Date(2020, 0, 1);
    const endDate = new Date(2023, 11, 31);

    for (let i = 0; i < count; i++) {
        const insuranceType = insuranceTypes[Math.floor(Math.random() * insuranceTypes.length)];
        const personType = personTypes[Math.floor(Math.random() * personTypes.length)];
        const idCard = generateIdCard();
        // 从身份证提取出生日期计算年龄
        const birthYear = parseInt(idCard.substr(6, 4));
        const age = new Date().getFullYear() - birthYear;
        const createDate = randomDate(startDate, endDate);

        data.push({
            id: String(i + 1).padStart(8, '0'),
            policyNumber: `P${String(Math.floor(Math.random() * 9999999999)).padStart(10, '0')}`,
            insuranceType: insuranceType.code,
            insuranceTypeName: insuranceType.name,
            personType: personType.code,
            personTypeName: personType.name,
            idCard: idCard,
            name: generateName(),
            age: age,
            gender: parseInt(idCard.substr(16, 1)) % 2 === 0 ? '2' : '1',
            startDate: randomDate(startDate, createDate).toISOString().split('T')[0],
            premium: Math.floor(Math.random() * 5000) + 1000,
            coverage: Math.floor(Math.random() * 900000) + 100000,
            status: Math.random() > 0.1 ? '1' : '2',
            createTime: createDate.toISOString()
        });
    }
    return data;
}

// 修改生成理赔数据的函数
function generateClaimsData(insuranceData, count) {
    const data = [];
    const endDate = new Date(2023, 11, 31);

    for (let i = 0; i < count; i++) {
        const insurance = insuranceData[Math.floor(Math.random() * insuranceData.length)];
        const diseaseType = diseaseTypes[Math.floor(Math.random() * diseaseTypes.length)];
        const hospital = hospitalNames[Math.floor(Math.random() * hospitalNames.length)];
        const totalAmount = Math.floor(Math.random() * 50000) + 1000;
        const coverageAmount = Math.floor(totalAmount * (Math.random() * 0.3 + 0.6));
        const visitDate = randomDate(new Date(insurance.startDate), endDate);

        data.push({
            id: String(i + 1).padStart(8, '0'),
            claimNumber: `C${String(Math.floor(Math.random() * 9999999999)).padStart(10, '0')}`,
            policyNumber: insurance.policyNumber,
            insuranceType: insurance.insuranceType,
            insuranceTypeName: insurance.insuranceTypeName,
            idCard: insurance.idCard,
            name: insurance.name,
            diseaseType: diseaseType.code,
            diseaseTypeName: diseaseType.name,
            hospitalLevel: hospital.level,
            hospitalLevelName: hospitalLevels.find(level => level.code === hospital.level).name,
            hospitalName: hospital.name,
            visitDate: visitDate.toISOString().split('T')[0],
            totalAmount: totalAmount,
            coverageAmount: coverageAmount,
            selfPayAmount: totalAmount - coverageAmount,
            status: Math.random() > 0.2 ? '2' : Math.random() > 0.5 ? '1' : '3',
            createTime: randomDate(visitDate, endDate).toISOString()
        });
    }
    return data;
}

// 生成数据
const insuranceData = generateInsuranceData(10000);
const claimsData = generateClaimsData(insuranceData, 15000);

// 写入文件
const outputDir = join(__dirname, 'data');
if (!existsSync(outputDir)) {
    mkdirSync(outputDir, { recursive: true });
}

writeFileSync(
    join(outputDir, 'insurance_data.json'),
    JSON.stringify(insuranceData, null, 2)
);

writeFileSync(
    join(outputDir, 'claims_data.json'),
    JSON.stringify(claimsData, null, 2)
);

console.log('数据生成完成！');

// 运行方式： node src/views/mock/generateInsuranceData.js

// 保险数据(insurance_data.json)字段含义：
// {
//     id: "00000001",                    // 主键ID，8位数字
//     policyNumber: "P0123456789",       // 保单号，P开头加10位数字
//     insuranceType: "1",                // 保险类型代码
//     insuranceTypeName: "城镇职工基本医疗保险", // 保险类型名称
//     personType: "1",                   // 参保人群类型代码
//     personTypeName: "在职职工",         // 参保人群类型名称
//     idCard: "110101196001011234",     // 身份证号（18位）
//     name: "张三",                      // 投保人姓名
//     age: 45,                          // 年龄（根据身份证计算）
//     gender: "1",                      // 性别（1:男 2:女，根据身份证判断）
//     startDate: "2020-01-01",          // 保险起始日期
//     premium: 2000,                    // 保费金额（1000-6000）
//     coverage: 500000,                 // 保障金额（100000-1000000）
//     status: "1",                      // 状态（1:有效 2:失效）
//     createTime: "2020-01-01T08:00:00Z" // 创建时间
// }

// 理赔数据(claims_data.json)字段含义：
// {
//     id: "00000001",                    // 主键ID，8位数字
//     claimNumber: "C0123456789",        // 理赔单号，C开头加10位数字
//     policyNumber: "P0123456789",       // 关联的保单号
//     insuranceType: "1",                // 保险类型代码
//     insuranceTypeName: "城镇职工基本医疗保险", // 保险类型名称
//     idCard: "110101196001011234",     // 身份证号
//     name: "张三",                      // 投保人姓名
//     diseaseType: "1",                 // 疾病类型代码
//     diseaseTypeName: "呼吸系统疾病",    // 疾病类型名称
//     hospitalLevel: "1",               // 医院等级代码
//     hospitalLevelName: "三级甲等",      // 医院等级名称
//     hospitalName: "北京协和医院",       // 医院名称
//     visitDate: "2020-02-01",          // 就医日期
//     totalAmount: 10000,               // 总金额（1000-51000）
//     coverageAmount: 8000,             // 报销金额（总金额的60%-90%）
//     selfPayAmount: 2000,              // 自付金额（总金额-报销金额）
//     status: "2",                      // 状态（1:待审核 2:已通过 3:已拒绝）
//     createTime: "2020-02-05T08:00:00Z" // 创建时间
// }