<template>
    <div class="note-manager">
        <!-- 左侧笔记本列表 -->
        <div class="notebook-sidebar">
            <div class="sidebar-header">
                <h3>我的笔记本</h3>
            </div>
            <div class="notebook-list">
                <a-tree v-model:selectedKeys="selectedKeys" v-model:expandedKeys="expandedKeys" :tree-data="notebooks"
                    :fieldNames="{ title: 'name', key: 'id' }" @select="handleNotebookSelect" @rightClick="handleNotebookRightClick">
                    <template #icon="{ type }">
                        <book-outlined />
                    </template>
                </a-tree>
            </div>
        </div>

        <!-- 中间笔记列表 -->
        <div class="note-list">
            <div class="list-header">
                <div class="header-left">
                    <h3>{{ currentNotebook?.name || '全部笔记' }}</h3>
                    <span class="note-count">{{ filteredNotes.length }}条笔记</span>
                </div>
                <div class="header-right">
                    <a-input-search
                        v-model:value="searchText"
                        placeholder="搜索笔记..."
                        style="width: 300px"
                    />
                    <div class="header-actions">
                        <a-button @click="refreshNotes" :loading="isRefreshing">
                            <template #icon><reload-outlined /></template>
                            刷新
                        </a-button>
                        <a-dropdown>
                            <a-button>
                                <template #icon><sort-ascending-outlined /></template>
                                排序
                            </a-button>
                            <template #overlay>
                                <a-menu @click="handleSortChange">
                                    <a-menu-item key="time">按时间排序</a-menu-item>
                                    <a-menu-item key="title">按标题排序</a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </div>
                </div>
            </div>
            <div class="note-items" ref="scrollContainer" @scroll="handleScroll">
                <div class="notes-wrapper">
                    <!-- 笔记列表 -->
                    <div v-for="note in filteredNotes" 
                        :key="note.id" 
                        class="note-item"
                        :class="{ active: selectedNote?.id === note.id }"
                        @click="selectNote(note)"
                        @contextmenu.prevent="handleNoteRightClick($event, note)"
                    >
                        <div class="note-title">{{ note.title }}</div>
                        <div class="note-preview">{{ note.content }}</div>
                        <div class="note-meta">
                            <span>{{ note.updateTime }}</span>
                            <span>{{ note.notebook }}</span>
                        </div>
                    </div>
                </div>
                <!-- 上拉加载提示 -->
                <div class="load-more-tip" v-show="hasMore || isLoading">
                    <a-spin v-if="isLoading" />
                    <span v-else-if="hasMore">上拉加载更多</span>
                    <span v-else>没有更多了</span>
                </div>
            </div>
            <div class="list-footer">
                <a-button type="primary" @click="createNote">
                    <template #icon><plus-outlined /></template>
                    新建笔记
                </a-button>
            </div>
        </div>

        <!-- 右侧编辑区 -->
        <div class="note-editor">
            <div class="editor-header">
                <div class="header-left">
                    <a-input v-model:value="selectedNote.title" placeholder="请输入标题" class="title-input" :bordered="false" />
                    <a-button type="primary" @click="handleSave" :loading="isSaving">
                        <template #icon><save-outlined /></template>
                        保存
                    </a-button>
                </div>
                <div class="editor-tools">
                    <span class="save-status" v-if="lastSaveTime">
                        上次保存时间: {{ lastSaveTime }}
                    </span>
                    <a-radio-group v-model:value="selectedNote.editorType" button-style="solid">
                        <a-radio-button value="markdown">Markdown</a-radio-button>
                        <a-radio-button value="rich">富文本</a-radio-button>
                    </a-radio-group>
                </div>
            </div>
            <div class="editor-content">
                <md-editor
                    v-if="selectedNote.editorType === 'markdown'"
                    v-model="selectedNote.content"
                    style="height: 100%"
                    @save="handleSave"
                    :toolbars="markdownToolbars"
                    :toolbarsExclude="[]"
                    previewTheme="github"
                    codeTheme="github"
                    showCodeRowNumber
                    :preview="true"
                    :noMermaid="true"
                    :noKatex="true"
                    :noHighlight="false"
                    :noImgZoom="false"
                />

                <div v-else class="rich-editor-container">
                    <Toolbar
                        style="border-bottom: 1px solid #ccc"
                        :editor="editorRef"
                        :defaultConfig="toolbarConfig"
                        mode="default"
                    />
                    <Editor
                        style="height: 500px; overflow-y: hidden;"
                        v-model="selectedNote.content"
                        :defaultConfig="editorConfig"
                        mode="default"
                        @onCreated="handleCreated"
                    />
                </div>
            </div>
        </div>

        <!-- 添加笔记本右键菜单 -->
        <a-dropdown 
            :open="notebookContextMenu.visible" 
            :trigger="['contextmenu']" 
            :overlayStyle="notebookContextMenuStyle"
        >
            <template #overlay>
                <a-menu @click="handleNotebookContextMenuClick">
                    <a-menu-item key="newNotebook">
                        <folder-add-outlined />新建笔记本
                    </a-menu-item>
                    <a-menu-item key="rename">
                        <edit-outlined />重命名
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                        <delete-outlined />删除
                    </a-menu-item>
                </a-menu>
            </template>
            <div style="display: none" />
        </a-dropdown>

        <!-- 添加笔记右键菜单 -->
        <a-dropdown 
            :open="noteContextMenu.visible" 
            :trigger="['contextmenu']" 
            :overlayStyle="noteContextMenuStyle"
        >
            <template #overlay>
                <a-menu @click="handleNoteContextMenuClick">
                    <a-menu-item key="edit">
                        <edit-outlined />编辑
                    </a-menu-item>
                    <a-menu-item key="move">
                        <folder-outlined />移动到...
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="copy">
                        <copy-outlined />复制
                    </a-menu-item>
                    <a-menu-item key="cut">
                        <scissor-outlined />剪切
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                        <delete-outlined />删除
                    </a-menu-item>
                </a-menu>
            </template>
            <div style="display: none" />
        </a-dropdown>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, shallowRef, onBeforeUnmount, watch } from 'vue'
import {
    PlusOutlined,
    BookOutlined,
    SortAscendingOutlined,
    ReloadOutlined,
    FolderAddOutlined,
    EditOutlined,
    DeleteOutlined,
    FolderOutlined,
    CopyOutlined,
    ScissorOutlined,
    SaveOutlined
} from '@ant-design/icons-vue'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { message } from 'ant-design-vue'

// 笔记本数据
const notebooks = ref([
    {
        id: '1',
        name: '工作笔记',
        children: [
            { id: '1-1', name: '会议记录' },
            { id: '1-2', name: '项目文档' },
            { id: '1-3', name: '工作计划' },
            { id: '1-4', name: '周报' },
            { id: '1-5', name: '月报' }
        ]
    },
    {
        id: '2',
        name: '个人笔记',
        children: [
            { id: '2-1', name: '学习笔记' },
            { id: '2-2', name: '日常记录' },
            { id: '2-3', name: '读书笔记' },
            { id: '2-4', name: '影视笔记' }
        ]
    },
    {
        id: '3',
        name: '技术文档',
        children: [
            { id: '3-1', name: '前端开发' },
            { id: '3-2', name: '后端开发' },
            { id: '3-3', name: '数据库' },
            { id: '3-4', name: '运维部署' },
            { id: '3-5', name: '算法笔记' }
        ]
    },
    {
        id: '4',
        name: '生活记录',
        children: [
            { id: '4-1', name: '旅行日记' },
            { id: '4-2', name: '美食记录' },
            { id: '4-3', name: '健身计划' },
            { id: '4-4', name: '购物清单' }
        ]
    },
    {
        id: '5',
        name: '项目管理',
        children: [
            { id: '5-1', name: '需求文档' },
            { id: '5-2', name: '设计方案' },
            { id: '5-3', name: '测试报告' },
            { id: '5-4', name: '发布记录' },
            { id: '5-5', name: '问题跟踪' }
        ]
    },
    {
        id: '6',
        name: '学习资料',
        children: [
            { id: '6-1', name: '课程笔记' },
            { id: '6-2', name: '考试复习' },
            { id: '6-3', name: '实验记录' },
            { id: '6-4', name: '论文笔记' }
        ]
    }
])

// 笔记数据
const notes = ref([
    {
        id: 1,
        title: 'Markdown笔记示例',
        content: '# 标题\n## 子标题\n这是一段 Markdown 格式的文本',
        updateTime: '2024-03-20 15:30',
        notebook: '技术文档',
        editorType: 'markdown'
    },
    {
        id: 2,
        title: '富文本笔记示例',
        content: '<h1>标题</h1><p>这是一段富文本格式的文本</p>',
        updateTime: '2024-03-20 14:20',
        notebook: '工作笔记',
        editorType: 'rich'
    },
    {
        id: 3,
        title: '读书笔记：深入理解计算机系统',
        content: '第一章主要内容：\n1. 计算机系统漫游\n2. 程序编译过程\n3. 处理器架构介绍',
        updateTime: '2024-03-20 11:15',
        notebook: '读书笔记',
        editorType: 'markdown'
    },
    {
        id: 4,
        title: '健身计划 2024',
        content: '每周计划：\n周一：胸部和三头\n周三：背部和二头\n周五：腿部\n每天：核心训练',
        updateTime: '2024-03-19 20:45',
        notebook: '健身计划',
        editorType: 'markdown'
    },
    {
        id: 5,
        title: '项目部署文档',
        content: '部署步骤：\n1. 环境准备\n2. 配置文件修改\n3. 数据库迁移\n4. 服务启动',
        updateTime: '2024-03-19 17:30',
        notebook: '运维部署',
        editorType: 'markdown'
    },
    {
        id: 6,
        title: '产品需求分析',
        content: '用户痛点：\n1. 操作流程复杂\n2. 响应速度慢\n3. 功能不够完善\n改进建议：...',
        updateTime: '2024-03-19 16:20',
        notebook: '需求文档',
        editorType: 'markdown'
    },
    {
        id: 7,
        title: '算法笔记：动态规划',
        content: '动态规划的核心思想：\n1. 最优子结构\n2. 重叠子问题\n3. 状态转移方程',
        updateTime: '2024-03-19 14:10',
        notebook: '算法笔记',
        editorType: 'markdown'
    },
    {
        id: 8,
        title: '周末旅行计划',
        content: '行程安排：\n1. 景点游览顺序\n2. 美食探店清单\n3. 住宿预订\n4. 交通规划',
        updateTime: '2024-03-19 11:25',
        notebook: '旅行日记',
        editorType: 'markdown'
    },
    {
        id: 9,
        title: '数据库优化方案',
        content: '优化方向：\n1. 索引优化\n2. SQL语句优化\n3. 表结构优化\n4. 配置优化',
        updateTime: '2024-03-18 16:40',
        notebook: '数据库',
        editorType: 'markdown'
    },
    {
        id: 10,
        title: '团队代码规范',
        content: '规范内\n1. 命名规范\n2. 注释规范\n3. 格式规范\n4. Git提交规范',
        updateTime: '2024-03-18 15:15',
        notebook: '项目文档',
        editorType: 'markdown'
    },
    {
        id: 11,
        title: '每日工作总结',
        content: '今日完成：\n1. Bug修复三个\n2. 新功能开发\n3. 代码审查\n4. 文档更新',
        updateTime: '2024-03-18 18:30',
        notebook: '工作计划',
        editorType: 'markdown'
    },
    {
        id: 12,
        title: '美食探店记录',
        content: '店铺名称：xxx\n特色菜品：\n1. 招牌菜\n2. 推荐菜\n整体评价：...',
        updateTime: '2024-03-18 12:20',
        notebook: '美食记录',
        editorType: 'markdown'
    },
    {
        id: 13,
        title: '月度总结报告',
        content: '本月主要成果：\n1. 项目按期交付\n2. 团队效率提升\n3. 技术创新...',
        updateTime: '2024-03-17 17:45',
        notebook: '月报',
        editorType: 'markdown'
    },
    {
        id: 14,
        title: '前端性能优化',
        content: '优化方案：\n1. 代码分割\n2. 懒加载\n3. 缓存策略\n4. 打包优化',
        updateTime: '2024-03-17 14:30',
        notebook: '前端开发',
        editorType: 'markdown'
    },
    {
        id: 15,
        title: '读书笔记：代码整洁之道',
        content: '核心观点：\n1. 命名的艺术\n2. 函数的设计\n3. 注释的正确使用\n4. 错误处理',
        updateTime: '2024-03-17 10:20',
        notebook: '读书笔记',
        editorType: 'markdown'
    }
])

const selectedKeys = ref([])
const expandedKeys = ref(['1', '2'])
const searchText = ref('')
const selectedNote = ref({})
const currentNotebook = ref(null)

// 过滤后的笔记列表
const filteredNotes = computed(() => {
    return notes.value.filter(note =>
        note.title.toLowerCase().includes(searchText.value.toLowerCase()) ||
        note.content.toLowerCase().includes(searchText.value.toLowerCase())
    )
})

// 处理笔记本选择
const handleNotebookSelect = (selectedKeys, info) => {
    currentNotebook.value = info.node
}

// 选择笔记
const selectNote = (note) => {
    selectedNote.value = { ...note }
}

// 创建新笔记
const createNote = () => {
    const newNote = {
        id: Date.now(),
        title: '新建笔记',
        content: '',
        updateTime: new Date().toLocaleString(),
        notebook: currentNotebook.value?.name || '未分类',
        editorType: 'markdown'
    }
    notes.value.unshift(newNote)
    selectNote(newNote)
}

// 创建笔记本
const showCreateNotebook = () => {
    // 实现创建笔记本的逻辑
}

// 处理排序
const handleSortChange = ({ key }) => {
    // 实现排序逻辑
}

// 添加下拉刷新和上拉加载相关的状态
const scrollContainer = ref(null)
const isRefreshing = ref(false)
const isLoading = ref(false)
const hasMore = ref(true)
const page = ref(1)
const pageSize = ref(15)

// 修改刷新方法
const refreshNotes = async () => {
    if (isRefreshing.value) return
    
    try {
        isRefreshing.value = true
        page.value = 1
        hasMore.value = true
        notes.value = await fetchNotes(1)
    } finally {
        isRefreshing.value = false
    }
}

// 滚动处理
const handleScroll = async () => {
    if (isLoading.value || !hasMore.value) return
    
    const container = scrollContainer.value
    const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight
    
    if (scrollBottom < 50) {
        isLoading.value = true
        // 模拟加载更多数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        await loadMoreNotes()
        isLoading.value = false
    }
}

// 加载更多笔记
const loadMoreNotes = async () => {
    if (!hasMore.value) return
    
    const newNotes = await fetchNotes(page.value + 1)
    if (newNotes.length < pageSize.value) {
        hasMore.value = false
    }
    
    notes.value = [...notes.value, ...newNotes]
    page.value++
}

// 模拟获取笔记数据
const fetchNotes = async (pageNum) => {
    // 这里模拟从服务器获取数据
    // 实际使用时替换为真实的API调用
    return new Array(pageSize.value).fill(null).map((_, index) => ({
        id: Date.now() + index,
        title: `笔记 ${(pageNum - 1) * pageSize.value + index + 1}`,
        content: '这是一条测试笔...',
        updateTime: new Date().toLocaleString(),
        notebook: '测试笔记本'
    }))
}

// 添加笔记本右键菜单状态
const notebookContextMenu = reactive({
    visible: false,
    node: null
})

const notebookContextMenuStyle = ref({})

const noteContextMenu = reactive({
    visible: false,
    note: null
})

const noteContextMenuStyle = ref({})

// 修改笔记右键菜单处理函数
const handleNoteRightClick = (event, note) => {
    event.preventDefault()
    noteContextMenu.visible = true
    noteContextMenu.note = note
    
    noteContextMenuStyle.value = {
        position: 'absolute',
        left: `${event.clientX}px`,
        top: `${event.clientY}px`,
        zIndex: 1000
    }
}

// 修改笔记右键菜单点击处理函数
const handleNoteContextMenuClick = ({ key }) => {
    const note = noteContextMenu.note
    try {
        switch (key) {
            case 'edit':
                selectNote(note)
                break
            case 'move':
                // 实现移动逻辑
                break
            case 'copy':
                // 实现复制逻辑
                break
            case 'cut':
                // 实现剪切逻辑
                break
            case 'delete':
                // 实现删除逻辑
                break
        }
    } finally {
        noteContextMenu.visible = false
    }
}

// 修改笔记本树的右键事件处理
const handleNotebookRightClick = ({ event, node }) => {
    event.preventDefault()
    notebookContextMenu.visible = true
    notebookContextMenu.node = node
    
    notebookContextMenuStyle.value = {
        position: 'absolute',
        left: `${event.clientX}px`,
        top: `${event.clientY}px`,
        zIndex: 1000
    }
}

// 处理笔记本右键菜单点击
const handleNotebookContextMenuClick = ({ key }) => {
    const node = notebookContextMenu.node
    try {
        switch (key) {
            case 'newNotebook':
                // 实现新建笔记本逻辑
                break
            case 'rename':
                // 实现重命名逻辑
                break
            case 'delete':
                // 实现删除逻辑
                break
        }
    } finally {
        notebookContextMenu.visible = false
    }
}

// 添加点击外部关闭菜单
onMounted(() => {
    document.addEventListener('click', (event) => {
        if (!event.target.closest('.ant-dropdown')) {
            notebookContextMenu.visible = false
            noteContextMenu.visible = false
        }
    })
})

// 添加富文本编辑器配置
const editorRef = shallowRef()
const toolbarConfig = {
    excludeKeys: []
}
const editorConfig = {
    placeholder: '请输入内容...',
    MENU_CONF: {}
}

// Markdown 编辑器工具栏配置
const markdownToolbars = [
    'bold',
    'underline',
    'italic',
    '-',
    'strikeThrough',
    'title',
    'sub',
    'sup',
    'quote',
    '-',
    'unorderedList',
    'orderedList',
    '-',
    'codeRow',
    'code',
    'link',
    'image',
    'table',
    '-',
    'revoke',
    'next',
    '-',
    'preview',
    'htmlPreview',
    'catalog',
    'pageFullscreen',
    'fullscreen',
    '-',
    'save'
].filter(item => !['mermaid', 'katex'].includes(item))

// 编辑器创建完成时的回调
const handleCreated = (editor) => {
    editorRef.value = editor
}

// 添加保存相关的状态
const isSaving = ref(false)
const lastSaveTime = ref(null)

// 修改保存处理函数
const handleSave = async () => {
    if (isSaving.value) return
    
    try {
        isSaving.value = true
        
        // 更新笔记内容
        const noteIndex = notes.value.findIndex(note => note.id === selectedNote.value.id)
        if (noteIndex !== -1) {
            notes.value[noteIndex] = {
                ...selectedNote.value,
                updateTime: new Date().toLocaleString()
            }
        }
        
        // 这里可以添加保存到后端的逻辑
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟保存延迟
        
        lastSaveTime.value = new Date().toLocaleString()
        message.success('保存成功')
    } catch (error) {
        console.error('保存笔记错误:', error)
        message.error('保存失败')
    } finally {
        isSaving.value = false
    }
}

// 添加自动保存功能
let autoSaveTimer = null

// 监听笔记内容变化
watch(() => selectedNote.value?.content, (newContent, oldContent) => {
    if (newContent === oldContent) return
    
    // 清除之前的定时器
    if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
    }
    
    // 设置新的定时器，2秒后自动保存
    autoSaveTimer = setTimeout(() => {
        handleSave()
    }, 2000)
})

// 组件卸载时清理定时器
onBeforeUnmount(() => {
    if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
    }
})

// 添加快捷键保存
const handleKeyDown = (event) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault()
        handleSave()
    }
}

// 在组件挂载时添加快捷键监听
onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
})

// 在组件卸载时移除快捷键监听
onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.note-manager {
    display: flex;
    height: 100%;
    background: #fff;
}

.notebook-sidebar {
    width: 200px;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
}

.notebook-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    height: calc(100% - 64px);
}

.notebook-list::-webkit-scrollbar,
.note-items::-webkit-scrollbar,
.editor-content::-webkit-scrollbar {
    width: 6px;
}

.notebook-list::-webkit-scrollbar-thumb,
.note-items::-webkit-scrollbar-thumb,
.editor-content::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
}

:deep(.ant-tree-node-content-wrapper) {
    padding: 4px 8px !important;
}

:deep(.ant-tree-node-selected) {
    background-color: #e6f7ff !important;
}

.note-list {
    width: 300px;
    min-width: 300px;
    flex-shrink: 0;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
}

.list-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.note-count {
    color: #999;
    font-size: 12px;
}

.header-right {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.header-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.note-items {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.note-item {
    padding: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.note-item:hover {
    background: #f5f5f5;
}

.note-item.active {
    background: #e6f7ff;
}

.note-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.note-preview {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.note-meta {
    font-size: 12px;
    color: #999;
    display: flex;
    justify-content: space-between;
}

.list-footer {
    padding: 16px;
    border-top: 1px solid #e8e8e8;
}

.note-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    overflow: hidden;
}

.editor-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.title-input {
    font-size: 20px;
    flex: 1;
}

.editor-tools {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
}

.save-status {
    font-size: 12px;
    color: #999;
}

.editor-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

.load-more-tip {
    padding: 16px;
    text-align: center;
    color: #999;
}

:deep(.ant-input-search) {
    width: 100% !important;
}

:deep(.ant-input-search .ant-input) {
    border-radius: 4px;
}

/* 添加右键菜单样式 */
:deep(.ant-dropdown) {
    position: fixed !important;
}

:deep(.ant-dropdown-menu) {
    padding: 4px;
    border-radius: 6px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
                0 6px 16px 0 rgba(0, 0, 0, 0.08),
                0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

:deep(.ant-dropdown-menu-item) {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.ant-dropdown-menu-item:hover) {
    background-color: #f0f7ff;
}

:deep(.ant-dropdown-menu-item-danger:hover) {
    background-color: #fff1f0;
}

/* 编辑器样式 */
:deep(.md-editor) {
    height: 100% !important;
    flex: 1;
}

:deep(.md-editor-toolbar) {
    padding: 8px !important;
    border-bottom: 1px solid #e8e8e8 !important;
}

:deep(.md-editor-toolbar svg) {
    width: 24px !important;
    height: 24px !important;
}

:deep(.md-editor-toolbar button) {
    padding: 8px !important;
    margin: 0 4px !important;
    border-radius: 4px !important;
    min-width: 40px !important;
    height: 40px !important;
}

/* 富文本编辑器样式 */
.rich-editor-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
}

:deep(.w-e-toolbar) {
    border: none !important;
    padding: 8px !important;
}

:deep(.w-e-text-container) {
    flex: 1;
    overflow: hidden;
}

:deep(.w-e-text-container [data-slate-editor]) {
    padding: 16px !important;
}
</style>