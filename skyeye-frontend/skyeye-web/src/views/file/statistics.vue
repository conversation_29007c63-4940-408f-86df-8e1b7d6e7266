<template>
    <div class="statistics-container" ref="containerRef">
        <!-- 顶部数据卡片 -->
        <div class="data-cards">
            <div class="data-card" v-for="(item, index) in topCards" :key="index">
                <div class="card-content">
                    <div class="card-data">
                        <div class="card-title">{{ item.label }}</div>
                        <span class="number">{{ item.value }}</span>
                        <span class="unit">{{ item.unit }}</span>
                    </div>
                    <div class="card-icon">
                        <component :is="item.icon" />
                    </div>
                </div>
                <div class="card-bg"></div>
            </div>
        </div>

        <!-- 中部图表 -->
        <div class="chart-row">
            <!-- 文件类型占比 -->
            <div class="chart-panel glass-effect">
                <div class="chart-title">文件类型占比</div>
                <div ref="fileTypeChart" class="chart-container"></div>
            </div>
            <!-- 文件存储占比 -->
            <div class="chart-panel glass-effect">
                <div class="chart-title">文件存储占比（前三）</div>
                <div class="storage-charts">
                    <div v-for="(item, index) in storageData" :key="index" class="storage-item">
                        <div class="storage-ring">
                            <canvas ref="storageCanvas" width="130" height="150"></canvas>
                        </div>
                        <div class="storage-label">{{ item.typeName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部图表 -->
        <div class="chart-row">
            <!-- 本年度新增文件数 -->
            <div class="chart-panel glass-effect">
                <div class="chart-title">本年度新增文件数</div>
                <div ref="yearlyChart" class="chart-container"></div>
            </div>
            <!-- 近七天新增文件数 -->
            <div class="chart-panel glass-effect">
                <div class="chart-title">
                    <span>新增文件数/</span>
                    <span class="highlight">近七天</span>
                </div>
                <div ref="weeklyChart" class="chart-container"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { FileOutlined, CloudUploadOutlined, CalendarOutlined, HddOutlined, CloudDownloadOutlined, CloudSyncOutlined } from '@ant-design/icons-vue'

const { proxy } = getCurrentInstance()

// 顶部卡片数据
const topCards = ref([])
const cardCanvas = ref([])

// 图表引用
const fileTypeChart = ref(null)
const storageCanvas = ref([])
const yearlyChart = ref(null)
const weeklyChart = ref(null)

// 存储数据
const storageData = ref([])

// 初始化数据
const initData = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().diskCloudBasePath + 'fileconsole037'
        )
        const data = res.bean

        // 设置顶部卡片数据
        topCards.value = [
            { value: data.allNum.fileNum, unit: '个', label: '文件总数量', icon: FileOutlined },
            { value: data.allNumToday.fileNum, unit: '个', label: '今日新增文件数量', icon: CloudUploadOutlined },
            { value: data.allNumThisWeek.fileNum, unit: '个', label: '本周新增文件数量', icon: CalendarOutlined },
            { value: data.allNum.fileSizeZh, unit: '', label: '文件总存储', icon: HddOutlined },
            { value: data.allNumToday.fileSizeZh, unit: '', label: '今日新增文件存储', icon: CloudDownloadOutlined },
            { value: data.allNumThisWeek.fileSizeZh, unit: '', label: '本周新增文件存储', icon: CloudSyncOutlined }
        ]

        // 存储占比数据
        storageData.value = data.fileStorageNum

        await nextTick()
        // 初始化所有图表
        initCardLabels()
        initFileTypeChart(data.fileTypeNumEntity)
        initStorageCharts()
        initYearlyChart(data.newFileNum)
        initWeeklyChart(data.fileTypeNumSevenDay)
    } catch (error) {
        SkMessage.error('获取数据失败')
    }
}

// 初始化卡片标签
const initCardLabels = () => {
    cardCanvas.value.forEach((canvas, index) => {
        drawCardLabel(canvas, topCards.value[index].label)
    })
}

// 绘制卡片标签
const drawCardLabel = (canvas, text) => {
    const ctx = canvas.getContext('2d')
    const colorValue = '#FFFFFF'

    // 绘制圆点
    ctx.beginPath()
    ctx.arc(35, 55, 2, 0, 2 * Math.PI)
    ctx.closePath()
    ctx.fillStyle = colorValue
    ctx.fill()

    // 绘制连接线
    ctx.moveTo(35, 55)
    ctx.lineTo(60, 80)
    ctx.lineTo(200, 80)
    ctx.lineWidth = 1
    ctx.strokeStyle = colorValue
    ctx.stroke()

    // 绘制文字
    ctx.font = '12px Georgia'
    ctx.fillStyle = colorValue
    ctx.fillText(text, 80, 92)
}

// 初始化文件类型占比图表
const initFileTypeChart = (data) => {
    const chart = echarts.init(fileTypeChart.value)
    chart.setOption({
        tooltip: {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            left: 20,
            top: 20,
            bottom: 20,
            textStyle: {
                color: '#fff'
            },
            data: data.fileTypeNumStr.replace(/(.*)[,，]$/, '$1').split(',')
        },
        series: [{
            name: '',
            type: 'pie',
            radius: [40, 80],
            center: ['50%', '50%'],
            data: data.fileTypeNum,
            label: {
                normal: {
                    show: true,
                    formatter: "{b}\n{d}%",
                    textStyle: {
                        color: '#fff'
                    }
                }
            }
        }]
    })
}

// 绘制存储占比图表
const drawStorageChart = (canvas, data, index) => {
    const ctx = canvas.getContext('2d')
    const circle = {
        x: 65,
        y: 80,
        r: 60
    }

    // 绘制底环
    ctx.beginPath()
    ctx.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2)
    ctx.lineWidth = 10
    ctx.strokeStyle = '#052639'
    ctx.stroke()
    ctx.closePath()

    // 绘制数据环
    let rate = 0
    if (data && data.fileSize && storageData.value[0] && storageData.value[0].fileSize) {
        rate = (data.fileSize / storageData.value[0].fileSize).toFixed(2)
    }
    ctx.beginPath()
    ctx.arc(circle.x, circle.y, circle.r, 1.5 * Math.PI, (1.5 + rate * 2) * Math.PI)
    ctx.lineWidth = 10
    ctx.lineCap = 'round'
    ctx.strokeStyle = ['#027825', '#006DD6', '#238681'][index] || '#027825'
    ctx.stroke()
    ctx.closePath()

    // 绘制百分比
    ctx.fillStyle = 'white'
    ctx.font = '20px Calibri'
    ctx.fillText((rate * 100).toFixed(0) + '%', circle.x - 15, circle.y + 10)
}

// 初始化存储占比图表
const initStorageCharts = () => {
    storageCanvas.value.forEach((canvas, index) => {
        drawStorageChart(canvas, storageData.value[index], index)
    })
}

// 初始化年度趋势图表
const initYearlyChart = (data) => {
    const chart = echarts.init(yearlyChart.value)
    const nameStr = data.map(item => item.monthName).join(',')
    const numStr = data.map(item => item.fileNum).join(',')

    chart.setOption({
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '5%',
            top: '4%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: nameStr.split(','),
            axisLabel: {
                textStyle: {
                    color: "white",
                    fontSize: 8
                },
                rotate: 45,
                interval: 2
            },
            axisTick: { show: false },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#0B3148',
                    width: 1,
                    type: 'solid'
                }
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                textStyle: {
                    color: "white",
                    fontSize: 8
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#0B3148',
                    width: 1,
                    type: 'solid'
                }
            },
            splitLine: { show: false }
        },
        series: [{
            type: 'line',
            smooth: true,
            symbol: 'none',
            areaStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: '#026B6F'
                    }, {
                        offset: 1,
                        color: '#012138'
                    }], false),
                    opacity: 0.2
                }
            },
            itemStyle: {
                normal: {
                    color: '#009991',
                    lineStyle: {
                        color: '#009895',
                        opacity: 1
                    }
                }
            },
            data: numStr.split(',')
        }]
    })
}

// 初始化周趋势图表
const initWeeklyChart = (data) => {
    const chart = echarts.init(weeklyChart.value)
    // 处理数据
    const dataStr = []
    const nameList = []
    const clickDate = []

    // 整理数据结构
    data.forEach(item => {
        clickDate.push(item.clickDate)

        const existingData = dataStr.find(d => d.name === item.typeName)
        if (existingData) {
            existingData.customData.push({ num: item.fileNum, date: item.clickDate })
        } else {
            dataStr.push({
                name: item.typeName,
                type: 'line',
                customData: [{ num: item.fileNum, date: item.clickDate }],
                data: []
            })
            nameList.push({ name: item.typeName })
        }
    })

    // 填充数据
    dataStr.forEach(entity => {
        const dates = [...new Set(clickDate)].sort()
        entity.data = dates.map(date => {
            const match = entity.customData.find(d => d.date === date)
            return match ? match.num : 0
        })
    })

    chart.setOption({
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            top: 20,
            right: 5,
            textStyle: { color: 'white' },
            orient: 'vertical',
            data: nameList.map(item => item.name)
        },
        grid: {
            left: '3%',
            right: '16%',
            bottom: '3%',
            top: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            axisTick: { show: false },
            axisLabel: {
                textStyle: {
                    color: "white",
                    fontSize: 8
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#0B3148',
                    width: 1,
                    type: 'solid'
                }
            },
            data: clickDate
        },
        yAxis: {
            type: 'value',
            axisTick: { show: false },
            axisLabel: {
                textStyle: {
                    color: "white",
                    fontSize: 8
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#0B3148',
                    width: 1,
                    type: 'solid'
                }
            },
            splitLine: { show: false }
        },
        series: dataStr
    })
}

onMounted(() => {
    initData()
})
</script>

<style lang="less" scoped>
.statistics-container {
    height: 100%;
    padding: 20px;
    background: linear-gradient(-45deg, #17213e, #000000, #006970, #007c5f);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    color: white;
    font-family: 'PingFang SC', sans-serif;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .data-cards {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 20px;
        flex: 0 0 120px;
        margin-bottom: 20px;

        .data-card {
            position: relative;
            perspective: 1000px;
            transition: transform 0.3s;

            &:hover {
                transform: translateY(-5px);

                .card-bg {
                    opacity: 1;
                }
            }

            .card-content {
                position: relative;
                z-index: 1;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 12px;
                padding: 20px;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            .card-data {
                .card-title {
                    font-size: 14px;
                    opacity: 0.8;
                    margin-bottom: 10px;
                }

                .number {
                    font-size: 26px;
                    font-weight: bold;
                    background: linear-gradient(45deg, #fff, #7ee8fa);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                .unit {
                    font-size: 16px;
                    margin-left: 5px;
                }
            }

            .card-icon {
                font-size: 24px;
                opacity: 0.5;
                text-align: right;
            }

            .card-bg {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
                border-radius: 12px;
                opacity: 0;
                transition: opacity 0.3s;
            }
        }
    }

    .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.3s;

        &:hover {
            transform: translateY(-5px);
        }
    }

    .chart-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        flex: 1;
        margin-bottom: 20px;
        min-height: 0;

        .chart-panel {
            .chart-title {
                font-size: 16px;
                font-weight: 500;
                padding-bottom: 15px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);

                .highlight {
                    color: #00A09A;
                }
            }

            .chart-container {
                height: calc(100% - 30px);
            }

            .storage-charts {
                display: flex;
                justify-content: space-around;
                height: calc(100% - 30px);
            }
        }
    }
}

@keyframes gradientBG {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}
</style>
