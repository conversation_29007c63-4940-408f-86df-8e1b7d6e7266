<template>
    <div class="file-manager">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="navigation">
                <a-button-group>
                    <a-button @click="goBack">
                        <template #icon><left-outlined /></template>
                    </a-button>
                    <a-button @click="goForward">
                        <template #icon><right-outlined /></template>
                    </a-button>
                    <a-button @click="goUp">
                        <template #icon><up-outlined /></template>
                    </a-button>
                </a-button-group>
                <a-breadcrumb class="path-breadcrumb">
                    <a-breadcrumb-item v-for="(item, index) in currentPath" :key="index">
                        <a @click="navigateTo(index)">{{ item }}</a>
                    </a-breadcrumb-item>
                </a-breadcrumb>
                <a-input-search v-model:value="searchText" placeholder="搜索文件..." class="search-input"
                    @search="handleSearch" style="width: 180px" />
            </div>
            <div class="view-options">
                <a-select v-model:value="sortBy" style="width: 120px; margin-right: 16px">
                    <a-select-option value="name">按名称</a-select-option>
                    <a-select-option value="date">按日期</a-select-option>
                    <a-select-option value="size">按大小</a-select-option>
                </a-select>
                <a-radio-group v-model:value="viewMode" button-style="solid">
                    <a-radio-button value="grid">
                        <AppstoreOutlined />
                    </a-radio-button>
                    <a-radio-button value="list">
                        <UnorderedListOutlined />
                    </a-radio-button>
                </a-radio-group>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 左侧导航 -->
            <div class="sidebar">
                <a-tree v-model:selectedKeys="selectedKeys" v-model:expandedKeys="expandedKeys" :tree-data="treeData"
                    :fieldNames="{ title: 'name', key: 'id' }" :indent="12" @select="handleSelect"
                    @rightClick="handleTreeRightClick">
                    <template #icon="{ type }">
                        <folder-outlined v-if="type === 'folder'" />
                        <file-outlined v-else />
                    </template>
                </a-tree>

                <!-- 添加底部功能按钮 -->
                <div class="sidebar-footer">
                    <div class="footer-item" @click="handleFooterClick('share')">
                        <share-alt-outlined />
                        <span>共享</span>
                    </div>
                    <div class="footer-item" @click="handleFooterClick('recycle')">
                        <delete-outlined />
                        <span>回收站</span>
                    </div>
                </div>
            </div>

            <!-- 文件列表 -->
            <div class="file-list" @mousedown="startSelection" @mousemove="updateSelection" @mouseup="endSelection">
                <!-- 添加选择框 -->
                <div v-show="selecting" class="selection-box" :style="selectionBoxStyle"></div>

                <template v-if="viewMode === 'grid'">
                    <div class="grid-view">
                        <div v-for="file in sortedFiles" :key="file.id" class="file-item"
                            :class="{ 'selected': selectedFiles.includes(file.id) }" @click.stop="handleFileClick(file)"
                            @contextmenu.prevent="showContextMenu($event, file)" @dblclick="handleFileDoubleClick(file)"
                            :data-file-id="file.id">
                            <folder-outlined v-if="file.type === 'folder'" class="file-icon" />
                            <file-outlined v-else class="file-icon" />
                            <div class="file-name">{{ file.name }}</div>
                        </div>
                    </div>
                </template>

                <template v-else>
                    <div class="list-container">
                        <a-table :columns="columns" :data-source="sortedFiles" :pagination="false"
                            :scroll="{ y: '100%' }" @row-contextmenu="handleRowContextMenu"
                            @row-dblclick="handleFileDoubleClick">
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'icon'">
                                    <folder-outlined v-if="record.type === 'folder'" />
                                    <file-outlined v-else />
                                </template>
                            </template>
                        </a-table>
                    </div>
                </template>
            </div>
        </div>

        <!-- 右键菜单 -->
        <a-dropdown :open="contextMenu.visible" :trigger="['contextmenu']" :getPopupContainer="getPopupContainer"
            :overlayStyle="contextMenuStyle">
            <template #overlay>
                <a-menu @click="handleContextMenuClick">
                    <a-menu-item key="open">
                        <folder-open-outlined />打开
                    </a-menu-item>
                    <a-menu-item key="preview" v-if="canPreview">
                        <eye-outlined />预览
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="copy">
                        <copy-outlined />复制
                    </a-menu-item>
                    <a-menu-item key="cut">
                        <scissor-outlined />剪切
                    </a-menu-item>
                    <a-menu-item key="rename">
                        <edit-outlined />重命名
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                        <delete-outlined />删除
                    </a-menu-item>
                </a-menu>
            </template>
            <div style="display: none" />
        </a-dropdown>

        <!-- 预览模态框 -->
        <a-modal v-model:open="previewModal.visible" :title="previewModal.title" :footer="null" width="800px">
            <div class="preview-content">
                <template v-if="previewModal.type === 'image'">
                    <img :src="previewModal.url" style="max-width: 100%" />
                </template>
                <template v-else-if="previewModal.type === 'text'">
                    <pre>{{ previewModal.content }}</pre>
                </template>
                <template v-else>
                    <p>暂不支持该文件类型的预览</p>
                </template>
            </div>
        </a-modal>

        <!-- 重命名模态框 -->
        <a-modal v-model:open="renameModal.visible" title="重命名" @ok="handleRename">
            <a-input v-model:value="renameModal.newName" />
        </a-modal>

        <!-- 添加树节点右键菜单 -->
        <a-dropdown :open="treeContextMenu.visible" :trigger="['contextmenu']" :getPopupContainer="getPopupContainer"
            :overlayStyle="treeContextMenuStyle">
            <template #overlay>
                <a-menu @click="handleTreeContextMenuClick">
                    <a-menu-item key="newFolder">
                        <folder-add-outlined />新建文件夹
                    </a-menu-item>
                    <a-menu-item key="rename">
                        <edit-outlined />重命名
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="copy">
                        <copy-outlined />复制
                    </a-menu-item>
                    <a-menu-item key="cut">
                        <scissor-outlined />剪切
                    </a-menu-item>
                    <a-menu-item key="paste" :disabled="!clipboardData">
                        <file-text-outlined />粘贴
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                        <delete-outlined />删除
                    </a-menu-item>
                </a-menu>
            </template>
            <div style="display: none" />
        </a-dropdown>
    </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import {
    LeftOutlined,
    RightOutlined,
    UpOutlined,
    UnorderedListOutlined,
    AppstoreOutlined,
    DesktopOutlined,
    FolderOutlined,
    DownloadOutlined,
    PictureOutlined,
    FileOutlined,
    FolderOpenOutlined,
    EyeOutlined,
    CopyOutlined,
    ScissorOutlined,
    EditOutlined,
    DeleteOutlined,
    FolderAddOutlined,
    FileTextOutlined,
    ShareAltOutlined,
} from '@ant-design/icons-vue'

const viewMode = ref('grid')
const currentPath = ref(['本地磁盘 (C:)', '用户', 'Documents'])
const currentLocation = ref('documents')

const selectedKeys = ref([])
const expandedKeys = ref(['1', '2'])

const treeData = ref([
    {
        id: '1',
        name: '本地磁盘 (C:)',
        type: 'folder',
        children: [
            {
                id: '1-1',
                name: '用户',
                type: 'folder',
                children: [
                    {
                        id: '1-1-1',
                        name: '文档',
                        type: 'folder',
                        children: [
                            { id: '1-1-1-1', name: '工作件', type: 'folder' },
                            { id: '1-1-1-2', name: '个人文件', type: 'folder' },
                            { id: '1-1-1-3', name: '学习资料', type: 'folder' }
                        ]
                    },
                    {
                        id: '1-1-2',
                        name: '下载',
                        type: 'folder',
                        children: [
                            { id: '1-1-2-1', name: '软件安装包', type: 'folder' },
                            { id: '1-1-2-2', name: '图片素材', type: 'folder' },
                            { id: '1-1-2-3', name: '视频资料', type: 'folder' }
                        ]
                    },
                    {
                        id: '1-1-3',
                        name: '图片',
                        type: 'folder',
                        children: [
                            { id: '1-1-3-1', name: '壁纸', type: 'folder' },
                            { id: '1-1-3-2', name: '截图', type: 'folder' },
                            { id: '1-1-3-3', name: '相册', type: 'folder' }
                        ]
                    }
                ]
            },
            {
                id: '1-2',
                name: 'Program Files',
                type: 'folder',
                children: [
                    { id: '1-2-1', name: 'Microsoft', type: 'folder' },
                    { id: '1-2-2', name: 'Adobe', type: 'folder' },
                    { id: '1-2-3', name: 'Common Files', type: 'folder' }
                ]
            },
            {
                id: '1-3',
                name: 'Windows',
                type: 'folder',
                children: [
                    { id: '1-3-1', name: 'System32', type: 'folder' },
                    { id: '1-3-2', name: 'System', type: 'folder' },
                    { id: '1-3-3', name: 'Boot', type: 'folder' }
                ]
            }
        ]
    },
    {
        id: '2',
        name: '本地磁盘 (D:)',
        type: 'folder',
        children: [
            {
                id: '2-1',
                name: '项目文件',
                type: 'folder',
                children: [
                    { id: '2-1-1', name: '前端项目', type: 'folder' },
                    { id: '2-1-2', name: '后端项目', type: 'folder' },
                    { id: '2-1-3', name: '数据库备份', type: 'folder' }
                ]
            },
            {
                id: '2-2',
                name: '备份',
                type: 'folder',
                children: [
                    { id: '2-2-1', name: '系统备份', type: 'folder' },
                    { id: '2-2-2', name: '文档备份', type: 'folder' },
                    { id: '2-2-3', name: '配置备份', type: 'folder' }
                ]
            }
        ]
    }
])

const handleSelect = (selectedKeys, info) => {
    try {
        console.log('selected', selectedKeys, info)
        if (info.node.type === 'folder') {
            const path = []
            let currentNode = info.node
            while (currentNode.parent) {
                path.unshift(currentNode.name)
                currentNode = currentNode.parent
            }
            path.unshift(currentNode.name)
            currentPath.value = path
        }
    } catch (error) {
        console.error('选择节点错误:', error)
    }
}

const columns = [
    { title: '', dataIndex: 'icon', key: 'icon', width: 50 },
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '修改日期', dataIndex: 'modifiedDate', key: 'modifiedDate' },
    { title: '类型', dataIndex: 'type', key: 'type' },
    { title: '大小', dataIndex: 'size', key: 'size' },
]

const files = ref([
    { id: 1, name: '我的文档', type: 'folder', modifiedDate: '2024-03-20 15:30', size: '-' },
    { id: 2, name: '下载', type: 'folder', modifiedDate: '2024-03-19 10:20', size: '-' },
    { id: 3, name: '项目文件', type: 'folder', modifiedDate: '2024-03-18 09:15', size: '-' },
    { id: 4, name: '报告.docx', type: 'file', modifiedDate: '2024-03-17 14:45', size: '2.5 MB' },
    { id: 5, name: '预算表.xlsx', type: 'file', modifiedDate: '2024-03-16 16:20', size: '1.8 MB' },
    { id: 6, name: '会议记录.doc', type: 'file', modifiedDate: '2024-03-15 11:30', size: '1.2 MB' },
    { id: 7, name: '项目计划.pptx', type: 'file', modifiedDate: '2024-03-14 09:45', size: '4.5 MB' },
    { id: 8, name: '源代码.zip', type: 'file', modifiedDate: '2024-03-13 16:55', size: '15.7 MB' },
    { id: 9, name: '设计稿.psd', type: 'file', modifiedDate: '2024-03-12 14:20', size: '25.3 MB' },
    { id: 10, name: '数据分析.pdf', type: 'file', modifiedDate: '2024-03-11 10:15', size: '3.2 MB' },
    { id: 11, name: '用户手册.pdf', type: 'file', modifiedDate: '2024-03-10 15:40', size: '5.1 MB' },
    { id: 12, name: '测试报告.docx', type: 'file', modifiedDate: '2024-03-09 11:25', size: '1.9 MB' },
    { id: 13, name: '系统配置.json', type: 'file', modifiedDate: '2024-03-08 09:30', size: '12 KB' },
    { id: 14, name: '数据库备份.sql', type: 'file', modifiedDate: '2024-03-07 16:15', size: '45.6 MB' },
    { id: 15, name: '接口文档.md', type: 'file', modifiedDate: '2024-03-06 14:50', size: '256 KB' },
    { id: 16, name: '更新日志.txt', type: 'file', modifiedDate: '2024-03-05 10:35', size: '45 KB' },
    { id: 17, name: '原型设.sketch', type: 'file', modifiedDate: '2024-03-04 15:20', size: '18.3 MB' },
    { id: 18, name: '需求文档.docx', type: 'file', modifiedDate: '2024-03-03 11:45', size: '2.8 MB' },
    { id: 19, name: '演示视频.mp4', type: 'file', modifiedDate: '2024-03-02 09:55', size: '156.7 MB' },
    { id: 20, name: '产品介绍.pdf', type: 'file', modifiedDate: '2024-03-01 16:40', size: '4.9 MB' }
])

const goBack = () => {
    try {
        console.log('后退')
    } catch (error) {
        console.error('后退错误:', error)
    }
}

const goForward = () => {
    try {
        console.log('前进')
    } catch (error) {
        console.error('前进错误:', error)
    }
}

const goUp = () => {
    try {
        if (currentPath.value.length > 1) {
            currentPath.value.pop()
        }
    } catch (error) {
        console.error('向上导航错误:', error)
    }
}

const navigateTo = (index) => {
    try {
        currentPath.value = currentPath.value.slice(0, index + 1)
    } catch (error) {
        console.error('导航错误:', error)
    }
}

const searchText = ref('')
const sortBy = ref('name')
const contextMenuStyle = ref({})
const previewModal = reactive({
    visible: false,
    title: '',
    type: '',
    url: '',
    content: ''
})
const renameModal = reactive({
    visible: false,
    newName: '',
    file: null
})

const contextMenu = reactive({
    visible: false,
    targetFile: null,
    x: 0,
    y: 0
})

const treeContextMenu = reactive({
    visible: false,
    node: null,
    event: null
})

const treeContextMenuStyle = ref({})

const clipboardData = ref(null)

const sortedFiles = computed(() => {
    try {
        let result = [...files.value]
        if (searchText.value) {
            result = result.filter(file => {
                try {
                    return file.name.toLowerCase().includes(searchText.value.toLowerCase())
                } catch (error) {
                    console.error('文件过滤错误:', error)
                    return false
                }
            })
        }

        switch (sortBy.value) {
            case 'name':
                result.sort((a, b) => {
                    try {
                        return a.name.localeCompare(b.name)
                    } catch (error) {
                        console.error('名称排序错误:', error)
                        return 0
                    }
                })
                break
            case 'date':
                result.sort((a, b) => {
                    try {
                        return new Date(b.modifiedDate) - new Date(a.modifiedDate)
                    } catch (error) {
                        console.error('日期排序错误:', error)
                        return 0
                    }
                })
                break
            case 'size':
                result.sort((a, b) => {
                    try {
                        if (a.type === 'folder' && b.type === 'folder') return 0
                        if (a.type === 'folder') return -1
                        if (b.type === 'folder') return 1
                        return parseFileSize(b.size) - parseFileSize(a.size)
                    } catch (error) {
                        console.error('大小排序错误:', error)
                        return 0
                    }
                })
                break
        }
        return result
    } catch (error) {
        console.error('文件排序错误:', error)
        return []
    }
})

const canPreview = computed(() => {
    try {
        if (!contextMenu.targetFile) return false
        const ext = contextMenu.targetFile.name.split('.').pop()?.toLowerCase()
        return ['jpg', 'jpeg', 'png', 'gif', 'txt', 'md', 'json'].includes(ext)
    } catch (error) {
        console.error('预览检查错误:', error)
        return false
    }
})

const parseFileSize = (size) => {
    try {
        if (size === '-') return 0
        const units = ['B', 'KB', 'MB', 'GB']
        const number = parseFloat(size.replace(/[^0-9.]/g, ''))
        const unit = size.replace(/[0-9.]/g, '').trim()
        const index = units.indexOf(unit)
        return number * Math.pow(1024, index)
    } catch (error) {
        console.error('文件大小解析错误:', error)
        return 0
    }
}

const handleContextMenuClick = ({ key }) => {
    try {
        const file = contextMenu.targetFile
        switch (key) {
            case 'open':
                handleFileDoubleClick(file)
                break
            case 'preview':
                showPreview(file)
                break
            case 'rename':
                showRenameModal(file)
                break
            case 'delete':
                handleDelete(file)
                break
        }
    } catch (error) {
        console.error('菜单点击错误:', error)
    } finally {
        contextMenu.visible = false
    }
}

const showPreview = (file) => {
    try {
        previewModal.title = file.name
        const ext = file.name.split('.').pop()?.toLowerCase()

        if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
            previewModal.type = 'image'
            previewModal.url = '模拟的文件URL'
        } else if (['txt', 'md', 'json'].includes(ext)) {
            previewModal.type = 'text'
            previewModal.content = '模拟的文件内容'
        }

        previewModal.visible = true
    } catch (error) {
        console.error('预览显示错误:', error)
    }
}

const showRenameModal = (file) => {
    renameModal.file = file
    renameModal.newName = file.name
    renameModal.visible = true
}

const handleRename = () => {
    if (renameModal.file && renameModal.newName) {
        renameModal.file.name = renameModal.newName
        renameModal.visible = false
    }
}

const handleDelete = (file) => {
    files.value = files.value.filter(f => f.id !== file.id)
}

const handleFileDoubleClick = (file) => {
    try {
        if (file.type === 'folder') {
            currentPath.value.push(file.name)
        } else {
            showPreview(file)
        }
    } catch (error) {
        console.error('文件双击错误:', error)
    }
}

const handleRowContextMenu = (record, event) => {
    try {
        event.preventDefault()
        showContextMenu(event, record)
    } catch (error) {
        console.error('右键菜单错误:', error)
    }
}

const getPopupContainer = () => {
    return document.querySelector('.file-manager') || document.body
}

const showContextMenu = (event, file) => {
    try {
        event.preventDefault()
        contextMenu.visible = true
        contextMenu.targetFile = file

        contextMenuStyle.value = {
            position: 'absolute',
            left: `${event.clientX}px`,
            top: `${event.clientY}px`,
            zIndex: 1000,
            minWidth: '160px'
        }
    } catch (error) {
        console.error('显示右键菜单错误:', error)
        contextMenu.visible = false
    }
}

const handleContextMenuVisibility = (visible) => {
    if (!visible) {
        contextMenu.visible = false
        contextMenu.targetFile = null
    }
}

const handleSearch = (value) => {
    try {
        searchText.value = value
    } catch (error) {
        console.error('搜索错误:', error)
    }
}

const handleTreeRightClick = ({ event, node }) => {
    event.preventDefault()
    treeContextMenu.visible = true
    treeContextMenu.node = node
    treeContextMenu.event = event

    treeContextMenuStyle.value = {
        position: 'absolute',
        left: `${event.clientX}px`,
        top: `${event.clientY}px`,
        zIndex: 1000,
        minWidth: '160px'
    }
}

const handleTreeContextMenuClick = ({ key }) => {
    const node = treeContextMenu.node

    try {
        switch (key) {
            case 'newFolder':
                handleNewFolder(node)
                break
            case 'rename':
                handleRenameFolder(node)
                break
            case 'copy':
                handleCopyFolder(node)
                break
            case 'cut':
                handleCutFolder(node)
                break
            case 'paste':
                handlePasteFolder(node)
                break
            case 'delete':
                handleDeleteFolder(node)
                break
        }
    } catch (error) {
        console.error('树节点菜单操作错误:', error)
    } finally {
        treeContextMenu.visible = false
    }
}

const handleNewFolder = (parentNode) => {
    console.log('新建文件夹在节点:', parentNode)
}

const handleRenameFolder = (node) => {
    renameModal.file = node
    renameModal.newName = node.name
    renameModal.visible = true
}

const handleCopyFolder = (node) => {
    clipboardData.value = {
        action: 'copy',
        node: { ...node }
    }
}

const handleCutFolder = (node) => {
    clipboardData.value = {
        action: 'cut',
        node: { ...node }
    }
}

const handlePasteFolder = (targetNode) => {
    if (!clipboardData.value) return

    console.log(`${clipboardData.value.action} 节点到:`, targetNode)

    if (clipboardData.value.action === 'cut') {
        clipboardData.value = null
    }
}

const handleDeleteFolder = (node) => {
    console.log('删除节点:', node)
}

// 添加底部按钮点击处理函数
const handleFooterClick = (type) => {
    try {
        switch (type) {
            case 'share':
                console.log('打开共享')
                // 这里添加共享功能的处理逻辑
                break
            case 'recycle':
                console.log('打开回收站')
                // 这里添加回收站功能的处理逻辑
                break
        }
    } catch (error) {
        console.error('底部按钮点击错误:', error)
    }
}

// 添加框选相关的状态
const selecting = ref(false)
const selectionStart = ref({ x: 0, y: 0 })
const selectionEnd = ref({ x: 0, y: 0 })
const selectedFiles = ref([])

// 计算选择框样式
const selectionBoxStyle = computed(() => {
    const left = Math.min(selectionStart.value.x, selectionEnd.value.x)
    const top = Math.min(selectionStart.value.y, selectionEnd.value.y)
    const width = Math.abs(selectionEnd.value.x - selectionStart.value.x)
    const height = Math.abs(selectionEnd.value.y - selectionStart.value.y)

    return {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`
    }
})

// 开始选择
const startSelection = (event) => {
    // 如果点击的是文件项，则不启动框选
    if (event.target.closest('.file-item')) return

    selecting.value = true
    const rect = event.currentTarget.getBoundingClientRect()
    selectionStart.value = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
    }
    selectionEnd.value = { ...selectionStart.value }

    // 如果没有按住 Ctrl 键，清除之前的选择
    if (!event.ctrlKey) {
        selectedFiles.value = []
    }
}

// 更新选择
const updateSelection = (event) => {
    if (!selecting.value) return

    const rect = event.currentTarget.getBoundingClientRect()
    selectionEnd.value = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
    }

    // 检查文件项是否在选择框内
    const fileItems = document.querySelectorAll('.file-item')
    const selectionRect = {
        left: Math.min(selectionStart.value.x, selectionEnd.value.x),
        top: Math.min(selectionStart.value.y, selectionEnd.value.y),
        right: Math.max(selectionStart.value.x, selectionEnd.value.x),
        bottom: Math.max(selectionStart.value.y, selectionEnd.value.y)
    }

    fileItems.forEach(item => {
        const itemRect = item.getBoundingClientRect()
        const itemRelativeRect = {
            left: itemRect.left - rect.left,
            top: itemRect.top - rect.top,
            right: itemRect.right - rect.left,
            bottom: itemRect.bottom - rect.top
        }

        const fileId = parseInt(item.dataset.fileId)
        const isIntersecting = !(
            itemRelativeRect.left > selectionRect.right ||
            itemRelativeRect.right < selectionRect.left ||
            itemRelativeRect.top > selectionRect.bottom ||
            itemRelativeRect.bottom < selectionRect.top
        )

        if (isIntersecting && !selectedFiles.value.includes(fileId)) {
            selectedFiles.value.push(fileId)
        } else if (!isIntersecting && !event.ctrlKey) {
            selectedFiles.value = selectedFiles.value.filter(id => id !== fileId)
        }
    })
}

// 结束选择
const endSelection = () => {
    selecting.value = false
}

// 修改文件点击处理函数
const handleFileClick = (file) => {
    try {
        if (event.ctrlKey) {
            // Ctrl + 点击进行多选
            const index = selectedFiles.value.indexOf(file.id)
            if (index === -1) {
                selectedFiles.value.push(file.id)
            } else {
                selectedFiles.value.splice(index, 1)
            }
        } else {
            // 普通点击只选择当前文件
            selectedFiles.value = [file.id]
        }
    } catch (error) {
        console.error('文件点击错误:', error)
    }
}

onMounted(() => {
    document.addEventListener('click', (event) => {
        if (!event.target.closest('.ant-tree') && !event.target.closest('.ant-dropdown')) {
            treeContextMenu.visible = false
            contextMenu.visible = false
        }
    })
})

onUnmounted(() => {
    try {
        document.removeEventListener('click', handleContextMenuVisibility)
    } catch (error) {
        console.error('卸载错误:', error)
    }
})
</script>

<style scoped>
/* 基础布局 */
.file-manager {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
}

/* 工具栏 */
.toolbar {
    padding: 8px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    flex-shrink: 0;
    background-color: #fafafa;
}

.navigation {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    min-height: 0;
    height: calc(100% - 56px);
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 220px;
    border-right: 1px solid #f0f0f0;
    padding: 8px;
    overflow-y: auto;
    height: 100%;
    flex-shrink: 0;
    position: relative;
}

/* 文件列表区域 */
.file-list {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    height: 100%;
}

/* 网格视图 */
.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
}

/* 文件项 */
.file-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px;
    border-radius: 6px;
    cursor: pointer;
    height: 84px;
    justify-content: center;
    border: 1px solid transparent;
    transition: all 0.2s;
}

.file-item:hover {
    background-color: #f0f7ff;
    border-color: #e6f4ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.file-icon {
    font-size: 26px;
    color: #1890ff;
    margin-bottom: 6px;
}

.file-name {
    text-align: center;
    font-size: 12px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.3;
}

/* 列表容器 */
.list-container {
    height: 100%;
    overflow: hidden;
}

/* 滚动条样式 */
.file-list::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.file-list::-webkit-scrollbar-thumb,
.sidebar::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
}

/* Ant Design 组件样式覆盖 */
:deep(.ant-tree-indent-unit) {
    width: 12px !important;
}

:deep(.ant-tree-switcher) {
    width: 20px !important;
}

:deep(.ant-tree-node-content-wrapper) {
    padding: 2px 4px !important;
}

:deep(.ant-tree-title) {
    margin-left: 4px !important;
}

:deep(.ant-tree-iconEle) {
    width: 16px !important;
    height: 16px !important;
    line-height: 16px !important;
    margin-right: 2px !important;
}

:deep(.ant-dropdown) {
    position: fixed !important;
}

:deep(.ant-dropdown-menu) {
    padding: 4px;
    border-radius: 6px;
}

:deep(.ant-dropdown-menu-item) {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
}

/* 修改侧边栏样式 */
.sidebar {
    width: 220px;
    border-right: 1px solid #f0f0f0;
    padding: 8px;
    overflow-y: auto;
    height: 100%;
    flex-shrink: 0;
    position: relative;
}

/* 修改导航栏样式 */
.navigation {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.path-breadcrumb {
    flex: 1;
    margin: 0 8px;
    overflow: hidden;
}

/* 修改搜索框样式 */
.search-input {
    transition: width 0.3s;
}

.search-input:hover,
.search-input:focus {
    width: 220px !important;
}

/* 添加底部功能按钮样式 */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 8px;
    display: flex;
    justify-content: space-around;
}

.footer-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.footer-item:hover {
    background-color: #f0f7ff;
    color: #1890ff;
}

.footer-item span {
    font-size: 14px;
}

/* 修改树的容器高度，为底部按钮留出空间 */
:deep(.ant-tree) {
    height: calc(100% - 50px);
    overflow-y: auto;
    padding-bottom: 50px;
}

/* 添加选择框样式 */
.selection-box {
    position: absolute;
    border: 1px solid #1890ff;
    background-color: rgba(24, 144, 255, 0.1);
    pointer-events: none;
    z-index: 1;
}

/* 添加文件选中状态样式 */
.file-item.selected {
    background-color: #e6f7ff;
    border-color: #91d5ff;
}

/* 修改文件列表容器样式 */
.file-list {
    position: relative;
    user-select: none;
}

/* 优化文件项样式 */
.file-item {
    position: relative;
    transition: background-color 0.2s, border-color 0.2s, transform 0.2s;
}

.file-item:hover {
    background-color: #f5f5f5;
}

.file-item.selected:hover {
    background-color: #e6f7ff;
}
</style>