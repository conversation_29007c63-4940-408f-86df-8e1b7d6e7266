<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入发票抬头，纳税识别号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="teamAuth.add" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'name'">
                        <a @click="handleDetail(record)">
                            {{ record.name }}
                        </a>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 编辑 -->
                            <a v-if="teamAuth.edit" @click="handleEdit(record, 'edit')">
                                {{ $t('common.edit') }}
                            </a>
                            <SkDivider v-if="teamAuth.edit" type="vertical" />

                            <!-- 删除 -->
                            <SkPopconfirm v-if="teamAuth.delete" :title="$t('common.deleteConfirm')"
                                @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t('common.delete') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @saveData="saveData">
                </ShowIndex>
            </SkModal>
        </SkCard>
    </div>

</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    objectId: {
        type: String,
        default: ''
    },
    objectKey: {
        type: String,
        default: ''
    }
})

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '发票抬头',
        dataIndex: 'name',
        width: 150
    },
    {
        title: '纳税识别号',
        dataIndex: 'identificationNumber',
        width: 150
    },
    {
        title: '开户行',
        dataIndex: 'openingBank',
        width: 130
    },
    {
        title: '开户帐号',
        dataIndex: 'openingAccount',
        width: 140
    },
    {
        title: '开票地址',
        dataIndex: 'billingAddress',
        width: 200
    },
    {
        title: '电话',
        dataIndex: 'phone',
        width: 140,
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 140
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center',
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 150,
        align: 'center',
        fixed: 'right'
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取团队权限
const teamAuth = ref({})
const getTeamAuth = async () => {
    teamAuth.value = await proxy.$config.teamObjectPermissionUtil.checkTeamBusinessAuthPermission(props.objectId, 'crmInvoiceHeaderAuthEnum', proxy.$util.classEnumMap['crmInvoiceHeaderAuthEnum'].className)
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            objectId: props.objectId,
            objectKey: props.objectKey,
            keyword: searchForm.keyword?.trim() || '' // 添加关键字搜索参数
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'queryInvoiceHeaderList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 保存数据
const saveData = (callback) => {
    callback({
        objectId: props.objectId,
        objectKey: props.objectKey
    })
}

const modalVisible = ref(false)
const modalWidth = ref('70%')
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050300002',
        params: {
            objectId: props.objectId,
        }
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050300003',
        params: {
            id: record.id,
            objectId: props.objectId,
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024050300004',
        params: {
            id: record.id,
            objectId: props.objectId,
        }
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().crmBasePath + 'deleteInvoiceHeaderById',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    getTeamAuth()
    fetchData()
})
</script>
<style scoped></style>