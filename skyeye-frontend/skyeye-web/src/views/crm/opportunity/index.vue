<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入商机名称" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="teamAuth.add" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'title'">
                        <a @click="handleDetail(record)">
                            {{ record.title }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['crmOpportunityStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <a v-if="teamAuth.submitToApproval" @click="handleConfirmOk(record)">
                                    {{ $t('common.submitApproval') }}
                                </a>
                                <SkDivider v-if="teamAuth.submitToApproval" type="vertical" />

                                <!-- 编辑 -->
                                <a v-if="teamAuth.edit" @click="handleEdit(record, 'edit')">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="teamAuth.edit" type="vertical" />

                                <!-- 删除 -->
                                <SkPopconfirm v-if="teamAuth.delete" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                                <SkDivider v-if="teamAuth.delete" type="vertical" />

                                <!-- 作废 -->
                                <SkPopconfirm v-if="teamAuth.invalid" :title="$t('common.abandonedConfirm')"
                                    @confirm="handleInvalid(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.abandoned') }}</a>
                                </SkPopconfirm>
                            </template>

                            <template v-if="record.editRow == 2 && record.state == 'inExamine'">
                                <!-- 撤销 -->
                                <SkPopconfirm v-if="teamAuth.revoke" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>
                            <!-- 状态变更 -->
                            <SkPopconfirm v-if="record.state == 'pass' || record.state == 'layAside' || record.state == 'businessNegotiation' || record.state == 'competitionAndBidding'
                                || record.state == 'schemeAndQuotation' || record.state == 'initialCommunication'"
                                @confirm="handleStateChange(record)" :okText="$t('common.confirm')"
                                :cancelText="$t('common.cancel')">
                                <template #title>
                                    <div>
                                        <div style="margin-bottom: 8px">{{ $t('common.stateChangeConfirm') }}</div>
                                        <SkSelect v-model="selectedState" style="width: 200px"
                                            :options="getStateOptions(record.state)" />
                                    </div>
                                </template>
                                <a>{{ $t('common.stateChange') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @saveData="saveData">
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>

</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    objectId: {
        type: String,
        default: ''
    },
    objectKey: {
        type: String,
        default: ''
    }
})

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '商机名称',
        dataIndex: 'title',
        width: 300
    },
    {
        title: '商机编号',
        dataIndex: 'oddNumber',
        width: 150
    },
    {
        title: '预计成交金额',
        dataIndex: 'estimatePrice',
        width: 130
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 200
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 120
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 140
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center',
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 250,
        align: 'center',
        fixed: 'right'
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['crmOpportunityStateEnum']);
    initEnumData.value = result
}

// 获取团队权限
const teamAuth = ref({})
const getTeamAuth = async () => {
    teamAuth.value = await proxy.$config.teamObjectPermissionUtil.checkTeamBusinessAuthPermission(props.objectId, 'crmOpportunityAuthEnum', proxy.$util.classEnumMap['crmOpportunityAuthEnum'].className)
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            objectId: props.objectId,
            objectKey: props.objectKey,
            keyword: searchForm.keyword?.trim() || '' // 添加关键字搜索参数
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'queryCrmOpportunityList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 保存数据
const saveData = (callback) => {
    callback({
        objectId: props.objectId,
        objectKey: props.objectKey
    })
}

const modalVisible = ref(false)
const modalWidth = ref('70%')
const modalTitle = ref('')
const modalType = ref('approval')
const operatorParams = ref({})

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023030900001',
        params: {
            objectId: props.objectId
        }
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023030900002',
        params: {
            objectId: props.objectId,
            id: record.id
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023030900003',
        params: {
            objectId: props.objectId,
            id: record.id
        }
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().crmBasePath + 'deleteCrmOpportunityById',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 作废
const handleInvalid = async (record) => {
    try {
        const params = {
            id: record.id
        }
        await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'opportunity013',
            params
        )
        SkMessage.success('作废成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('作废失败')
    }
}

// 当前行记录
const currentRecord = ref(null)

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick()
        modalTitle.value = '提交审批'
        modalType.value = 'approval'
        // 打开审批人选择弹窗
        currentRecord.value = record
        modalVisible.value = true
    } catch (error) {
        SkMessage.error('操作失败')
    }
}

// 处理弹窗取消
const handleModalCancel = async () => {
    try {
        await nextTick()
        modalVisible.value = false
        currentRecord.value = null
    } catch (error) {
        console.error('处理失败:', error)
    }
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            serviceClassName: currentRecord.value.serviceClassName,
            approvalId: person.id
        }

        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'opportunity017',
            params
        )

        // 提交成功
        SkMessage.success('提交成功')
        // 关闭弹窗
        modalVisible.value = false
        // 刷新数据
        await fetchData()
    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 撤销处理
const handleRevoke = async (record) => {
    try {
        const params = {
            processInstanceId: record.processInstanceId
        }

        await proxy.$http.put(
            proxy.$config.getConfig().crmBasePath + 'opportunity027',
            params
        )

        SkMessage.success('撤销成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 状态选择相关
const selectedState = ref('')

// 根据当前状态获取可选的下一个状态
const getStateOptions = (currentState) => {
    const allStates = {
        'initialCommunication': [
            { value: 'schemeAndQuotation', label: '方案报价' },
            { value: 'competitionAndBidding', label: '竞争投标' },
            { value: 'businessNegotiation', label: '商务谈判' },
            { value: 'strikeBargain', label: '成交' },
            { value: 'lostOrder', label: '丢单' },
            { value: 'layAside', label: '搁置' }
        ],
        'businessNegotiation': [
            { value: 'initialCommunication', label: '初步沟通' },
            { value: 'schemeAndQuotation', label: '方案报价' },
            { value: 'competitionAndBidding', label: '竞争投标' },
            { value: 'strikeBargain', label: '成交' },
            { value: 'lostOrder', label: '丢单' },
            { value: 'layAside', label: '搁置' }
        ],
        'schemeAndQuotation': [
            { value: 'initialCommunication', label: '初步沟通' },
            { value: 'competitionAndBidding', label: '竞争投标' },
            { value: 'businessNegotiation', label: '商务谈判' },
            { value: 'strikeBargain', label: '成交' },
            { value: 'lostOrder', label: '丢单' },
            { value: 'layAside', label: '搁置' }
        ],
        'competitionAndBidding': [
            { value: 'initialCommunication', label: '初步沟通' },
            { value: 'schemeAndQuotation', label: '方案报价' },
            { value: 'businessNegotiation', label: '商务谈判' },
            { value: 'strikeBargain', label: '成交' },
            { value: 'lostOrder', label: '丢单' },
            { value: 'layAside', label: '搁置' }
        ],
        'pass': [
            { value: 'initialCommunication', label: '初步沟通' },
            { value: 'schemeAndQuotation', label: '方案报价' },
            { value: 'competitionAndBidding', label: '竞争投标' },
            { value: 'businessNegotiation', label: '商务谈判' },
            { value: 'strikeBargain', label: '成交' },
            { value: 'lostOrder', label: '丢单' },
            { value: 'layAside', label: '搁置' }
        ],
        'layAside': [
            { value: 'initialCommunication', label: '初步沟通' },
            { value: 'lostOrder', label: '丢单' }
        ]
    }
    const mapAuth = {
        'initialCommunication': 'conmunicate',
        'businessNegotiation': 'negotiate',
        'schemeAndQuotation': 'quotedPrice',
        'competitionAndBidding': 'tender',
        'strikeBargain': 'turnover',
        'lostOrder': 'losingTable',
        'layAside': 'layAside'
    }

    // 获取所有状态
    let statesList = allStates[currentState] || []

    // 根据团队权限过滤状态
    statesList.forEach(item => {
        const authKey = mapAuth[item.value]
        if (teamAuth.value[authKey]) {
            item.auth = true
        }
    })
    statesList = statesList.filter(item => item.auth)

    return statesList
}

// 处理状态变更
const handleStateChange = async (record) => {
    if (!selectedState.value) {
        SkMessage.warning('请选择状态')
        return
    }
    const mapRequest = {
        'initialCommunication': 'opportunity019',
        'businessNegotiation': 'opportunity022',
        'schemeAndQuotation': 'opportunity020',
        'competitionAndBidding': 'opportunity021',
        'strikeBargain': 'opportunity023',
        'lostOrder': 'opportunity024',
        'layAside': 'opportunity025'
    }
    try {
        await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + mapRequest[selectedState.value],
            {
                id: record.id
            }
        )
        SkMessage.success('状态变更成功')
        selectedState.value = '' // 重置选择
        fetchData() // 重新加载数据
    } catch (error) {
        SkMessage.error('状态变更失败')
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    getInitData()
    getTeamAuth()
    fetchData()
})
</script>
<style scoped>
:deep(.ant-popover-message-title) {
    padding-left: 0;
}

:deep(.ant-popover-inner-content) {
    min-width: 250px;
}
</style>