<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <!-- 搜索区域 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" :showButtons="false">
                    <a-form-item label="指定年度" required>
                        <SkDatePicker v-model="searchForm.year" picker="year" @change="handleYearChange" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 图表区域 -->
            <SkCard class="chart-card" title="客户变量分析">
                <div ref="chartRef" class="chart-container"></div>
            </SkCard>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkDatePicker from '@/components/SkDatePicker/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 搜索表单数据
const searchForm = reactive({
    year: undefined
})

// 图表实例
let chartInstance = null
const chartRef = ref(null)

// 初始化图表
const initChart = () => {
    if (chartInstance) {
        chartInstance.dispose()
    }
    chartInstance = echarts.init(chartRef.value)
}

// 渲染图表
const renderChart = (data) => {
    const nameStr = data.map(item => item.yearMonth)
    const insertCustomerNum = data.map(item => item.insertCustomerNum)
    const insertContactsNum = data.map(item => item.insertContactsNum)

    const option = {
        title: {
            text: '指定年度新增量',
            subtext: '指定年度客户月增量与联系人月增量',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            data: ['客户月增量', '联系人月增量'],
            bottom: 0
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: nameStr
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '客户月增量',
                type: 'line',
                smooth: true,
                data: insertCustomerNum,
                itemStyle: {
                    color: '#1890ff'
                }
            },
            {
                name: '联系人月增量',
                type: 'line',
                smooth: true,
                data: insertContactsNum,
                itemStyle: {
                    color: '#52c41a'
                }
            }
        ]
    }

    chartInstance.setOption(option)
}

// 获取数据
const fetchData = async () => {
    try {
        const params = {
            year: searchForm.year
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'crmpage001',
            params
        )
        renderChart(res.rows || [])
    } catch (error) {
        SkMessage.error('获取数据失败')
    }
}

// 年份变化处理
const handleYearChange = () => {
    fetchData()
}

// 搜索处理
const handleSearch = () => {
    fetchData()
}

// 窗口大小变化处理
const handleResize = () => {
    if (chartInstance) {
        chartInstance.resize()
    }
}

onMounted(() => {
    // 通过date获取当前年份
    const date = new Date()
    searchForm.year = String(date.getFullYear())
    initChart()
    fetchData()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-card {
    background: #fff;
    border-radius: 2px;
    height: auto;

    .chart-container {
        height: 500px;
        width: 100%;
    }
}
</style>