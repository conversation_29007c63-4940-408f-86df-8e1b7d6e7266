<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <!-- 搜索区域 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" :showButtons="false">
                    <a-form-item label="指定年度" required>
                        <SkDatePicker v-model="searchForm.year" picker="year" @change="handleYearChange" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 图表区域 -->
            <SkCard class="chart-card" title="员工跟单月增量分析">
                <div ref="chartRef" class="chart-container"></div>
            </SkCard>

            <!-- 表格区域 -->
            <SkCard class="table-card" title="员工跟单月增量表格分析">
                <SkTable :columns="columns" :data-source="tableData" :pagination="false" :loading="loading">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'insertDocumentaryNum'">
                            {{ record.insertDocumentaryNum }}
                        </template>
                    </template>
                </SkTable>
            </SkCard>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkDatePicker from '@/components/SkDatePicker/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 搜索表单数据
const searchForm = reactive({
    year: undefined
})

// 表格列定义
const columns = [
    { title: '日期', dataIndex: 'yearMonth', width: 120 },
    { title: '当月新增跟单数量(个)', dataIndex: 'insertDocumentaryNum', width: 180, align: 'right' }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 图表实例
let chartInstance = null
const chartRef = ref(null)

// 初始化图表
const initChart = () => {
    if (chartInstance) {
        chartInstance.dispose()
    }
    chartInstance = echarts.init(chartRef.value)
}

// 渲染图表
const renderChart = (data) => {
    const name = data.map(item => item.yearMonth)
    const num = data.map(item => item.insertDocumentaryNum)

    const option = {
        title: {
            text: '员工跟单月增量分析',
            subtext: '员工跟单在指定年份不同的月增量分析',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: name
        },
        yAxis: {
            type: 'value',
            name: '跟单数量'
        },
        series: [
            {
                type: 'line',
                smooth: true,
                data: num,
                itemStyle: {
                    color: '#1890ff'
                },
                label: {
                    show: true,
                    position: 'top'
                }
            }
        ]
    }

    chartInstance.setOption(option)
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            year: searchForm.year
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'crmpage005',
            params
        )
        const rows = res.rows || []
        tableData.value = rows
        renderChart(rows)
    } catch (error) {
        SkMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 年份变化处理
const handleYearChange = () => {
    fetchData()
}

// 搜索处理
const handleSearch = () => {
    fetchData()
}

// 窗口大小变化处理
const handleResize = () => {
    if (chartInstance) {
        chartInstance.resize()
    }
}

onMounted(() => {
    // 设置当前年份
    const date = new Date()
    searchForm.year = String(date.getFullYear())
    initChart()
    fetchData()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-card {
    background: #fff;
    border-radius: 2px;
    height: auto;
    margin-bottom: 16px;

    .chart-container {
        height: 500px;
        width: 100%;
    }
}

.table-card {
    background: #fff;
    border-radius: 2px;
    height: auto;
}
</style>