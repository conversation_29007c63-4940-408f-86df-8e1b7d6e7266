<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <a-row>
                <!-- 客户类型分析 -->
                <a-col :span="12">
                    <SkCard class="chart-card" title="客户类型分析">
                        <div ref="chart1Ref" class="chart-container"></div>
                    </SkCard>
                </a-col>

                <!-- 客户来源分析 -->
                <a-col :span="12">
                    <SkCard class="chart-card" title="客户来源分析">
                        <div ref="chart2Ref" class="chart-container"></div>
                    </SkCard>
                </a-col>

                <!-- 所属行业分析 -->
                <a-col :span="12">
                    <SkCard class="chart-card" title="所属行业分析">
                        <div ref="chart3Ref" class="chart-container"></div>
                    </SkCard>
                </a-col>

                <!-- 客户分组分析 -->
                <a-col :span="12">
                    <SkCard class="chart-card" title="客户分组分析">
                        <div ref="chart4Ref" class="chart-container"></div>
                    </SkCard>
                </a-col>
            </a-row>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import SkCard from '@/components/SkCard/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 图表实例
let chart1Instance = null
let chart2Instance = null
let chart3Instance = null
let chart4Instance = null
const chart1Ref = ref(null)
const chart2Ref = ref(null)
const chart3Ref = ref(null)
const chart4Ref = ref(null)

// 初始化图表
const initCharts = () => {
    chart1Instance = echarts.init(chart1Ref.value)
    chart2Instance = echarts.init(chart2Ref.value)
    chart3Instance = echarts.init(chart3Ref.value)
    chart4Instance = echarts.init(chart4Ref.value)
}

// 获取图表配置
const getChartOption = (title, subtext, name, num) => {
    return {
        title: {
            text: title,
            subtext: subtext,
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '25%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: name,
            axisLabel: {
                interval: 0,
                rotate: 55,
                formatter: function (value) {
                    return value.length > 4 ? value.slice(0, 4) + '\n' + value.slice(4) : value;
                }
            }
        },
        yAxis: {
            type: 'value',
            name: '客户数量'
        },
        series: [
            {
                type: 'bar',
                data: num,
                itemStyle: {
                    color: '#1890ff'
                },
                label: {
                    show: true,
                    position: 'top'
                }
            }
        ]
    }
}

// 渲染图表
const renderCharts = (data) => {
    // 客户类型分析
    const typeNames = data.numType.map(item => item.name)
    const typeNums = data.numType.map(item => item.number)
    chart1Instance.setOption(getChartOption(
        '客户类型分析',
        '根据客户类型统计客户数量',
        typeNames,
        typeNums
    ))

    // 客户来源分析
    const fromNames = data.numFrom.map(item => item.name)
    const fromNums = data.numFrom.map(item => item.number)
    chart2Instance.setOption(getChartOption(
        '客户来源分析',
        '根据客户来源统计客户数量',
        fromNames,
        fromNums
    ))

    // 所属行业分析
    const industryNames = data.numIndustry.map(item => item.name)
    const industryNums = data.numIndustry.map(item => item.number)
    chart3Instance.setOption(getChartOption(
        '所属行业分析',
        '根据所属行业统计客户数量',
        industryNames,
        industryNums
    ))

    // 客户分组分析
    const groupNames = data.numGroup.map(item => item.name)
    const groupNums = data.numGroup.map(item => item.number)
    chart4Instance.setOption(getChartOption(
        '客户分组分析',
        '根据客户分组统计客户数量',
        groupNames,
        groupNums
    ))
}

// 获取数据
const fetchData = async () => {
    try {
        const params = {
            crmCustomerType: 'CRM_CUSTOMER_TYPE',
            crmCustomerFrom: 'CRM_CUSTOMER_FROM',
            crmCustomerIndustry: 'CRM_CUSTOMER_INDUSTRY',
            crmCustomerGroup: 'CRM_CUSTOMER_GROUP'
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'crmpage002',
            params
        )
        renderCharts(res.bean || {})
    } catch (error) {
        SkMessage.error('获取数据失败')
    }
}

// 窗口大小变化处理
const handleResize = () => {
    chart1Instance?.resize()
    chart2Instance?.resize()
    chart3Instance?.resize()
    chart4Instance?.resize()
}

onMounted(() => {
    initCharts()
    fetchData()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    chart1Instance?.dispose()
    chart2Instance?.dispose()
    chart3Instance?.dispose()
    chart4Instance?.dispose()
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-card {
    background: #fff;
    border-radius: 2px;
    height: auto;
    margin-bottom: 16px;
    padding: 0 8px;

    :deep(.ant-card-body) {
        overflow: hidden !important;
    }

    .chart-container {
        height: 300px;
        width: 100%;
    }
}
</style>