<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <!-- 搜索区域 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" :showButtons="false">
                    <a-form-item label="指定年度" required>
                        <SkDatePicker v-model="searchForm.year" picker="year" @change="handleYearChange" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 图表区域 -->
            <SkCard class="chart-card" title="客户跟单方式分析">
                <div ref="chartRef" class="chart-container"></div>
            </SkCard>

            <!-- 表格区域 -->
            <SkCard class="table-card" title="客户跟单方式占比分析">
                <SkTable :columns="columns" :data-source="tableData" :pagination="false" :loading="loading">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'proportion'">
                            {{ record.proportion }}%
                        </template>
                    </template>
                </SkTable>
            </SkCard>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkDatePicker from '@/components/SkDatePicker/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { proxy } = getCurrentInstance()

// 搜索表单数据
const searchForm = reactive({
    year: undefined
})

// 表格列定义
const columns = [
    { title: '跟单方式', dataIndex: 'name', width: 200 },
    { title: '数量', dataIndex: 'number', width: 200, align: 'right' },
    { title: '占比（%）', dataIndex: 'proportion', width: 200, align: 'right' }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 图表实例
let chartInstance = null
const chartRef = ref(null)

// 初始化图表
const initChart = () => {
    if (chartInstance) {
        chartInstance.dispose()
    }
    chartInstance = echarts.init(chartRef.value)
}

// 渲染图表
const renderChart = (data) => {
    const name = data.map(item => item.name)
    const num = data.map(item => item.number)

    const option = {
        title: {
            text: '客户跟单方式分析',
            subtext: '统计在指定年份不同的跟单方式的数量分析',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: name,
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '跟单数量',
                type: 'bar',
                data: num,
                itemStyle: {
                    color: '#1890ff'
                },
                label: {
                    show: true,
                    position: 'top'
                }
            }
        ]
    }

    chartInstance.setOption(option)
}

// 计算占比
const calculateProportion = (data) => {
    const total = data.reduce((sum, item) => sum + Number(item.number), 0)
    return data.map(item => ({
        ...item,
        proportion: total === 0 ? '0.00' : (item.number / total * 100).toFixed(2)
    }))
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            year: searchForm.year,
            crmDocumentaryType: 'CRM_DOCUMENTARY_TYPE'
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().crmBasePath + 'crmpage003',
            params
        )
        const processedData = calculateProportion(res.rows || [])
        tableData.value = processedData
        renderChart(processedData)
    } catch (error) {
        SkMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 年份变化处理
const handleYearChange = () => {
    fetchData()
}

// 搜索处理
const handleSearch = () => {
    fetchData()
}

// 窗口大小变化处理
const handleResize = () => {
    if (chartInstance) {
        chartInstance.resize()
    }
}

onMounted(() => {
    // 设置当前年份
    const date = new Date()
    searchForm.year = String(date.getFullYear())
    initChart()
    fetchData()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.chart-card {
    background: #fff;
    border-radius: 2px;
    height: auto;
    margin-bottom: 16px;

    .chart-container {
        height: 500px;
        width: 100%;
    }
}

.table-card {
    background: #fff;
    border-radius: 2px;
    height: auto;
}
</style>