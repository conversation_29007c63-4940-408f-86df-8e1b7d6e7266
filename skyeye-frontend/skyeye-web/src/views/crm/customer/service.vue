<template>
    <div class="container-manage">
        <SkTabs v-model="activeTab" :tabs="tabs">
            <template #details>
                <div v-if="activeTab === 'details'">
                    <ShowIndex pageId="FP2023030300001" :params="{ id: objectId }" />
                </div>
            </template>
            <template #contract>
                <div v-if="activeTab === 'contract'">
                    <ServiceContract :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #contacts>
                <div v-if="activeTab === 'contacts'">
                    <ServiceContacts :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #opportunity>
                <div v-if="activeTab === 'opportunity'">
                    <ServiceOpportunity :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #followUp>
                <div v-if="activeTab === 'followUp'">
                    <ServiceDocumentary :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #documents>
                <div v-if="activeTab === 'documents'">
                    <ServiceDocuments :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #discussion>
                <div v-if="activeTab === 'discussion'">
                    <ServiceDiscuss :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #revisit>
                <div v-if="activeTab === 'revisit'">
                    <ServiceFollowUp :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #payment>
                <div v-if="activeTab === 'payment'">
                    <ServicePayment :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #invoiceTitle>
                <div v-if="activeTab === 'invoiceTitle'">
                    <ServiceInvoiceHeader :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #invoice>
                <div v-if="activeTab === 'invoice'">
                    <ServiceInvoice :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #soldProducts>
                <div v-if="activeTab === 'soldProducts'">
                    <ServiceHolder :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #team>
                <div v-if="activeTab === 'team'">
                    <ServiceTeam :objectId="objectId" :objectKey="objectKey" :objectType="1" />
                </div>
            </template>
        </SkTabs>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import SkTabs from '@/components/SkTabs/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ServiceContract from '@/views/crm/contract/index.vue'
import ServiceContacts from '@/views/system/contacts/index.vue'
import ServiceOpportunity from '@/views/crm/opportunity/index.vue'
import ServiceDocumentary from '@/views/crm/documentary/index.vue'
import ServiceDocuments from '@/views/system/document/index.vue'
import ServiceDiscuss from '@/views/system/discuss/index.vue'
import ServiceFollowUp from '@/views/crm/follow/index.vue'
import ServicePayment from '@/views/crm/paymentCollection/index.vue'
import ServiceInvoiceHeader from '@/views/crm/invoiceHeader/index.vue'
import ServiceInvoice from '@/views/crm/invoice/index.vue'
import ServiceHolder from '@/views/system/holder/index.vue'
import ServiceTeam from '@/views/system/team/index.vue'

const { t } = useI18n()

const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    }
})

const objectId = computed(() => props.params.objectId)
const objectKey = computed(() => props.params.objectKey)

// 当前激活的选项卡
const activeTab = ref('details')

// 定义选项卡配置
const tabs = [
    { key: 'details', label: t('common.details') },
    { key: 'contract', label: t('common.contract') },
    { key: 'contacts', label: t('common.contacts') },
    { key: 'opportunity', label: t('common.opportunity') },
    { key: 'followUp', label: t('common.followUp') },
    { key: 'documents', label: t('common.documents') },
    { key: 'discussion', label: t('common.discussion') },
    { key: 'revisit', label: t('common.revisit') },
    { key: 'payment', label: t('common.payment') },
    { key: 'invoiceTitle', label: t('common.invoiceTitle') },
    { key: 'invoice', label: t('common.invoice') },
    { key: 'soldProducts', label: t('common.soldProducts') },
    { key: 'team', label: t('common.team') }
]
</script>

<style scoped></style>
