<template>
    <view class="sk-grid" :style="containerStyle">
        <!-- 设计器预览 -->
        <view class="sk-grid-preview" v-if="isDesign">
            <view class="preview-header">
                <text>宫格预览</text>
            </view>
        </view>

        <!-- 宫格主体 -->
        <view class="grid-container" :class="gridClasses" :style="gridStyles">
            <template v-if="list && list.length">
                <view v-for="(item, index) in list" :key="index" class="grid-item" :class="{
                    'grid-item--clickable': clickable,
                    'grid-item--square': square
                }" :style="itemStyle" @tap="handleClick(item, index)">
                    <view class="grid-item__content" :class="{
                        [`grid-item__content--h-${horizontalAlign}`]: true,
                        [`grid-item__content--v-${verticalAlign}`]: true
                    }">
                        <view class="grid-item__icon" :style="{ fontSize: iconPxSize }">
                            <template v-if="item.isImage">
                                <template v-if="isDesign">
                                    <img :src="item.icon" :style="{
                                        width: iconPxSize,
                                        height: iconPxSize,
                                        display: 'block',
                                        objectFit: 'contain'
                                    }" />
                                </template>
                                <template v-else>
                                    <image :src="item.icon" mode="aspectFit" :style="{
                                        width: iconSize,
                                        height: iconSize
                                    }" />
                                </template>
                            </template>
                            <template v-else>
                                <text class="iconfont" :class="'icon-' + item.icon"></text>
                            </template>
                        </view>
                        <text class="grid-item__text" :style="{ fontSize: textPxSize }">
                            {{ item.text }}
                        </text>
                    </view>
                </view>
            </template>

            <!-- 空状态展示 -->
            <view v-else class="grid-empty">
                <view class="empty-content">
                    <image class="logo" src="/static/images/logo.png" mode="aspectFit" />
                    <text class="title">Skyeye云</text>
                    <text class="subtitle">请添加轮播图片</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { defineComponent, ref, computed, onMounted, watch, getCurrentInstance } from 'vue'
import { loadDataByType, rpxToPx } from '../util/designTool'

export default defineComponent({
    name: 'sk-grid',

    props: {
        // 是否为设计模式
        isDesign: {
            type: Boolean,
            default: false
        },
        // 数据源配置
        dataSource: {
            type: Object,
            default: () => ({
                type: 'json',
                data: [],
                url: '',
                method: 'GET',
                headers: {},
                params: {},
                dataPath: '',
                transform: '',
                fieldMapping: {
                    icon: 'icon',
                    text: 'text',
                    url: 'url'
                }
            })
        },
        // 布局配置
        column: {
            type: Number,
            default: 4
        },
        border: {
            type: Boolean,
            default: true
        },
        square: {
            type: Boolean,
            default: true
        },
        clickable: {
            type: Boolean,
            default: true
        },
        // 样式配置
        gutter: {
            type: String,
            default: '16rpx'
        },
        iconSize: {
            type: String,
            default: '28rpx'
        },
        textSize: {
            type: String,
            default: '14rpx'
        },
        horizontalAlign: {
            type: String,
            default: 'center',
            validator: (value) => ['left', 'center', 'right'].includes(value)
        },
        verticalAlign: {
            type: String,
            default: 'center',
            validator: (value) => ['top', 'center', 'bottom'].includes(value)
        }
    },

    emits: ['click'],

    // 解决UNIAPP编译到微信小程序时的问题
    options: {
        virtualHost: true
    },

    setup(props, { emit }) {
        const { proxy } = getCurrentInstance()
        const internalList = ref([])

        // 设计器相关逻辑
        const isInDesigner = computed(() => {
            return props.isDesign || window.__DESIGNER__
        })

        // 获取预览数据
        const getPreviewData = () => {
            // 在设计器中，非静态数据返回示例数据
            return [
                {
                    icon: '/src/uni-components/static/images/no-mation.png',
                    text: '首页',
                    url: '/pages/index/index',
                    isImage: true
                },
                {
                    icon: '/src/uni-components/static/images/no-mation.png',
                    text: '我的',
                    url: '/pages/user/index',
                    isImage: true
                },
                {
                    icon: '/src/uni-components/static/images/no-mation.png',
                    text: '设置',
                    url: '/pages/setting/index',
                    isImage: true
                },
                {
                    icon: '/src/uni-components/static/images/no-mation.png',
                    text: '消息',
                    url: '/pages/message/index',
                    isImage: true
                }
            ]
        }

        // 数据列表
        const list = computed(() => {
            return internalList.value
        })

        // 加载数据
        const loadData = async () => {
            internalList.value = loadDataByType(props, proxy)
            if (isInDesigner.value) {
                internalList.value = getPreviewData()
            }
        }

        // 监听数据源变化
        watch(() => props.dataSource, () => {
            loadData()
        }, { deep: true })

        // 初始化时加载数据
        onMounted(() => {
            loadData()
        })

        // 样式计算
        const containerStyle = computed(() => ({
            width: '100%'
        }))

        const gridClasses = computed(() => ({
            'grid--border': props.border
        }))

        const gridStyles = computed(() => ({
            paddingLeft: props.gutter,
            paddingTop: props.gutter
        }))

        const itemStyle = computed(() => {
            const width = `${100 / Number(props.column)}%`
            return {
                '--item-width': width,
                'flex-basis': width,
                'max-width': width,
                paddingRight: props.gutter,
                paddingBottom: props.gutter
            }
        })

        // 点击处理
        const handleClick = (item, index) => {
            if (!props.clickable) return
            emit('click', item, index)

            // 在设计器中不执行跳转
            if (isInDesigner.value) {
                return
            }

            if (item.url) {
                // #ifdef H5
                if (item.url.startsWith('http')) {
                    window.location.href = item.url
                } else {
                    uni.navigateTo({
                        url: item.url,
                        fail: () => {
                            uni.switchTab({ url: item.url })
                        }
                    })
                }
                // #endif

                // #ifdef MP || APP-PLUS
                if (item.url.startsWith('http')) {
                    uni.navigateTo({
                        url: `/pages/webview/index?url=${encodeURIComponent(item.url)}`,
                        fail: (err) => {
                            console.error('跳转失败:', err)
                            uni.showToast({
                                title: '跳转失败',
                                icon: 'none'
                            })
                        }
                    })
                } else {
                    uni.navigateTo({
                        url: item.url,
                        fail: () => {
                            uni.switchTab({
                                url: item.url,
                                fail: (err) => {
                                    console.error('跳转失败:', err)
                                    uni.showToast({
                                        title: '跳转失败',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    })
                }
                // #endif
            }
        }

        // 计算图标尺寸
        const iconPxSize = computed(() => {
            return rpxToPx(props.iconSize)
        })

        // 添加文字尺寸计算
        const textPxSize = computed(() => {
            return rpxToPx(props.textSize)
        })

        return {
            list,
            containerStyle,
            gridClasses,
            gridStyles,
            itemStyle,
            handleClick,
            isInDesigner,
            iconPxSize,
            textPxSize
        }
    }
})
</script>

<style lang="scss">
/* #ifndef APP-NVUE */
.sk-grid {
    width: 100%;

    // 设计器预览样式
    .sk-grid-preview {
        background: #f5f5f5;
        padding: 16rpx;
        margin-bottom: 16rpx;
        border-radius: 8rpx;

        .preview-header {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 8rpx;
        }
    }

    .grid-container {
        position: relative;
        display: flex;
        flex-wrap: wrap;

        &.grid--border {
            &::after {
                content: '';
                position: absolute;
                box-sizing: border-box;
                width: 200%;
                height: 200%;
                transform: scale(0.5);
                transform-origin: left top;
                border: 1px solid #ebedf0;
                pointer-events: none;
            }
        }
    }

    .grid-item {
        position: relative;
        box-sizing: border-box;
        flex-shrink: 0;
        width: var(--item-width);

        &--clickable {
            cursor: pointer;

            &:active {
                background-color: #f2f3f5;
            }
        }

        &--square {
            .grid-item__content {
                position: absolute;
                top: 0;
                right: var(--gutter);
                bottom: var(--gutter);
                left: 0;
                width: 100%;
            }

            &::before {
                display: block;
                content: '';
                padding-top: 100%;
            }
        }

        &__content {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            height: 100%;
            padding: 16rpx 8rpx;

            // 水平对齐
            &--h-left {
                align-items: flex-start;
            }

            &--h-center {
                align-items: center;
            }

            &--h-right {
                align-items: flex-end;
            }

            // 垂直对齐
            &--v-top {
                justify-content: flex-start;
            }

            &--v-center {
                justify-content: center;
            }

            &--v-bottom {
                justify-content: flex-end;
            }
        }

        &__icon {
            margin-bottom: 8rpx;
            color: #323233;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 1;
        }

        &__text {
            color: #646566;
            word-break: break-all;
            line-height: 1.2;
        }
    }

    .grid-empty {
        width: 100%;
        padding: 32rpx;
        background: #f5f7fa;
        border-radius: 8rpx;

        .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16rpx;

            .logo {
                width: 120rpx;
                height: 120rpx;
            }

            .title {
                font-size: 32rpx;
                font-weight: 500;
                color: #333;
            }

            .subtitle {
                font-size: 24rpx;
                color: #999;
            }
        }
    }
}

/* #endif */

/* #ifdef APP-NVUE */
.sk-grid {
    flex: 1;

    .grid-container {
        flex: 1;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .grid-item {
        flex: none;
        width: var(--item-width);
        position: relative;
    }
}

/* #endif */
</style>