import { helpContent } from '../util/helpContent'

export default {
    // 组件属性配置
    props: {
        // 数据来源配置
        dataSource: {
            type: 'object',
            label: '数据来源',
            defaultValue: {
                type: 'json',
                url: '',
                fileUrl: [],
                method: 'GET',
                headers: {},
                params: {},
                dataPath: '',
                transform: '',
                fieldMapping: {
                    icon: 'icon',
                    text: 'text',
                    url: 'url'
                }
            },
            config: {
                type: {
                    type: 'select',
                    label: '来源类型',
                    help: helpContent.dataSourceType,
                    options: [
                        { label: '接口数据', value: 'api' },
                        { label: 'JSON数据', value: 'json' },
                        { label: 'XML数据', value: 'xml' }
                    ]
                },
                url: {
                    type: 'string',
                    label: '数据地址',
                    component: 'ApiSelector',
                    placeholder: '请输入接口地址或文件路径'
                },
                method: {
                    type: 'select',
                    label: '请求方法',
                    showOn: (values) => values.type === 'api',
                    options: [
                        { label: 'GET', value: 'GET' },
                        { label: 'POST', value: 'POST' }
                    ]
                },
                headers: {
                    type: 'object',
                    label: '请求头',
                    showOn: (values) => values.type === 'api',
                    component: 'JsonEditor'
                },
                params: {
                    type: 'object',
                    label: '请求参数',
                    showOn: (values) => values.type === 'api',
                    component: 'JsonEditor'
                },
                dataPath: {
                    type: 'string',
                    label: '数据路径',
                    showOn: (values) => values.type !== 'static',
                    component: 'PathSelector',
                    placeholder: '如: data.list',
                    help: helpContent.dataPath
                },
                transform: {
                    type: 'function',
                    label: '数据转换',
                    showOn: (values) => values.type !== 'static',
                    component: 'CodeEditor',
                    language: 'javascript',
                    placeholder: '自定义数据转换函数',
                    help: helpContent.transform
                },
                fieldMapping: {
                    type: 'object',
                    label: '字段映射',
                    showOn: (values) => values.type !== 'static',
                    component: 'FieldMapper',
                    help: helpContent.fieldMapping,
                    config: {
                        icon: {
                            type: 'string',
                            label: '图标字段',
                            defaultValue: 'icon',
                            placeholder: '数据中图标对应的字段名'
                        },
                        text: {
                            type: 'string',
                            label: '文本字段',
                            defaultValue: 'text',
                            placeholder: '数据中文本对应的字段名'
                        },
                        url: {
                            type: 'string',
                            label: '链接字段',
                            defaultValue: 'url',
                            placeholder: '数据中跳转链接对应的字段名'
                        }
                    }
                },
                fileUpload: {
                    type: 'file',
                    label: '上传文件',
                    help: helpContent.fileUpload,
                    showOn: (values) => ['xml', 'json'].includes(values.type)
                }
            }
        },

        // 布局配置
        column: {
            type: 'number',
            label: '列数',
            defaultValue: 4,
            min: 1,
            max: 6
        },
        border: {
            type: 'boolean',
            label: '显示边框',
            defaultValue: true
        },
        square: {
            type: 'boolean',
            label: '正方形格子',
            defaultValue: true
        },
        horizontalAlign: {
            type: 'select',
            label: '水平对齐',
            defaultValue: 'center',
            options: [
                { label: '左对齐', value: 'left' },
                { label: '居中', value: 'center' },
                { label: '右对齐', value: 'right' }
            ]
        },
        verticalAlign: {
            type: 'select',
            label: '垂直对齐',
            defaultValue: 'center',
            options: [
                { label: '顶部对齐', value: 'top' },
                { label: '居中', value: 'center' },
                { label: '底部对齐', value: 'bottom' }
            ]
        },
        clickable: {
            type: 'boolean',
            label: '点击反馈',
            defaultValue: true,
            help: helpContent.clickable
        },
        gutter: {
            type: 'string',
            label: '格子间距',
            defaultValue: '16rpx'
        },
        iconSize: {
            type: 'string',
            label: '图标大小',
            defaultValue: '80rpx'
        },
        textSize: {
            type: 'string',
            label: '文字大小',
            defaultValue: '27rpx'
        }
    }
} 