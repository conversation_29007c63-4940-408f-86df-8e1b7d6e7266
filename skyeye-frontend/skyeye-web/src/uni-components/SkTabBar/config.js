import { helpContent } from '../util/helpContent'

export default {
    props: {
        // 数据源配置
        dataSource: {
            type: 'object',
            label: '数据来源',
            defaultValue: {
                type: 'json',
                url: '',
                fileUrl: [],
                method: 'GET',
                headers: {},
                params: {},
                dataPath: '',
                transform: '',
                fieldMapping: {
                    icon: 'icon',
                    text: 'text',
                    url: 'url'
                }
            },
            config: {
                type: {
                    type: 'select',
                    label: '来源类型',
                    help: helpContent.dataSourceType,
                    options: [
                        { label: '接口数据', value: 'api' },
                        { label: 'JSON数据', value: 'json' },
                        { label: 'XML数据', value: 'xml' }
                    ]
                },
                url: {
                    type: 'string',
                    label: '数据地址',
                    component: 'ApiSelector',
                    placeholder: '请输入接口地址或文件路径'
                },
                method: {
                    type: 'select',
                    label: '请求方法',
                    showOn: (values) => values.type === 'api',
                    options: [
                        { label: 'GET', value: 'GET' },
                        { label: 'POST', value: 'POST' }
                    ]
                },
                headers: {
                    type: 'object',
                    label: '请求头',
                    showOn: (values) => values.type === 'api',
                    component: 'JsonEditor'
                },
                params: {
                    type: 'object',
                    label: '请求参数',
                    showOn: (values) => values.type === 'api',
                    component: 'JsonEditor'
                },
                dataPath: {
                    type: 'string',
                    label: '数据路径',
                    component: 'PathSelector',
                    placeholder: '如: data.list',
                    help: helpContent.dataPath
                },
                transform: {
                    type: 'function',
                    label: '数据转换',
                    component: 'CodeEditor',
                    language: 'javascript',
                    placeholder: '自定义数据转换函数',
                    help: helpContent.transform
                },
                fieldMapping: {
                    type: 'object',
                    label: '字段映射',
                    component: 'FieldMapper',
                    help: helpContent.fieldMapping,
                    config: {
                        icon: {
                            type: 'string',
                            label: '图标字段',
                            defaultValue: 'icon',
                            placeholder: '数据中图标对应的字段名'
                        },
                        text: {
                            type: 'string',
                            label: '文本字段',
                            defaultValue: 'text',
                            placeholder: '数据中文本对应的字段名'
                        },
                        url: {
                            type: 'string',
                            label: '链接字段',
                            defaultValue: 'url',
                            placeholder: '数据中链接对应的字段名'
                        }
                    }
                },
                fileUpload: {
                    type: 'file',
                    label: '上传文件',
                    help: helpContent.fileUpload,
                    showOn: (values) => ['xml', 'json'].includes(values.type)
                }
            }
        },

        // 布局配置
        fixed: {
            type: 'boolean',
            label: '固定在底部',
            defaultValue: true,
            help: helpContent.tabBar.fixed
        },
        border: {
            type: 'boolean',
            label: '显示上边框',
            defaultValue: true,
            help: helpContent.tabBar.border
        },
        safeAreaInsetBottom: {
            type: 'boolean',
            label: '底部安全区适配',
            defaultValue: true,
            help: helpContent.tabBar.safeAreaInsetBottom
        },

        // 样式配置
        backgroundColor: {
            type: 'color',
            label: '背景颜色',
            defaultValue: '#ffffff',
            help: helpContent.tabBar.backgroundColor
        },
        iconSize: {
            type: 'string',
            label: '图标大小',
            defaultValue: '48rpx',
            help: helpContent.tabBar.iconSize
        },
        textSize: {
            type: 'string',
            label: '文字大小',
            defaultValue: '24rpx',
            help: helpContent.tabBar.textSize
        },
        activeColor: {
            type: 'color',
            label: '选中颜色',
            defaultValue: '#1890ff',
            help: helpContent.tabBar.activeColor
        },
        inactiveColor: {
            type: 'color',
            label: '未选中颜色',
            defaultValue: '#646566',
            help: helpContent.tabBar.inactiveColor
        },
        spacing: {
            type: 'string',
            label: '图文间距',
            defaultValue: '4rpx',
            help: helpContent.tabBar.spacing
        }
    },

    // 组件事件配置
    events: {
        change: {
            label: '切换事件',
            params: ['index', 'item'],
            help: helpContent.tabBar.changeEvent
        },
        click: {
            label: '点击事件',
            params: ['item'],
            help: helpContent.tabBar.clickEvent
        }
    },

    // 组件设计器配置
    designer: {
        // 组件分类
        category: 'navigation',
        // 组件图标
        icon: 'TabsOutlined',
        // 组件标题
        title: '标签栏',
        // 组件描述
        description: '用于移动端底部导航的标签栏组件',
        // 文档链接
        docUrl: '/docs/components/tabbar',
        // 组件预览配置
        preview: {
            // 预览数据
            props: {
                dataSource: {
                    type: 'static',
                    data: [
                        {
                            icon: 'home',
                            text: '首页',
                            url: '/pages/index/index'
                        },
                        {
                            icon: 'user',
                            text: '我的',
                            url: '/pages/user/index'
                        }
                    ]
                }
            }
        }
    }
}