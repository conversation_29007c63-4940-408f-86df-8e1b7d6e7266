<template>
    <view class="sk-tabbar" :class="{
        'sk-tabbar--fixed': fixed && !isDesign,  // 在非设计模式下才应用固定定位
        'sk-tabbar--design-fixed': fixed && isDesign  // 设计模式下使用特殊的固定定位
    }">
        <!-- 设计器预览 -->
        <view class="sk-tabbar-preview" v-if="isDesign">
            <view class="preview-header">
                <text>标签栏预览</text>
            </view>
        </view>

        <!-- 标签栏主体 -->
        <view class="tabbar" :class="{ 'tabbar--border': border }" :style="{ backgroundColor }">
            <template v-if="list && list.length">
                <view v-for="(item, index) in list" :key="index" class="tabbar-item" @tap="handleClick(item, index)">
                    <view class="tabbar-item__icon" :style="{ fontSize: iconPxSize }">
                        <template v-if="item.isImage">
                            <template v-if="isDesign">
                                <img :src="item.icon" :style="{
                                    width: iconPxSize,
                                    height: iconPxSize,
                                    display: 'block',
                                    objectFit: 'contain'
                                }" />
                            </template>
                            <template v-else>
                                <image :src="item.icon" mode="aspectFit" :style="{
                                    width: iconSize,
                                    height: iconSize
                                }" />
                            </template>
                        </template>
                        <template v-else>
                            <text class="iconfont" :class="'icon-' + item.icon"></text>
                        </template>
                    </view>
                    <text class="tabbar-item__text" :style="{
                        fontSize: textPxSize,
                        color: currentIndex === index ? activeColor : inactiveColor
                    }">
                        {{ item.text }}
                    </text>
                </view>
            </template>

            <!-- 空状态展示 -->
            <view v-else class="tabbar-empty">
                <view class="empty-content">
                    <image class="logo" src="/static/images/logo.png" mode="aspectFit" />
                    <text class="title">Skyeye云</text>
                    <text class="subtitle">请添加标签项</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { defineComponent, ref, computed, onMounted, watch, getCurrentInstance } from 'vue'
import { loadDataByType, rpxToPx } from '../util/designTool'

export default defineComponent({
    name: 'sk-tabbar',

    props: {
        // 是否为设计模式
        isDesign: {
            type: Boolean,
            default: false
        },
        // 数据源配置
        dataSource: {
            type: Object,
            default: () => ({
                type: 'static',
                data: [],
                url: '',
                method: 'GET',
                headers: {},
                params: {},
                dataPath: '',
                transform: '',
                fieldMapping: {
                    icon: 'icon',
                    text: 'text',
                    url: 'url'
                }
            })
        },
        // 样式配置
        fixed: {
            type: Boolean,
            default: true
        },
        border: {
            type: Boolean,
            default: true
        },
        backgroundColor: {
            type: String,
            default: '#ffffff'
        },
        iconSize: {
            type: String,
            default: '48rpx'
        },
        textSize: {
            type: String,
            default: '24rpx'
        },
        activeColor: {
            type: String,
            default: '#1890ff'
        },
        inactiveColor: {
            type: String,
            default: '#646566'
        }
    },

    emits: ['change', 'click'],

    setup(props, { emit }) {
        const { proxy } = getCurrentInstance()
        const currentIndex = ref(0)
        const internalList = ref([])

        // 设计器相关逻辑
        const isInDesigner = computed(() => {
            return props.isDesign || window.__DESIGNER__
        })

        // 获取预览数据
        const getPreviewData = () => {
            return [
                {
                    icon: 'home',
                    text: '首页',
                    url: '/pages/index/index'
                },
                {
                    icon: 'user',
                    text: '我的',
                    url: '/pages/user/index'
                }
            ]
        }

        // 数据列表
        const list = computed(() => {
            return internalList.value
        })

        // 加载数据
        const loadData = async () => {
            try {
                if (props.dataSource.type === 'static') {
                    internalList.value = props.dataSource.data
                    return
                }

                if (isInDesigner.value) {
                    internalList.value = getPreviewData()
                    return
                }

                internalList.value = await loadDataByType(props, proxy)
            } catch (error) {
                internalList.value = []
                if (isInDesigner.value) {
                    internalList.value = getPreviewData()
                }
            }
        }

        // 监听数据源变化
        watch(() => props.dataSource, () => {
            loadData()
        }, { deep: true })

        // 初始化时加载数据
        onMounted(() => {
            loadData()
        })

        // 计算图标尺寸
        const iconPxSize = computed(() => {
            return rpxToPx(props.iconSize)
        })

        // 计算文字尺寸
        const textPxSize = computed(() => {
            return rpxToPx(props.textSize)
        })

        // 点击处理
        const handleClick = (item, index) => {
            currentIndex.value = index
            emit('change', { index, item })
            emit('click', item)

            if (isInDesigner.value) {
                return
            }

            if (item.url) {
                uni.switchTab({
                    url: item.url,
                    fail: () => {
                        uni.navigateTo({
                            url: item.url,
                            fail: (err) => {
                                console.error('跳转失败:', err)
                                uni.showToast({
                                    title: '跳转失败',
                                    icon: 'none'
                                })
                            }
                        })
                    }
                })
            }
        }

        return {
            currentIndex,
            list,
            iconPxSize,
            textPxSize,
            handleClick,
            isInDesigner
        }
    }
})
</script>

<style lang="scss">
.sk-tabbar {
    width: 100%;

    // 正常模式下的固定定位
    &--fixed {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
    }

    // 设计模式下的固定定位
    &--design-fixed {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        background: #fff;
    }

    // 设计器预览样式
    .sk-tabbar-preview {
        background: #f5f7fa;
        padding: 12rpx 16rpx;
        margin-bottom: 12rpx;
        border-radius: 6rpx;

        .preview-header {
            font-size: 24rpx;
            color: #666;
        }
    }

    .tabbar {
        width: 100%;
        height: 98rpx; // 修改高度
        display: flex;
        align-items: center;
        background: #fff;
        padding-bottom: env(safe-area-inset-bottom); // 适配底部安全区

        &--border {
            border-top: 1px solid rgba(0, 0, 0, 0.05); // 更柔和的边框
        }
    }

    .tabbar-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 6rpx 0;
        transition: all 0.3s ease; // 添加过渡效果

        &:active {
            opacity: 0.7; // 点击反馈
        }

        &__icon {
            margin-bottom: 6rpx; // 微调间距
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .iconfont {
                transition: transform 0.3s ease; // 图标过渡效果
            }

            img,
            image {
                transition: transform 0.3s ease;
            }
        }

        &__text {
            line-height: 1;
            font-size: 20rpx; // 默认字号
            transition: transform 0.2s ease, color 0.3s ease; // 文字过渡效果
            white-space: nowrap;
            transform-origin: center;
        }

        // 选中状态效果
        &.active {
            .tabbar-item__icon {
                transform: translateY(-2rpx); // 轻微上移

                .iconfont {
                    transform: scale(1.1); // 图标放大
                }

                img,
                image {
                    transform: scale(1.1);
                }
            }

            .tabbar-item__text {
                transform: scale(1.05); // 文字轻微放大

                .tabbar-empty {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f5f7fa;

                    .empty-content {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 16rpx;

                        .logo {
                            width: 80rpx;
                            height: 80rpx;
                        }

                        .title {
                            font-size: 28rpx;
                            font-weight: 500;
                            color: #333;
                        }

                        .subtitle {
                            font-size: 24rpx;
                            color: #999;
                        }
                    }
                }
            }
        }
    }
}
</style>