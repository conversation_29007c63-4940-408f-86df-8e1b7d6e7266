import { helpContent } from '../util/helpContent'

export default {
    // 组件属性配置
    props: {
        // 数据源配置
        dataSource: {
            type: 'object',
            label: '数据来源',
            defaultValue: {
                type: 'static',
                data: [],
                url: '',
                fileUrl: [],
                method: 'GET',
                headers: {},
                params: {},
                dataPath: '',
                transform: '',
                fieldMapping: {
                    image: 'image',
                    url: 'url',
                    title: 'title'
                }
            },
            config: {
                type: {
                    type: 'select',
                    label: '来源类型',
                    help: helpContent.dataSourceType,
                    options: [
                        { label: '静态数据', value: 'static' },
                        { label: '接口数据', value: 'api' },
                        { label: 'JSON数据', value: 'json' },
                        { label: 'XML数据', value: 'xml' }
                    ]
                },
                data: {
                    type: 'array',
                    label: '静态数据',
                    showOn: (values) => values.type === 'static',
                    config: {
                        item: {
                            type: 'object',
                            props: {
                                image: {
                                    type: 'image',
                                    label: '图片',
                                    defaultValue: ''
                                },
                                url: {
                                    type: 'string',
                                    label: '链接',
                                    defaultValue: ''
                                },
                                title: {
                                    type: 'string',
                                    label: '标题',
                                    defaultValue: ''
                                }
                            }
                        }
                    }
                },
                url: {
                    type: 'string',
                    label: '数据地址',
                    component: 'ApiSelector',
                    placeholder: '请输入接口地址或文件路径'
                },
                method: {
                    type: 'select',
                    label: '请求方法',
                    showOn: (values) => values.type === 'api',
                    options: [
                        { label: 'GET', value: 'GET' },
                        { label: 'POST', value: 'POST' }
                    ]
                },
                headers: {
                    type: 'object',
                    label: '请求头',
                    showOn: (values) => values.type === 'api',
                    component: 'JsonEditor'
                },
                params: {
                    type: 'object',
                    label: '请求参数',
                    showOn: (values) => values.type === 'api',
                    component: 'JsonEditor'
                },
                dataPath: {
                    type: 'string',
                    label: '数据路径',
                    showOn: (values) => values.type !== 'static',
                    component: 'PathSelector',
                    placeholder: '如: data.list',
                    help: helpContent.dataPath
                },
                transform: {
                    type: 'function',
                    label: '数据转换',
                    showOn: (values) => values.type !== 'static',
                    component: 'CodeEditor',
                    language: 'javascript',
                    placeholder: '自定义数据转换函数',
                    help: helpContent.transform
                },
                fieldMapping: {
                    type: 'object',
                    label: '字段映射',
                    showOn: (values) => values.type !== 'static',
                    component: 'FieldMapper',
                    help: helpContent.fieldMapping,
                    config: {
                        image: {
                            type: 'string',
                            label: '图片字段',
                            defaultValue: 'image',
                            placeholder: '数据中图片地址对应的字段名'
                        },
                        url: {
                            type: 'string',
                            label: '链接字段',
                            defaultValue: 'url',
                            placeholder: '数据中跳转链接对应的字段名'
                        },
                        title: {
                            type: 'string',
                            label: '标题字段',
                            defaultValue: 'title',
                            placeholder: '数据中标题对应的字段名'
                        }
                    }
                },
                fileUpload: {
                    type: 'file',
                    label: '上传文件',
                    help: helpContent.fileUpload,
                    showOn: (values) => ['xml', 'json'].includes(values.type)
                }
            }
        },
        autoplay: {
            type: 'boolean',
            label: '自动播放',
            defaultValue: true,
            help: helpContent.swiper.autoplay
        },
        interval: {
            type: 'number',
            label: '播放间隔(ms)',
            defaultValue: 3000,
            min: 1000,
            max: 10000,
            help: helpContent.swiper.interval
        },
        duration: {
            type: 'number',
            label: '动画时长(ms)',
            defaultValue: 500,
            min: 100,
            max: 2000,
            help: helpContent.swiper.duration
        },
        circular: {
            type: 'boolean',
            label: '循环播放',
            defaultValue: true,
            help: helpContent.swiper.circular
        },
        indicatorDots: {
            type: 'boolean',
            label: '显示指示点',
            defaultValue: true,
            help: helpContent.swiper.indicatorDots
        },
        indicatorColor: {
            type: 'color',
            label: '指示点颜色',
            defaultValue: 'rgba(255, 255, 255, 0.6)',
            help: helpContent.swiper.indicatorColor
        },
        indicatorActiveColor: {
            type: 'color',
            label: '当前指示点颜色',
            defaultValue: '#ffffff',
            help: helpContent.swiper.indicatorActiveColor
        },
        imageMode: {
            type: 'select',
            label: '图片填充模式',
            defaultValue: 'aspectFill',
            help: helpContent.swiper.imageMode,
            options: [
                { label: '裁剪填充', value: 'aspectFill' },
                { label: '缩放填充', value: 'scaleToFill' },
                { label: '等比缩放', value: 'aspectFit' }
            ]
        },
        clickable: {
            type: 'boolean',
            label: '启用点击',
            defaultValue: true,
            help: helpContent.swiper.clickable
        }
    },

    // 组件事件配置
    events: {
        change: {
            label: '切换事件',
            params: ['current', 'item']
        },
        click: {
            label: '点击事件',
            params: ['item']
        }
    },

    // 组件样式配置
    styles: {
        height: {
            type: 'string',
            label: '高度',
            defaultValue: '300rpx',
            help: helpContent.swiper.height
        },
        borderRadius: {
            type: 'string',
            label: '圆角',
            defaultValue: '0',
            help: helpContent.swiper.borderRadius
        }
    },

    // 添加设计器配置
    designer: {
        // 组件分类
        category: 'basic',
        // 组件图标
        icon: 'PictureOutlined',
        // 组件标题
        title: '轮播图',
        // 组件描述
        description: '用于展示多张图片的轮播组件',
        // 文档链接
        docUrl: '/docs/components/swiper',
        // 组件预览配置
        preview: {
            // 预览数据
            props: {
                dataSource: {
                    type: 'static',
                    data: [
                        {
                            image: '/static/images/banner1.jpg',
                            url: '/pages/index/index',
                            title: '示例图片1'
                        },
                        {
                            image: '/static/images/banner2.jpg',
                            url: '/pages/index/index',
                            title: '示例图片2'
                        }
                    ]
                },
                height: '300rpx',
                autoplay: true,
                indicatorDots: true
            }
        }
    }
} 