<template>
    <view class="sk-swiper">
        <!-- 设计器预览 -->
        <view class="sk-swiper-preview" v-if="isDesign">
            <view class="preview-header">
                <text>轮播图预览</text>
            </view>
        </view>

        <!-- 轮播图主体 -->
        <view class="swiper-container">
            <!-- 当有图片时显示轮播 -->
            <template v-if="list && list.length">
                <view class="swiper-wrapper">
                    <view v-for="(item, index) in displayList" :key="index" class="swiper-item" :style="{
                        transform: `translateX(${100 * (index - currentIndex - (circular ? 1 : 0))}%)`,
                        opacity: isCurrentItem(index) ? 1 : 0,
                        pointerEvents: isCurrentItem(index) ? 'auto' : 'none',
                        zIndex: isCurrentItem(index) ? 1 : 0
                    }">
                        <template v-if="isDesign">
                            <img :src="item.image" :mode="imageMode" class="swiper-image" @tap="handleClick(item)" />
                        </template>
                        <template v-else>
                            <image :src="item.image" :mode="imageMode" class="swiper-image" @tap="handleClick(item)" />
                        </template>
                    </view>
                </view>

                <!-- 指示器 -->
                <view v-if="indicatorDots" class="indicator-dots">
                    <view v-for="(_, index) in list" :key="index" class="dot"
                        :class="{ active: currentIndex === index }" :style="{
                            backgroundColor: currentIndex === index ? indicatorActiveColor : indicatorColor
                        }" />
                </view>

                <!-- 切换按钮 -->
                <view v-if="!isDesign" class="navigation-buttons">
                    <view class="nav-button prev" @tap="slidePrev">
                        <text class="nav-icon">〈</text>
                    </view>
                    <view class="nav-button next" @tap="slideNext">
                        <text class="nav-icon">〉</text>
                    </view>
                </view>
            </template>

            <!-- 空状态展示 -->
            <view v-else class="swiper-empty">
                <view class="empty-content">
                    <image class="logo" src="/static/images/logo.png" mode="aspectFit" />
                    <text class="title">Skyeye云</text>
                    <text class="subtitle">请添加轮播图片</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { defineComponent, ref, computed, onMounted, onUnmounted, watch, getCurrentInstance } from 'vue'
import { loadDataByType } from '../util/designTool'

export default defineComponent({
    name: 'sk-swiper',
    props: {
        // 是否为设计模式
        isDesign: {
            type: Boolean,
            default: false
        },
        // 数据源配置
        dataSource: {
            type: Object,
            default: () => ({
                type: 'static',
                data: [],
                url: '',
                method: 'GET',
                headers: {},
                params: {},
                dataPath: '',
                transform: '',
                fieldMapping: {
                    image: 'image',
                    url: 'url',
                    title: 'title'
                }
            })
        },
        // 是否自动播放
        autoplay: {
            type: Boolean,
            default: true
        },
        // 自动播放间隔时间(ms)
        interval: {
            type: Number,
            default: 3000
        },
        // 滑动动画时长(ms)
        duration: {
            type: Number,
            default: 500
        },
        // 是否循环播放
        circular: {
            type: Boolean,
            default: true
        },
        // 是否显示指示点
        indicatorDots: {
            type: Boolean,
            default: true
        },
        // 指示点颜色
        indicatorColor: {
            type: String,
            default: 'rgba(255, 255, 255, 0.6)'
        },
        // 当前指示点颜色
        indicatorActiveColor: {
            type: String,
            default: '#ffffff'
        },
        // 图片填充模式
        imageMode: {
            type: String,
            default: 'aspectFill'
        },
        // 点击事件是否启用
        clickable: {
            type: Boolean,
            default: true
        },
        // 容器高度
        height: {
            type: String,
            default: '300rpx'
        },
        // 容器圆角
        borderRadius: {
            type: String,
            default: '0'
        }
    },

    emits: ['change', 'click'],

    // 解决UNIAPP编译到微信小程序时,会多一层以组件命名的标签的问题
    options: {
        virtualHost: true
    },

    setup(props, { emit }) {
        const { proxy } = getCurrentInstance()

        const currentIndex = ref(0)
        const isAnimating = ref(false)
        const touchDeltaX = ref(0)
        let autoplayTimer = null

        // 触摸状态变量
        let touchStartX = 0
        let touchStartY = 0
        let lastTouchX = 0
        let isSwiping = false

        // 获取系统信息
        const containerWidth = ref(375) // 默认宽度

        // 内部数据列表
        const internalList = ref([])

        // 设计器相关逻辑
        const isInDesigner = computed(() => {
            return props.isDesign || window.__DESIGNER__
        })

        // 获取预览数据
        const getPreviewData = () => {
            // 在设计器中，非静态数据返回示例数据
            return [
                {
                    image: '/src/uni-components/static/images/no-mation.png',
                    url: '/pages/index/index',
                    title: '示例图片1'
                },
                {
                    image: '/src/uni-components/static/images/no-mation.png',
                    url: '/pages/index/index',
                    title: '示例图片2'
                }
            ]
        }

        // 修改数据列表的计算属性
        const list = computed(() => {
            return internalList.value
        })

        // 修改加载数据的逻辑
        const loadData = async () => {
            internalList.value = loadDataByType(props, proxy)
            if (isInDesigner.value) {
                internalList.value = getPreviewData()
            }
        }

        // 监听数据源变化
        watch(() => props.dataSource, () => {
            loadData()
        }, { deep: true })

        // 初始化时加载数据
        onMounted(() => {
            loadData()
        })

        // 计算需要显示的列表（包含首尾过渡项）
        const displayList = computed(() => {
            console.log(list.value)
            if (!list.value.length) return []
            if (!props.circular) return list.value

            return [
                list.value[list.value.length - 1],
                ...list.value,
                list.value[0]
            ]
        })

        // 计算容器样式
        const containerStyle = computed(() => ({
            height: props.height,
            borderRadius: props.borderRadius
        }))

        // 计算包装器样式
        const wrapperStyle = computed(() => ({
            transform: `translateX(${-100 * (currentIndex.value + (props.circular ? 1 : 0)) + touchDeltaX.value}%)`,
            transition: isAnimating.value ? `transform ${props.duration}ms` : 'none'
        }))

        // 计算轮播项样式
        const itemStyle = computed(() => ({
            width: '100%',
            flex: 'none'
        }))

        // 自动播放控制
        const startAutoplay = () => {
            if (!props.autoplay) return
            stopAutoplay()
            autoplayTimer = setInterval(() => {
                slideNext()
            }, props.interval)
        }

        const stopAutoplay = () => {
            if (autoplayTimer) {
                clearInterval(autoplayTimer)
                autoplayTimer = null
            }
        }

        // 切换到指定索引
        const slideTo = (index) => {
            if (isAnimating.value) return
            isAnimating.value = true

            const lastIndex = list.value.length - 1
            let targetIndex = index

            if (props.circular) {
                if (index < 0) {
                    targetIndex = lastIndex
                    setTimeout(() => {
                        isAnimating.value = false
                        currentIndex.value = lastIndex
                    }, props.duration)
                } else if (index > lastIndex) {
                    targetIndex = 0
                    setTimeout(() => {
                        isAnimating.value = false
                        currentIndex.value = 0
                    }, props.duration)
                }
            } else {
                targetIndex = Math.max(0, Math.min(index, lastIndex))
            }

            currentIndex.value = targetIndex
            setTimeout(() => {
                isAnimating.value = false
            }, props.duration)

            emit('change', {
                current: targetIndex,
                item: list.value[targetIndex]
            })
        }

        // 上一张
        const slidePrev = () => {
            if (!props.circular && currentIndex.value === 0) return
            slideTo(currentIndex.value - 1)
        }

        // 下一张
        const slideNext = () => {
            if (!props.circular && currentIndex.value === list.value.length - 1) return
            slideTo(currentIndex.value + 1)
        }

        // 触摸事件处理
        const handleTouchStart = (e) => {
            if (!isSwiping) {
                touchStartX = e.touches[0].clientX
                touchStartY = e.touches[0].clientY
                lastTouchX = touchStartX
                stopAutoplay()
            }
        }

        const handleTouchMove = (e) => {
            if (!isSwiping) {
                const touchX = e.touches[0].clientX
                const touchY = e.touches[0].clientY
                const deltaX = touchX - touchStartX
                const deltaY = touchY - touchStartY

                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                    isSwiping = true
                    const percentDelta = (deltaX / containerWidth.value) * 100
                    touchDeltaX.value = percentDelta
                }

                lastTouchX = touchX
            }
        }

        const handleTouchEnd = () => {
            if (isSwiping) {
                const threshold = 30 // 滑动阈值（百分比）
                const deltaX = touchDeltaX.value

                if (Math.abs(deltaX) > threshold) {
                    if (deltaX > 0) {
                        slidePrev()
                    } else {
                        slideNext()
                    }
                } else {
                    // 回到当前位置
                    isAnimating.value = true
                    touchDeltaX.value = 0
                }

                setTimeout(() => {
                    touchDeltaX.value = 0
                    isSwiping = false
                    startAutoplay()
                }, props.duration)
            }
        }

        // 点击事件处理
        const handleClick = (item) => {
            if (!props.clickable) return
            emit('click', item)

            // 在设计器中不执行跳转
            if (isInDesigner.value) {
                return
            }

            if (item.url && !props.isDesign) {
                // 在设计器中，不执行跳转逻辑
                if (window.__DESIGNER__) {
                    return
                }

                // #ifdef H5
                if (item.url.startsWith('http')) {
                    window.location.href = item.url
                } else {
                    uni.navigateTo({
                        url: item.url,
                        fail: () => {
                            uni.switchTab({
                                url: item.url
                            })
                        }
                    })
                }
                // #endif

                // #ifdef MP || APP-PLUS
                if (item.url.startsWith('http')) {
                    uni.navigateTo({
                        url: `/pages/webview/index?url=${encodeURIComponent(item.url)}`,
                        fail: (err) => {
                            console.error('跳转失败:', err)
                            uni.showToast({
                                title: '跳转失败',
                                icon: 'none'
                            })
                        }
                    })
                } else {
                    uni.navigateTo({
                        url: item.url,
                        fail: () => {
                            uni.switchTab({
                                url: item.url,
                                fail: (err) => {
                                    console.error('跳转失败:', err)
                                    uni.showToast({
                                        title: '跳转失败',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    })
                }
                // #endif
            }
        }

        // 监听自动播放属性变化
        watch(() => props.autoplay, (newVal) => {
            if (newVal) {
                startAutoplay()
            } else {
                stopAutoplay()
            }
        })

        // 生命周期处理
        onMounted(() => {
            // 在设计器中直接使用默认宽度
            if (props.isDesign) {
                containerWidth.value = 375
                if (props.autoplay) {
                    startAutoplay()
                }
                return
            }

            // #ifdef H5
            containerWidth.value = document.documentElement.clientWidth
            // #endif

            // #ifdef MP || APP-PLUS
            const info = uni.getSystemInfoSync()
            containerWidth.value = info.screenWidth
            // #endif

            if (props.autoplay) {
                startAutoplay()
            }
        })

        onUnmounted(() => {
            stopAutoplay()
        })

        // 添加判断当前项的方法
        const isCurrentItem = (index) => {
            return index === currentIndex.value + (props.circular ? 1 : 0)
        }

        return {
            currentIndex,
            displayList,
            containerStyle,
            wrapperStyle,
            itemStyle,
            handleTouchStart,
            handleTouchMove,
            handleTouchEnd,
            handleClick,
            slidePrev,
            slideNext,
            list,
            isInDesigner,
            isCurrentItem,
            circular: props.circular
        }
    }
})
</script>

<style lang="scss">
.sk-swiper {
    width: 100%;
}

/* #ifndef APP-NVUE */
.sk-swiper {

    .swiper-container {
        height: 200px;
        width: 100%;
        overflow: hidden;
        display: flex;
        position: relative;
    }

    .swiper-item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transition: all 0.3s ease-out;
        will-change: transform, opacity;

        &:not([style*="opacity: 1"]) {
            visibility: hidden;
        }
    }

    .swiper-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    .sk-swiper-preview {
        background: #f5f5f5;
        padding: 16rpx;
        margin-bottom: 16rpx;
        border-radius: 8rpx;

        .preview-header {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 8rpx;
        }
    }

    .indicator-dots {
        position: absolute;
        bottom: 20rpx;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 16rpx;

        .dot {
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            transition: all 0.3s;

            &.active {
                width: 32rpx;
                border-radius: 8rpx;
            }
        }
    }

    .navigation-buttons {
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 72rpx;
            height: 72rpx;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;

            &:active {
                background: rgba(0, 0, 0, 0.5);
            }

            &.prev {
                left: 32rpx;
            }

            &.next {
                right: 32rpx;
            }

            .nav-icon {
                color: #fff;
                font-size: 32rpx;
            }
        }
    }

    .swiper-empty {
        width: 100%;
        height: 300rpx; // 默认高度
        background: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8rpx;

        .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16rpx;

            .logo {
                width: 120rpx;
                height: 120rpx;
            }

            .title {
                font-size: 32rpx;
                font-weight: 500;
                color: #333;
            }

            .subtitle {
                font-size: 24rpx;
                color: #999;
            }
        }
    }
}

/* #endif */

/* #ifdef APP-NVUE */
.sk-swiper {

    .swiper-item {
        flex: 1;
        width: 750rpx; // nvue 中需要明确指定宽度
    }

    .swiper-image {
        flex: 1;
    }

    .indicator-dots {
        position: absolute;
        bottom: 20rpx;
        left: 375rpx;
        transform: translateX(-50%);
        flex-direction: row;
    }

    .navigation-buttons {
        position: relative;

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 72rpx;
            height: 72rpx;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 36rpx;
            align-items: center;
            justify-content: center;
        }
    }
}

/* #endif */
</style>