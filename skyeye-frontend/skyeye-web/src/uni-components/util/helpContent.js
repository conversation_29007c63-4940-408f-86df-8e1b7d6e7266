import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'

// 通用帮助图标配置
const helpIcon = () => h(QuestionCircleOutlined)

// 通用帮助内容
export const helpContent = {
    // 数据路径帮助
    dataPath: {
        icon: helpIcon,
        title: '数据路径说明',
        content: `用于指定接口返回数据中的具体数据位置。

示例：
- data.list
- result.items
- records

说明：
- 使用点号(.)分隔多级路径
- 留空表示使用根路径
- 支持多级嵌套`
    },

    // 数据转换帮助
    transform: {
        icon: helpIcon,
        title: '数据转换说明',
        content: `数据转换函数用于处理接口返回的数据，使其符合组件要求的格式。

示例代码：
function(data) {
&nbsp;&nbsp;&nbsp;&nbsp;return data.list.map(item => ({
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;icon: item.menuIcon,
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;text: item.menuName,
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;url: item.menuPath
&nbsp;&nbsp;&nbsp;&nbsp;}));
}

参数说明：
- data: 接口返回的原始数据
- 返回值: 必须是数组，每个元素包含 icon/text/url 字段

常见用途：
1. 数据格式转换
2. 数据过滤
3. 数据排序
4. 数据计算
5. 数据清洗`
    },

    // 字段映射帮助
    fieldMapping: {
        icon: helpIcon,
        title: '字段映射说明',
        content: `用于将接口返回的字段名映射到组件所需的字段名。

示例：
icon: menuIcon   // 接口返回的 menuIcon 字段映射到组件的 icon 字段
text: menuName   // 接口返回的 menuName 字段映射到组件的 text 字段
url: menuPath    // 接口返回的 menuPath 字段映射到组件的 url 字段

说明：
- 左侧为组件字段名
- 右侧为接口字段名
- 支持多级路径，如：'data.icon'`
    },

    // 添加数据来源类型帮助
    // 添加数据来源类型帮助
    dataSourceType: {
        icon: helpIcon,  // 添加这行
        title: '数据来源类型说明',
        content: `用于选择数据的来源方式。

选项说明：
- 接口数据：从后端接口获取数据
- JSON数据：上传或指定JSON格式的数据文件
- XML数据：上传或指定XML格式的数据文件

使用说明：
1. 接口数据：需要配置接口地址、请求方法等
2. JSON数据：支持上传.json文件或指定文件地址
3. XML数据：支持上传.xml文件或指定文件地址`
    },

    // 添加文件上传帮助
    fileUpload: {
        icon: helpIcon,
        title: '文件上传说明',
        content: `用于上传数据文件，支持JSON和XML格式。

文件格式要求：
- JSON文件：标准的JSON格式，UTF-8编码
- XML文件：标准的XML格式，UTF-8编码

文件内容示例：
JSON格式：
{
&nbsp;&nbsp;&nbsp;&nbsp;"data": {
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"list": [
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"icon": "home",
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text": "首页"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;},
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"icon": "user",
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text": "我的"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;]
&nbsp;&nbsp;&nbsp;&nbsp;}
}

XML格式：
&lt;root&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;data&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;list&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;item&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;icon&gt;home&lt;/icon&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;text&gt;首页&lt;/text&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/item&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/list&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/data&gt;
&lt;/root&gt;`
    },

    // 添加点击反馈帮助
    clickable: {
        icon: helpIcon,
        title: '点击反馈说明',
        content: `控制宫格项目是否有点击反馈效果。

功能说明：
- 开启后，点击时会有背景色变化
- 关闭后，点击时无视觉反馈
- 不影响跳转功能的使用

使用场景：
1. 需要明确的点击反馈时开启
2. 纯展示用途时可以关闭
3. 与页面整体交互风格统一`
    },

    // 轮播图相关帮助内容
    swiper: {
        autoplay: {
            icon: helpIcon,
            title: '自动播放说明',
            content: `控制轮播图是否自动播放。

功能说明：
- 开启后，轮播图会自动切换
- 关闭后，需要手动切换
- 可配合播放间隔使用`
        },

        interval: {
            icon: helpIcon,
            title: '播放间隔说明',
            content: `设置自动播放时的切换间隔时间。

说明：
- 单位为毫秒
- 最小值：1000ms (1秒)
- 最大值：10000ms (10秒)
- 建议值：3000-5000ms`
        },

        duration: {
            icon: helpIcon,
            title: '动画时长说明',
            content: `设置切换时的动画过渡时间。

说明：
- 单位为毫秒
- 最小值：100ms
- 最大值：2000ms
- 建议值：300-800ms`
        },

        circular: {
            icon: helpIcon,
            title: '循环播放说明',
            content: `控制是否启用循环播放模式。

功能说明：
- 开启后，最后一张图片切换后会回到第一张
- 关闭后，播放到最后一张时停止`
        },

        indicatorDots: {
            icon: helpIcon,
            title: '指示点说明',
            content: `控制是否显示轮播图底部的指示点。

功能说明：
- 开启后，显示当前页面位置的指示点
- 可以通过点击指示点切换图片
- 可配合指示点颜色使用`
        },

        indicatorColor: {
            icon: helpIcon,
            title: '指示点颜色说明',
            content: `设置轮播图底部指示点的默认颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 建议使用半透明颜色
- 默认值：rgba(255, 255, 255, 0.6)
- 需要配合"显示指示点"使用`
        },

        indicatorActiveColor: {
            icon: helpIcon,
            title: '当前指示点颜色说明',
            content: `设置轮播图当前页面指示点的颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 建议使用不透明颜色
- 默认值：#ffffff
- 与指示点颜色搭配使用`
        },

        imageMode: {
            icon: helpIcon,
            title: '图片填充模式说明',
            content: `控制轮播图中图片的显示方式。

选项说明：
- 裁剪填充：保持纵横比缩放图片，使图片填满容器，超出部分被裁剪
- 缩放填充：不保持纵横比缩放图片，拉伸或压缩以填满容器
- 等比缩放：保持纵横比缩放图片，确保图片完整显示，可能有留白

使用建议：
- 图片比例一致时，建议使用"裁剪填充"
- 需要显示完整图片时，使用"等比缩放"
- 特殊场景下可使用"缩放填充"`
        },

        clickable: {
            icon: helpIcon,
            title: '点击功能说明',
            content: `控制轮播图是否可以点击跳转。

功能说明：
- 开启后，点击图片可以跳转到设定的链接
- 关闭后，点击图片无反应
- 不影响轮播和指示点功能

使用场景：
1. 需要跳转到详情页时开启
2. 纯展示用途时可以关闭
3. 配合链接字段使用`
        },

        height: {
            icon: helpIcon,
            title: '轮播图高度说明',
            content: `设置轮播图的显示高度。

说明：
- 支持 rpx/px 单位
- 建议使用 rpx 单位以适配不同屏幕
- 默认值：300rpx
- 建议值：240rpx - 400rpx

注意：高度会影响图片的显示效果，建议配合图片填充模式使用`
        },

        borderRadius: {
            icon: helpIcon,
            title: '圆角说明',
            content: `设置轮播图的圆角大小。

说明：
- 支持 rpx/px 单位
- 设置为 0 时无圆角
- 常用值：8rpx、16rpx
- 设置较大值可实现圆形效果

使用建议：
- 与整体设计风格保持一致
- 建议使用偶数值
- 过大的圆角可能会裁剪图片内容`
        }
    },

    // 标签栏相关帮助内容
    tabBar: {
        fixed: {
            icon: helpIcon,
            title: '固定底部说明',
            content: `控制标签栏是否固定在页面底部。

功能说明：
- 开启后，标签栏会固定在页面底部
- 关闭后，标签栏会随页面滚动
- 建议在主导航中开启此功能`
        },
        border: {
            icon: helpIcon,
            title: '上边框说明',
            content: `控制是否显示标签栏上方的边框线。

功能说明：
- 开启后，会显示一条分隔线
- 关闭后，无分隔线
- 建议在浅色背景下开启`
        },
        safeAreaInsetBottom: {
            icon: helpIcon,
            title: '底部安全区适配说明',
            content: `控制是否适配底部安全区域（如iPhone X等机型）。

功能说明：
- 开启后，会自动适配底部安全区域
- 关闭后，可能会被底部黑条遮挡
- 建议始终保持开启状态`
        },
        backgroundColor: {
            icon: helpIcon,
            title: '背景颜色说明',
            content: `设置标签栏的背景颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 建议使用浅色背景
- 默认值：#ffffff
- 需要与整体设计风格统一`
        },
        iconSize: {
            icon: helpIcon,
            title: '图标大小说明',
            content: `设置标签栏图标的大小。

说明：
- 支持 rpx/px 单位
- 建议使用 rpx 单位以适配不同屏幕
- 默认值：48rpx
- 建议范围：40rpx - 56rpx`
        },
        textSize: {
            icon: helpIcon,
            title: '文字大小说明',
            content: `设置标签栏文字的大小。

说明：
- 支持 rpx/px 单位
- 建议使用 rpx 单位以适配不同屏幕
- 默认值：24rpx
- 建议范围：20rpx - 28rpx`
        },
        activeColor: {
            icon: helpIcon,
            title: '选中颜色说明',
            content: `设置标签栏选中状态的颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 建议使用品牌主色
- 默认值：#1890ff
- 需要与未选中颜色形成对比`
        },
        inactiveColor: {
            icon: helpIcon,
            title: '未选中颜色说明',
            content: `设置标签栏未选中状态的颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 建议使用中性色
- 默认值：#646566
- 需要与选中颜色形成对比`
        },
        spacing: {
            icon: helpIcon,
            title: '图文间距说明',
            content: `设置图标与文字之间的间距。

说明：
- 支持 rpx/px 单位
- 建议使用 rpx 单位以适配不同屏幕
- 默认值：4rpx
- 建议范围：2rpx - 8rpx`
        },
        changeEvent: {
            icon: helpIcon,
            title: '切换事件说明',
            content: `当切换标签时触发的事件。

参数说明：
- index：当前选中的标签索引
- item：当前选中的标签数据对象

使用场景：
- 需要在切换标签时执行特定操作
- 需要记录用户切换行为
- 需要同步其他组件状态`
        },
        clickEvent: {
            icon: helpIcon,
            title: '点击事件说明',
            content: `当点击标签时触发的事件。

参数说明：
- item：当前点击的标签数据对象

使用场景：
- 需要在点击时执行特定操作
- 需要拦截默认跳转行为
- 需要记录用户点击行为`
        }
    },

    // 容器组件相关帮助内容
    container: {
        padding: {
            icon: helpIcon,
            title: '内边距说明',
            content: `设置容器的内边距。

说明：
- 支持 rpx/px 单位
- 支持简写形式，如：16rpx 或 16rpx 32rpx
- 可分别设置四个方向：上右下左
- 建议使用 rpx 单位以适配不同屏幕`
        },
        margin: {
            icon: helpIcon,
            title: '外边距说明',
            content: `设置容器的外边距。

说明：
- 支持 rpx/px 单位
- 支持简写形式，如：16rpx 或 16rpx 32rpx
- 可分别设置四个方向：上右下左
- 建议使用 rpx 单位以适配不同屏幕`
        },
        backgroundColor: {
            icon: helpIcon,
            title: '背景颜色说明',
            content: `设置容器的背景颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 默认值：#ffffff
- 建议与整体设计风格统一
- 可以设置透明度来创建叠加效果`
        },
        borderRadius: {
            icon: helpIcon,
            title: '圆角说明',
            content: `设置容器的圆角大小。

说明：
- 支持 rpx/px 单位
- 支持简写形式，如：8rpx
- 可分别设置四个角：左上 右上 右下 左下
- 建议使用偶数值，如：8rpx、16rpx
- 过大的圆角可能会影响内容显示`
        },
        shadow: {
            icon: helpIcon,
            title: '阴影效果说明',
            content: `控制是否显示容器阴影。

说明：
- 开启后会添加轻微的阴影效果
- 用于提升视觉层次感
- 建议在浅色背景下使用
- 可以配合边框一起使用增强立体感`
        },
        border: {
            icon: helpIcon,
            title: '边框显示说明',
            content: `控制是否显示容器边框。

说明：
- 开启后会显示1px的边框
- 可以自定义边框颜色
- 用于区分不同内容区域
- 建议搭配圆角使用`
        },
        borderColor: {
            icon: helpIcon,
            title: '边框颜色说明',
            content: `设置容器边框的颜色。

说明：
- 支持 RGB、RGBA、HEX 格式
- 默认值：#ebedf0
- 建议使用浅色系
- 仅在开启边框时生效`
        },
        height: {
            icon: helpIcon,
            title: '容器高度说明',
            content: `设置容器的高度。

说明：
- 支持 rpx/px 单位
- 支持 auto 自适应高度
- 默认值：300px
- 建议根据内容多少调整
- 可以使用百分比值`
        },
        clickEvent: {
            icon: helpIcon,
            title: '点击事件说明',
            content: `当点击容器时触发的事件。

参数说明：
- event：点击事件对象

使用场景：
- 需要响应容器的点击操作
- 需要触发特定的交互效果
- 需要统计点击行为
- 可以用于展开/收起内容`
        }
    }
}