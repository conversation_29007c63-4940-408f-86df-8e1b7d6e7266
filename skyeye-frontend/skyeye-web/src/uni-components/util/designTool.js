/**
 * 解析 XML 字符串为 JSON 对象
 * @param {string} xmlString XML 字符串
 * @returns {Object} JSON 对象
 */
export function parseXML(xmlString) {
    // 简单的 XML 解析实现
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml')

    function xmlToJson(node) {
        const obj = {}

        if (node.nodeType === 1) {
            if (node.attributes.length > 0) {
                obj['@attributes'] = {}
                for (let i = 0; i < node.attributes.length; i++) {
                    const attribute = node.attributes[i]
                    obj['@attributes'][attribute.nodeName] = attribute.nodeValue
                }
            }
        } else if (node.nodeType === 3) {
            return node.nodeValue
        }

        if (node.hasChildNodes()) {
            for (let i = 0; i < node.childNodes.length; i++) {
                const item = node.childNodes[i]
                const nodeName = item.nodeName

                if (typeof obj[nodeName] === 'undefined') {
                    obj[nodeName] = xmlToJson(item)
                } else {
                    if (typeof obj[nodeName].push === 'undefined') {
                        const old = obj[nodeName]
                        obj[nodeName] = []
                        obj[nodeName].push(old)
                    }
                    obj[nodeName].push(xmlToJson(item))
                }
            }
        }

        return obj
    }

    return xmlToJson(xmlDoc)
}

// 服务列表配置
export const serviceList = [
    {
        id: 'default',
        name: '默认服务',
        baseUrl: '/api',
        description: '系统默认服务'
    },
    {
        id: 'user',
        name: '用户服务',
        baseUrl: '/api/user',
        description: '用户相关接口服务'
    },
    {
        id: 'content',
        name: '内容服务',
        baseUrl: '/api/content',
        description: '内容管理相关接口服务'
    },
    {
        id: 'custom',
        name: '自定义服务',
        baseUrl: '',
        description: '自定义服务配置',
        isCustom: true
    }
]

import { markRaw, h } from 'vue'
import {
    PictureOutlined,
    AppstoreOutlined,
    FormOutlined,
    TableOutlined,
    MenuOutlined,
    ContainerOutlined
    // ... 可以继续添加其他图标
} from '@ant-design/icons-vue'

// 导入组件和配置
import SkSwiper from '../SkSwiper/index.vue'
import swiperConfig from '../SkSwiper/config.js'
import SkGrid from '../SkGrid/index.vue'
import gridConfig from '../SkGrid/config.js'
import SkTabBar from '../SkTabBar/index.vue'
import tabBarConfig from '../SkTabBar/config.js'
import SkContainer from '../SkContainer/index.vue'
import containerConfig from '../SkContainer/config.js'
// ... 可以继续添加其他组件

// 组件注册配置
export const designComponents = {
    'sk-swiper': markRaw(SkSwiper),
    'sk-grid': markRaw(SkGrid),
    'sk-tabbar': markRaw(SkTabBar),
    'sk-container': markRaw(SkContainer)
    // ... 可以继续添加其他组件
}

// 组件配置映射
export const designConfigs = {
    'sk-swiper': swiperConfig,
    'sk-grid': gridConfig,
    'sk-tabbar': tabBarConfig,
    'sk-container': containerConfig
    // ... 可以继续添加其他组件配置
}

// 组件分类数据
export const componentCategories = [
    {
        key: 'all',
        name: '全部组件',
        components: [
            {
                type: 'sk-swiper',
                name: '轮播图',
                icon: markRaw(PictureOutlined),
                category: 'basic'
            },
            {
                type: 'sk-grid',
                name: '宫格导航',
                icon: markRaw(AppstoreOutlined),
                category: 'basic'
            },
            {
                type: 'sk-tabbar',
                name: '标签栏',
                icon: markRaw(MenuOutlined),
                category: 'navigation'
            },
            {
                type: 'sk-container',
                name: '容器',
                icon: markRaw(ContainerOutlined),
                category: 'layout'
            }
        ]
    }
]

// 获取服务配置
export const getServiceConfig = (serviceId) => {
    return serviceList.find(service => service.id === serviceId)
}

// 修改单位转换函数
export const rpxToPx = (rpx) => {
    if (typeof rpx === 'string') {
        if (rpx.includes('rpx')) {
            const value = parseInt(rpx.replace('rpx', ''))
            return `${value / 2}px`
        }
        if (rpx.includes('px')) {
            return rpx
        }
    }
    return `${rpx}px`
}

export const getFileData = async (isDesign, url, _proxy, type, config) => {
    if (url == null || url == '') {
        throw new Error('文件路径不能为空')
    }
    if (isDesign) {
        if (type === 'json' || type === 'xml') {
            const response = await _proxy.$http.get(url)
            return response.data
        } else {
            const response = await _proxy.$http.request({ url: url, ...config })
            return response.data
        }
    } else {
        if (type === 'json' || type === 'xml') {
            const response = await this.$http.get(url)
            return response.data
        } else {
            const response = await this.$http.send(url, config.method, config.data, config)
            return response.data
        }
    }
}

// 处理数据映射
export const mapData = (data, mapping) => {
    if (Array.isArray(data)) {
        return data.map(item => ({
            image: item[mapping.image || 'image'],
            url: item[mapping.url || 'url'],
            title: item[mapping.title || 'title']
        }))
    }
    return []
}

// 加载数据
export const loadDataByType = async (props, proxy) => {
    let internalList = []
    try {
        const { type, data, url, fileUrl, method, headers, params, dataPath, transform, fieldMapping } = props.dataSource

        // 根据类型使用不同的数据地址
        const dataUrl = ['json', 'xml'].includes(type) ? (fileUrl[0] || '') : url

        switch (type) {
            case 'static':
                internalList = data
                break
            case 'api':
                const response = await getFileData(props.isDesign, dataUrl, proxy, type, {
                    method,
                    header: headers,
                    data: params
                })
                let apiData = response
                if (dataPath) {
                    apiData = dataPath.split('.').reduce((obj, key) => obj?.[key], apiData)
                }
                if (transform) {
                    apiData = eval(transform)(apiData)
                }
                internalList = mapData(apiData, fieldMapping)
                break

            case 'json':
                const jsonResponse = await getFileData(props.isDesign, dataUrl, proxy)
                let jsonData = jsonResponse
                if (dataPath) {
                    jsonData = dataPath.split('.').reduce((obj, key) => obj?.[key], jsonData)
                }
                if (transform) {
                    jsonData = eval(transform)(jsonData)
                }
                internalList = mapData(jsonData, fieldMapping)
                break

            case 'xml':
                const xmlResponse = await getFileData(props.isDesign, dataUrl, proxy)
                const xmlData = parseXML(xmlResponse)
                let parsedData = xmlData
                if (dataPath) {
                    parsedData = dataPath.split('.').reduce((obj, key) => obj?.[key], xmlData)
                }
                if (transform) {
                    parsedData = eval(transform)(parsedData)
                }
                internalList = mapData(parsedData, fieldMapping)
                break
        }
    } catch (error) {
        internalList = []
    }
    return internalList
}