import { helpContent } from '../util/helpContent'

export default {
    props: {
        // 布局配置
        padding: {
            type: 'string',
            label: '内边距',
            defaultValue: '16rpx',
            help: helpContent.container.padding
        },
        margin: {
            type: 'string',
            label: '外边距',
            defaultValue: '0',
            help: helpContent.container.margin
        },
        backgroundColor: {
            type: 'color',
            label: '背景颜色',
            defaultValue: '#ffffff',
            help: helpContent.container.backgroundColor
        },
        borderRadius: {
            type: 'string',
            label: '圆角',
            defaultValue: '0',
            help: helpContent.container.borderRadius
        },
        shadow: {
            type: 'boolean',
            label: '显示阴影',
            defaultValue: false,
            help: helpContent.container.shadow
        },
        border: {
            type: 'boolean',
            label: '显示边框',
            defaultValue: false,
            help: helpContent.container.border
        },
        borderColor: {
            type: 'color',
            label: '边框颜色',
            defaultValue: '#ebedf0',
            showOn: (values) => values.border,
            help: helpContent.container.borderColor
        },
        height: {
            type: 'string',
            label: '容器高度',
            defaultValue: '300px',
            help: helpContent.container.height
        }
    },

    // 组件事件配置
    events: {
        click: {
            label: '点击事件',
            params: ['event'],
            help: helpContent.container.clickEvent
        }
    },

    // 组件设计器配置
    designer: {
        // 组件分类
        category: 'layout',
        // 组件图标
        icon: 'ContainerOutlined',
        // 组件标题
        title: '容器',
        // 组件描述
        description: '用于包裹和布局其他组件的容器组件',
        // 文档链接
        docUrl: '/docs/components/container',
        // 是否可以包含其他组件
        isContainer: true,  // 添加这个标记
        // 组件预览配置
        preview: {
            // 预览数据
            props: {
                padding: '16rpx',
                backgroundColor: '#ffffff',
                height: '200rpx'
            }
        }
    }
} 