<template>
    <view class="sk-container" :class="{
        'sk-container--shadow': shadow,
        'sk-container--border': border,
        'is-design': isDesign
    }" :style="containerStyle" @tap="handleClick">
        <!-- 设计器预览 -->
        <view class="sk-container-preview" v-if="isDesign">
            <view class="preview-header">
                <text>容器预览</text>
            </view>
        </view>

        <!-- 容器主体 -->
        <view class="container-body" @dragover.prevent="handleDragOver" @drop.prevent="handleDrop"
            @dragleave.prevent="handleDragLeave">
            <view class="container-content">
                <!-- 子组件列表 -->
                <template v-if="children && children.length">
                    <div v-for="(child, index) in children" :key="index" class="container-child">
                        <component :is="getComponent(child.type)" v-bind="child.props" :is-design="isDesign" />
                    </div>
                </template>

                <!-- 空状态提示 -->
                <view v-else-if="isDesign" class="container-empty">
                    <view class="empty-content">
                        <text class="empty-text">拖拽组件到这里</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { designComponents } from '../util/designTool'

export default defineComponent({
    name: 'sk-container',

    props: {
        // 是否为设计模式
        isDesign: {
            type: Boolean,
            default: false
        },
        // 内边距
        padding: {
            type: String,
            default: '16rpx'
        },
        // 外边距
        margin: {
            type: String,
            default: '0'
        },
        // 背景颜色
        backgroundColor: {
            type: String,
            default: '#ffffff'
        },
        // 圆角
        borderRadius: {
            type: String,
            default: '0'
        },
        // 阴影
        shadow: {
            type: Boolean,
            default: false
        },
        // 边框
        border: {
            type: Boolean,
            default: false
        },
        // 边框颜色
        borderColor: {
            type: String,
            default: '#ebedf0'
        },
        // 容器高度
        height: {
            type: String,
            default: 'auto'
        },
        children: {
            type: Array,
            default: () => []
        }
    },

    emits: ['click', 'drop'],

    setup(props, { emit }) {
        // 计算容器样式
        const containerStyle = computed(() => ({
            padding: props.padding,
            margin: props.margin,
            backgroundColor: props.backgroundColor,
            borderRadius: props.borderRadius,
            borderColor: props.border ? props.borderColor : 'transparent',
            height: props.height === 'auto' ? 'auto' : props.height
        }))

        const getComponent = (type) => {
            if (!type) return null
            return designComponents[type.toLowerCase()]
        }

        const handleDragOver = (event) => {
            event.preventDefault()
            if (props.isDesign) {
                event.currentTarget.classList.add('drag-over')
            }
        }

        const handleDragLeave = (event) => {
            event.preventDefault()
            if (props.isDesign) {
                event.currentTarget.classList.remove('drag-over')
            }
        }

        const handleDrop = (event) => {
            event.preventDefault()
            if (props.isDesign) {
                event.currentTarget.classList.remove('drag-over')
                emit('drop', event)
            }
        }

        return {
            containerStyle,
            getComponent,
            handleDragOver,
            handleDragLeave,
            handleDrop
        }
    }
})
</script>

<style lang="scss">
:root {
    --sk-container-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --sk-container-border-width: 1px;
    --sk-container-preview-bg: #f5f7fa;
    --sk-container-empty-bg: #fafafa;
}

.sk-container {
    width: 100%;
    display: block;
    position: relative;
    box-sizing: border-box;
    border: var(--sk-container-border-width) solid transparent;
    transition: all 0.3s ease;
    overflow: hidden;

    &--shadow {
        box-shadow: var(--sk-container-shadow);
    }

    &--border {
        border-style: solid;
        border-width: var(--sk-container-border-width);
    }

    .sk-container-preview {
        background: var(--sk-container-preview-bg);
        padding: 12px 16px;
        margin-bottom: 12px;
        border-radius: 6px;

        .preview-header {
            font-size: 14px;
            color: #666;
        }
    }

    .container-body {
        width: 100%;
        min-height: 100px;
        position: relative;
        overflow: hidden;

        .container-content {
            padding: 8px;
            min-height: inherit;

            .container-child {
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .container-empty {
        width: 100%;
        min-height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--sk-container-empty-bg);
        border: 2px dashed #eee;
        border-radius: 4px;

        .empty-content {
            text-align: center;
            color: #999;
            font-size: 14px;
            padding: 16px;
        }
    }

    &.is-design {
        &:hover {
            border-color: #1890ff;
        }

        &.active {
            border-color: #1890ff;

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                background: rgba(24, 144, 255, 0.1);
                pointer-events: none;
            }
        }

        .container-body {
            &.drag-over {
                background-color: rgba(24, 144, 255, 0.1);
                border: 2px dashed #1890ff;

                .container-content {
                    pointer-events: none;
                }
            }
        }
    }
}
</style>