/*
 * refresh_env.c - 刷新系统环境变量的小工具
 * 编译命令: gcc refresh_env.c -o refresh_env.exe -luser32
 * 或使用 cl refresh_env.c user32.lib (Visual Studio)
 */

#include <windows.h>
#include <stdio.h>

int main() {
    DWORD_PTR result;
    LRESULT ret;
    
    printf("正在广播WM_SETTINGCHANGE消息...\n");
    
    // 发送WM_SETTINGCHANGE消息到所有顶级窗口
    ret = SendMessageTimeout(
        HWND_BROADCAST,          // 广播到所有窗口
        WM_SETTINGCHANGE,        // 设置更改消息
        0,                       // wParam = 0
        (LPARAM)"Environment",   // lParam = "Environment"
        SMTO_ABORTIFHUNG,        // 如果窗口挂起则中止
        5000,                    // 5秒超时
        &result                  // 返回结果
    );
    
    if (ret == 0) {
        printf("错误: SendMessageTimeout失败 (错误代码: %lu)\n", GetLastError());
        return 1;
    } else {
        printf("成功: 环境变量刷新信号已发送\n");
        return 0;
    }
}
