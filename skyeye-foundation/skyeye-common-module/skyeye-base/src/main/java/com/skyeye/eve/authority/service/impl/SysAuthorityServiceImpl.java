/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.authority.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.skyeye.cache.redis.RedisCache;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.constans.CacheConstants;
import com.skyeye.common.constans.RedisConstants;
import com.skyeye.common.constans.SysUserAuthConstants;
import com.skyeye.common.util.ToolUtil;
import com.skyeye.eve.authority.rest.ISysRoleServiceRest;
import com.skyeye.eve.authority.service.SysAuthorityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: SysAuthorityServiceImpl
 * @Description:
 * @author: skyeye云系列--卫志强
 * @date: 2022/1/15 13:09
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class SysAuthorityServiceImpl implements SysAuthorityService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysRoleServiceRest iSysRoleServiceRest;

    /**
     * 根据角色ID(逗号隔开的字符串)获取该角色拥有的菜单权限点列表
     *
     * @param roleIds         角色id(逗号隔开的字符串)
     * @param userTokenUserId userTokenUserId
     * @return 该角色拥有的菜单权限点列表
     * @throws Exception
     */
    @Override
    public List<Map<String, Object>> getRoleHasMenuPointListByRoleIds(String roleIds, String userTokenUserId) {
        List<Map<String, Object>> menuPointResult = new ArrayList<>();
        if (ToolUtil.isBlank(roleIds)) {
            return menuPointResult;
        }
        String[] roleIdList = roleIds.split(",");

        for (String roleId : roleIdList) {
            if (!ToolUtil.isBlank(roleId)) {
                List<Map<String, Object>> roleMenuPoint;
                if (userTokenUserId.lastIndexOf(SysUserAuthConstants.APP_IDENTIFYING) < 0) {
                    // PC端
                    roleMenuPoint = this.getRoleHasMenuPointListByRoleId(roleId);
                } else {
                    // 手机端
                    roleMenuPoint = this.getRoleHasAPPMenuPointListByRoleId(roleId);
                }
                if (!CollectionUtils.isEmpty(roleMenuPoint)) {
                    // 删除authPoints里面的createId和createTime
                    for (Map<String, Object> authPoint : roleMenuPoint) {
                        authPoint.remove("createId");
                        authPoint.remove("createTime");
                        authPoint.remove("lastUpdateId");
                        authPoint.remove("lastUpdateTime");
                        authPoint.remove("serviceClassName");
                    }
                    menuPointResult.addAll(roleMenuPoint);
                }
            }
        }
        // 去重
        menuPointResult = menuPointResult.stream().collect(
            Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(m -> m.get("id").toString()))), ArrayList::new));
        // 转成树结构
        menuPointResult = ToolUtil.listToTree(menuPointResult, "id", "parentId", "children");
        return menuPointResult;
    }

    /**
     * 根据角色ID获取该角色拥有的菜单权限点列表
     *
     * @param roleId 角色id
     * @return 该角色拥有的菜单权限点列表
     * @throws Exception
     */
    private List<Map<String, Object>> getRoleHasMenuPointListByRoleId(String roleId) {
        String cacheKey = String.format("%s:%s", CacheConstants.SYS_ROLE_CACHE_KEY, roleId);
        Map<String, Object> role = redisCache.getMap(cacheKey, key -> {
            return ExecuteFeignClient.get(() -> iSysRoleServiceRest.querySysRoleById(roleId)).getBean();
        }, RedisConstants.ALL_USE_TIME);
        List<Map<String, Object>> authNum = (List<Map<String, Object>>) role.get("pcAuthNum");
        if (CollectionUtil.isNotEmpty(authNum)) {
            return authNum;
        }
        return new ArrayList<>();
    }

    /**
     * 根据角色ID获取该角色拥有的菜单权限点列表(手机端)
     *
     * @param roleId 角色id
     * @return 该角色拥有的菜单权限点列表
     * @throws Exception
     */
    private List<Map<String, Object>> getRoleHasAPPMenuPointListByRoleId(String roleId) {
        String cacheKey = String.format("%s:%s", CacheConstants.SYS_ROLE_CACHE_KEY, roleId);
        Map<String, Object> role = redisCache.getMap(cacheKey, key -> {
            return ExecuteFeignClient.get(() -> iSysRoleServiceRest.querySysRoleById(roleId)).getBean();
        }, RedisConstants.ALL_USE_TIME);
        List<Map<String, Object>> authNum = (List<Map<String, Object>>) role.get("appAuthNum");
        if (CollectionUtil.isNotEmpty(authNum)) {
            return authNum;
        }
        return new ArrayList<>();
    }

}
