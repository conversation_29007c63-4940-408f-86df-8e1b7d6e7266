/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.servicebean.service.impl;

import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.eve.servicebean.rest.ISkyeyeServiceBeanRest;
import com.skyeye.eve.servicebean.service.ISkyeyeServiceBeanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @ClassName: ISkyeyeServiceBeanServiceImpl
 * @Description: 所有实现了SkyeyeBusinessService的服务类管理的服务类
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/18 16:41
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class ISkyeyeServiceBeanServiceImpl implements ISkyeyeServiceBeanService {

    @Autowired
    private ISkyeyeServiceBeanRest iSkyeyeServiceBeanRest;

    @Override
    public Map<String, Object> queryServiceBeanCustom(String appId, String className) {
        return ExecuteFeignClient.get(() -> iSkyeyeServiceBeanRest.queryServiceBeanCustom(appId, className)).getBean();
    }
}
