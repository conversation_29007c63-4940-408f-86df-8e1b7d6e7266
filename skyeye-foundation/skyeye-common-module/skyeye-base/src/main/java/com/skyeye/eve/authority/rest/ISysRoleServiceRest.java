/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.authority.rest;

import com.skyeye.common.client.ClientConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: ISysRoleServiceRest
 * @Description: 角色管理
 * @author: skyeye云系列--卫志强
 * @date: 2024/3/22 14:15
 * @Copyright: 2024 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-pro}", configuration = ClientConfiguration.class)
public interface ISysRoleServiceRest {

    /**
     * 根据id查询角色信息
     *
     * @param id 角色id
     * @return
     */
    @GetMapping("/querySysRoleById")
    String querySysRoleById(@RequestParam("id") String id);

}
