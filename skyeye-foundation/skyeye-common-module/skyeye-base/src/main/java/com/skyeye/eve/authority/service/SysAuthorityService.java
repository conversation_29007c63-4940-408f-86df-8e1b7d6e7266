/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.authority.service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: SysAuthorityService
 * @Description: 权限服务接口类
 * @author: skyeye云系列--卫志强
 * @date: 2022/1/15 13:08
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface SysAuthorityService {

    /**
     * 根据角色ID(逗号隔开的字符串)获取该角色拥有的菜单权限点列表
     *
     * @param roleIds         角色id(逗号隔开的字符串)
     * @param userTokenUserId userTokenUserId
     * @return 该角色拥有的菜单权限点列表
     * @throws Exception
     */
    List<Map<String, Object>> getRoleHasMenuPointListByRoleIds(String roleIds, String userTokenUserId);

}
