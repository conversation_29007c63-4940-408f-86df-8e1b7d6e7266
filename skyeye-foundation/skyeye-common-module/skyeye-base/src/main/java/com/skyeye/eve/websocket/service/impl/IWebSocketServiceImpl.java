/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.websocket.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.skyeye.eve.websocket.rest.IWebSocketServiceRest;
import com.skyeye.eve.websocket.service.IWebSocketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: IWebSocketServiceImpl
 * @Description: websocket服务实现类
 * @author: skyeye云系列--卫志强
 * @date: 2025/1/11 20:46
 * @Copyright: 2025 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class IWebSocketServiceImpl implements IWebSocketService {

    @Autowired
    private IWebSocketServiceRest iWebSocketServiceRest;

    @Override
    public void sendWebSocketMsgToUser(List<String> userIds, String msg, Integer messageType) {
        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }
        if (StrUtil.isEmpty(msg)) {
            return;
        }
        if (messageType == null) {
            // 默认为系统消息
            messageType = 5;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("userIdList", JSONUtil.toJsonStr(userIds));
        param.put("msg", msg);
        param.put("messageType", messageType);
        iWebSocketServiceRest.sendWebSocketMsgToUser(param);
    }

    @Override
    public void sendWebSocketMsgToAll(String msg, Integer messageType) {
        if (StrUtil.isEmpty(msg)) {
            return;
        }
        if (messageType == null) {
            // 默认为系统消息
            messageType = 5;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("msg", msg);
        param.put("messageType", messageType);
        iWebSocketServiceRest.sendWebSocketMsgToAll(param);
    }
}
