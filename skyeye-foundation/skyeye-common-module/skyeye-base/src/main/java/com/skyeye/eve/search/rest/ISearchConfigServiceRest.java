/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.search.rest;

import com.skyeye.common.client.ClientConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: ISearchConfigServiceRest
 * @Description: 公共的分页信息的rest接口，webroot.skyeye-pro配置项来自skyeye-common-rest工程
 * @author: skyeye云系列--卫志强
 * @date: 2022/6/29 22:25
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-pro}", configuration = ClientConfiguration.class)
public interface ISearchConfigServiceRest {

    /**
     * 根据urlId以及appId获取高级查询的参数配置信息----用于前台使用
     *
     * @param urlId urlId
     * @param appId appId
     * @return 高级查询的参数配置信息
     */
    @PostMapping("/querySearchParamsConfigToHtml")
    String querySearchParamsConfigToHtml(@RequestParam("urlId") String urlId, @RequestParam("appId") String appId);

    /**
     * 根据urlId以及appId获取高级查询的参数配置信息----用于后台使用
     *
     * @param urlId urlId
     * @param appId appId
     * @return 高级查询的参数配置信息
     */
    @PostMapping("/querySearchParamsConfig")
    String querySearchParamsConfig(@RequestParam("urlId") String urlId, @RequestParam("appId") String appId);

}
