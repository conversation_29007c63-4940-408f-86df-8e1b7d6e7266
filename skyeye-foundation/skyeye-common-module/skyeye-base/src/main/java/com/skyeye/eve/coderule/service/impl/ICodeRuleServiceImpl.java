/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.coderule.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.constans.CommonCharConstants;
import com.skyeye.common.constans.CommonNumConstants;
import com.skyeye.eve.coderule.entity.CodeMation;
import com.skyeye.eve.coderule.rest.ICodeRuleServiceRest;
import com.skyeye.eve.coderule.service.ICodeRuleService;
import com.skyeye.eve.servicebean.service.ISkyeyeServiceBeanService;
import com.skyeye.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ICodeRuleServiceImpl
 * @Description: 编码规则公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/16 10:09
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class ICodeRuleServiceImpl implements ICodeRuleService {

    private static final String ERD_SIMPLE_CODE = "SkyeyeSimpleCode";

    @Autowired
    private ICodeRuleServiceRest iCodeRuleServiceRest;

    @Autowired
    private ISkyeyeServiceBeanService iSkyeyeServiceBeanService;

    @Value("${skyeye.appid}")
    private String appId;

    /**
     * 获取简单编码
     * 编码规则 "Skyeye-"yyyynnnnnnnn
     *
     * @return 编码
     */
    @Override
    public String getCodeSimple() {
        return getNextCode(ERD_SIMPLE_CODE, new HashMap<>(0));
    }

    /**
     * 获取简单编码
     * 编码规则 "Skyeye-"yyyynnnnnnnn
     *
     * @param size 获取数量 最多一次获取5000个
     * @return List<String>
     */
    @Override
    public List<String> getCodeSimple(int size) {
        return getNextCodes(ERD_SIMPLE_CODE, new HashMap<>(0), size);
    }

    /**
     * 获取下一个编码
     *
     * @param ruleCode     命名规则编码
     * @param businessData 业务数据
     * @return String
     */
    @Override
    public String getNextCode(String ruleCode, Map<String, Object> businessData) {
        return getNextCodes(ruleCode, businessData, CommonNumConstants.NUM_ONE).get(0);
    }

    /**
     * 批量获取编码
     *
     * @param ruleCode     命名规则编码
     * @param businessData 业务数据
     * @param size         获取数量 最多一次获取5000个
     * @return List<String>
     */
    @Override
    public List<String> getNextCodes(String ruleCode, Map<String, Object> businessData, int size) {
        CodeMation codeMation = new CodeMation();
        codeMation.setRuleCode(ruleCode);
        codeMation.setBusinessData(businessData);
        codeMation.setSize(size);
        Map<String, Object> bean = ExecuteFeignClient.get(() -> iCodeRuleServiceRest.getNextCodes(codeMation)).getBean();
        return (List<String>) bean.get("list");
    }

    /**
     * 获取下一个编码
     *
     * @param className
     * @param businessData
     * @return
     */
    @Override
    public String getNextCodeByClassName(String className, Map<String, Object> businessData) {
        return this.getNextCodeByClassName(className, businessData, CommonNumConstants.NUM_ONE).get(0);
    }

    /**
     * 批量获取编码
     *
     * @param className
     * @param businessData
     * @param size
     * @return
     */
    @Override
    public List<String> getNextCodeByClassName(String className, Map<String, Object> businessData, int size) {
        if (className.indexOf(CommonCharConstants.QUESTION_TWO_DOLLAR) >= 0) {
            className = className.split(CommonCharConstants.QUESTION_TWO_DOLLAR_REG)[0];
        }
        Map<String, Object> serviceMation = iSkyeyeServiceBeanService.queryServiceBeanCustom(appId, className);
        if (CollectionUtils.isEmpty(serviceMation)) {
            throw new CustomException("未找到该服务的编码配置");
        }
        // 获取编码配置信息
        Map<String, Object> codeRule = (Map<String, Object>) serviceMation.get("codeRule");
        if (CollectionUtil.isNotEmpty(codeRule)) {
            return this.getNextCodes(codeRule.get("codeNum").toString(), businessData, size);
        } else {
            throw new CustomException("未配置编码规则");
        }
    }
}
