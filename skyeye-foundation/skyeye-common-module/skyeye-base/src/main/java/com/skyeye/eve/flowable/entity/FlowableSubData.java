/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.flowable.entity;

import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: FlowableSubData
 * @Description: 启动流程的入参信息实体类
 * @author: skyeye云系列--卫志强
 * @date: 2022/10/24 15:58
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@ApiModel("启动流程的入参信息实体类")
public class FlowableSubData implements Serializable {

    @ApiModelProperty(value = "表单的提交类型，可参考#FormSubType枚举类", required = "required,num")
    private Integer formSubType;

    @ApiModelProperty(value = "审批人id", required = "required")
    private String approvalId;

    @ApiModelProperty(value = "业务数据的id", required = "required")
    private String objectId;

    @ApiModelProperty(value = "业务数据的serviceClassName", required = "required")
    private String objectKey;

    @ApiModelProperty(value = "应用id", required = "required")
    private String appId;

}
