/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.websocket.rest;

import com.skyeye.common.client.ClientConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * @ClassName: IWebSocketServiceRest
 * @Description: webSocket服务接口
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/16 10:09
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-pro}", configuration = ClientConfiguration.class)
public interface IWebSocketServiceRest {

    /**
     * 发送websocket消息给指定用户
     *
     * @param params
     * @return
     */
    @PostMapping("/sendWebSocketMsgToUser")
    String sendWebSocketMsgToUser(Map<String, Object> params);

    /**
     * 发送websocket消息给所有用户
     *
     * @param params
     * @return
     */
    @PostMapping("/sendWebSocketMsgToAll")
    String sendWebSocketMsgToAll(Map<String, Object> params);

}
