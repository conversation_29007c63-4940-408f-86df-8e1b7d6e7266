/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.flowable.rest;

import com.skyeye.common.client.ClientConfiguration;
import com.skyeye.eve.flowable.entity.FlowableSubData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: IFlowableRest
 * @Description: 工作流公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/11/19 17:26
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-flowable}", configuration = ClientConfiguration.class)
public interface IFlowableRest {

    /**
     * 启动流程
     *
     * @param flowableSubData
     * @return
     */
    @PostMapping("/startProcess")
    String startProcess(FlowableSubData flowableSubData);

    /**
     * 撤销流程
     *
     * @param processInstanceId 流程实例id
     * @return
     */
    @PostMapping("/revokeProcess")
    String revokeProcess(@RequestParam("processInstanceId") String processInstanceId);

}
