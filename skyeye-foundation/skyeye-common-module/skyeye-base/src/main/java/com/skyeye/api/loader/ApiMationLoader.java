/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.api.loader;

import com.skyeye.annotation.api.Api;
import com.skyeye.annotation.api.ApiImplicitParam;
import com.skyeye.annotation.api.ApiImplicitParams;
import com.skyeye.annotation.api.ApiOperation;
import com.skyeye.annotation.api.entity.ApiMation;
import com.skyeye.annotation.api.entity.ApiProperty;
import com.skyeye.common.constans.ApiConstants;
import com.skyeye.common.util.ToolUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ApiMationLoader
 * @Description: api读取加载器
 * @author: skyeye云系列--卫志强
 * @date: 2022/1/22 20:22
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public class ApiMationLoader {

    private ApiModelLoader apiModelLoader;

    public ApiMationLoader(ApiModelLoader apiModelLoader) {
        this.apiModelLoader = apiModelLoader;
    }

    public void apiLoader(Class<?> clazz, List<String> apiIds, Map<String, Map<String, ApiProperty>> modelMation, String proPrefix) {
        if (judgeIsController(clazz)) {
            // 获取API分组信息
            Map<String, String> apiGroupMation = this.loadApiModel(clazz);
            loadApiList(clazz, apiGroupMation, apiIds, modelMation, proPrefix);
        }
    }

    private boolean judgeIsController(Class<?> clazz) {
        Api apiAnno = clazz.getAnnotation(Api.class);
        Controller anno = clazz.getAnnotation(Controller.class);
        if (anno != null && apiAnno != null) {
            return true;
        }
        RestController restAnno = clazz.getAnnotation(RestController.class);
        if (restAnno != null && apiAnno != null) {
            return true;
        }
        return false;
    }

    private Map<String, String> loadApiModel(Class<?> clazz) {
        Api api = clazz.getAnnotation(Api.class);
        Map<String, String> apiGroupMation = new HashMap<>();
        apiGroupMation.put("modelName", api.modelName());
        apiGroupMation.put("groupName", api.value());
        return apiGroupMation;
    }

    private void loadApiList(Class<?> clazz, Map<String, String> apiGroupMation,
                             List<String> apiIds, Map<String, Map<String, ApiProperty>> modelMation, String proPrefix) {
        // 获取当前类中已添加要扫描注解的方法
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            if (apiOperation != null && !ToolUtil.isBlank(apiOperation.id())) {
                // 获取api接口信息
                ApiMation apiMation = loadApiDetails(apiOperation, method, apiGroupMation);
                // 获取api入参信息
                ApiImplicitParams apiImplicitParams = method.getAnnotation(ApiImplicitParams.class);
                List<ApiProperty> apiProperty = getRequestParams(apiImplicitParams, modelMation, proPrefix);
                apiMation.setList(apiProperty);
                ApiConstants.API_CACHE_MAP.put(apiOperation.id(), apiMation);
                apiIds.add(apiOperation.id());
            }
        }
    }

    private ApiMation loadApiDetails(ApiOperation apiOperation, Method method, Map<String, String> apiGroupMation) {
        ApiMation apiMation = new ApiMation();
        apiMation.setId(apiOperation.id());
        // 工程模块
        apiMation.setModelName(apiGroupMation.get("modelName"));
        // 是否需要登录才能使用 1是 0否 2需要登陆才能访问，但无需授权 默认为否
        apiMation.setAllUse(apiOperation.allUse());
        // 请求地址
        RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
        apiMation.setPath(requestMapping.value()[0]);
        // 请求方式
        apiMation.setMethod(apiOperation.method());
        // API描述
        apiMation.setVal(apiOperation.value());
        // API分组
        apiMation.setGroupName(apiGroupMation.get("groupName"));
        return apiMation;
    }

    private List<ApiProperty> getRequestParams(ApiImplicitParams apiImplicitParams,
                                               Map<String, Map<String, ApiProperty>> modelMation, String proPrefix) {
        List<ApiProperty> apiPropertyList = new ArrayList<>();
        if (apiImplicitParams == null) {
            return apiPropertyList;
        }
        // 防止有入参相同的，用map作为临时容器
        Map<String, ApiProperty> value = new HashMap<>();
        // 如果是Void说明没有配置实体类
        if (!apiImplicitParams.classBean().getName().equals("java.lang.Void")) {
            value.putAll(this.getClassProperty(apiImplicitParams.classBean(), modelMation, proPrefix));
        }
        int i = value.size() + 1;
        for (ApiImplicitParam apiImplicitParam : apiImplicitParams.value()) {
            String id = apiImplicitParam.id();
            if (ToolUtil.isBlank(id)) {
                continue;
            }
            ApiProperty apiProperty = new ApiProperty();
            apiProperty.setNumber(i);
            // 前端传递的key
            apiProperty.setId(id);
            // 后端接收的key
            apiProperty.setName(apiImplicitParam.name());
            // 参数要求：require、num等
            apiProperty.setRef(apiImplicitParam.required());
            // 参数描述
            apiProperty.setVar(apiImplicitParam.value());
            // 示例默认值
            apiProperty.setExampleDefault(apiImplicitParam.exampleDefault());
            // 默认值
            apiProperty.setDefaultValue(apiImplicitParam.defaultValue());
            // 属性对应的枚举类
            if (!apiImplicitParam.enumClass().getName().equals("java.lang.Void")) {
                String enumClassName = apiImplicitParam.enumClass().getName();
                apiProperty.setEnumClassStr(proPrefix + "#" + enumClassName);
            }
            i++;
            value.put(id, apiProperty);
        }
        loadApiImplicitParamsClassBean(apiPropertyList, value);
        return apiPropertyList;
    }

    private Map<String, ApiProperty> getClassProperty(Class<?> clazz, Map<String, Map<String, ApiProperty>> modelMation, String proPrefix) {
        Map<String, ApiProperty> value = new HashMap<>();
        if (clazz == null || clazz.getName().equals("java.lang.Object")) {
            return value;
        }
        // 装载实体bean
        apiModelLoader.apiLoader(clazz, modelMation, "init", proPrefix);
        String key = clazz.getName();
        value.putAll(modelMation.get(key));
        Map<String, ApiProperty> supperValue = this.getClassProperty(clazz.getSuperclass(), modelMation, proPrefix);
        value.putAll(supperValue);
        return value;
    }

    private void loadApiImplicitParamsClassBean(List<ApiProperty> apiPropertyList, Map<String, ApiProperty> value) {
        for (Map.Entry<String, ApiProperty> entry : value.entrySet()) {
            ApiProperty bean = new ApiProperty();
            BeanUtils.copyProperties(entry.getValue(), bean);
            apiPropertyList.add(bean);
        }
    }

}
