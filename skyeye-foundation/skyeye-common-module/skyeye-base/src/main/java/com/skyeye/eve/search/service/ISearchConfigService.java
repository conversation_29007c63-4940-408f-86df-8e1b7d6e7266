/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.search.service;

import java.util.Map;

/**
 * @ClassName: ISearchConfigService
 * @Description: 公共的分页信息的服务接口
 * @author: skyeye云系列--卫志强
 * @date: 2022/6/29 22:25
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface ISearchConfigService {

    /**
     * 根据urlId以及appId获取高级查询的参数配置信息----用于前台使用
     *
     * @param urlId urlId
     * @param appId appId
     * @return 高级查询的参数配置信息
     */
    Map<String, Object> querySearchParamsConfigToHtml(String urlId, String appId);

    /**
     * 根据urlId以及appId获取高级查询的参数配置信息----用于后台使用
     *
     * @param urlId urlId
     * @param appId appId
     * @return 高级查询的参数配置信息
     */
    Map<String, Object> querySearchParamsConfig(String urlId, String appId);

    /**
     * 根据urlId以及appId获取缓存在redis中的key
     *
     * @param urlId urlId
     * @param appId appId
     * @return 缓存在redis中的key
     */
    String querySearchParamsConfigToHtmlCacheKeyById(String urlId, String appId);

    /**
     * 根据字典ID获取缓存在redis中的key
     *
     * @param urlId urlId
     * @param appId appId
     * @return 缓存在redis中的key
     */
    String querySearchParamsConfigCacheKeyById(String urlId, String appId);
}
