/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.search.service.impl;

import com.skyeye.cache.redis.RedisCache;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.constans.RedisConstants;
import com.skyeye.eve.search.rest.ISearchConfigServiceRest;
import com.skyeye.eve.search.service.ISearchConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * @ClassName: ISearchConfigServiceImpl
 * @Description: 公共的分页信息的rest接口，webroot.skyeye-pro配置项来自skyeye-common-rest工程
 * @author: skyeye云系列--卫志强
 * @date: 2022/6/29 22:25
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class ISearchConfigServiceImpl implements ISearchConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ISearchConfigServiceImpl.class);

    @Autowired
    private ISearchConfigServiceRest iSearchConfigServiceRest;

    @Autowired
    private RedisCache redisCache;

    /**
     * 根据urlId以及appId获取高级查询的参数配置信息----用于前台使用
     *
     * @param urlId urlId
     * @param appId appId
     * @return 高级查询的参数配置信息
     */
    @Override
    public Map<String, Object> querySearchParamsConfigToHtml(String urlId, String appId) {
        String cacheKey = this.querySearchParamsConfigToHtmlCacheKeyById(urlId, appId);
        return redisCache.getMap(cacheKey, key -> {
            LOGGER.info("querySearchParamsConfigToHtml rest api. urlId is {}. appId is {}.", urlId, appId);
            Map<String, Object> bean = ExecuteFeignClient.get(() -> iSearchConfigServiceRest.querySearchParamsConfigToHtml(urlId, appId)).getBean();
            if (CollectionUtils.isEmpty(bean)) {
                return new HashMap<>();
            }
            return bean;
        }, RedisConstants.ALL_USE_TIME);
    }

    /**
     * 根据urlId以及appId获取高级查询的参数配置信息----用于后台使用
     *
     * @param urlId urlId
     * @param appId appId
     * @return 高级查询的参数配置信息
     */
    @Override
    public Map<String, Object> querySearchParamsConfig(String urlId, String appId) {
        String cacheKey = this.querySearchParamsConfigCacheKeyById(urlId, appId);
        return redisCache.getMap(cacheKey, key -> {
            LOGGER.info("querySearchParamsConfig rest api. urlId is {}. appId is {}.", urlId, appId);
            Map<String, Object> bean = ExecuteFeignClient.get(() -> iSearchConfigServiceRest.querySearchParamsConfig(urlId, appId)).getBean();
            if (CollectionUtils.isEmpty(bean)) {
                return new HashMap<>();
            }
            return bean;
        }, RedisConstants.ALL_USE_TIME);
    }

    /**
     * 根据urlId以及appId获取缓存在redis中的key
     *
     * @param urlId urlId
     * @param appId appId
     * @return 缓存在redis中的key
     */
    @Override
    public String querySearchParamsConfigToHtmlCacheKeyById(String urlId, String appId) {
        return String.format(Locale.ROOT, "searchParamsConfigToHtml:%s:%s", appId, urlId);
    }

    /**
     * 根据字典ID获取缓存在redis中的key
     *
     * @param urlId urlId
     * @param appId appId
     * @return 缓存在redis中的key
     */
    @Override
    public String querySearchParamsConfigCacheKeyById(String urlId, String appId) {
        return String.format(Locale.ROOT, "searchParamsConfig:%s:%s", appId, urlId);
    }

}
