/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.flowable.service.impl;

import cn.hutool.core.util.StrUtil;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.eve.flowable.classenum.FormSubType;
import com.skyeye.eve.flowable.entity.FlowableSubData;
import com.skyeye.eve.flowable.rest.IFlowableRest;
import com.skyeye.eve.flowable.service.IFlowableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @ClassName: IFlowableServiceImpl
 * @Description: 工作流公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/11/19 17:25
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class IFlowableServiceImpl implements IFlowableService {

    @Autowired
    private IFlowableRest iFlowableRest;

    /**
     * 启动流程
     *
     * @param flowableSubData
     * @return 流程实例id
     */
    @Override
    public String startProcess(FlowableSubData flowableSubData) {
        Map<String, Object> result = ExecuteFeignClient.get(() -> iFlowableRest.startProcess(flowableSubData)).getBean();
        if (flowableSubData.getFormSubType().equals(FormSubType.SUB_FLOWABLE.getKey())) {
            return result.get("processInstanceId").toString();
        }
        return StrUtil.EMPTY;
    }

    /**
     * 撤销流程
     *
     * @param processInstanceId 流程实例id
     */
    @Override
    public void revokeProcess(String processInstanceId) {
        ExecuteFeignClient.get(() -> iFlowableRest.revokeProcess(processInstanceId));
    }
}
