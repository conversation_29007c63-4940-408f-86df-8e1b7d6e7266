/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.servicebean.rest;

import com.skyeye.common.client.ClientConfiguration;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @ClassName: ISkyeyeServiceBeanRest
 * @Description: 所有实现了SkyeyeBusinessService的服务类的注册服务
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/12 0:37
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-pro}", configuration = ClientConfiguration.class)
public interface ISkyeyeServiceBeanRest {

    /**
     * 批量注册服务类
     *
     * @param params 入参
     * @return
     */
    @PostMapping("/registerServiceBean")
    String registerServiceBean(@RequestBody Map<String, Object> params, @RequestHeader(required = false, name = "options") Request.Options options);

    /**
     * 获取服务类详情信息
     *
     * @param className 业务对象的className
     * @return
     */
    @GetMapping("/queryServiceBeanCustom")
    String queryServiceBeanCustom(@RequestParam("appId") String appId, @RequestParam("className") String className);

}
