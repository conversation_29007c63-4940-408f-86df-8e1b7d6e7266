/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.search.entity;

import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: SearchParamsConfigMation
 * @Description: 高级查询的参数配置筛选条件实体类
 * @author: skyeye云系列--卫志强
 * @date: 2022/7/12 11:01
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@ApiModel("高级查询的参数配置筛选条件实体类")
public class SearchOperatorMation implements Serializable {

    @ApiModelProperty(value = "筛选条件名称", required = "required")
    private String operatorName;

    @ApiModelProperty(value = "筛选条件", required = "required")
    private String operator;

    @ApiModelProperty(value = "筛选条件对应的MySQL的查询语句")
    private String mySql;

    @ApiModelProperty(value = "筛选条件对应的pgsql的查询语句")
    private String pgSql;

    @ApiModelProperty(value = "筛选条件对应的SqlServer的查询语句")
    private String sqlServer;

    @ApiModelProperty(value = "筛选条件对应的Oracle的查询语句")
    private String oracle;

}
