/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.coderule.service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: ICodeRuleService
 * @Description: 编码规则公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/16 10:09
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface ICodeRuleService {

    /**
     * 获取简单编码
     * 编码规则 "Skyeye-"yyyynnnnnnnn
     *
     * @return 编码
     */
    String getCodeSimple();

    /**
     * 获取简单编码
     * 编码规则 "Skyeye-"yyyynnnnnnnn
     *
     * @param size 获取数量 最多一次获取5000个
     * @return List<String>
     */
    List<String> getCodeSimple(int size);


    /**
     * 获取下一个编码
     *
     * @param ruleCode     命名规则编码
     * @param businessData 业务数据
     * @return String
     */
    String getNextCode(String ruleCode, Map<String, Object> businessData);

    /**
     * 批量获取编码
     *
     * @param ruleCode     命名规则编码
     * @param businessData 业务数据
     * @param size         获取数量 最多一次获取5000个
     * @return List<String>
     */
    List<String> getNextCodes(String ruleCode, Map<String, Object> businessData, int size);

    /**
     * 获取下一个编码
     *
     * @param className
     * @param businessData
     * @return
     */
    String getNextCodeByClassName(String className, Map<String, Object> businessData);

    /**
     * 批量获取编码
     *
     * @param className
     * @param businessData
     * @param size
     * @return
     */
    List<String> getNextCodeByClassName(String className, Map<String, Object> businessData, int size);

}
