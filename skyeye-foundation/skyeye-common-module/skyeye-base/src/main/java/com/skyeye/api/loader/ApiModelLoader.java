/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.api.loader;

import cn.hutool.core.util.ReflectUtil;
import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.annotation.api.entity.ApiProperty;
import com.skyeye.common.util.ClassUtil;
import com.skyeye.common.util.ToolUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ApiModelLoader
 * @Description: 实体类读取加载器
 * @author: skyeye云系列--卫志强
 * @date: 2022/1/22 21:02
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public class ApiModelLoader {

    /**
     * 标识递归的类型
     */
    private static final String RECURSION_TYPE = "recursion_type";

    private static final List<String> LOADED_CLASS_KEY = new ArrayList<>();

    public boolean apiLoader(Class<?> clazz, Map<String, Map<String, ApiProperty>> modelMation, String type, String proPrefix) {
        ApiModel apiModel = clazz.getAnnotation(ApiModel.class);
        if (apiModel != null) {
            String key = clazz.getName();
            firstComeIn(type);
            if (LOADED_CLASS_KEY.contains(key)) {
                return false;
            }
            LOADED_CLASS_KEY.add(key);
            if (!modelMation.containsKey(key)) {
                Field[] fields = ReflectUtil.getFields(clazz);
                Map<String, ApiProperty> value = new HashMap<>();
                modelMation.put(key, value);
                int num = 1;
                for (Field field : fields) {
                    ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                    if (apiModelProperty != null) {
                        // 获取字段的类型
                        Class<?> children = ClassUtil.getFieldType(field);
                        String childrenKey = "";
                        if (children != null) {
                            boolean re = this.apiLoader(children, modelMation, RECURSION_TYPE, proPrefix);
                            if (re) {
                                childrenKey = children.getName();
                            }
                        }
                        getRequestParams(value, apiModelProperty, field, num, childrenKey, modelMation, proPrefix);
                        num++;
                    }
                }
            }
        }
        return true;
    }

    private void firstComeIn(String type) {
        if (!RECURSION_TYPE.equals(type)) {
            // 如果是第一次进来
            LOADED_CLASS_KEY.clear();
        }
    }

    private void getRequestParams(Map<String, ApiProperty> value, ApiModelProperty apiModelProperty, Field field,
                                  int num, String childrenKey, Map<String, Map<String, ApiProperty>> modelMation, String proPrefix) {
        String id = ToolUtil.isBlank(apiModelProperty.id()) ? field.getName() : apiModelProperty.id();
        ApiProperty apiProperty = new ApiProperty();
        apiProperty.setNumber(num);
        // 前端传递的key
        apiProperty.setId(id);
        // 后端接收的key
        apiProperty.setName(field.getName());
        // 参数要求：require、num等
        apiProperty.setRef(apiModelProperty.required());
        // 参数描述
        apiProperty.setVar(apiModelProperty.value());
        // 示例默认值
        apiProperty.setExampleDefault(apiModelProperty.exampleDefault());
        // 默认值
        apiProperty.setDefaultValue(apiModelProperty.defaultValue());
        // 字段类型
        apiProperty.setType(field.getType().getSimpleName());
        // 属性对应的枚举类
        if (!apiModelProperty.enumClass().getName().equals("java.lang.Void")) {
            String enumClassName = apiModelProperty.enumClass().getName();
            apiProperty.setEnumClassStr(proPrefix + "#" + enumClassName);
        }
        if (!ToolUtil.isBlank(childrenKey)) {
            apiProperty.setChildren(modelMation.get(childrenKey));
        }
        value.put(id, apiProperty);
    }

}
