/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.search.entity;

import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.common.enumeration.SearchParamsConfigDataType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: SearchParamsConfigMation
 * @Description: 高级查询的参数配置实体类
 * @author: skyeye云系列--卫志强
 * @date: 2022/7/12 11:01
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@ApiModel("高级查询的参数配置实体类")
public class SearchParamsConfigMation implements Serializable {

    @ApiModelProperty(value = "参数类型", enumClass = SearchParamsConfigDataType.class, required = "required")
    private String dataType;

    @ApiModelProperty(value = "dataType为constantSelect时，需要指定字段值(id)以及展示的字段值(name)，格式为：[{\"id\": \"\", \"name\": \"\"}]")
    private List constantDataFrom;

    @ApiModelProperty(value = "dataType为virtualSelect时，指定服务(service), 需要指定接口地址(url)，请求方式(method)，接口的字段值(valueKey)，展示的字段值(showKey)，参数(params)，格式为：" +
        "{\"service\": \"\", \"url\": \"\", \"method\": \"\", \"valueKey\": \"\", \"showKey\": \"\", \"params\": \"\"}")
    private Map virtualDataFrom;

    @ApiModelProperty(value = "筛选条件", required = "required")
    private List<SearchOperatorMation> searchCondition;

}
