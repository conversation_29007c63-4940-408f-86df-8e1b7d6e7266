/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.websocket.service;

import java.util.List;

/**
 * @ClassName: IWebSocketService
 * @Description:
 * @author: skyeye云系列--卫志强
 * @date: 2025/1/11 20:46
 * @Copyright: 2025 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface IWebSocketService {

    /**
     * 发送WebSocket消息到指定用户
     *
     * @param userIds 用户ID列表
     * @param msg     消息内容
     */
    void sendWebSocketMsgToUser(List<String> userIds, String msg, Integer messageType);

    /**
     * 发送WebSocket消息到所有用户
     *
     * @param msg 消息内容
     */
    void sendWebSocketMsgToAll(String msg, Integer messageType);

}
