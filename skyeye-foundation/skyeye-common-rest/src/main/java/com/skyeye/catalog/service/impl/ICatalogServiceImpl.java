/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.catalog.service.impl;

import com.skyeye.base.rest.service.impl.IServiceImpl;
import com.skyeye.catalog.rest.ICatalogDynamicRest;
import com.skyeye.catalog.service.ICatalogService;
import com.skyeye.common.client.ExecuteFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ICatalogServiceImpl
 * @Description: 目录管理对外提供的服务
 * @author: skyeye云系列--卫志强
 * @date: 2022/7/29 11:49
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class ICatalogServiceImpl extends IServiceImpl implements ICatalogService {

    @Autowired
    private ICatalogDynamicRest iCatalogDynamicRest;


    /**
     * 删除目录下的业务数据
     *
     * @param uri
     * @param className
     * @param ids
     */
    @Override
    public void deleteDataMationByCatalogIds(URI uri, String className, List<String> ids) {
        Map<String, Object> params = new HashMap<>();
        params.put("className", className);
        params.put("ids", ids);
        ExecuteFeignClient.get(() -> iCatalogDynamicRest.deleteDataMationByCatalogIds(uri, params));
    }
}
