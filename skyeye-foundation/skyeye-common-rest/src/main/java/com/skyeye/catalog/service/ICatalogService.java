/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.catalog.service;

import com.skyeye.base.rest.service.IService;

import java.net.URI;
import java.util.List;

/**
 * @ClassName: ICatalogService
 * @Description: 目录管理对外提供的服务接口
 * @author: skyeye云系列--卫志强
 * @date: 2022/7/29 11:49
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface ICatalogService extends IService {

    /**
     * 删除目录下的业务数据
     *
     * @param uri
     * @param className
     * @param ids
     */
    default void deleteDataMationByCatalogIds(URI uri, String className, List<String> ids) {

    }

}
