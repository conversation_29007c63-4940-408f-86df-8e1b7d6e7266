/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.catalog.rest;

import com.skyeye.common.client.ClientConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.net.URI;
import java.util.Map;

/**
 * @ClassName: ICatalogDynamicRest
 * @Description: 目录管理公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/11/19 17:26
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(name = "ICatalogDynamicRest${spring.application.name}", configuration = ClientConfiguration.class, url = "EMPTY")
public interface ICatalogDynamicRest {

    /**
     * 根据目录id批量删除业务数据
     *
     * @param params
     * @return
     */
    @PostMapping("/deleteDataMationByCatalogIds")
    String deleteDataMationByCatalogIds(URI uri, Map<String, Object> params);

}
