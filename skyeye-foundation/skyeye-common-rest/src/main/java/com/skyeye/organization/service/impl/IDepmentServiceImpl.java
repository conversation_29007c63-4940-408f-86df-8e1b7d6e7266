/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.organization.service.impl;

import com.skyeye.base.rest.service.impl.IServiceImpl;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.constans.CacheConstants;
import com.skyeye.organization.rest.IDepmentRest;
import com.skyeye.organization.service.IDepmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * @ClassName: IDepmentServiceImpl
 * @Description: 部门管理公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/11/19 17:26
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
public class IDepmentServiceImpl extends IServiceImpl implements IDepmentService {

    @Autowired
    private IDepmentRest iDepmentRest;

    /**
     * 根据id查询数据信息
     *
     * @param id
     * @return
     */
    @Override
    public Map<String, Object> queryEntityMationById(String id) {
        return queryEntityMationByIds(id).stream().findFirst().orElse(new HashMap<>());
    }

    @Override
    public List<Map<String, Object>> queryEntityMationByIds(String ids) {
        return ExecuteFeignClient.get(() -> iDepmentRest.queryDepartmentListByIds(ids)).getRows();
    }

    /**
     * 根据id获取缓存的key
     *
     * @param id
     * @return
     */
    @Override
    public String queryCacheKeyById(String id) {
        return String.format(Locale.ROOT, "%s:%s", CacheConstants.ORGANIZATION_DEPARTMENT_CACHE_KEY, id);
    }

}
