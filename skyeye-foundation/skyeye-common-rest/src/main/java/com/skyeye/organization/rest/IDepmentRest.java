/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.organization.rest;

import com.skyeye.common.client.ClientConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: IDepmentRest
 * @Description: 部门管理公共的一些操作
 * @author: skyeye云系列--卫志强
 * @date: 2022/11/19 17:26
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-pro}", configuration = ClientConfiguration.class)
public interface IDepmentRest {

    /**
     * 根据id批量获取部门信息
     *
     * @param ids 主键id，多个用逗号隔开
     */
    @PostMapping("/queryDepartmentListByIds")
    String queryDepartmentListByIds(@RequestParam("ids") String ids);

}
