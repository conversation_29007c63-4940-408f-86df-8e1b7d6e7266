/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.listener.service;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.annotation.api.Property;
import com.skyeye.annotation.service.SkyeyeService;
import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.common.base.handler.enclosure.helper.ServiceBeanToEntityHelper;
import com.skyeye.common.base.listener.SkyeyeStartListenerService;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.enumeration.WhetherEnum;
import com.skyeye.common.util.ClassUtil;
import com.skyeye.common.util.PropertiesUtil;
import com.skyeye.common.util.SpringUtils;
import com.skyeye.eve.servicebean.rest.ISkyeyeServiceBeanRest;
import com.skyeye.listener.service.rest.IApplicationRest;
import feign.Request;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: LoadAnnoServiceBeanListener
 * @Description: 扫描并获取 SkyeyeBusinessService 的实现类(bean注册类型的)
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/11 8:30
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Component
public class LoadAnnoServiceBeanListener implements SkyeyeStartListenerService {

    private static Logger LOGGER = LoggerFactory.getLogger(LoadAnnoServiceBeanListener.class);

    @Override
    public void run() throws Exception {
        try {
            LOGGER.info("start loading service bean service.");
            Map<String, SkyeyeBusinessService> serviceBeans = SpringUtils.getApplicationContext().getBeansOfType(SkyeyeBusinessService.class);
            String springApplicationName = PropertiesUtil.getPropertiesValue("${spring.application.name}");
            String env = PropertiesUtil.getPropertiesValue("${spring.profiles.active}");
            String proPrefix = springApplicationName.replace("-" + env, StrUtil.EMPTY);

            List<Map<String, Object>> classNameList = new ArrayList<>();
            Map<String, List<Map<String, Object>>> tempEntityAttrMap = new HashMap<>();
            for (SkyeyeBusinessService bean : serviceBeans.values()) {
                String className = ClassUtil.getClassNameFromBean(bean.getClass().getName());
                LOGGER.info("read {} service class service.", className);
                Map<String, Object> classMation = buildServiceMation(className, bean, tempEntityAttrMap, proPrefix);
                classNameList.add(classMation);
                ServiceBeanToEntityHelper.put(className, bean.getEntityClass().getName());
                ServiceBeanToEntityHelper.putService(className, classMation);
            }
            String appId = PropertiesUtil.getPropertiesValue("${skyeye.appid}");
            Map<String, Object> inputParams = new HashMap<>();
            inputParams.put("springApplicationName", springApplicationName);
            inputParams.put("classNameList", classNameList);
            inputParams.put("appId", appId);

            // 设置60秒超时
            Request.Options options = new Request.Options(60 * 1000, 60 * 1000);
            // 注册服务类
            ISkyeyeServiceBeanRest iSkyeyeServiceBeanRest = SpringUtils.getBean(ISkyeyeServiceBeanRest.class);
            ExecuteFeignClient.get(() -> iSkyeyeServiceBeanRest.registerServiceBean(inputParams, options));
            // 注册应用
            registerApplication(appId, options);

            LOGGER.info("end loading service bean service.");
        } catch (Exception e) {
            LOGGER.error("load service bean service is wrong {}", e);
        }
    }

    private void registerApplication(String appId, Request.Options options) {
        String appName = PropertiesUtil.getPropertiesValue("${skyeye.serviceName}");
        Map<String, Object> application = new HashMap<>();
        application.put("appId", appId);
        application.put("appName", appName);
        IApplicationRest iApplicationRest = SpringUtils.getBean(IApplicationRest.class);
        ExecuteFeignClient.get(() -> iApplicationRest.registerApplication(application, options));
    }

    private Map<String, Object> buildServiceMation(String className, SkyeyeBusinessService bean,
                                                   Map<String, List<Map<String, Object>>> tempEntityAttrMap, String proPrefix) {
        Map<String, Object> classMation = new HashMap<>();
        classMation.put("name", className);
        classMation.put("className", className);
        classMation.put("entityClassName", bean.getEntityClass().getName());
        classMation.put("manageShow", false);
        classMation.put("flowable", false);
        classMation.put("teamAuth", false);
        SkyeyeService skyeyeService = bean.getClass().getAnnotation(SkyeyeService.class);
        if (skyeyeService != null) {
            if (StrUtil.isNotEmpty(skyeyeService.name())) {
                classMation.put("name", skyeyeService.name());
                classMation.put("remark", skyeyeService.name());
            }
            classMation.put("groupName", skyeyeService.groupName());
            classMation.put("tenant", skyeyeService.tenant());
            classMation.put("manageShow", skyeyeService.manageShow());
            classMation.put("flowable", skyeyeService.flowable());
            classMation.put("teamAuth", skyeyeService.teamAuth());
        }
        classMation.put("attrDefinitionList", buildEntityAttr(className, bean.getEntityClass(), tempEntityAttrMap, proPrefix));

        return classMation;
    }

    private List<Map<String, Object>> buildEntityAttr(String className, Class entityClass, Map<String, List<Map<String, Object>>> tempEntityAttrMap, String proPrefix) {
        if (tempEntityAttrMap.containsKey(entityClass.getName())) {
            return tempEntityAttrMap.get(entityClass.getName());
        }

        Map<String, Field> fieldMap = ReflectUtil.getFieldMap(entityClass);
        List<Map<String, Object>> fieldList = new ArrayList<>();
        fieldMap.forEach((key, field) -> {
            ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
            if (apiModelProperty != null) {
                buildAttrMation(className, fieldList, apiModelProperty.value(), apiModelProperty.required(), WhetherEnum.ENABLE_USING.getKey(), field,
                    proPrefix, apiModelProperty.enumClass());
                return;
            }
            Property property = field.getAnnotation(Property.class);
            if (property != null) {
                buildAttrMation(className, fieldList, property.value(), StrUtil.EMPTY, WhetherEnum.DISABLE_USING.getKey(), field,
                    proPrefix, property.enumClass());
            }
        });
        tempEntityAttrMap.put(entityClass.getName(), fieldList);
        return fieldList;
    }

    private void buildAttrMation(String className, List<Map<String, Object>> fieldList, String value,
                                 String required, Integer whetherInputParams, Field field, String proPrefix, Class<?> aClass) {
        Map<String, Object> attrMation = new HashMap<>();
        attrMation.put("className", className);
        attrMation.put("attrKey", field.getName());
        Class<?> children = ClassUtil.getFieldType(field);
        if (children != null) {
            attrMation.put("attrType", children.getName());
        } else {
            attrMation.put("attrType", field.getType().getSimpleName());
        }
        attrMation.put("name", value);
        attrMation.put("remark", value);
        attrMation.put("whetherInputParams", whetherInputParams);
        attrMation.put("required", required);
        attrMation.put("modelAttribute", true);
        if (!aClass.getName().equals("java.lang.Void")) {
            String enumClassName = aClass.getName();
            attrMation.put("enumClassStr", proPrefix + "#" + enumClassName);
        }
        fieldList.add(attrMation);
    }

    @Override
    public Integer getOrder() {
        return 8;
    }
}
