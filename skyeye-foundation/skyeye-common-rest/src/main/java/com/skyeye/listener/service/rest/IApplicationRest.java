/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.listener.service.rest;

import com.skyeye.common.client.ClientConfiguration;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * @ClassName: IApplicationRest
 * @Description: 应用的注册服务
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/12 0:37
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@FeignClient(value = "${webroot.skyeye-pro}", configuration = ClientConfiguration.class)
public interface IApplicationRest {

    /**
     * 批量注册应用
     *
     * @param params 入参
     * @return
     */
    @PostMapping("/registerApplication")
    String registerApplication(@RequestBody Map<String, Object> params, @RequestHeader(required = false, name = "options") Request.Options options);

}
