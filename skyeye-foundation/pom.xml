<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.skyeye</groupId>
        <artifactId>skyeye-root</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>skyeye-foundation</artifactId>
    <packaging>pom</packaging>

    <name>SkyEye Foundation Modules</name>
    <description>SkyEye基础设施模块聚合项目</description>

    <modules>
        <!-- 父pom - 统一依赖版本管理 -->
        <module>skyeye-parent</module>
        
        <!-- 通用基础模块 -->
        <module>skyeye-common-module</module>
        
        <!-- REST通用模块 -->
        <module>skyeye-common-rest</module>
    </modules>

</project>