admin_name=ä»»åèª¿åº¦ä¸­å¿
admin_name_full=åå¸å¼ä»»åèª¿åº¦å¹³èºXXL-JOB
admin_version=2.3.0
admin_i18n=

## system
system_tips=ç³»çµ±æç¤º
system_ok=ç¢ºå®
system_close=éé
system_save=å²å­
system_cancel=åæ¶
system_search=æå°
system_status=çæ
system_opt=æä½
system_please_input=è«è¼¸å¥
system_please_choose=è¯·é¸æ
system_success=æå
system_fail=å¤±æ
system_add_suc=æ°å¢æå
system_add_fail=æ°å¢å¤±æ
system_update_suc=æ´æ°æå
system_update_fail=æ´æ°å¤±æ
system_all=å¨é¨
system_api_error=APIé¯èª¤
system_show=æ¥ç
system_empty=ç¡
system_opt_suc=æä½æå
system_opt_fail=æä½å¤±æ
system_opt_edit=ç·¨è¼¯
system_opt_del=åªé¤
system_opt_copy=å¾©å¶
system_unvalid=éæ³
system_not_found=ä¸å­å¨
system_nav=å°èª
system_digits=æ´æ¸
system_lengh_limit=é·åº¦éå¶
system_permission_limit=æ¬éæ§ç®¡
system_welcome=æ­¡è¿

## daterangepicker
daterangepicker_ranges_recent_hour=æè¿ä¸å°æ
daterangepicker_ranges_today=ä»æ¥
daterangepicker_ranges_yesterday=æ¨æ¥
daterangepicker_ranges_this_month=æ¬æ
daterangepicker_ranges_last_month=ä¸åæ
daterangepicker_ranges_recent_week=æè¿ä¸å¨
daterangepicker_ranges_recent_month=æè¿ä¸æ
daterangepicker_custom_name=èªå®ç¾©
daterangepicker_custom_starttime=èµ·å§æé
daterangepicker_custom_endtime=çµææé
daterangepicker_custom_daysofweek=æ¥,ä¸,äº,ä¸,å,äº,å­
daterangepicker_custom_monthnames=ä¸æ,äºæ,ä¸æ,åæ,äºæ,å­æ,ä¸æ,å«æ,ä¹æ,åæ,åä¸æ,åäºæ

## dataTable
dataTable_sProcessing=èçä¸­...
dataTable_sLengthMenu=æ¯é  _MENU_ æ¢è¨é
dataTable_sZeroRecords=æ²æç¸ç¬¦åè¨é
dataTable_sInfo=ç¬¬ _PAGE_ é  ( ç¸½å± _PAGES_ é ï¼_TOTAL_ æ¢è¨é )
dataTable_sInfoEmpty=ç¡è¨é
dataTable_sInfoFiltered=(ç± _MAX_ é çµæéæ¿¾)
dataTable_sSearch=æå°
dataTable_sEmptyTable=è¡¨ä¸­è³æçºç©º
dataTable_sLoadingRecords=è¼å¥ä¸­...
dataTable_sFirst=é¦é 
dataTable_sPrevious=ä¸é 
dataTable_sNext=ä¸é 
dataTable_sLast=æ«é 
dataTable_sSortAscending=: ä»¥åå¹æåºæ­¤å
dataTable_sSortDescending=: ä»¥éå¹æåºæ­¤å

## login
login_btn=ç»å¥
login_remember_me=è¨ä½å¯ç¢¼
login_username_placeholder=è«è¼¸å¥ç»å¥å¸³è
login_password_placeholder=è«è¼¸å¥ç»å¥å¯ç¢¼
login_username_empty=è«è¼¸å¥ç»å¥å¸³è
login_username_lt_4=ç»å¥å¸³èä¸æä½æ¼4ä½æ¸
login_password_empty=è«è¼¸å¥ç»å¥å¯ç¢¼
login_password_lt_4=ç»å¥å¯ç¢¼ä¸æä½æ¼4ä½æ¸
login_success=ç»å¥æå
login_fail=ç»å¥å¤±æ
login_param_empty=å¸³èæå¯ç¢¼çºç©ºå¼
login_param_unvalid=å¸³èæå¯ç¢¼é¯èª¤

## logout
logout_btn=ç»åº
logout_confirm=ç¢ºèªç»åº?
logout_success=ç»åºæå
logout_fail=ç»åºå¤±æ

## change pwd
change_pwd=ä¿®æ¹å¯ç¢¼
change_pwd_suc_to_logout=ä¿®æ¹å¯ç¢¼æåï¼å³å°ç»åº
change_pwd_field_newpwd=æ°å¯ç¢¼

## dashboard
job_dashboard_name=éè¡å ±è¡¨
job_dashboard_job_num=ä»»åæ¸é
job_dashboard_job_num_tip=èª¿åº¦ä¸­å¿éè¡çä»»åæ¸é
job_dashboard_trigger_num=èª¿åº¦æ¬¡æ¸
job_dashboard_trigger_num_tip=èª¿åº¦ä¸­å¿è§¸ç¼çèª¿åº¦æ¬¡æ¸
job_dashboard_jobgroup_num=å·è¡å¨æ¸é
job_dashboard_jobgroup_num_tip=èª¿åº¦ä¸­å¿å¨ç·çå·è¡å¨æ©å¨æ¸é
job_dashboard_report=èª¿åº¦å ±è¡¨
job_dashboard_report_loaddata_fail=èª¿åº¦å ±è¡¨è³æå è¼ç°å¸¸
job_dashboard_date_report=æ¥æåå¸å
job_dashboard_rate_report=æåæ¯ä¾å

## job info
jobinfo_name=ä»»åç®¡ç
jobinfo_job=ä»»å
jobinfo_field_add=æ°å¢
jobinfo_field_update=æ´æ°ä»»å
jobinfo_field_id=ä»»åID
jobinfo_field_jobgroup=å·è¡å¨
jobinfo_field_jobdesc=ä»»åæè¿°
jobinfo_field_gluetype=éè¡æ¨¡å¼
jobinfo_field_executorparam=ä»»ååæ¸
jobinfo_field_author=è² è²¬äºº
jobinfo_field_timeout=ä»»åè¶æç§æ¸
jobinfo_field_alarmemail=åè­¦éµä»¶
jobinfo_field_alarmemail_placeholder=è¼¸å¥å¤ååè­¦éµä»¶å°åï¼è«ä»¥éèåé
jobinfo_field_executorRouteStrategy=è·¯ç±ç­ç¥
jobinfo_field_childJobId=å­ä»»åID
jobinfo_field_childJobId_placeholder=è¼¸å¥å­ä»»åIDï¼å¦æå¤åè«ä»¥éèåé
jobinfo_field_executorBlockStrategy=é»å¡èçç­ç¥
jobinfo_field_executorFailRetryCount=å¤±æéè©¦æ¬¡æ¸
jobinfo_field_executorFailRetryCount_placeholder=å¤±æéè©¦æ¬¡æ¸ï¼å¤§æ¼é¶æçæ
jobinfo_script_location=è³æ¬ä½ç½®
jobinfo_shard_index=åçåºè
jobinfo_shard_total=åçç¸½æ¸
jobinfo_opt_stop=åæ­¢
jobinfo_opt_start=åå
jobinfo_opt_log=æ¥è©¢æ¥èª
jobinfo_opt_run=å·è¡ä¸æ¬¡
jobinfo_opt_run_tips=è«è¼¸å¥æ¬æ¬¡å·è¡çæ©å¨å°åï¼çºç©ºåå¾å·è¡å¨ç²å
jobinfo_opt_registryinfo=æ³¨åç¯é»
jobinfo_opt_next_time=ä¸æ¬¡å·è¡æé
jobinfo_glue_remark=æºç¢¼åè¨»
jobinfo_glue_remark_limit=æºç¢¼åè¨»é·åº¦éå¶çº4~100
jobinfo_glue_rollback=çæ¬åå¾©
jobinfo_glue_jobid_unvalid=ä»»åIDéæ³
jobinfo_glue_gluetype_unvalid=è©²ä»»åéGLUEæ¨¡å¼
jobinfo_field_executorTimeout_placeholder=ä»»åè¶ææéï¼å®ä½ç§ï¼å¤§æ¼é¶æçæ
schedule_type=èª¿åº¦é¡å
schedule_type_none=ç¡
schedule_type_cron=CRON
schedule_type_fix_rate=åºå®éåº¦
schedule_type_fix_delay=åºå®å»¶é²
schedule_type_none_limit_start=ç¶åèª¿åº¦é¡åç¦æ­¢åå
misfire_strategy=èª¿åº¦éæç­ç¥
misfire_strategy_do_nothing=å¿½ç¥
misfire_strategy_fire_once_now=ç«å³å·è¡å£¹æ¬¡
jobinfo_conf_base=åºç¤éç½®
jobinfo_conf_schedule=èª¿åº¦éç½®
jobinfo_conf_job=ä»»åéç½®
jobinfo_conf_advanced=é«ç´éç½®

## job log
joblog_name=èª¿åº¦æ¥èª
joblog_status=çæ
joblog_status_all=å¨é¨
joblog_status_suc=æå
joblog_status_fail=å¤±æ
joblog_status_running=é²è¡ä¸­
joblog_field_triggerTime=èª¿åº¦æé
joblog_field_triggerCode=èª¿åº¦çµæ
joblog_field_triggerMsg=èª¿åº¦åè¨»
joblog_field_handleTime=å·è¡æé
joblog_field_handleCode=å·è¡ç»æ
joblog_field_handleMsg=å·è¡åè¨»
joblog_field_executorAddress=å·è¡å¨å°å
joblog_clean=æ¸ç
joblog_clean_log=æ¥èªæ¸ç
joblog_clean_type=æ¸çæ¹å¼
joblog_clean_type_1=æ¸çä¸åæä¹åæ¥èªè³æ
joblog_clean_type_2=æ¸çä¸åæä¹åæ¥èªè³æ
joblog_clean_type_3=æ¸çå­åæä¹åæ¥èªè³æ
joblog_clean_type_4=æ¸çä¸å¹´ä¹åæ¥èªè³æ
joblog_clean_type_5=æ¸çä¸åæ¢ä»¥åæ¥èªè³æ
joblog_clean_type_6=æ¸çä¸è¬æ¢ä»¥åæ¥èªè³æ
joblog_clean_type_7=æ¸çä¸è¬æ¢ä»¥åæ¥èªè³æ
joblog_clean_type_8=æ¸çåè¬æ¢ä»¥åæ¥èªè³æ
joblog_clean_type_9=æ¸çæææ¥èªè³æ
joblog_clean_type_unvalid=æ¸çé¡ååæ°ç°å¸¸
joblog_handleCode_200=æå
joblog_handleCode_500=å¤±æ
joblog_handleCode_502=å¤±æ(è¶æ)
joblog_kill_log=ç»æ­¢ä»»å
joblog_kill_log_limit=èª¿åº¦å¤±æï¼ç¡æ³ç»æ­¢æ¥èª
joblog_kill_log_byman=äººçºæä½ï¼ä¸»åçµæ­¢
joblog_lost_fail=ä»»åçµæä¸å¤±ï¼æ¨è¨å¤±æ
joblog_rolling_log=å·è¡æ¥èª
joblog_rolling_log_refresh=æ´æ°
joblog_rolling_log_triggerfail=ä»»åç¼èµ·èª¿åº¦å¤±æï¼ç¡æ³æ¥çå·è¡æ¥èª
joblog_rolling_log_failoften=çµæ­¢è«æ±Rollingæ¥èªï¼è«æ±å¤±ææ¬¡æ¸è¶ä¸éï¼å¯å·æ°é é¢éæ°å è¼æ¥èª
joblog_logid_unvalid=æ¥èªIDéæ³

## job group
jobgroup_name=å·è¡å¨ç®¡ç
jobgroup_list=å·è¡å¨åè¡¨
jobgroup_add=æ°å¢å·è¡å¨
jobgroup_edit=ç·¨è¼¯å·è¡å¨
jobgroup_del=åªé¤å·è¡å¨
jobgroup_field_title=åç¨±
jobgroup_field_addressType=æ³¨åæ¹å¼
jobgroup_field_addressType_0=èªåæ³¨å
jobgroup_field_addressType_1=æåç»é
jobgroup_field_addressType_limit=æåç»éæ³¨åæ¹å¼ï¼æ©å¨å°åä¸å¯çºç©º
jobgroup_field_registryList=æ©å¨å°å
jobgroup_field_registryList_unvalid=æ©å¨å°åæ ¼å¼éæ³
jobgroup_field_registryList_placeholder=è«è¼¸å¥å·è¡å¨å°ååè¡¨ï¼å¤åå°åè«ä»¥éèåé
jobgroup_field_appname_limit=éå¶ä»¥å°å¯«å­æ¯éé ­ï¼ç±å°å¯«å­æ¯ãæ¸å­åä¸­åç·çµæ
jobgroup_field_appname_length=AppNameé·åº¦éå¶çº4~64
jobgroup_field_title_length=åç¨±é·åº¦éå¶çº4~12
jobgroup_field_order_digits=è«è¼¸å¥æ´æ¸
jobgroup_field_orderrange=åå¼ç¯åçº1~1000
jobgroup_del_limit_0=æçµåªé¤ï¼è©²å·è¡å¨ä½¿ç¨ä¸­
jobgroup_del_limit_1=æçµå é¤ï¼ç³»ç»è³å°ä¿çä¸åå·è¡å¨
jobgroup_empty=ä¸å­å¨ææå·è¡å¨ï¼è«è¯çµ¡ç³»çµ±ç®¡çå¡

## job conf
jobconf_block_SERIAL_EXECUTION=å®æ©ä¸²è¡
jobconf_block_DISCARD_LATER=ä¸¢æ£åçºèª¿åº¦
jobconf_block_COVER_EARLY=è¦èä¹åèª¿åº¦
jobconf_route_first=ç¬¬ä¸å
jobconf_route_last=æå¾ä¸å
jobconf_route_round=è¼ªè©¢
jobconf_route_random=é¨æ©
jobconf_route_consistenthash=ä¸è´æ§HASH
jobconf_route_lfu=æä¸ç¶å¸¸ä½¿ç¨
jobconf_route_lru=æè¿æä¹æªä½¿ç¨
jobconf_route_failover=æéè½ç§»
jobconf_route_busyover=å¿ç¢è½ç§»
jobconf_route_shard=åçå»£æ­
jobconf_idleBeat=ç©ºéæª¢æ¸¬
jobconf_beat=å¿è·³æª¢æ¸¬
jobconf_monitor=ä»»åèª¿åº¦ä¸­å¿ç£æ§åè­¦
jobconf_monitor_detail=ç£æ§åè­¦æç»
jobconf_monitor_alarm_title=åè­¦é¡å
jobconf_monitor_alarm_type=èª¿åº¦å¤±æ
jobconf_monitor_alarm_content=åè­¦åå®¹
jobconf_trigger_admin_adress=èª¿åº¦æ©å¨
jobconf_trigger_exe_regtype=å·è¡å¨-æ³¨åæ¹å¼
jobconf_trigger_exe_regaddress=å·è¡å¨-å°ååè¡¨
jobconf_trigger_address_empty=èª¿åº¦å¤±æï¼å·è¡å¨å°åçºç©º
jobconf_trigger_run=è§¸ç¼èª¿åº¦
jobconf_trigger_child_run=è§¸ç¼å­ä»»å
jobconf_callback_child_msg1={0}/{1} [ä»»åID={2}], è§¸ç¼{3}, è§¸ç¼åè¨»: {4} <br>
jobconf_callback_child_msg2={0}/{1} [ä»»åID={2}], è§¸ç¼å¤±è´¥, è§¸ç¼åè¨»: ä»»åIDæ ¼å¼é¯èª¤ <br>
jobconf_trigger_type=ä»»åè§¸ç¼é¡å
jobconf_trigger_type_cron=Cronè§¸ç¼
jobconf_trigger_type_manual=æåè§¸ç¼
jobconf_trigger_type_parent=ç¶ä»»åè§¸ç¼
jobconf_trigger_type_api=APIè§¸ç¼
jobconf_trigger_type_retry=å¤±æéè©¦è§¸ç¼
jobconf_trigger_type_misfire=èª¿åº¦éæè£å

## user
user_manage=ç¨æ·ç®¡ç
user_username=å¸³è
user_password=å¯ç¢¼
user_role=è§è²
user_role_admin=ç®¡çå¡
user_role_normal=æ®éç¨æ¶
user_permission=æ¬é
user_add=æ°å¢ç¨æ¶
user_update=æ´æ°ç¨æ¶
user_username_repeat=å¸³èéè¤
user_username_valid=éå¶ä»¥å°å¯«å­æ¯éé ­ï¼ç±å°å¯«å­æ¯ãæ¸å­çµæ
user_password_update_placeholder=è«è¼¸å¥æ°å¯ç¢¼ï¼çºç©ºåä¸æ´æ°å¯ç¢¼
user_update_loginuser_limit=ç¦æ­¢æä½ç¶åç»å¥å¸³è

## help
job_help=ä½¿ç¨æç¨
job_help_document=å®æ¹æä»¶