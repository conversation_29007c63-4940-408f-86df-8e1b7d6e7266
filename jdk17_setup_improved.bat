@echo off
setlocal enabledelayedexpansion

REM =============================================================================
REM JDK17环境变量系统级设置脚本 - 改进版
REM 版本: 5.0 - 解决PATH重复添加问题，应用KISS/DRY/SOLID原则
REM 用途: 设置JDK17系统级环境变量，避免重复添加PATH
REM 说明: 将此脚本放在JDK根目录下，以管理员身份运行
REM 改进: 统一路径清理逻辑，确保无重复添加
REM =============================================================================

echo.
echo ========================================
echo JDK17环境变量系统级设置脚本 - 改进版
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 此脚本需要管理员权限才能设置系统级环境变量
    echo [INFO] 请右键点击脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo [INFO] 管理员权限验证通过

REM 获取当前脚本所在目录作为JAVA_HOME
set "JAVA_HOME=%~dp0"
if "%JAVA_HOME:~-1%"=="\" set "JAVA_HOME=%JAVA_HOME:~0,-1%"

echo [INFO] 检测到JDK路径: %JAVA_HOME%

REM 验证JDK路径
if not exist "%JAVA_HOME%\bin\java.exe" (
    echo [ERROR] 当前目录不是有效的JDK安装目录
    echo [ERROR] 请确保脚本放在JDK根目录下执行
    pause
    exit /b 1
)

REM 获取Java版本并验证是否为JDK17+
echo [INFO] 正在验证Java版本...
for /f "tokens=3" %%i in ('"%JAVA_HOME%\bin\java.exe" -version 2^>^&1 ^| findstr "version"') do (
    set "JAVA_VERSION=%%i"
    set "JAVA_VERSION=!JAVA_VERSION:"=!"
)
echo [INFO] Java版本: %JAVA_VERSION%

echo.
echo [INFO] 准备设置以下系统级环境变量 (基于JDK17最佳实践):
echo   JAVA_HOME = %JAVA_HOME%
echo   PATH      = 清理现有Java路径后，添加 %%JAVA_HOME%%\bin
echo.
echo [INFO] 注意: JDK17不再需要设置JRE_HOME和CLASSPATH
echo [INFO] 改进: 自动清理重复的Java路径，确保无重复添加
echo.

set /p CONFIRM="是否继续设置系统级环境变量? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo [INFO] 操作已取消
    pause
    exit /b 0
)

echo.
echo [INFO] 开始设置系统级环境变量...

REM 设置JAVA_HOME
echo [INFO] 设置JAVA_HOME...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v JAVA_HOME /t REG_SZ /d "%JAVA_HOME%" /f >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 设置JAVA_HOME失败
    goto :error
) else (
    echo [SUCCESS] JAVA_HOME已设置
)

REM 改进的PATH处理逻辑 - 应用KISS和DRY原则
echo [INFO] 处理PATH环境变量...

REM 获取当前系统PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "CURRENT_PATH=%%b"

if not defined CURRENT_PATH (
    echo [ERROR] 无法读取当前系统PATH
    goto :error
)

REM 统一清理所有可能的Java路径变体（DRY原则）
echo [INFO] 清理现有Java路径...
call :CleanJavaFromPath "!CURRENT_PATH!" CLEANED_PATH

REM 检查清理结果
if "!CLEANED_PATH!"=="!CURRENT_PATH!" (
    echo [INFO] 未发现现有Java路径
) else (
    echo [INFO] 已清理现有Java路径
)

REM 添加新的Java路径（单一职责）
echo [INFO] 添加 %%JAVA_HOME%%\bin 到PATH开头...
set "NEW_PATH=%%JAVA_HOME%%\bin;!CLEANED_PATH!"

REM 更新系统PATH
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "!NEW_PATH!" /f >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 更新系统PATH失败
    goto :error
) else (
    echo [SUCCESS] PATH已更新，%%JAVA_HOME%%\bin 已添加到开头
)

REM 通知系统环境变量已更改
echo [INFO] 通知系统更新环境变量...
powershell -Command "[Environment]::SetEnvironmentVariable('JAVA_HOME', '%JAVA_HOME%', 'Machine')" >nul 2>&1

echo.
echo [SUCCESS] JDK17系统级环境变量设置完成！
echo.
echo [INFO] 已设置的系统级环境变量:
echo   JAVA_HOME = %JAVA_HOME%
echo   PATH      = %%JAVA_HOME%%\bin 已添加到开头（已清理重复项）
echo.

REM 验证Java环境（使用临时环境变量，不影响系统设置）
echo [INFO] 验证Java环境...
set "TEMP_PATH=%JAVA_HOME%\bin;%PATH%"
echo [INFO] 验证Java版本:
"%JAVA_HOME%\bin\java.exe" -version
echo.
echo [INFO] 验证javac版本:
"%JAVA_HOME%\bin\javac.exe" -version
echo.

echo.
echo [SUCCESS] 设置完成！新的命令提示符窗口将自动应用这些环境变量
echo [INFO] 无需重启系统
echo [INFO] 改进特性: 已自动清理重复的Java路径，避免PATH污染
echo.
echo 按任意键退出...
pause >nul
exit /b 0

REM =============================================================================
REM 功能函数：清理PATH中所有Java相关路径（单一职责原则）
REM 参数：%1=原始PATH, %2=返回变量名
REM 应用原则：KISS（简单分割检查）、DRY（统一清理逻辑）
REM =============================================================================
:CleanJavaFromPath
setlocal enabledelayedexpansion
set "INPUT_PATH=%~1"
set "RESULT_PATH="
set "ITEM_COUNT=0"
set "CLEANED_COUNT=0"

REM 分割PATH并逐个检查（KISS原则）
for %%i in ("!INPUT_PATH:;=" "!") do (
    set /a ITEM_COUNT+=1
    set "CURRENT_ITEM=%%~i"
    set "SKIP_ITEM=0"
    
    REM 跳过空项
    if "!CURRENT_ITEM!"=="" set "SKIP_ITEM=1"
    
    REM 检查是否为Java相关路径（统一检查逻辑）
    if !SKIP_ITEM! equ 0 (
        echo !CURRENT_ITEM! | findstr /i "\\bin\\java" >nul && set "SKIP_ITEM=1" && set /a CLEANED_COUNT+=1
        echo !CURRENT_ITEM! | findstr /i "\\jdk" >nul && set "SKIP_ITEM=1" && set /a CLEANED_COUNT+=1
        echo !CURRENT_ITEM! | findstr /i "\\jre" >nul && set "SKIP_ITEM=1" && set /a CLEANED_COUNT+=1
        echo !CURRENT_ITEM! | findstr /i "java_home" >nul && set "SKIP_ITEM=1" && set /a CLEANED_COUNT+=1
        echo !CURRENT_ITEM! | findstr /i "%%JAVA_HOME%%" >nul && set "SKIP_ITEM=1" && set /a CLEANED_COUNT+=1
    )
    
    REM 如果不是Java路径，添加到结果中
    if !SKIP_ITEM! equ 0 (
        if defined RESULT_PATH (
            set "RESULT_PATH=!RESULT_PATH!;!CURRENT_ITEM!"
        ) else (
            set "RESULT_PATH=!CURRENT_ITEM!"
        )
    )
)

REM 输出清理统计信息
if !CLEANED_COUNT! gtr 0 (
    echo [INFO] 已清理 !CLEANED_COUNT! 个Java相关路径项
)

endlocal & set "%~2=%RESULT_PATH%"
exit /b 0

:error
echo.
echo [ERROR] 设置环境变量时发生错误
echo [INFO] 请检查是否以管理员身份运行此脚本
echo.
echo 按任意键退出...
pause >nul
exit /b 1
