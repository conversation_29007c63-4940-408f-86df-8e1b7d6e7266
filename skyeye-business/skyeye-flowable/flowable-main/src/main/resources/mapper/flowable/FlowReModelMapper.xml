<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.activiti.mapper.FlowReModelDao">

    <select id="getModelByIds" resultType="org.flowable.engine.impl.persistence.entity.ModelEntityImpl">
        SELECT
            a.ID_ id,
            a.NAME_ name,
            a.VERSION_ version,
            a.DEPLOYMENT_ID_ deploymentId
        FROM
            act_re_model a
        <where>
            <foreach collection="ids" item="id" separator="," open=" AND a.ID_ in(" close=")">
                #{id}
            </foreach>
        </where>
    </select>

</mapper>
