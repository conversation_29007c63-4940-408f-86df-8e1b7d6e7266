/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.activiti.listener;

import com.alibaba.fastjson.JSON;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * @ClassName: MultiInstanceloopListener
 * @Description:
 * @author: skyeye云系列--卫志强
 * @date: 2021/12/14 22:37
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public class MultiInstanceloopListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        System.out.println(JSON.toJSONString(execution));
    }

}
