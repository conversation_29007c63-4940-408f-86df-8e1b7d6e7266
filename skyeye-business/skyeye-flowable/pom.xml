<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.skyeye</groupId>
        <artifactId>skyeye-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>com.skyeye</groupId>
    <artifactId>skyeye-flowable</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>flowable-common</module>
        <module>flowable-web</module>
        <module>flowable-main</module>
        <module>flowable-entity</module>
    </modules>

    <properties>
        <flowable.version>6.4.2</flowable.version>
    </properties>

    <dependencies>

        <!-- flowable 核心包 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <!-- flowable json转换包 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-json-converter</artifactId>
            <version>${flowable.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-bpmn-converter</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <!-- flowable 设计器接口包 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-ui-modeler-rest</artifactId>
            <version>${flowable.version}</version>
        </dependency>

    </dependencies>

</project>