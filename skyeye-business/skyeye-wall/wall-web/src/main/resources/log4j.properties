### set log levelsè¿éçINFO,<PERSON>dout,D,Eå¯ä»¥çè§£ä¸ºåéï¼ä¹å¯ä»¥è¯´æ¯è¾åºå¹³å°ï¼å¨ä¸é¢æä»¬å¯ä»¥çå° ###  
log4j.rootLogger = INFO, C, D, E

### consoleæ§å¶å°è¾åº ###  
log4j.appender.C = org.apache.log4j.ConsoleAppender  
### System.outä¹å°±æ¯è¾åº outè¾åºæ¯é»è²å­ä½ï¼errè¾åºçå­ä½æ¯çº¢è² ###
log4j.appender.C.Target = System.out  
### layoutæ¯æå¸å±ï¼ä¹å°±æ¯è¯´è¾åºæ¥å¿ä¿¡æ¯çæ ¼å¼æ ·å¼ï¼å¨è¿éæä»¬ä½¿ç¨çæ¯log4jæä¾ç ###
log4j.appender.C.layout = org.apache.log4j.PatternLayout 
### è¿éå°±æ¯æå®æä»¬æ¥å¿æä»¶ä»¥åªä¸ç§æ ¼å¼å»è¾åº ### 
log4j.appender.C.layout.ConversionPattern = [skyeye-promote][%p] [%-d{yyyy-MM-dd HH:mm:ss}] %C.%M(%L) | %m%n  


### log file INFOçº§å«è¾åºæ¥å¿æä»¶ ###  
log4j.appender.D = org.apache.log4j.DailyRollingFileAppender  
### æå®æ¥å¿è¾åºä½ç½® ###
log4j.appender.D.File = ../logs/skyeye.log  
### è¿ä¸ªçæææ¯ææ¯è¿½å è¿æ¯è¦ç é»è®¤æ¯ true  trueæ¯è¿½å  falseæ¯è¦ç ###
log4j.appender.D.Append = true  
### è¿ä¸ªæ¯ææ¥å¿è¾åºççº§å«å¨è¿éæå®çæ¯ INFOçº§å« ###
log4j.appender.D.Threshold = INFO   
### layoutæ¯æå¸å±ï¼ä¹å°±æ¯è¯´è¾åºæ¥å¿ä¿¡æ¯çæ ¼å¼æ ·å¼ï¼å¨è¿éæä»¬ä½¿ç¨çæ¯log4jæä¾ç ###
log4j.appender.D.layout = org.apache.log4j.PatternLayout  
### è¿éå°±æ¯æå®æä»¬æ¥å¿æä»¶ä»¥åªä¸ç§æ ¼å¼å»è¾åº ### 
log4j.appender.D.layout.ConversionPattern = [skyeye-promote][%p] [%-d{yyyy-MM-dd HH:mm:ss}] %C.%M(%L) | %m%n  


### exception ERRORçº§å«è¾åºæ¥å¿æä»¶ ###  
#è¿ä¸ªè·ä¸é¢ä¸æ · åªä¸è¿æ¯æ¥å¿çº§å«æ¯ ERRORçº§çï¼æ¹ä¾¿æä»¬ç´æ¥æ¥çç³»ç»å¼å¸¸ä¿¡æ¯
log4j.appender.E = org.apache.log4j.DailyRollingFileAppender
log4j.appender.E.File = ../logs/skyeye_error.log
log4j.appender.E.Append = true
log4j.appender.E.Threshold = ERROR
log4j.appender.E.layout = org.apache.log4j.PatternLayout
log4j.appender.E.layout.ConversionPattern = [skyeye-promote][%p] [%-d{yyyy-MM-dd HH:mm:ss}] %C.%M(%L) | %m%n  

#localhostæ¥å¿æä»¶è¾åºçº§å«ä¸ºINFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].level = INFO
#localhostæ¥å¿æä»¶è¾åºå¤çç±»2localhost.org.apache.juli.FileHandler
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].handlers = 2localhost.org.apache.juli.FileHandler

#manageræ¥å¿æä»¶è¾åºçº§å«ä¸ºINFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].level = INFO
#manageræ¥å¿æä»¶è¾åºå¤çç±»3manager.org.apache.juli.FileHandler
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].handlers = 3manager.org.apache.juli.FileHandler

#host-manageræ¥å¿æä»¶è¾åºçº§å«ä¸ºINFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].level = INFO
#host-manageræ¥å¿æä»¶è¾åºå¤çç±»4host-manager.org.apache.juli.FileHandler
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].handlers = 4host-manager.org.apache.juli.FileHandler

#è®¾ç½®ååçè¾åºçº§å«
log4j.logger.com.skyeye.common.filter=info, database
# è®°å½æ¥å¿è³æ°æ®åº
# è¿éå®ä¹äºæ°æ®æº
log4j.appender.database=org.apache.log4j.jdbc.JDBCAppender
log4j.appender.database.driver=com.mysql.jdbc.Driver
# BufferSizeå°±æ¯æ¯æ¬¡ç¼å­å¤å°æ¡æ°æ®ç¶åæå¥æ°æ®åºï¼ä¸ºäºæ¼ç¤ºè¿éè®¾ç½®ä¸º1
log4j.appender.database.BufferSize=1
# æ°æ®åºè¿æ¥æ± 
# è®¾ç½®è¦å°æ¥å¿æå¥å°æ°æ®åºçé©±å¨
log4j.appender.database.Threshold=info
log4j.appender.database.URL=${jdbc.database.path}
log4j.appender.database.user=${jdbc.database.username}
log4j.appender.database.password=${jdbc.database.password}
# çåå­ä¹è¯¥æç½è¿éæ¯å®ä¹Sqlè¯­å¥çå¦
log4j.appender.database.sql=insert into sys_work_log (id, class, mothod, create_time, log_level, log_line, message, user_name, file_name, real_path, req_ip) values (REPLACE(UUID(), '-', ''), '%C', '%M', '%d{yyyy-MM-dd HH:mm:ss}', '%p', '%l', '%m', '%X{userName}', '%F', '%X{realPath}', '%X{ip}')
log4j.appender.database.layout=org.apache.log4j.PatternLayout


