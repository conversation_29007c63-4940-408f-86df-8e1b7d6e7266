package com.skyeye.videotag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.annotation.cache.RedisCacheField;
import com.skyeye.annotation.unique.UniqueField;
import com.skyeye.common.constans.RedisConstants;
import com.skyeye.common.entity.features.OperatorUserInfo;
import com.skyeye.common.enumeration.EnableEnum;
import lombok.Data;

/**
 * @ClassName: VideoTag
 * @Description: 视频标签实体类
 * @author: skyeye云系列--lqy
 * @date: 2024/3/9 14:31
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */

@Data
@UniqueField("tagName")
@TableName(value = "wall_video_tag")
@ApiModel(value = "视频标签实体类")
public class VideoTag extends OperatorUserInfo {

    @TableId("id")
    @ApiModelProperty("视频id。为空时新增，不为空时编辑")
    private String id;

    @TableField("tag_name")
    @ApiModelProperty(value = "标签名称", required = "required", fuzzyLike = true)
    private String tagName;

    @TableField("order_by")
    @ApiModelProperty(value = "排序:值越大越往后")
    private Integer orderBy;

}
