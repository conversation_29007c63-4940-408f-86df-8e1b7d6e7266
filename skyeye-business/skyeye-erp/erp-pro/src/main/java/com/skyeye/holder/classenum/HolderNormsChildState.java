/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.holder.classenum;

import com.skyeye.common.base.classenum.SkyeyeEnumClass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName: HolderNormsChildState
 * @Description: 关联的客户/供应商/会员购买或者出售的商品子信息状态枚举类
 * @author: skyeye云系列--卫志强
 * @date: 2024/6/5 11:16
 * @Copyright: 2024 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum HolderNormsChildState implements SkyeyeEnumClass {

    NORMAL_TRANSACTIONS(1, "正常交易", "green", true, true),
    RETURN_OF_GOODS(2, "退还货物", "red", true, false);

    private Integer key;

    private String value;

    private String color;

    private Boolean show;

    private Boolean isDefault;

}
