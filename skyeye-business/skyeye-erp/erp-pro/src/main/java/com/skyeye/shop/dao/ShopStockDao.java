/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.shop.dao;

import com.skyeye.eve.dao.SkyeyeBaseMapper;
import com.skyeye.shop.entity.ShopStock;

/**
 * @ClassName: ShopStockDao
 * @Description: 门店物料库存信息数据接口层
 * @author: skyeye云系列--卫志强
 * @date: 2023/3/31 16:56
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface ShopStockDao extends SkyeyeBaseMapper<ShopStock> {

}
