/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.brand.dao;

import com.skyeye.brand.entity.Brand;
import com.skyeye.eve.dao.SkyeyeBaseMapper;

/**
 * @ClassName: BrandDao
 * @Description: 品牌管理数据接口层
 * @author: skyeye云系列--卫志强
 * @date: 2024/6/17 21:16
 * @Copyright: 2024 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface Brand<PERSON><PERSON> extends SkyeyeBaseMapper<Brand> {
}
