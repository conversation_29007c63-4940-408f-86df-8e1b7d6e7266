/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.dynamic.dao;

import com.skyeye.dynamic.entity.DynamicAttrValue;
import com.skyeye.eve.dao.SkyeyeBaseMapper;

/**
 * @ClassName: DynamicAttrValueDao
 * @Description: 动态属性值数据层
 * @author: skyeye云系列--卫志强
 * @date: 2025/4/13 14:13
 * @Copyright: 2025 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface DynamicAttrValueDao extends SkyeyeBaseMapper<DynamicAttrValue> {
}
