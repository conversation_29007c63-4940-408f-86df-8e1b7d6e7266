/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.lifecycle.dao;

import com.skyeye.eve.dao.SkyeyeBaseMapper;
import com.skyeye.lifecycle.entity.LifecycleState;

/**
 * @ClassName: LifecycleStateDao
 * @Description: 生命周期状态管理数据层
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/3 20:45
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface LifecycleStateDao extends SkyeyeBaseMapper<LifecycleState> {

}
