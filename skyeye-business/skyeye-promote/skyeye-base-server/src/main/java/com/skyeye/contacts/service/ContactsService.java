/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.contacts.service;

import com.skyeye.base.business.service.SkyeyeTeamAuthService;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.contacts.entity.Contacts;

/**
 * @ClassName: ContactsService
 * @Description: 联系人管理服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2022/11/28 22:07
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface ContactsService extends SkyeyeTeamAuthService<Contacts> {

    void queryContactsListByObject(InputObject inputObject, OutputObject outputObject);

    void queryContactsListByObjectIds(InputObject inputObject, OutputObject outputObject);
}
