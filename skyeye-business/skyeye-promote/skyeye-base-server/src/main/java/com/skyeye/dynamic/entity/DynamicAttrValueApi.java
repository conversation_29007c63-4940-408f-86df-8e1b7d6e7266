/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.dynamic.entity;

import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: DynamicAttrValueApi
 * @Description: 批量动态属性值实体类
 * @author: skyeye云系列--卫志强
 * @date: 2025/4/13 14:13
 * @Copyright: 2025 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@ApiModel("批量动态属性值实体类")
public class DynamicAttrValueApi implements Serializable {

    @ApiModelProperty(value = "动态属性值列表", required = "required,json")
    private List<DynamicAttrValue> dynamicAttrValueList;

}
