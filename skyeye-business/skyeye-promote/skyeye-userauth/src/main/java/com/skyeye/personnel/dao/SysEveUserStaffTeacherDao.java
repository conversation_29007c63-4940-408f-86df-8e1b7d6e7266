/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.personnel.dao;

import com.skyeye.eve.dao.SkyeyeBaseMapper;
import com.skyeye.personnel.entity.SysEveUserStaffTeacher;
import org.apache.ibatis.annotations.Mapper;

/**
 * @ClassName: SysEveUserStaffTeacherDao
 * @Description: 员工所属学校关系数据接口层
 * @author: skyeye云系列--卫志强
 * @date: 2023/11/23 21:38
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目
 */

public interface SysEveUserStaffTeacherDao extends SkyeyeBaseMapper<SysEveUserStaffTeacher> {

}
