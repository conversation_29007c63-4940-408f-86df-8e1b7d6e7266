<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.menu.dao.AuthPointDao">
	
	<select id="queryMenuAuthPointList" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.auth_menu authMenu,
			a.menu_num menuNum,
			a.parent_id parentId,
			a.type,
			a.order_by orderBy,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime,
			(SELECT COUNT(1) FROM sys_eve_role_menu b WHERE b.menu_id = a.id) useNum,
			(SELECT COUNT(1) FROM sys_eve_menu_auth_point c WHERE c.parent_id = a.id) childNum
		FROM
			sys_eve_menu_auth_point a
		<where>
			a.object_id = #{objectId}
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != '' and keyword != null">
				AND (a.`name` like '%${keyword}%' OR a.menu_num like '%${keyword}%')
			</if>
		</where>
		ORDER BY a.order_by ASC, a.create_time DESC
	</select>

</mapper>