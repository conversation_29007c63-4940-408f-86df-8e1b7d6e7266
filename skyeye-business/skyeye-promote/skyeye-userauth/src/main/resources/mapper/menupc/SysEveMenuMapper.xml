<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.menu.dao.SysEveMenuDao">
	
	<select id="querySysMenuList" resultType="java.util.Map">
		SELECT
			a.id
		FROM
			sys_eve_menu a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<choose>
				<when test="keyword != null and keyword != ''">
					AND a.`name` LIKE '%${keyword}%'
				</when>
				<otherwise>
					AND a.parent_id = '0'
				</otherwise>
			</choose>
			<if test="sysWinId != '' and sysWinId != null and sysWinId != '0'.toString()">
				AND a.sys_win_id = #{sysWinId}
			</if>
		</where>
		ORDER BY a.order_num ASC
	</select>

	<select id="querySysMenuMationBySimpleLevel" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.desktop_id desktopId,
			a.level
		FROM
			sys_eve_menu a
			<if test="parentId != &quot;0&quot;">
				,sys_eve_menu b
			</if>
		WHERE a.parent_id = #{parentId}
		<if test="parentId != &quot;0&quot;">
			AND b.id = #{parentId}
			AND (b.level + 1) = a.level
		</if>
		ORDER BY a.order_num ASC
	</select>
	
	<select id="queryUseThisMenuRoleById" resultType="java.util.Map">
		SELECT 
			COUNT(*) roleNum
		FROM 
			sys_eve_role_menu d 
		WHERE d.menu_id = #{id}
	</select>
	
	<select id="queryAllChildIdsByParentId" resultType="java.lang.String">
		<foreach collection="ids" item="id" index="idx" separator="UNION">
			SELECT
				d.id
			FROM
				(
					SELECT
						@ids${idx} AS _ids,
						(
							SELECT
								@ids${idx} := GROUP_CONCAT(id)
							FROM
								sys_eve_menu
							WHERE FIND_IN_SET(parent_id, @ids${idx})
						) AS cids,
						@l${idx} := @l${idx} + 1 AS level
					FROM
						sys_eve_menu,
						(
							SELECT
								@ids${idx} := #{id},
								@l${idx} := 0
						) b
					WHERE @ids${idx} IS NOT NULL
				) i,
				sys_eve_menu d
			WHERE FIND_IN_SET(d.id, i._ids)
		</foreach>
	</select>

	<select id="queryAllMenuList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			*
		FROM
			(SELECT
				 a.id,
				 a.`name`,
				 CASE a.parent_id WHEN '0' THEN a.desktop_id ELSE a.parent_id END pId,
				 b.`name` sysName,
				 CASE a.page_url WHEN '--' THEN '盒子' ELSE '页面' END pageType
			 FROM
				 sys_eve_menu a
				 LEFT JOIN sys_eve_win b ON a.sys_win_id = b.id
			 ORDER BY a.order_num ASC) m
		UNION
		SELECT
			a.id,
			a.`name`,
			CASE a.parent_id WHEN '0' THEN a.object_id ELSE a.parent_id END pId,
			c.`name` sysName,
			CASE a.type WHEN '1' THEN '权限点' WHEN '2' THEN '数据权限分组' WHEN '3' THEN '数据权限' ELSE '' END pageType
		FROM
			sys_eve_menu_auth_point a,
			sys_eve_menu b
			LEFT JOIN sys_eve_win c ON b.sys_win_id = c.id
		WHERE a.object_id = b.id
	</select>

</mapper>