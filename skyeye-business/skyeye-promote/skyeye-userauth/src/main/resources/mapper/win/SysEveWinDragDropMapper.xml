<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.win.dao.SysEveWinDragDropDao">
	
	<select id="queryMenuBoxNameInByName" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.`name` menuBoxName
		FROM
			sys_eve_menu a,
			sys_eve_role_menu b,
			sys_eve_user c,
			sys_eve_role d
		WHERE INSTR(CONCAT(',', c.role_id, ','), CONCAT(',', d.id, ','))
		AND d.id = b.role_id
		AND a.id = b.menu_id
		AND c.id = #{userId}
		AND a.page_url = '--'
		AND a.level = '0'
		AND a.id NOT IN (SELECT e.menu_id AS id FROM sys_eve_user_custom_menu_del e WHERE e.create_id = #{userId})
		AND a.`name` = #{menuBoxName}
		GROUP BY a.id
		UNION
		SELECT
			a.menu_box_name menuBoxName
		FROM
			sys_eve_user_custom_menubox a
		WHERE a.create_id = #{userId}
		AND a.menu_box_name = #{menuBoxName}
	</select>
	
	<select id="queryWinCustomMenuBoxNumByUserId" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.order_num orderNum
		FROM
			sys_eve_user_custom_menubox a
		WHERE
			a.create_id = #{userId}
		ORDER BY a.order_num DESC
		LIMIT 1
	</select>
	
	<insert id="insertWinCustomMenuBox" parameterType="java.util.Map">
	     INSERT into sys_eve_user_custom_menubox 
	     (id, menu_box_name, order_num, desktop_id, create_id, create_time)
	     VALUES
	     (#{id}, #{menuBoxName}, #{order}, #{desktopId}, #{createId}, #{createTime})
	</insert>
	
	<select id="queryMenuNameInByName" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.`name` menuName
		FROM
			sys_eve_menu a,
			sys_eve_role_menu b,
			sys_eve_user c,
			sys_eve_role d
		WHERE INSTR(CONCAT(',', c.role_id, ','), CONCAT(',', d.id, ','))
		AND d.id = b.role_id
		AND a.id = b.menu_id
		AND c.id = #{userId}
		AND a.page_url != '--'
		AND a.level != '0'
		AND a.id NOT IN (SELECT e.menu_id AS id FROM sys_eve_user_custom_menu_del e WHERE e.create_id = #{userId})
		AND a.`name` = #{menuName}
		GROUP BY a.id
		UNION
		SELECT
			a.menu_name menuName
		FROM
			sys_eve_user_custom_menu a
		WHERE a.create_id = #{userId}
		AND a.menu_name = #{menuName}
	</select>
	
	<insert id="insertWinCustomMenu" parameterType="java.util.Map">
	     INSERT into sys_eve_user_custom_menu 
	     (id, menu_name, menu_name_en, menu_url, menu_icon_type, menu_icon_pic, menu_icon, menu_icon_color, menu_icon_bg, menu_type, menu_parent_id, open_type,
	      light_app_id, desktop_id, create_id, create_time)
	     VALUES
	     (#{id}, #{menuName}, #{menuNameEn}, #{menuUrl}, #{menuIconType}, #{menuIconPic}, #{menuIcon}, #{menuIconColor}, #{menuIconBg}, #{menuType}, #{menuParentId}, #{openType},
	      #{lightAppId}, #{desktopId}, #{createId}, #{createTime})
	</insert>
	
	<select id="queryMenuMationFromSysById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			'1' type
		FROM
			sys_eve_menu a
		WHERE a.id = #{id}
		AND a.id NOT IN (SELECT e.menu_id FROM sys_eve_user_custom_menu_del e WHERE e.create_id = #{userId})
		UNION 
		SELECT 
			b.id,
			'2' type
		FROM 
			sys_eve_user_custom_menubox b
		WHERE b.id = #{id}
		UNION
		SELECT
			c.id,
			'3' type
		FROM
			sys_eve_user_custom_menu c
		WHERE c.id = #{id}
	</select>
	
	<delete id="deleteCustomMenuById" parameterType="java.util.Map">
		DELETE
		FROM
			sys_eve_user_custom_menu
		WHERE
			id = #{id}
	</delete>
	
	<delete id="deleteCustomBoxMenuById" parameterType="java.util.Map">
		DELETE
		FROM
			sys_eve_user_custom_menubox
		WHERE
			id = #{id}
	</delete>
	
	<select id="queryChildsMenuById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			'1' type
		FROM
			sys_eve_menu a,
			sys_eve_role_menu b,
			sys_eve_user c,
			sys_eve_role d
		WHERE INSTR(CONCAT(',', a.parent_id, ','), CONCAT(',', #{id}, ','))
		AND INSTR(CONCAT(',', c.role_id, ','), CONCAT(',', d.id, ','))
		AND d.id = b.role_id
		AND a.id = b.menu_id
		AND c.id = #{userId}
		AND a.id NOT IN (SELECT e.menu_id FROM sys_eve_user_custom_menu_del e WHERE e.create_id = #{userId})
		UNION
		SELECT
			b.id,
			'2' type
		FROM
			sys_eve_user_custom_menu b,
			sys_eve_user_custom_parent c
		WHERE INSTR(CONCAT(',', c.parent_id, ','), CONCAT(',', #{id}, ','))
		AND b.create_id = #{userId}
		AND c.menu_id = b.id
	</select>
	
	<delete id="deleteCustomMenuByIds" parameterType="java.util.Map">
		DELETE
		FROM
			sys_eve_user_custom_menu
		WHERE
			INSTR(CONCAT(',', #{ids}, ','), CONCAT(',', id, ','))
	</delete>
	
	<delete id="deleteCustomMenuParentByIds" parameterType="java.util.Map">
		DELETE
		FROM
			sys_eve_user_custom_parent
		WHERE
			INSTR(CONCAT(',', #{ids}, ','), CONCAT(',', menu_id, ','))
	</delete>
	
	<insert id="deleteUserSysMenuByIds" parameterType="java.util.Map">
	     insert into sys_eve_user_custom_menu_del
	     (id, menu_id, create_id, create_time)
	     values
		<foreach collection="list" item="item" index="index" separator="," >  
			(#{item.rowId}, #{item.id}, #{item.createId}, #{item.createTime})  
		</foreach>  
	</insert>
	
	<insert id="deleteSysBoxMenuById" parameterType="java.util.Map">
	     INSERT into sys_eve_user_custom_menu_del 
	     (id, menu_id, create_id, create_time)
	     VALUES
	     (#{id}, #{menuId}, #{createId}, #{createTime})
	</insert>
	
	<delete id="delMenuParentIdById" parameterType="java.util.Map">
		DELETE
		FROM
			sys_eve_user_custom_parent
		WHERE
			menu_id = #{menuId}
		AND create_id = #{userId}
	</delete>
	
	<insert id="insertMenuParentId" parameterType="java.util.Map">
	     INSERT into sys_eve_user_custom_parent 
	     (id, menu_id, menu_level, parent_id, create_id, create_time)
	     VALUES
	     (#{id}, #{menuId}, #{menuLevel}, #{parentId}, #{userId}, #{createTime})
	</insert>
	
	<select id="queryMenuMationTypeById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			'1' menuType#系统菜单，禁止修改
		FROM
			sys_eve_menu a
		WHERE a.id = #{id}
		UNION
		SELECT
			b.id,
			'2' menuType#用户自定义快捷方式，可以修改
		FROM
			sys_eve_user_custom_menu b
		WHERE b.id = #{id}
		UNION
		SELECT
			c.id,
			'3' menuType#用户自定义菜单盒子，可以修改
		FROM
			sys_eve_user_custom_menubox c
		WHERE c.id = #{id}
	</select>
	
	<select id="queryCustomMenuBoxMationEditById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.menu_box_name menuBoxName
		FROM
			sys_eve_user_custom_menubox a
		WHERE a.id = #{id}
		AND a.create_id = #{userId}
	</select>
	
	<update id="editCustomMenuBoxMationById" parameterType="java.util.Map">
		UPDATE sys_eve_user_custom_menubox
		<set>
			menu_box_name = #{menuBoxName},
		</set>
		WHERE id = #{id}
		AND create_id = #{userId}
	</update>
	
	<select id="queryCustomMenuMationEditById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.menu_name menuName,
			a.menu_name_en menuNameEn,
			a.menu_url menuUrl,
			a.menu_icon menuIcon,
			a.menu_icon_color menuIconColor,
			a.menu_icon_bg menuIconBg,
			a.menu_icon_pic menuIconPic,
			a.menu_icon_type menuIconType
		FROM
			sys_eve_user_custom_menu a
		WHERE a.id = #{id}
		AND a.create_id = #{userId}
	</select>
	
	<update id="editCustomMenuMationById" parameterType="java.util.Map">
		UPDATE sys_eve_user_custom_menu
		<set>
			<if test="menuName != '' and menuName != null">
				menu_name = #{menuName},
			</if>
			<if test="menuNameEn != '' and menuNameEn != null">
				menu_name_en = #{menuNameEn},
			</if>
			menu_icon = #{menuIcon},
			menu_icon_pic = #{menuIconPic},
			<if test="menuIconType != '' and menuIconType != null">
				menu_icon_type = #{menuIconType},
			</if>
			<if test="menuUrl != '' and menuUrl != null">
				menu_url = #{menuUrl},
			</if>
			menu_icon_color = #{menuIconColor},
			menu_icon_bg = #{menuIconBg},
		</set>
		WHERE id = #{id}
		AND create_id = #{userId}
	</update>
	
	<select id="queryCustomMenuToDeskTopById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id
		FROM
			sys_eve_user_custom_menu_del a
		WHERE
			a.menu_id = #{id}
		AND a.create_id = #{userId}
	</select>
	
	<delete id="editCustomMenuToDeskTopById" parameterType="java.util.Map">
		DELETE
		FROM
			sys_eve_user_custom_menu_del
		WHERE
			menu_id = #{id}
		AND create_id = #{userId}
	</delete>
	
	<select id="queryMenuToDeskTopById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			c.id,
			c.`name`,
			c.page_url pageUrl,
			c.path,
			c.open_type openType,
			'-1' maxOpen,
			'false' extend,
			null childs,
			IFNULL(e.parent_id, c.parent_id) parentId,
			IFNULL(e.menu_level, c.level) menuLevel,
			c.icon_color menuIconColor,
			c.icon_bg menuIconBg,
			c.icon_pic menuIconPic,
			c.icon_type menuIconType,
			c.icon,
			IFNULL(c.desktop_id, '') desktopId
		FROM
			sys_eve_menu c
			LEFT JOIN sys_eve_user_custom_parent e ON e.menu_id = c.id AND e.create_id = #{userId}
		WHERE
			c.id = #{id}
	</select>
	
</mapper>