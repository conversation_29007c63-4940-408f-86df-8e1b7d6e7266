<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.win.dao.SysEveDesktopDao">

	<select id="querySysDesktopList" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.enabled,
			a.logo,
			a.code desktopCode,
			a.app_page_url appPageUrl,
			a.order_by orderBy,
			(SELECT COUNT(*) FROM sys_eve_menu b WHERE b.desktop_id = a.id) allNum,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			sys_eve_desktop a
		<where>
			a.delete_flag = #{deleteFlag}
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.`name` LIKE '%${keyword}%'
			</if>
		</where>
		ORDER BY a.order_by ASC
	</select>
	
    <select id="querySysDesktopStateAndMenuNumById" resultType="java.util.Map">
        SELECT
            a.enabled,
            (SELECT COUNT(*) FROM sys_eve_menu b WHERE b.desktop_id = a.id) allNum
        FROM
            sys_eve_desktop a	
        WHERE a.id = #{id}
    </select>

</mapper>