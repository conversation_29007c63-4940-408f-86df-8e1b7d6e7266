<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.dao.CompanyTalkGroupDao">

	<select id="queryGroupMemberByGroupId" resultType="java.util.Map">
		SELECT
			c.user_id id,
			c.user_name username,
			c.user_photo avatar,
			IFNULL(c.user_sign, '暂无签名') sign,
            a.create_id <![CDATA[ <=> ]]> c.user_id groupMaster
		FROM
			sys_talk_group a,
			sys_talk_group_user b,
			sys_eve_user_staff c
		WHERE 
			a.id = #{groupId}
		AND a.id = b.group_id
		AND b.user_id = c.user_id
		ORDER BY groupMaster DESC
	</select>
	
	<select id="queryGroupMemberByGroupIdAndNotThisUser" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			c.user_id id
		FROM
			sys_talk_group a,
			sys_talk_group_user b,
			sys_eve_user_staff c
		WHERE a.id = #{id}
		AND a.id = b.group_id
		AND b.user_id = c.user_id
		AND c.user_id != #{userId}
	</select>
	
	<select id="queryChatLogByPerToPer" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.send_id sendId,
			(SELECT b.user_name FROM sys_eve_user_staff b WHERE a.send_id = b.user_id) sendName,
			(SELECT b.user_name FROM sys_eve_user_staff b WHERE a.receive_id = b.user_id) receiveName,
			a.content,
			#{userId} userId,
			CONVERT(a.create_time, char) createTime
		FROM
			sys_talk_chat_history a
		WHERE ((a.send_id = #{userId} AND a.receive_id = #{receiveId})
		OR (a.send_id = #{receiveId} AND a.receive_id = #{userId}))
		AND a.chat_type = '1'
		ORDER BY a.create_time DESC
	</select>
	
	<select id="queryChatLogByPerToGroup" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.send_id sendId,
			(SELECT b.user_name FROM sys_eve_user_staff b WHERE a.send_id = b.user_id) sendName,
			(SELECT b.user_name FROM sys_eve_user_staff b WHERE a.receive_id = b.user_id) receiveName,
			a.content,
			#{userId} userId,
			CONVERT(a.create_time, char) createTime
		FROM
			sys_talk_chat_history a,
			sys_talk_group_user c
		WHERE a.send_id = #{userId} AND a.receive_id = #{receiveId}
		AND a.chat_type = '2'
		AND c.user_id = a.send_id
		AND a.create_time > c.create_time
		ORDER BY a.create_time DESC
	</select>
	
</mapper>