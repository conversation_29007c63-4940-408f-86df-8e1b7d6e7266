<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.dao.StickyNotesDao">

	<insert id="insertStickyNotesMation" parameterType="java.util.Map">
		insert into sticky_notes
		(id, content, create_id, create_time)
		values
		(#{id}, #{content}, #{createId}, #{createTime})  
	</insert>
	
	<delete id="deleteStickyNotesMation" parameterType="java.util.Map">
		DELETE
		FROM
			sticky_notes
		WHERE
			id = #{id}
	</delete>

	<select id="selectStickyNotesMation" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.content
		FROM
			sticky_notes a
		WHERE
			a.create_id = #{createId}
	</select>
	
	<update id="editStickyNotesMation" parameterType="java.util.Map">
		UPDATE sticky_notes
		<set>
			content = #{content}
		</set>
		WHERE id = #{id}
	</update>
	
</mapper>