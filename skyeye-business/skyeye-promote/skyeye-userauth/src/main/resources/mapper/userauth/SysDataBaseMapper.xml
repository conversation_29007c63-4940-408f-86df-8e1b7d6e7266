<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.dao.SysDataBaseDao">
	
	<select id="querySysDataBaseSelectList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.TABLE_NAME AS id,
			a.TABLE_NAME AS name
		FROM
			information_schema.COLUMNS a
		WHERE
			a.TABLE_SCHEMA = #{dbName}
		GROUP BY
			a.TABLE_NAME
	</select>
	
	<select id="querySysDataBaseDescSelectList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			b.TABLE_COMMENT AS id,
			b.TABLE_COMMENT AS name
		FROM
			information_schema. COLUMNS a
		LEFT JOIN information_schema. TABLES b ON a.TABLE_NAME = b.TABLE_NAME
		WHERE
			a.TABLE_SCHEMA = #{dbName}
		AND b.TABLE_COMMENT IS NOT NULL
		AND b.TABLE_COMMENT != ''
		GROUP BY b.TABLE_COMMENT
	</select>
	
</mapper>