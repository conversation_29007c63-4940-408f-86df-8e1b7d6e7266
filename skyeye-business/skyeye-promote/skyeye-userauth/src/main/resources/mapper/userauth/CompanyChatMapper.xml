<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.dao.CompanyChatDao">
	
	<select id="queryUserMineByUserId" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			b.user_name username,
			a.id,
			'online' status,
			b.user_photo avatar,
			b.company_id companyId,
			b.department_id departmentId,
			b.user_sign sign
		FROM
			sys_eve_user a,
			sys_eve_user_staff b
		WHERE a.id = b.user_id
		AND a.id = #{userId}
	</select>
	
	<select id="queryUserGroupByUserId" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			b.id,
			b.group_name groupname,
			b.group_img avatar,
			b.create_id createId
		FROM
			sys_talk_group_user a,
			sys_talk_group b
		WHERE
			a.group_id = b.id
		AND a.user_id = #{userId}
		AND b.state = '1'
	</select>
	
	<select id="queryCompanyDepartmentByUserId" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			b.id,
			b.`name` groupname,
			@rownum := @rownum + 1 AS `online`,
			#{userId} thisUserId
		FROM
			sys_eve_user_staff a,
			company_department b,
			( SELECT @rownum := 0 ) r
		WHERE a.user_id = #{userId}
		AND a.company_id = b.company_id
	</select>
	
	<select id="queryDepartmentUserByDepartId" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.user_name username,
			a.id staffId,
			b.id,
			'offline' status,
			a.user_photo avatar,
			IFNULL(a.user_sign, '暂无签名') sign,
			a.company_id companyId,
			a.department_id departmentId,
			a.job_id jobId
		FROM
			sys_eve_user_staff a,
			sys_eve_user b
		WHERE a.department_id = #{id}
		AND a.user_id = b.id
		AND a.state = '1'
		AND b.id != #{thisUserId}
		AND b.id != #{notUserId}
	</select>
	
	<update id="editUserSignByUserId" parameterType="java.util.Map">
		UPDATE sys_eve_user_staff
		<set>
			user_sign = #{userSign},
		</set>
		WHERE user_id = #{userId}
	</update>
	
</mapper>