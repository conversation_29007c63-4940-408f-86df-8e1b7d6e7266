/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.upload.service;

import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.upload.entity.File;

/**
 * @ClassName: FileService
 * @Description: 文件服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2024/8/18 19:55
 * @Copyright: 2024 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface FileService extends SkyeyeBusinessService<File> {

    File queryByPath(String path);

    void queryFileListByPath(InputObject inputObject, OutputObject outputObject);
}
