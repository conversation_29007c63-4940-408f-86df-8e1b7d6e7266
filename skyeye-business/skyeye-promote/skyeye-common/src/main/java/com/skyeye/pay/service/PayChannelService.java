/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.pay.service;

import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.pay.core.PayClient;
import com.skyeye.pay.entity.PayChannel;

/**
 * @ClassName: PayChannelService
 * @Description: 支付渠道服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2024/3/9 14:31
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface PayChannelService extends SkyeyeBusinessService<PayChannel> {

    PayClient getPayClient(String id);

    PayChannel getPayChannelByCode(String codeNum);

}
