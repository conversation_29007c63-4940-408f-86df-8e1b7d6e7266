/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.attr.dao;

import com.skyeye.attr.entity.AttrDefinitionCustom;
import com.skyeye.eve.dao.SkyeyeBaseMapper;

/**
 * @ClassName: AttrDefinitionCustomDao
 * @Description: 用户自定义服务类属性数据层
 * @author: skyeye云系列--卫志强
 * @date: 2022/12/30 10:55
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface AttrDefinitionCustomDao extends SkyeyeBaseMapper<AttrDefinitionCustom> {
}
