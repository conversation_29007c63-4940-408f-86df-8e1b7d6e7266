/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.framework.file.core.client;

import javax.validation.Validator;

/**
 * @ClassName: FileClientConfig
 * @Description: 文件客户端的配置，不同实现的客户端，需要不同的配置，通过子类来定义
 * @author: skyeye云系列--卫志强
 * @date: 2024/8/18 9:21
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface FileClientConfig {

    /**
     * 参数校验
     *
     * @param validator 校验对象
     */
    void validate(Validator validator);

}
