/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.operate.classenum;

import com.skyeye.common.base.classenum.SkyeyeEnumClass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName: PageOpenType
 * @Description: 页面布局打开方式枚举类
 * @author: skyeye云系列--卫志强
 * @date: 2021/7/18 23:29
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PageOpenType implements SkyeyeEnumClass {

    NORMAL(1, "正常打开", true, true),
    PARENT_PAGE(2, "父页面打开", true, false),
    NEW_WINDOW(3, "新窗口打开", true, false);

    private Integer key;

    private String value;

    private Boolean show;

    private Boolean isDefault;

}
