/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.attr.dao;

import com.skyeye.attr.entity.AttrDefinition;
import com.skyeye.eve.dao.SkyeyeBaseMapper;

/**
 * @ClassName: AttrDefinitionDao
 * @Description: 服务类属性管理数据层
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/18 13:11
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface AttrDefinitionDao extends SkyeyeBaseMapper<AttrDefinition> {
}
