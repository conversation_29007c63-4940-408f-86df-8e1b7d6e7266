<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>skyeye-promote</artifactId>
		<groupId>com.skyeye</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>skyeye-common</artifactId>

	<name>skyeye-common</name>
	<url>http://www.example.com</url>

	<dependencies>

		<!-- 引入公共Rest模块 -->
		<dependency>
			<groupId>com.skyeye</groupId>
			<artifactId>skyeye-common-rest</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.32</version>
        </dependency>
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.5.7</version>
		</dependency>

		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-pay</artifactId>
			<version>${weixin-java.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>wx-java-mp-spring-boot-starter</artifactId>
			<version>${weixin-java.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
			<version>${weixin-java.version}</version>
		</dependency>

	</dependencies>

</project>
