/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.email.dao;

import com.skyeye.common.entity.search.CommonPageInfo;
import com.skyeye.eve.dao.SkyeyeBaseMapper;
import com.skyeye.eve.email.entity.EmailSendModel;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: EmailSendModelDao
 * @Description: 邮件发送模板数据层
 * @author: skyeye云系列--卫志强
 * @date: 2021/10/31 22:51
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface EmailSendModelDao extends SkyeyeBaseMapper<EmailSendModel> {

    List<Map<String, Object>> queryEmailSendModelList(CommonPageInfo commonPageInfo);

}
