/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.seal.service;

import com.skyeye.base.business.service.SkyeyeFlowableService;
import com.skyeye.eve.seal.entity.SealRevert;

/**
 * @ClassName: SealApplyRevertService
 * @Description: 印章归还申请服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2021/7/24 17:40
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface SealApplyRevertService extends SkyeyeFlowableService<SealRevert> {

}
