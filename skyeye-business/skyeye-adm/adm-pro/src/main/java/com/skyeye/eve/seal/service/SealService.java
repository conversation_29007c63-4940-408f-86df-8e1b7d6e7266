/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.seal.service;

import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.eve.seal.entity.Seal;

/**
 * @ClassName: SealService
 * @Description: 印章管理服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2021/7/24 16:04
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface SealService extends SkyeyeBusinessService<Seal> {

    void queryAllEnabledSealList(InputObject inputObject, OutputObject outputObject);

    void queryMyRevertSealList(InputObject inputObject, OutputObject outputObject);

    /**
     * 设置印章领用信息
     *
     * @param id        印章id
     * @param useUserId 领用人id
     */
    void setSealUse(String id, String useUserId);

    /**
     * 设置印章归还信息
     *
     * @param id 印章id
     */
    void setSealRevert(String id);

    void queryMyRevertSealPageList(InputObject inputObject, OutputObject outputObject);
}
