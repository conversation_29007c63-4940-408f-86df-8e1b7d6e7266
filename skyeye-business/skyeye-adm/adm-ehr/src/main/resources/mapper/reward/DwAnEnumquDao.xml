<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.reward.dao.RewardPunishDao">

    <select id="queryRewardPunishList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
			a.id,
			a.`name`,
			a.price,
			a.content,
			a.reward_punish_time rewardPunishTime,
			a.award_unit awardUnit,
			a.type_id typeId,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			sys_staff_reward_punish a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.`name` like '%${keyword}%'
			</if>
			<if test="objectId != null and objectId != ''">
				AND a.object_id = #{objectId}
			</if>
		</where>
        ORDER BY a.create_time DESC
    </select>

</mapper>