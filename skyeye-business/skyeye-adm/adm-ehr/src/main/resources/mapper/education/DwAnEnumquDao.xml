<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.education.dao.EducationDao">

    <select id="queryEducationList" resultType="java.util.Map">
        SELECT
			a.id,
			a.graduction_school graductionSchool,
			a.major major,
			a.education_id educationId,
			a.learning_modality_id learningModalityId,
			a.school_nature schoolNature,
			a.start_time startTime,
			a.end_time endTIme,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			sys_staff_education a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
			</if>
			<if test="objectId != null and objectId != ''">
				AND a.object_id = #{objectId}
			</if>
		</where>
        ORDER BY a.create_time DESC
    </select>

</mapper>