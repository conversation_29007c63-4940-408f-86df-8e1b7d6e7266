<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.archives.dao.ArchivesDao">

    <select id="querySysStaffArchivesList" resultType="java.util.Map">
        SELECT
			a.id,
			a.archives_number archivesNumber,
			a.custody_place custodyPlace,
			a.archives_center archivesCenter,
			a.state state,
			a.archives_time archivesTime,
			a.remark,
			a.education_id educationId,
			a.whether_archives whetherArchives,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			sys_staff_archives a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.archives_number LIKE '%${keyword}%'
			</if>
			<if test="objectId != null and objectId != ''">
				AND a.object_id = #{objectId}
			</if>
		</where>
        ORDER BY a.create_time DESC
    </select>
    
</mapper>