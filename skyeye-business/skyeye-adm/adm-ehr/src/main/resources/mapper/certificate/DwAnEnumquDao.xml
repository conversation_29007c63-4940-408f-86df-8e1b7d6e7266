<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.certificate.dao.CertificateDao">

    <select id="queryCertificateList" resultType="java.util.Map">
        SELECT
			a.id,
			a.certificate_number certificateNumber,
			a.`name`,
			a.issue_organ issueOrgan,
			a.issue_time issueTime,
			a.type_id typeId,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			sys_staff_certificate a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND (a.certificate_number LIKE '%${keyword}%' OR a.`name` LIKE '%${keyword}%')
			</if>
			<if test="objectId != null and objectId != ''">
				AND a.object_id = #{objectId}
			</if>
		</where>
        ORDER BY a.create_time DESC
    </select>

</mapper>