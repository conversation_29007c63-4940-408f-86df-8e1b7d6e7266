<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>adm-common</module>
        <module>adm-pro</module>
        <module>adm-web</module>
        <module>adm-survey</module>
        <module>adm-ehr</module>
        <module>adm-ai</module>
    </modules>

    <parent>
        <groupId>com.skyeye</groupId>
        <artifactId>skyeye-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>com.skyeye</groupId>
    <artifactId>skyeye-adm</artifactId>
    <version>1.0-SNAPSHOT</version>

    <dependencies>

        <!-- solr客户 -->
        <dependency>
            <groupId>org.apache.solr</groupId>
            <artifactId>solr-solrj</artifactId>
        </dependency>

    </dependencies>

    <!-- 使用 huawei / aliyun 的 Maven 源，提升下载速度 -->
    <repositories>
        <repository>
            <id>huaweicloud</id>
            <name>huawei</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>

</project>