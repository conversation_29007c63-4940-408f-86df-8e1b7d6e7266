<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>skyeye-adm</artifactId>
        <groupId>com.skyeye</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>adm-common</artifactId>

    <dependencies>

        <!-- 引入公共Rest模块 -->
        <dependency>
            <groupId>com.skyeye</groupId>
            <artifactId>skyeye-common-rest</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- 电子书插件 -->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>15.8.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/aspose-words-15.8.0-jdk16.jar</systemPath>
        </dependency>

    </dependencies>

</project>