/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.voucher.service;

import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.voucher.entity.Voucher;

/**
 * @ClassName: IfsVoucherService
 * @Description: 凭证信息管理服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2022/1/3 18:19
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface IfsVoucherService extends SkyeyeBusinessService<Voucher> {

    void editIfsVoucherState(String id, Integer state);

}
