/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.school.subject.dao;

import com.skyeye.eve.dao.SkyeyeBaseMapper;
import com.skyeye.school.subject.entity.SubjectClassesStu;

/**
 * @ClassName: SubjectClassesStuDao
 * @Description: 科目表与班级表关系下的学生信息数据接口层
 * @author: skyeye云系列--卫志强
 * @date: 2024/6/12 8:18
 * @Copyright: 2024 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface SubjectClassesStuDao extends SkyeyeBaseMapper<SubjectClassesStu> {

}
