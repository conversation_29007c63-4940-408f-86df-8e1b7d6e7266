package com.skyeye.school.chat.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.skyeye.annotation.service.SkyeyeService;
import com.skyeye.base.business.service.impl.SkyeyeBusinessServiceImpl;
import com.skyeye.common.constans.CommonConstants;
import com.skyeye.common.entity.search.CommonPageInfo;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.common.util.DateUtil;
import com.skyeye.common.util.mybatisplus.MybatisPlusUtil;
import com.skyeye.exception.CustomException;
import com.skyeye.school.chat.enums.ChatFriendType;
import com.skyeye.school.chat.dao.TalkRequestDao;
import com.skyeye.school.chat.entity.TalkRequest;
import com.skyeye.school.chat.service.FriendRelationshipService;
import com.skyeye.school.chat.service.TalkRequestService;
import com.skyeye.school.common.entity.UserOrStudent;
import com.skyeye.school.common.service.SchoolCommonService;
import com.skyeye.school.student.entity.Student;
import com.skyeye.school.student.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@SkyeyeService(name = "好友申请管理", groupName = "好友申请管理")
public class TalkRequestServiceImpl extends SkyeyeBusinessServiceImpl<TalkRequestDao, TalkRequest> implements TalkRequestService {

    @Autowired
    private StudentService studentService;

    @Autowired
    private FriendRelationshipService friendRelationshipService;

    @Autowired
    private SchoolCommonService schoolCommonService;

    @Override
    protected void createPrepose(TalkRequest entity) {
        try {
            String createTime = entity.getCreateTime();
            if (StrUtil.isEmpty(createTime)) {
                throw new CustomException("createTime不能为空");
            }
            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(createTime);
            Date afDate = DateUtil.getAfDate(date, 7, "d");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedAfDate = sdf.format(afDate); // 格式化为 MySQL 所需的格式
            entity.setExpireTime(formattedAfDate);
        } catch (DateTimeParseException e) {
            throw new CustomException("时间格式不正确: " + e.getMessage());
        } catch (Exception e) {
            throw new CustomException("处理过期时间失败: " + e.getMessage());
        }
        entity.setStatus(ChatFriendType.PENDING_REQUEST.getIndex());
        //被申请人Id
        String recipientId = entity.getRecipientId();
        //申请人Id
        String applicantId = entity.getApplicantId();
        QueryWrapper<TalkRequest> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper ->
            wrapper.or(wrapperOr -> wrapperOr
                    .eq(MybatisPlusUtil.toColumns(TalkRequest::getRecipientId), recipientId)
                    .eq(MybatisPlusUtil.toColumns(TalkRequest::getApplicantId), applicantId))
                .or(wrapperOr -> wrapperOr
                    .eq(MybatisPlusUtil.toColumns(TalkRequest::getRecipientId), applicantId)
                    .eq(MybatisPlusUtil.toColumns(TalkRequest::getApplicantId), recipientId)))
            .and(wrapper -> wrapper
                .eq(MybatisPlusUtil.toColumns(TalkRequest::getStatus), ChatFriendType.PENDING_REQUEST.getIndex())
                .or()
                .eq(MybatisPlusUtil.toColumns(TalkRequest::getStatus), ChatFriendType.ACCEPTED.getIndex()));
        List<TalkRequest> talkRequestList = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(talkRequestList)) {
            throw new CustomException("禁止重新添加好友");
        }
    }

    @Override
    protected void createPostpose(TalkRequest entity, String userId) {
        String recipientId = entity.getRecipientId();
        String applicantId = entity.getApplicantId();
        Integer status = entity.getStatus();
        friendRelationshipService.addFriendRelationship(entity.getId(), applicantId, recipientId, status, entity.getCreateId());
    }

    @Override
    public void queryTalkRequestByRecipient(InputObject inputObject, OutputObject outputObject) {
        CommonPageInfo commonPageInfo = inputObject.getParams(CommonPageInfo.class);
        Page page = PageHelper.startPage(commonPageInfo.getPage(), commonPageInfo.getLimit());
        QueryWrapper<TalkRequest> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc(MybatisPlusUtil.toColumns(TalkRequest::getCreateTime));
        queryWrapper.eq(MybatisPlusUtil.toColumns(TalkRequest::getRecipientId), commonPageInfo.getHolderId());
        queryList(outputObject, commonPageInfo, page, queryWrapper);
    }

    @Override
    public void queryTalkRequestByApplicant(InputObject inputObject, OutputObject outputObject) {
        CommonPageInfo commonPageInfo = inputObject.getParams(CommonPageInfo.class);
        Page page = PageHelper.startPage(commonPageInfo.getPage(), commonPageInfo.getLimit());
        QueryWrapper<TalkRequest> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc(MybatisPlusUtil.toColumns(TalkRequest::getCreateTime));
        queryWrapper.eq(MybatisPlusUtil.toColumns(TalkRequest::getApplicantId), commonPageInfo.getHolderId());
        queryList(outputObject, commonPageInfo, page, queryWrapper);
    }

    private void queryList(OutputObject outputObject, CommonPageInfo commonPageInfo, Page page, QueryWrapper<TalkRequest> queryWrapper) {
        if (StrUtil.isNotEmpty(commonPageInfo.getState())) {
            queryWrapper.eq(MybatisPlusUtil.toColumns(TalkRequest::getStatus), commonPageInfo.getState());
        }
        List<TalkRequest> talkRequestList = list(queryWrapper);
        for (TalkRequest talkRequest : talkRequestList) {
            UserOrStudent userOrStudent = schoolCommonService.queryUserOrStudent(talkRequest.getApplicantId());
            if (userOrStudent.getUserOrStudent()) {
                talkRequest.setStudentApplicantMation(userOrStudent.getDataMation());
            } else {
                talkRequest.setTeacherApplicantMation(userOrStudent.getDataMation());
            }
        }
        outputObject.setBeans(talkRequestList);
        outputObject.settotal(page.getTotal());
    }

    @Override
    @Transactional
    public void changeFriendStatus(InputObject inputObject, OutputObject outputObject) {
        Map<String, Object> map = inputObject.getParams();
        String userId = map.get("id").toString();
        String status = map.get("status").toString();
        UpdateWrapper<TalkRequest> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CommonConstants.ID, userId);
        updateWrapper.set(MybatisPlusUtil.toColumns(TalkRequest::getStatus), status);
        update(updateWrapper);
        friendRelationshipService.changeFriendStatus(userId, status);
    }

    @Override
    public void queryTalkRequestFriend(InputObject inputObject, OutputObject outputObject) {
        Map<String, Object> map = inputObject.getParams();
        String id = map.get("id").toString();
        Student student = studentService.selectById(id);
        outputObject.setBean(student);
    }
}

