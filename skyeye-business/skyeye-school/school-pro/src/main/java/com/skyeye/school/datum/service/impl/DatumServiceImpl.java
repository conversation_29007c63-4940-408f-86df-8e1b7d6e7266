/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.school.datum.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.skyeye.annotation.service.SkyeyeService;
import com.skyeye.base.business.service.impl.SkyeyeBusinessServiceImpl;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.common.util.mybatisplus.MybatisPlusUtil;
import com.skyeye.school.chapter.service.ChapterService;
import com.skyeye.school.datum.dao.DatumDao;
import com.skyeye.school.datum.entity.Datum;
import com.skyeye.school.datum.service.DatumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: DatumServiceImpl
 * @Description: 资料信息管理服务层
 * @author: luyujia
 * @date: 2024/7/14 16:57
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Service
@SkyeyeService(name = "资料管理", groupName = "资料管理")
public class DatumServiceImpl extends SkyeyeBusinessServiceImpl<DatumDao, Datum> implements DatumService {

    @Autowired
    private ChapterService chapterService;

    @Override
    public Datum selectById(String id) {
        Datum datum = super.selectById(id);
        chapterService.setDataMation(datum, Datum::getChapterId);
        if (ObjectUtil.isNotEmpty(datum.getChapterMation())) {
            datum.getChapterMation().setRealName(String.format(Locale.ROOT, "第 %s 章 %s", datum.getChapterMation().getSection(), datum.getChapterMation().getName()));
        }
        iAuthUserService.setDataMation(datum, Datum::getCreateId);
        return datum;
    }

    @Override
    public void queryDatumListBySubjectId(InputObject inputObject, OutputObject outputObject) {
        Map<String, Object> map = inputObject.getParams();
        String subjectId = map.get("subjectId").toString();
        QueryWrapper<Datum> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(MybatisPlusUtil.toColumns(Datum::getObjectId), subjectId);
        queryWrapper.orderByDesc(MybatisPlusUtil.toColumns(Datum::getCreateTime));
        List<Datum> datumList = list(queryWrapper);
        if (CollectionUtil.isEmpty(datumList)) {
            return;
        }
        chapterService.setDataMation(datumList, Datum::getChapterId);
        datumList.forEach(datum -> {
            String serviceClassName = getServiceClassName();
            datum.setServiceClassName(serviceClassName);
            if (ObjectUtil.isNotEmpty(datum.getChapterMation())) {
                datum.getChapterMation().setRealName(String.format(Locale.ROOT, "第 %s 章 %s", datum.getChapterMation().getSection(), datum.getChapterMation().getName()));
            }
        });
        iAuthUserService.setDataMation(datumList, Datum::getCreateId);
        iAuthUserService.setName(datumList, "lastUpdateId", "lastUpdateName");
        outputObject.setBeans(datumList);
        outputObject.settotal(datumList.size());
    }

    @Override
    public Long queryClassDataNum(String subjectId) {
        QueryWrapper<Datum> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(MybatisPlusUtil.toColumns(Datum::getObjectId), subjectId);
        return count(queryWrapper);
    }

    @Override
    public Map<String, Long> queryDatumBySubjectIdAndStuIds(String subjectId, List<String> stuIds) {
        QueryWrapper<Datum> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(MybatisPlusUtil.toColumns(Datum::getObjectId), subjectId);
        queryWrapper.in(MybatisPlusUtil.toColumns(Datum::getCreateId), stuIds);
        List<Datum> List = list(queryWrapper);
        if(CollectionUtils.isEmpty(List)){
            return Collections.emptyMap();
        }
        // 统计每个创建人的资料数量stream流
        Map<String, Long> map = List.stream().collect(Collectors.groupingBy(Datum::getCreateId, Collectors.counting()));
        return map;
    }
}