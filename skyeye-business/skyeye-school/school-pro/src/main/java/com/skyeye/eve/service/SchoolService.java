/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.eve.service;

import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.eve.entity.School;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: SchoolController
 * @Description: 学校管理服务接口层
 * @author: skyeye云系列--卫志强
 * @date: 2023/8/6 21:13
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface SchoolService extends SkyeyeBusinessService<School> {

    void queryAllSchoolList(InputObject inputObject, OutputObject outputObject);

    void coverBackground(InputObject inputObject, OutputObject outputObject);

    Map<String, List<School>> selectByIdList(List<String> schoolIds);
}
