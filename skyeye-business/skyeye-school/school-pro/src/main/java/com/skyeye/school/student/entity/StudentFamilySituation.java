/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.school.student.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.common.entity.CommonInfo;
import lombok.Data;

/**
 * @ClassName: SchoolFamilySituation
 * @Description: 家庭情况实体类
 * @author: skyeye云系列--卫志强
 * @date: 2023/8/11 19:53
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@TableName(value = "school_student_family_situation")
@ApiModel(value = "学生家庭情况实体类")
public class StudentFamilySituation extends CommonInfo {

    @TableId("id")
    @ApiModelProperty("主键id。为空时新增，不为空时编辑")
    private String id;

    @TableField("school_id")
    @ApiModelProperty(value = "所属学校", required = "required")
    private String schoolId;

    @TableField("situation_id")
    @ApiModelProperty(value = "身心障碍", required = "required")
    private String bodyMindId;

    @TableField("student_id")
    @ApiModelProperty(value = "学生id", required = "required")
    private String studentId;
}
