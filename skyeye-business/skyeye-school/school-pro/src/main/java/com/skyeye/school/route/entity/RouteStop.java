package com.skyeye.school.route.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.common.entity.features.OperatorUserInfo;
import lombok.Data;

/**
 * @ClassName: RouteStop
 * @Description: 路线站点实体类
 * @author: skyeye云系列--lqy
 * @date: 2024/12/1 14:35
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@TableName(value = "school_route_stop")
@ApiModel(value = "路线站点实体类")
public class RouteStop extends OperatorUserInfo {

    @TableId("id")
    @ApiModelProperty(value = "主键id。为空时新增，不为空时编辑")
    private String id;

    @TableField("route_id")
    @ApiModelProperty(value = "路线id")
    private String routeId;

    @TableField("latitude")
    @ApiModelProperty(value = "纬度", required = "required")
    private String latitude;

    @TableField("longitude")
    @ApiModelProperty(value = "经度", required = "required")
    private String longitude;

    @TableField("stop_order")
    @ApiModelProperty(value = "站点排序")
    private int stopOrder;

}
