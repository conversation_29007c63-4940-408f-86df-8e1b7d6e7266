/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.school.score.dao;

import com.skyeye.eve.dao.SkyeyeBaseMapper;
import com.skyeye.school.score.entity.ScoreTypeChild;

/**
 * @ClassName: ScoreTypeChildDao
 * @Description: 成绩类型子表数据层
 * @author: skyeye云系列--卫志强
 * @date: 2023/8/29 10:53
 * @Copyright: 2023 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface ScoreTypeChildDao extends SkyeyeBaseMapper<ScoreTypeChild> {
}
