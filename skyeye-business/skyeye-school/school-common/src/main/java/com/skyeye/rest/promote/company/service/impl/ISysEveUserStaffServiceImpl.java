/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.rest.promote.company.service.impl;

import com.skyeye.base.rest.service.impl.IServiceImpl;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.entity.search.CommonPageInfo;
import com.skyeye.rest.promote.company.rest.ISysEveUserStaffRest;
import com.skyeye.rest.promote.company.service.ISysEveUserStaffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ISysEveUserStaffServiceImpl extends IServiceImpl implements ISysEveUserStaffService {

    @Autowired
    private ISysEveUserStaffRest iSysEveUserStaffRest;

    @Override
    public List<Map<String, Object>> querySysUserStaffList(CommonPageInfo commonPageInfo) {
        return ExecuteFeignClient.get(() -> iSysEveUserStaffRest.querySysUserStaffList(commonPageInfo)).getRows();
    }

}
