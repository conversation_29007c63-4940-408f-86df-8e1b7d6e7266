<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.bug.dao.AutoBugDao">

    <select id="queryAutoBugList" resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.no,
            a.state,
            a.handle_id handleId,
            a.object_id objectId,
            a.object_key objectKey,
            a.version_id versionId,
            a.module_id moduleId,
            a.environment_id environmentId,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            auto_bug a
        <where>
            <if test="moduleId != null and moduleId != ''">
                AND a.module_id = #{moduleId}
            </if>
            <if test="environmentId != null and environmentId != ''">
                AND a.environment_id = #{environmentId}
            </if>
            <if test="versionId != null and versionId != ''">
                AND a.version_id = #{versionId}
            </if>
            AND a.object_id = #{objectId}
            AND a.object_key = #{objectKey}
            <if test="type != null and type != ''">
                <!-- 待我解决的 -->
                <if test="type == 'myWaitResolved'">
                    AND a.handle_id = #{createId} AND a.state = 'unresolved'
                </if>
                <!-- 我已解决的 -->
                <if test="type == 'myResolved'">
                    AND a.handle_id = #{createId} AND a.state IN('toBeReturned', 'resolved')
                </if>
                <!-- 我处理的 -->
                <if test="type == 'myHandle'">
                    AND a.handle_id = #{createId}
                </if>
                <!-- 所有未解决的 -->
                <if test="type == 'unResolved'">
                    AND a.state = 'unresolved'
                </if>
                <!-- 所有待回归的 -->
                <if test="type == 'allToBeReturned'">
                    AND a.state = 'toBeReturned'
                </if>
                <!-- 待我回归的 -->
                <if test="type == 'toBeReturned'">
                    AND a.state = 'toBeReturned' AND a.create_id = #{createId}
                </if>
                <!-- 所有已解决的 -->
                <if test="type == 'resolved'">
                    AND a.state = 'resolved'
                </if>
                <!-- 我创建的 -->
                <if test="type == 'myCreate'">
                    AND a.create_id = #{createId}
                </if>
            </if>

            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.`name` like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>