<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.history.dao.AutoHistoryCaseDao">

    <select id="queryAutoCaseHistoryList" resultType="java.util.Map">
        SELECT
            a.id,
            a.name,
            a.module_id moduleId,
            a.execute_start_time executeStartTime,
            a.execute_end_time executeEndTime,
            a.execute_time executeTime,
            a.execute_result executeResult,
            a.result_key resultKey
        FROM
            auto_history_case a
        <where>
            a.case_id = #{objectId}
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != '' and keyword != null">
                AND a.`name` like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.execute_start_time DESC
    </select>

</mapper>






