<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.api.dao.AutoApiDao">

    <select id="queryAutoApiList" parameterType="java.util.Map"  resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.address,
            a.environment_id environmentId,
            a.server_id serverId,
            a.microservice_id microserviceId,
            a.module_id moduleId,
            a.request_way requestWay,
            a.object_id objectId,
            a.object_key objectKey,
            a.input_example inputExample,
            a.output_example outputExample,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            auto_api a
        <where>
            <if test="moduleId != null and moduleId != ''">
                AND a.module_id = #{moduleId}
            </if>
            <if test="objectId != null and objectId != ''">
                AND a.object_id = #{objectId}
            </if>
            <if test="objectKey != null and objectKey != ''">
                AND a.object_key = #{objectKey}
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (a.`name` like '%${keyword}%' OR a.address like '%${keyword}%')
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>