<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.database.dao.AutoDataBaseDao">

    <select id="getAutoDataBaseList" resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.driver_class driverClass,
            a.jdbc_url jdbcUrl,
            a.user,
            a.password,
            a.queryer_class queryerClass,
            a.pool_class poolClass,
            a.options,
            a.remark,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            auto_database a
        <where>
            <if test="objectId != null and objectId != ''">
                AND a.object_id = #{objectId}
            </if>
            <if test="objectKey != null and objectKey != ''">
                AND a.object_key = #{objectKey}
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.`name` like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>