<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.project.dao.AutoProjectDao">

    <select id="queryAutoProjectList" resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.product_id productId,
            a.state,
            a.team_template_id teamTemplateId,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            auto_project a
        <where>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.`name` like '%${keyword}%'
            </if>
            <!-- 我负责的 -->
            <if test="type != null and type != '' and type == 'myCharge'">
                <if test="ids != null and ids.size() &gt; 0">
                    <foreach collection="ids" item="id" separator="," open=" AND a.id in(" close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <!-- 我创建的 -->
            <if test="type != null and type != '' and type == 'myCreate'">
                AND a.create_id = #{createId}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>