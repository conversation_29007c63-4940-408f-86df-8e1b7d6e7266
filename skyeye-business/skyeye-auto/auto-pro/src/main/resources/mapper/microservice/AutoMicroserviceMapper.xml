<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.microservice.dao.AutoMicroserviceDao">

    <select id="queryAutoMicroserviceList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
            a.id,
            a.name,
            a.remark,
            a.protocol,
            a.port,
            a.path,
            a.environment_id environmentId,
            a.server_id serverId,
            a.object_id objectId,
            a.object_key objectKey,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            auto_microservice a
         <where>
             <if test="objectId != null and objectId != ''">
                 AND a.object_id = #{objectId}
             </if>
             <if test="objectKey != null and objectKey != ''">
                 AND a.object_key = #{objectKey}
             </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != '' and keyword != null">
                AND a.`name` like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>