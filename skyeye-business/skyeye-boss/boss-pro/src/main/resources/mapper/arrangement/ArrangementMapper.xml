<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.arrangement.dao.ArrangementDao">

    <select id="queryBossInterviewArrangementList" resultType="java.util.Map">
        SELECT
            a.id,
            a.odd_number oddNumber,
            a.interview_time interviewTime,
            a.person_require_id personRequireId,
            a.interviewer,
            a.interview_id interviewId,
            a.job_score_id jobScoreId,
            a.state,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            boss_interview_arrangement a
        <where>
            <if test="createId != '' and createId != null">
                AND a.create_id = #{createId}
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.odd_number like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="queryMyEntryBossPersonRequireAboutArrangementList" resultType="java.util.Map">
        SELECT
            a.id,
            a.odd_number oddNumber,
            a.interview_id interviewId,
            a.interview_time interviewTime,
            a.person_require_id personRequireId,
            a.job_score_id jobScoreId,
            a.interviewer,
            a.state,
            CONVERT(a.create_time, char) createTime
        FROM
            boss_person_require k,
            boss_interview_arrangement a
        <where>
            k.create_id = #{createId}
            AND k.id = a.person_require_id
            <if test="stateList != null and stateList.size() &gt; 0">
                <foreach collection="stateList" item="state" separator="," open=" AND a.state in(" close=")">
                    #{state}
                </foreach>
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.odd_number like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="queryArrangementInterviewerIsMyList" resultType="java.util.Map">
        SELECT
            a.id,
            a.odd_number oddNumber,
            a.interview_id interviewId,
            a.interview_time interviewTime,
            a.person_require_id personRequireId,
            a.job_score_id jobScoreId,
            a.interviewer,
            a.state,
            CONVERT(a.create_time, char) createTime
        FROM
            boss_interview_arrangement a
        <where>
            a.interviewer = #{createId}
            <if test="stateList != null and stateList.size() &gt; 0">
                <foreach collection="stateList" item="state" separator="," open=" AND a.state in(" close=")">
                    #{state}
                </foreach>
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.odd_number like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>