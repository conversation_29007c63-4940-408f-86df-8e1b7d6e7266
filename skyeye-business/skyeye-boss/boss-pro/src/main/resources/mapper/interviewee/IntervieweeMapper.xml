<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.interviewee.dao.IntervieweeDao">

    <select id="queryBossIntervieweeList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.sex,
            a.idcard,
            a.phone,
            a.from_id fromId,
            a.favorite_job favoriteJob,
            a.basic_resume basicResume,
            a.work_years workYears,
            a.state,
            a.charge_person_id chargePersonId,
            a.refuse_reason refuseReason,
            a.refuse_time refuseTime,
            a.last_join_department_id lastJoinDepartmentId,
            a.last_join_time lastJoinTime,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            boss_interview a
        <where>
            <if test="createId != '' and createId != null">
                AND a.create_id = #{createId}
            </if>
            <if test="chargePersonId != '' and chargePersonId != null">
                AND a.charge_person_id = #{chargePersonId}
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (a.`name` LIKE '%${keyword}%' OR a.phone LIKE '%${keyword}%')
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>