<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.jobtransfer.dao.JobTransferDao">

    <select id="queryJobTransferList" resultType="java.util.Map">
        SELECT
            a.id,
			a.odd_number oddNumber,
			a.transfer_type transferType,
			a.transfer_staff_id transferStaffId,
			a.primary_company_id primaryCompanyId,
			a.primary_department_id primaryDepartmentId,
			a.primary_job_id primaryJobId,
			a.primary_job_score_id primaryJobScoreId,
			a.current_company_id currentCompanyId,
			a.current_department_id currentDepartmentId,
			a.current_job_id currentJobId,
			a.current_job_score_id currentJobScoreId,
			a.remark,
			IFNULL(a.process_instance_id, '') processInstanceId,
			a.state,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            boss_interview_job_transfer a
        <where>
			a.create_id = #{createId}
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.odd_number like '%${keyword}%'
			</if>
		</where>
        ORDER BY a.create_time DESC
    </select>

	<update id="updateBossInterviewJobMation">
		UPDATE sys_eve_user_staff
		<set>
			company_id = #{currentCompanyId},
			department_id = #{currentDepartmentId},
			job_id = #{currentJobId},
			job_score_id = #{currentJobScoreId},
		</set>
		WHERE id = #{transferStaffId}
	</update>

</mapper>