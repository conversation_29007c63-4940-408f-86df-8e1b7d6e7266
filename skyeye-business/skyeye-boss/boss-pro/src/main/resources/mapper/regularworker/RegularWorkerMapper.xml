<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.regularworker.dao.RegularWorkerDao">

    <select id="queryBossRegularWorkerList" resultType="java.util.Map">
        SELECT
            a.id,
            a.odd_number oddNumber,
			a.department_id departmentId,
			a.job_id jobId,
			a.regular_time regularTime,
            IFNULL(a.process_instance_id, '') processInstanceId,
			a.state,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            boss_interview_regular_worker a
        <where>
            a.create_id = #{createId}
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.odd_number like '%${keyword}%'
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <update id="updateUserStaffState">
        UPDATE sys_eve_user_staff
        <set>
            state = #{state},
            become_worker_time = #{regularTime}
        </set>
        WHERE user_id = #{userId}
    </update>

</mapper>