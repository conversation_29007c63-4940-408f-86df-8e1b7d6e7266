/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.delivery.enums;

import com.skyeye.common.base.classenum.SkyeyeEnumClass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName: DeliveryExpressType
 * @Description: 快递配送计费方式枚举类
 * @author: skyeye云系列--卫志强
 * @date: 2024/9/20 17:16
 * @Copyright: 2024 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DeliveryExpressType implements SkyeyeEnumClass {

    COUNT(1, "按件", true, false),
    WEIGHT(2, "按重量", true, false),
    VOLUME(3, "按体积", true, false);

    private Integer key;

    private String value;

    private Boolean show;

    private Boolean isDefault;

}
