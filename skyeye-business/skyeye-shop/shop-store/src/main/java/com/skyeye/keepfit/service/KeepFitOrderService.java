/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.keepfit.service;

import com.skyeye.base.business.service.SkyeyeBusinessService;
import com.skyeye.common.object.InputObject;
import com.skyeye.common.object.OutputObject;
import com.skyeye.keepfit.entity.KeepFitOrder;

/**
 * @ClassName: KeepFitOrderService
 * @Description: 保养订单管理服务接口类
 * @author: skyeye云系列--卫志强
 * @date: 2022/2/8 15:14
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface KeepFitOrderService extends SkyeyeBusinessService<KeepFitOrder> {

    void keepFitOrderNotify(InputObject inputObject, OutputObject outputObject);

    void verificationOrder(InputObject inputObject, OutputObject outputObject);

    void complateKeepFitOrder(InputObject inputObject, OutputObject outputObject);
}
