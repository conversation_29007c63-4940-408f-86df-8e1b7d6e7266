<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.dao.MemberDao">

    <select id="queryMemberByList" resultType="java.util.Map">
        SELECT
            s.id,
            s.`name`,
            s.phone,
            s.email,
            s.remark,
            s.enabled,
            s.create_id createId,
            CONVERT(s.create_time, char) createTime,
            s.last_update_id lastUpdateId,
            CONVERT(s.last_update_time, char) lastUpdateTime
        FROM
            sys_member s
        <where>
            s.delete_flag = #{deleteFlag}
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="holderId != '' and holderId != null">
                AND s.store_id = #{holderId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (s.`name` like '%${keyword}%' OR s.phone like '%${keyword}%')
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>

    <select id="queryMemberByList_COUNT" resultType="java.lang.Long">
        SELECT
            COUNT(s.id)
        FROM
            sys_member s
        <where>
            s.delete_flag = #{deleteFlag}
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="holderId != '' and holderId != null">
                AND s.store_id = #{holderId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (s.`name` like '%${keyword}%' OR s.phone like '%${keyword}%')
            </if>
        </where>
    </select>

</mapper>