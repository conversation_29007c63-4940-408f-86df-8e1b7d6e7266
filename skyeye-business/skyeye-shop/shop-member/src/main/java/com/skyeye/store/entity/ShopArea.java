/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.store.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.skyeye.annotation.api.ApiModel;
import com.skyeye.annotation.api.ApiModelProperty;
import com.skyeye.annotation.cache.RedisCacheField;
import com.skyeye.annotation.unique.UniqueField;
import com.skyeye.common.constans.RedisConstants;
import com.skyeye.common.entity.features.BaseGeneralInfo;
import lombok.Data;

/**
 * @ClassName: ShopArea
 * @Description: 区域管理实体类
 * @author: skyeye云系列--卫志强
 * @date: 2022/2/4 10:12
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Data
@UniqueField
@RedisCacheField(name = "shop:area", cacheTime = RedisConstants.THIRTY_DAY_SECONDS)
@TableName(value = "shop_area")
@ApiModel("区域管理实体类")
public class ShopArea extends BaseGeneralInfo {

    @TableField(value = "enabled")
    @ApiModelProperty(value = "启用状态，参考#EnableEnum", required = "required,num")
    private Integer enabled;

}
