-- jvm堆的最小值3G
-Xms3072m
-- jvm堆的最大值3G
-Xmx3072m
-- 新生代内存区域的大小，设置新生代区域后，老年代内存=堆内存 - 新生代内存；老年代的最大内存 = 堆内存 - 新生代最大内存
-Xmn2048M
-- 新生代内存区域中Eden和Survivor的比例，SurvivorRatio=8，那么Eden区占8/10，2个Survivor区各占1/10
-XX:SurvivorRatio=8
-- 大小为区间为[0,15],如果高于15，JDK7 会默认15，JDK 8会启动报错
-XX:MaxTenuringThreshold=15
-- 输出GC的时间戳（以日期的形式，如 2019-05-04T21:53:59.234+0800）
-XX:+PrintGCDateStamps
-- 打印出GC的详细信息
-XX:+PrintGCDetails
-- 在进行GC的前后打印出堆的信息
-XX:+PrintHeapAtGC
-- 打印年轻代各个引用的数量以及时长
-XX:+PrintReferenceGC
-- 开启gc日志
-verbose:gc
-- gc日志的存放位置
-Xloggc:d:/gc.log
