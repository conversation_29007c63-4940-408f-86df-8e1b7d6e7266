/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.customer.dao;

import com.skyeye.customer.entity.CustomerMation;
import com.skyeye.customer.entity.CustomerQueryDo;
import com.skyeye.eve.dao.SkyeyeBaseMapper;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: CustomerDao
 * @Description: 客户信息管理数据层
 * @author: skyeye云系列--卫志强
 * @date: 2021/8/7 18:41
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface CustomerDao extends SkyeyeBaseMapper<CustomerMation> {

    List<Map<String, Object>> queryInternationalCustomerList(CustomerQueryDo customerQuery);
}
