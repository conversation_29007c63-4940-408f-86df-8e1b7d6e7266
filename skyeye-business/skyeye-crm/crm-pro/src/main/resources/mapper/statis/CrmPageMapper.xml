<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.statis.dao.CrmPageDao">
	
	<select id="queryInsertNumByYear" resultType="java.util.Map">
		SELECT
			a.`year_month` yearMonth,
			(SELECT COUNT(b.id) FROM crm_customer b WHERE date_format(b.create_time, '%Y-%m') = a.`year_month`) insertCustomerNum,
			(SELECT COUNT(c.id) FROM skyeye_contacts c WHERE date_format(c.create_time, '%Y-%m') = a.`year_month`) insertContactsNum
		FROM
			(SELECT CONCAT(#{year}, '-01') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-02') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-03') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-04') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-05') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-06') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-07') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-08') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-09') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-10') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-11') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-12') AS `year_month` ) a
		GROUP BY a.`year_month`
	</select>
	
	<select id="queryCustomNumByType" resultType="java.util.Map">
		SELECT
			COUNT(a.id) number,
			a.type_id dictDataId
		FROM
			crm_customer a
		GROUP BY a.type_id
	</select>
	
	<select id="queryCustomNumByFrom" resultType="java.util.Map">
		SELECT
			COUNT(a.id) number,
			a.from_id dictDataId
		FROM
			crm_customer a
		GROUP BY a.from_id
	</select>
	
	<select id="queryCustomNumByIndustry" resultType="java.util.Map">
		SELECT
			COUNT(a.id) number,
			a.industry_id dictDataId
		FROM
			crm_customer a
		GROUP BY a.industry_id
	</select>
	
	<select id="queryCustomNumByGroup" resultType="java.util.Map">
		SELECT
			COUNT(a.id) number,
			a.group_id dictDataId
		FROM
			crm_customer a
		GROUP BY a.group_id
	</select>
	
	<select id="queryCustomDocumentaryType" resultType="java.util.Map">
		SELECT
			COUNT(d.id) number,
			d.type_id dictDataId
		FROM
			crm_documentary d
		WHERE YEAR(d.create_time) = #{year}
		GROUP BY d.type_id
	</select>
	
	<select id="queryNewContractNum" resultType="java.util.Map">
		SELECT
			a.`year_month` yearMonth,
			(SELECT COUNT(b.id) FROM crm_contract b WHERE date_format(b.create_time, '%Y-%m') = a.`year_month`) insertContractNum,
			(SELECT CONVERT(SUM(IFNULL(c.price, 0)), decimal(24, 2)) FROM crm_contract c WHERE date_format(c.create_time, '%Y-%m') = a.`year_month`) insertContractPrice
		FROM
			(SELECT CONCAT(#{year}, '-01') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-02') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-03') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-04') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-05') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-06') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-07') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-08') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-09') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-10') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-11') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-12') AS `year_month` ) a
		GROUP BY a.`year_month`
	</select>
	
	<select id="queryNewDocumentaryNum" resultType="java.util.Map">
		SELECT
			a.`year_month` yearMonth,
			(SELECT COUNT(b.id) FROM crm_documentary b WHERE date_format(b.create_time, '%Y-%m') = a.`year_month`) insertDocumentaryNum
		FROM
			(SELECT CONCAT(#{year}, '-01') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-02') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-03') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-04') AS `year_month` 
			UNION SELECT CONCAT(#{year}, '-05') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-06') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-07') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-08') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-09') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-10') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-11') AS `year_month`
			UNION SELECT CONCAT(#{year}, '-12') AS `year_month` ) a
		GROUP BY a.`year_month`
	</select>
	
</mapper>